package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.google.android.material.tabs.TabLayout;
import com.yxl.commonlibrary.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.fragment.SalesSumInfoFragment;
import cn.bl.mobile.buyhoostore_sea.view.NoScrollViewPager;

/**
 * Describe:店铺-今日营业额-详情
 * Created by jingang on 2024/5/8
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SalesSumInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tabLayout)
    TabLayout tabLayout;
    @BindView(R.id.viewPager)
    NoScrollViewPager viewPager;

    private String[] titles = new String[]{getLanguageValue("collection"), getLanguageValue("refund")};
    private SimpleFragmentPagerAdapter sfpAdapter;
    private List<Fragment> fragmentList = new ArrayList<>();

    private String startTime, endTime, staffId;
    private int saleType,//0.线下 1.线上
            payMethod;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_sales_sum_info;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText(getIntent().getStringExtra("title"));
        startTime = getIntent().getStringExtra("startTime");
        endTime = getIntent().getStringExtra("endTime");
        staffId = getIntent().getStringExtra("staffId");
        payMethod = getIntent().getIntExtra("payMethod", 0);
        saleType = getIntent().getIntExtra("saleType", 0);
        setFragment();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        fragmentList.clear();
        for (int i = 0; i < titles.length; i++) {
            fragmentList.add(SalesSumInfoFragment.newInstance(startTime, endTime, staffId, saleType, payMethod, i + 1));
        }
        sfpAdapter = new SimpleFragmentPagerAdapter(getSupportFragmentManager(), fragmentList, titles);
        viewPager.setAdapter(sfpAdapter);
        viewPager.setCurrentItem(0);
        viewPager.setOffscreenPageLimit(titles.length);
        tabLayout.setupWithViewPager(viewPager);
    }
}
