package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;

/**
 * Describe:商品列表（实体类）
 * Created by jingang on 2022/12/1
 */
public class GoodsData implements Serializable {
    /**
     * goodsCount : 13
     * supplierUnique :
     * stockType : 1
     * saleCount : 11.0
     * goodsInPrice : 10.0
     * goodsPicturePath : /image/1497230981128/694788221153499.jpg
     * purCount : 0
     * goodsName : 老村长40度 450ml
     * goodsSalePrice : 2.3
     * goodsBarcode : 694788221153499
     * shelfStatue : 1
     */

    private String goodsId;
    private double goodsCount;
    private String supplierUnique;
    private int stockType;
    private double saleCount;
    private double goodsInPrice;
    private String goodsPicturePath;
    private int purCount;
    private String goodsName;
    private double goodsSalePrice;
    private String goodsBarcode;
    private int shelfState;//小程序上架状态 1.已上架 2.已下架
    private int pcShelfState;//收银机上架状态 1.已上架 2.已下架
    private boolean check;//是否选择
    private int cartNum;//购物车数量

    /*供货商详情-所供商品*/
    private int id;
    private String goodsImageUrl;
    private double goodsSalePriceUndoc;//未建档商品售价
    private double goodsInPriceUndoc;//未建档商品进价
    private String goodsAlias;//别名
    private int expirationDate;//保存天数
    private String goodsAddress;
    private String goodsStandard;//商品规格
    private String goodsUnit;//商品单位
    private String goodsProduceDate;//生产日期
    private double monthlySales;//月销量
    private int goodsChengType;

    /**
     * goodsBarcode : 6908512108211
     * goodsCount : 2.60
     * goodsName : 可口可乐1.25L()
     * goodsInPrice : 0.00
     * goodsPicturePath :
     * unit :
     * endTime : null
     * inventoryCount : null
     * dayCount : null
     * inBoundCount : 0
     * outBoundCount : 0
     */

    //扫码查询商品
    private String unit;
    private String goodsPosition;//货位id
    private String completePositionName;//货位名称

    public GoodsData() {
    }

    public GoodsData(String goodsId,String goodsBarcode,String goodsName,double goodsInPrice,double goodsSalePrice,int cartNum) {
        this.goodsId = goodsId;
        this.goodsBarcode = goodsBarcode;
        this.goodsName = goodsName;
        this.goodsInPrice = goodsInPrice;
        this.goodsSalePrice = goodsSalePrice;
        this.cartNum = cartNum;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public int getStockType() {
        return stockType;
    }

    public void setStockType(int stockType) {
        this.stockType = stockType;
    }

    public double getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(double saleCount) {
        this.saleCount = saleCount;
    }

    public double getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(double goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public int getPurCount() {
        return purCount;
    }

    public void setPurCount(int purCount) {
        this.purCount = purCount;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public double getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(double goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public int getShelfState() {
        return shelfState;
    }

    public void setShelfState(int shelfState) {
        this.shelfState = shelfState;
    }

    public int getPcShelfState() {
        return pcShelfState;
    }

    public void setPcShelfState(int pcShelfState) {
        this.pcShelfState = pcShelfState;
    }

    public int getCartNum() {
        return cartNum;
    }

    public void setCartNum(int cartNum) {
        this.cartNum = cartNum;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }

    public double getGoodsSalePriceUndoc() {
        return goodsSalePriceUndoc;
    }

    public void setGoodsSalePriceUndoc(double goodsSalePriceUndoc) {
        this.goodsSalePriceUndoc = goodsSalePriceUndoc;
    }

    public double getGoodsInPriceUndoc() {
        return goodsInPriceUndoc;
    }

    public void setGoodsInPriceUndoc(double goodsInPriceUndoc) {
        this.goodsInPriceUndoc = goodsInPriceUndoc;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public int getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(int expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsProduceDate() {
        return goodsProduceDate;
    }

    public void setGoodsProduceDate(String goodsProduceDate) {
        this.goodsProduceDate = goodsProduceDate;
    }

    public double getMonthlySales() {
        return monthlySales;
    }

    public void setMonthlySales(double monthlySales) {
        this.monthlySales = monthlySales;
    }

    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public String getCompletePositionName() {
        return completePositionName;
    }

    public void setCompletePositionName(String completePositionName) {
        this.completePositionName = completePositionName;
    }
}
