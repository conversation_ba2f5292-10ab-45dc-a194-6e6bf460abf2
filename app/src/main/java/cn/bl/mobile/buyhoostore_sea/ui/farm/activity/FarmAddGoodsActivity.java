package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ChuRuHistoryActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmGoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:农批-添加商品
 * Created by jingang on 2023/5/25
 */
@SuppressLint("NonConstantResourceId")
public class FarmAddGoodsActivity extends BaseActivity {
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvCate)
    TextView tvCate;
    @BindView(R.id.etPrice)
    EditText etPrice;
    @BindView(R.id.linOpen)
    LinearLayout linOpen;
    @BindView(R.id.ivImg)
    ImageView ivIMg;
    @BindView(R.id.ivDel)
    ImageView ivDel;

    private FarmGoodsData.GoodsListBean data;
    private String goodsBarcode, name, kindUnique, price;

    private boolean isOpen = true;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_farm_add_goods;
    }

    @Override
    public void initViews() {
        data = (FarmGoodsData.GoodsListBean) getIntent().getSerializableExtra("data");
//        goodsBarcode = getIntent().getStringExtra("goodsBarcode");
        setUI();
    }

    @OnClick({R.id.ivBack, R.id.tvChuRuRecord, R.id.tvPrint, R.id.tvCate, R.id.ivOpen, R.id.ivImg, R.id.ivDel, R.id.tvDel, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvChuRuRecord:
                //出入库记录
                goToActivity(ChuRuHistoryActivity.class);
                break;
            case R.id.tvPrint:
                //打印
                break;
            case R.id.tvCate:
                //选择商品分类
                startActivityForResult(new Intent(this, GoodsCateActivity.class)
                                .putExtra("type", 1)
                        , Constants.CATE);
                break;
            case R.id.ivOpen:
                //展开/收起
                isOpen = !isOpen;
                if (isOpen) {
                    linOpen.setVisibility(View.VISIBLE);
                } else {
                    linOpen.setVisibility(View.GONE);
                }
                break;
            case R.id.ivImg:
                //选择图片
                getImg();
                break;
            case R.id.ivDel:
                //删除图片
                IAlertDialog.showDialog(this,
                        "确认删除图片？",
                        "确认",
                        (dialog, which) -> {

                        });
                break;
            case R.id.tvDel:
                //删除
                break;
            case R.id.tvConfirm:
                //保存
                name = etName.getText().toString().trim();
                price = etPrice.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage("请输入货品名称");
                    return;
                }
                if (TextUtils.isEmpty(kindUnique)) {
                    showMessage("请选择分类");
                    return;
                }
                if (TextUtils.isEmpty(price)) {
                    showMessage("请输入指导价");
                    return;
                }
                if (Double.parseDouble(price) == 0) {
                    showMessage("请输入指导价");
                    return;
                }
                postGoods();
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        goodsBarcode = data.getGoodsBarcode();
        name = data.getGoodsName();
        kindUnique = data.getKindUnique();
        tvCate.setText(data.getKindName());
        price = DFUtils.getNum2(data.getGoodsPrice());
        etName.setText(name);
        etPrice.setText(price);
    }

    /**
     * 添加(编辑)农批商品
     */
    private void postGoods() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        map.put("goodsName", name);
        map.put("kindUnique", kindUnique);
        map.put("goodsPrice", price);
        map.put("goodsChengType", 1);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getFarmAddGoods(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setResult(Constants.ADD, new Intent());
                            finish();
                        }
                    }
                });
    }


    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(this, 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                runOnUiThread(() -> {
                    showMessage("上传失败，请重试");
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage("上传失败，请重试");
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage("上传失败，请重试");
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            Glide.with(FarmAddGoodsActivity.this)
                                    .load(data.getData().getUrl())
                                    .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                                    .into(ivIMg);
                            return;
                        }
                        showMessage("上传失败，请重试");
                    } catch (Exception e) {
                        showMessage("上传失败，请重试");
                    }
                });
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage("因权限未开启，该功能无法使用，请去设置中开启。");
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.CATE:
                    kindUnique = data.getStringExtra("unique");
                    tvCate.setText(data.getStringExtra("name"));
                    break;
            }
        }
    }
}
