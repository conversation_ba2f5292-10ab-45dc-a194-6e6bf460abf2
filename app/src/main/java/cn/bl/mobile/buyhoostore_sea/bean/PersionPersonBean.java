package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/9/26 0026.
 */
public class PersionPersonBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"powerPrice":2,"powerAdd":1,"powerCount":2,"powerSupplier":2,"powerPur":2,"powerKind":2,"staffName":"张三","powerChange":2,"powerInPrice":2,"powerName":1,"staffId":4,"powerDelete":1},{"powerPrice":1,"powerAdd":1,"powerCount":1,"powerSupplier":1,"powerPur":1,"powerKind":1,"staffName":"测试用二号店铺","powerChange":1,"powerInPrice":1,"powerName":1,"staffId":24,"powerDelete":1}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * powerPrice : 2
         * powerAdd : 1
         * powerCount : 2
         * powerSupplier : 2
         * powerPur : 2
         * powerKind : 2
         * staffName : 张三
         * powerChange : 2
         * powerInPrice : 2
         * powerName : 1
         * staffId : 4
         * powerDelete : 1
         */

        private int powerPrice;
        private int powerAdd;
        private int powerCount;
        private int powerSupplier;
        private int powerPur;
        private int powerKind;
        private String staffName;
        private int powerChange;
        private int powerInPrice;
        private int powerName;
        private int staffId;
        private int powerDelete;

        public int getPowerPrice() {
            return powerPrice;
        }

        public void setPowerPrice(int powerPrice) {
            this.powerPrice = powerPrice;
        }

        public int getPowerAdd() {
            return powerAdd;
        }

        public void setPowerAdd(int powerAdd) {
            this.powerAdd = powerAdd;
        }

        public int getPowerCount() {
            return powerCount;
        }

        public void setPowerCount(int powerCount) {
            this.powerCount = powerCount;
        }

        public int getPowerSupplier() {
            return powerSupplier;
        }

        public void setPowerSupplier(int powerSupplier) {
            this.powerSupplier = powerSupplier;
        }

        public int getPowerPur() {
            return powerPur;
        }

        public void setPowerPur(int powerPur) {
            this.powerPur = powerPur;
        }

        public int getPowerKind() {
            return powerKind;
        }

        public void setPowerKind(int powerKind) {
            this.powerKind = powerKind;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public int getPowerChange() {
            return powerChange;
        }

        public void setPowerChange(int powerChange) {
            this.powerChange = powerChange;
        }

        public int getPowerInPrice() {
            return powerInPrice;
        }

        public void setPowerInPrice(int powerInPrice) {
            this.powerInPrice = powerInPrice;
        }

        public int getPowerName() {
            return powerName;
        }

        public void setPowerName(int powerName) {
            this.powerName = powerName;
        }

        public int getStaffId() {
            return staffId;
        }

        public void setStaffId(int staffId) {
            this.staffId = staffId;
        }

        public int getPowerDelete() {
            return powerDelete;
        }

        public void setPowerDelete(int powerDelete) {
            this.powerDelete = powerDelete;
        }
    }
}
