package cn.bl.mobile.buyhoostore_sea.ui;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.view.BadgeView;

/**
 * tablayout（适配器）
 * Created by jingang on 2016/11/2.
 */
public class SimpleFragmentPagerWhiteAdapter extends FragmentPagerAdapter {
    private Context mContext;
    private List<Fragment> mFragmentList;     //  对应fragment的集合
    private List<String> mPageTitleList;      //  对应TabLayout对应的标题，与fragment相对应
    private List<Integer> mBadgeCountList;    //  对应TabLayout对应的标题，右上角的数字展示

    public SimpleFragmentPagerWhiteAdapter(Context context, FragmentManager fm, List<Fragment> fragmentList, List<String> pageTitleList, List<Integer> badgeCountList) {
        super(fm);
        this.mContext = context;
        this.mFragmentList = fragmentList;
        this.mPageTitleList = pageTitleList;
        this.mBadgeCountList = badgeCountList;
    }

    @Override
    public Fragment getItem(int position) {
        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mFragmentList.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return mPageTitleList.get(position);
    }

    /**
     * 根据角标获取标题item的布局文件
     */
    public View getTabItemView(int position) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_simple_fm_white, null);  // 标题布局
        TextView tvName = view.findViewById(R.id.tvItemName);
        tvName.setText(mPageTitleList.get(position));  // 设置标题内容
        LinearLayout.LayoutParams lp = new LinearLayout.
                LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(lp);
        View target = view.findViewById(R.id.tvItemCount);  // 右上角数字标记
        BadgeView badgeView = new BadgeView(mContext);
        badgeView.setTargetView(target);
        badgeView.setText(formatBadgeNumber(mBadgeCountList.get(position)));
        return view;
    }

    /**
     * 将int转化为String
     */
    public static String formatBadgeNumber(int value) {
        if (value <= 0) {
            return null;
        }
        if (value < 100) {
            return Integer.toString(value);
        }
        return "99+";
    }
}
