package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
public class NPLuZhiChuActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.et_zhichu)
    EditText zhichuET;
    @BindView(R.id.et_remark)
    EditText remarkET;
    @BindView(R.id.tv_select_pici)
    TextView selectPiCiTV;

    private MemberBean.DataBean mMemberBean;

    private File mFile;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_lu_zhi_chu;
    }

    @Override
    public void initViews() {
        tvTitle.setText("录入支出");
        tvRight.setText("支出记录");
        tvRight.setTextColor(ContextCompat.getColor(this, R.color.green));
    }

    private void submit() {

        if (TextUtils.isEmpty(zhichuET.getText())) {
            ToastUtil.showToast(this, "请输入欠款金额");
            return;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("cusName", zhichuET.getText());
        map.put("cusPhone", remarkET.getText());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAddCusNP(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        ToastUtil.showToast(NPLuZhiChuActivity.this, "添加成功");
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFRESH_MEMBER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @OnClick({R.id.saveTV, R.id.ivBack, R.id.tvRight,R.id.tv_select_pici})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                goToActivity(NPZhiChuRecordActivity.class);

                break;
            case R.id.saveTV:
                submit();
                break;

        }
    }



}
