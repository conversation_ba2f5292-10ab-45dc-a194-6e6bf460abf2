package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:调拨单-商品详情（适配器）
 * Created by jingang on 2023/5/19
 */
public class AllotGoodsDialogAdapter extends BaseAdapter<AllotData.DetailInfoListBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public AllotGoodsDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_allot_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice, tvSpecs, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvSpecs = holder.getView(R.id.tvItemSpecs);
        tvCount = holder.getView(R.id.tvItemCount);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvPrice.setText("RM" + DFUtils.getNum2(mDataList.get(position).getGoodsSalePrice()) + "/" + mDataList.get(position).getGoodsUnit());
        tvSpecs.setText(getLanguageValue("spec") + " " + mDataList.get(position).getGoodsSpec());
        tvCount.setText("x" + DFUtils.getNum2(mDataList.get(position).getPurchaseListDetailCount()));
    }
}
