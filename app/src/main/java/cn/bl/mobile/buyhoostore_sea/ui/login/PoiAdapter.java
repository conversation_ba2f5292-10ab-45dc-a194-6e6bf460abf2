package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.amap.api.services.core.PoiItem;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:地图选址（适配器）
 * Created by jingang on 2023/11/17
 */
public class PoiAdapter extends BaseAdapter<PoiItem> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public PoiAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_poi;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvPoi, tvAddress;
        tvPoi = holder.getView(R.id.tvItemPoi);
        tvAddress = holder.getView(R.id.tvItemAddress);
        tvPoi.setText(mDataList.get(position).toString());
        tvAddress.setText(mDataList.get(position).getProvinceName()
                + mDataList.get(position).getCityName()
                + mDataList.get(position).getAdName()
                + mDataList.get(position).getSnippet());
    }
}
