package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.BaseData;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotGoodsDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotListData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-调拨单-搜索
 * Created by jingang on 2023/5/20
 */
@SuppressLint("NonConstantResourceId")
public class AllotSearchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keyWords;

    private AllotAdapter mAdapter;
    private List<AllotData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_search;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("search"));
        etSearch.setHint(getLanguageValue("search") + getLanguageValue("commodity") + getLanguageValue("name") + "/" + getLanguageValue("barcode"));
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getAllotList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @OnClick({R.id.ivBack, R.id.ivClear, R.id.ivScan})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                break;
            case R.id.ivScan:
                //扫一哈子
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "state":
                page = 1;
                getAllotList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new AllotAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getAllotList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getAllotList();
            }
        });
        mAdapter.setListener(new AllotAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(AllotSearchActivity.this, AllotInfoActivity.class)
                        .putExtra("data", dataList.get(position))
                );
            }

            @Override
            public void onGoodsClick(View view, int position) {
                //商品
                showDialogGoods(dataList.get(position).getDetailInfoList());
            }

            @Override
            public void onOperationClick(View view, int position) {
                //操作
                switch (dataList.get(position).getAllocationStatus()) {
                    case 2:
                        IAlertDialog.showDialog(AllotSearchActivity.this,
                                getLanguageValue("confirm") + getLanguageValue("revoke") + getLanguageValue("transfer") + "?",
                                getLanguageValue("confirm"),
                                (DialogInterface.OnClickListener) (dialog, which) -> {
                                    postAllotQuash(dataList.get(position).getPurchaseListId());
                                });
                        break;
                    case 3:
                        IAlertDialog.showDialog(AllotSearchActivity.this,
                                getLanguageValue("confirm") + getLanguageValue("receipt") + "?",
                                getLanguageValue("confirm"),
                                (DialogInterface.OnClickListener) (dialog, which) -> {
                                    postAllotFinish(dataList.get(position).getPurchaseListId());
                                });
                        break;
                }
            }
        });
    }

    /**
     * dialog（商品详情）
     *
     * @param detailInfoList
     */
    private void showDialogGoods(List<AllotData.DetailInfoListBean> detailInfoList) {
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_allot_goods, null);
        dialog.setContentView(view);

        TextView tvTitle = view.findViewById(R.id.tvDialogTitle);
        RecyclerView rvGoods = view.findViewById(R.id.rvDialogGoods);
        tvTitle.setText(getLanguageValue("commodity")+getLanguageValue("details"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        view.findViewById(R.id.vDialog).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());

        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        AllotGoodsDialogAdapter adapter = new AllotGoodsDialogAdapter(this);
        rvGoods.setAdapter(adapter);
        adapter.setDataList(detailInfoList);
        adapter.setOnItemClickListener((view1, position) -> {

        });

    }

    /**
     * 调拨单列表
     */
    private void getAllotList() {
        if (TextUtils.isEmpty(keyWords)) {
            showMessage(getLanguageValue("enterSearchKeyword"));
            smartRefreshLayout.finishRefresh();
            smartRefreshLayout.finishLoadMore();
            return;
        }
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("searchKey", keyWords);
        map.put("pullStoreOfId", getShop_id());//调出 店铺 ID
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAllot_list(),
                map,
                AllotListData.class,
                new RequestListener<AllotListData>() {
                    @Override
                    public void success(AllotListData allotListData) {
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (allotListData.getRows() != null) {
                            dataList.addAll(allotListData.getRows());
                        }
                        if (dataList.size() > 0) {
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 调拨-撤销
     *
     * @param id
     */
    private void postAllotQuash(int id) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getAllot_quash(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 调拨-确认收货
     *
     * @param id
     */
    private void postAllotFinish(int id) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getAllot_finish(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    etSearch.setText(data.getStringExtra("result"));
                    //搜索
                    break;
            }
        }
    }
}
