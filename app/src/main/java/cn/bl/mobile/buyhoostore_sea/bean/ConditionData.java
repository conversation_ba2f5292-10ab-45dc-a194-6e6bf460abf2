package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Describe:选择条件（实体类）
 * Created by jingang on 2023/2/2
 */
public class ConditionData {
    private String name;
    private int value;
    private boolean check;

    public ConditionData(String name, int value, boolean check) {
        this.name = name;
        this.value = value;
        this.check = check;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }
}
