package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsCateImgData;

/**
 * Describe:商品分类图标（适配器）
 * Created by jingang on 2023/8/22
 */
public class GoodsCateImgAdapter extends BaseAdapter<GoodsCateImgData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public GoodsCateImgAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_cate_img;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        FrameLayout frameLayout = holder.getView(R.id.flItem);
        ImageView ivImg, ivCheck;
        ivImg = holder.getView(R.id.ivItemImg);
        ivCheck = holder.getView(R.id.ivItemCheck);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_kind_icon_picture()))
                .apply(RequestOptions.bitmapTransform(new CircleCrop()).error(R.mipmap.ic_nothing))
                .into(ivImg);
        if (mDataList.get(position).isCheck()) {
            frameLayout.setBackgroundResource(R.drawable.shape_yuan_blue_kuang);
            ivCheck.setVisibility(View.VISIBLE);
        } else {
            frameLayout.setBackgroundResource(0);
            ivCheck.setVisibility(View.GONE);
        }
    }
}
