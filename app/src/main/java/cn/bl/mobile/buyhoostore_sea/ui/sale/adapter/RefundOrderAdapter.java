package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.RefundOrderBean;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:销售-退款单（适配器）
 * Created by jingang on 2023/4/18
 */
public class RefundOrderAdapter extends BaseAdapter<RefundOrderBean> {

    public RefundOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_refund_order;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivHead = holder.getView(R.id.ivItemHead);
        TextView tvName, tvTime, tvNo, tvStatus, tvTotalOf, tvCount, tvPieces, tvBeRefund, tvTotal, tvReason, tvCancel, tvConfirm;
        tvName = holder.getView(R.id.tvItemName);
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvStatus = holder.getView(R.id.tvItemStatus);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);
        tvTotalOf = holder.getView(R.id.tvItemTotalOf);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPieces = holder.getView(R.id.tvItemPieces);
        tvBeRefund = holder.getView(R.id.tvItemBeRefund);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvReason = holder.getView(R.id.tvItemReason);
        View vLine = holder.getView(R.id.vItemLine);
        tvCancel = holder.getView(R.id.tvItemCancel);
        tvConfirm = holder.getView(R.id.tvItemConfirm);

        tvTotalOf.setText(getLanguageValue("aTotalOf"));
        tvPieces.setText(getLanguageValue("pieces"));
        tvBeRefund.setText(getLanguageValue("beRefunded"));
        tvCancel.setText(getLanguageValue("denied"));
        tvConfirm.setText(getLanguageValue("agree") + getLanguageValue("refund"));

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getCusProtrait()))
                .centerCrop()
                .circleCrop()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .into(ivHead);
        tvName.setText(mDataList.get(position).getCusName());
        tvTime.setText(getLanguageValue("apply") + mDataList.get(position).getRetListDatetime());
        tvNo.setText(mDataList.get(position).getRetListUnique());
        switch (mDataList.get(position).getRetListHandlestate()) {
            case 1:
                tvStatus.setText(getLanguageValue("beReviewed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                vLine.setVisibility(View.VISIBLE);
                tvCancel.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.VISIBLE);
                break;
            case 3:
                tvStatus.setText(getLanguageValue("refunded"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                vLine.setVisibility(View.GONE);
                tvCancel.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                break;
            case 4:
                tvStatus.setText(getLanguageValue("refused"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.GONE);
                tvCancel.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                vLine.setVisibility(View.GONE);
                tvCancel.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                break;
        }
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getRetListCount()));
        tvTotal.setText("RM" + DFUtils.getNum2(mDataList.get(position).getRetListTotal()));
        tvReason.setText(getLanguageValue("refund") + getLanguageValue("reason") + ":" + mDataList.get(position).getRetListReason());

        //商品列表
        if (mDataList.get(position).getGoodsList().size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            recyclerView.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
            RefundOrderGoodsAdapter adapter = new RefundOrderGoodsAdapter(mContext);
            recyclerView.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getGoodsList());
            adapter.setOnItemClickListener((view, position1) -> {
                if (listener != null) {
                    listener.onClick(0, mDataList.get(position));
                }
            });
        } else {
            recyclerView.setVisibility(View.GONE);
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onClick(0,mDataList.get(position)));
            tvCancel.setOnClickListener(v -> listener.onClick(1,mDataList.get(position)));
            tvConfirm.setOnClickListener(v -> listener.onClick(2, mDataList.get(position)));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(int type, RefundOrderBean saleOrderBean);
    }
}
