package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:订单列表（实体类）
 * Created by jingang on 2022/12/10
 */
public class OrderListData implements Serializable {

    /**
     * /**
     * saleListId : 4113
     * saleListUnique : 1670490842933655
     * retListUnique :
     * dateTime : 2022-12-08 17:14:03
     * shopUnique : 1536215939565
     * saleListTotal : 3.0
     * saleListPur : 1.5
     * saleListDelfee : 0.0
     * totalCount : 1.0
     * saleType : 0
     * saleListName : 本地收银
     * saleListPhone :
     * saleListAddress :
     * saleListState : 已付款
     * saleListStateCode : 3
     * handleState : 已完成
     * handleStateCode : 4
     * saleListPayment : 微信
     * payMethods : []
     * saleListPaymentCode : 3
     * saleListRemarks : 普通订单
     * receiptDateTime :
     * sendDateTime :
     * saleListNumber : 0
     * saleListCashier : 3586
     * actuallyReceived : 3.0
     * machineNum : 1
     * shipping_method : 1
     * delivery_type : null
     * peisong_money : null
     * coupon_amount : null
     * card_deduction : 0.0
     * beans_money : 0.0
     * delivery_status : 配送单已确定
     * listDetail : [{"saleListDetailId":6303,"goodsBarcode":"6938315016123","goodsName":"抽纸","goodsPicturepath":"/image/1548662488097/8256a36a-3dd1-4d75-8f6b-fe52920aa433.jpg","saleListDetailCount":1,"saleListDetailPrice":3,"subTotal":3,"goods_unit":null,"goods_purprice":null}]
     */

    private int saleListId;
    private long saleListUnique;
    private String retListUnique;
    private String dateTime;
    private long shopUnique;
    private double saleListTotal;
    private double saleListPur;
    private double saleListDelfee;
    private double totalCount;
    private String saleType;
    private String saleListName;
    private String saleListPhone;
    private String saleListAddress;
    private String saleListState;
    private int saleListStateCode;
    private String handleState;
    private int handleStateCode;
    private String saleListPayment;
    private int saleListPaymentCode;
    private String saleListRemarks;
    private String receiptDateTime;
    private String sendDateTime;
    private int saleListNumber;
    private int saleListCashier;
    private double actuallyReceived;
    private int machineNum;
    private int shipping_method;
    private int delivery_type;
    private Object peisong_money;
    private Object coupon_amount;
    private double card_deduction;
    private double beans_money;
    private String delivery_status;
    private String cusProtrait;
    private String driverName;
    private String driverPhone;
    private String staffName;//收银员
    private List<?> payMethods;
    private List<ListDetailBean> listDetail;

    public int getSaleListId() {
        return saleListId;
    }

    public void setSaleListId(int saleListId) {
        this.saleListId = saleListId;
    }

    public long getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(long saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public double getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(double saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public double getSaleListPur() {
        return saleListPur;
    }

    public void setSaleListPur(double saleListPur) {
        this.saleListPur = saleListPur;
    }

    public double getSaleListDelfee() {
        return saleListDelfee;
    }

    public void setSaleListDelfee(double saleListDelfee) {
        this.saleListDelfee = saleListDelfee;
    }

    public double getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(double totalCount) {
        this.totalCount = totalCount;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getSaleListName() {
        return saleListName;
    }

    public void setSaleListName(String saleListName) {
        this.saleListName = saleListName;
    }

    public String getSaleListPhone() {
        return saleListPhone;
    }

    public void setSaleListPhone(String saleListPhone) {
        this.saleListPhone = saleListPhone;
    }

    public String getSaleListAddress() {
        return saleListAddress;
    }

    public void setSaleListAddress(String saleListAddress) {
        this.saleListAddress = saleListAddress;
    }

    public String getSaleListState() {
        return saleListState;
    }

    public void setSaleListState(String saleListState) {
        this.saleListState = saleListState;
    }

    public int getSaleListStateCode() {
        return saleListStateCode;
    }

    public void setSaleListStateCode(int saleListStateCode) {
        this.saleListStateCode = saleListStateCode;
    }

    public String getHandleState() {
        return handleState;
    }

    public void setHandleState(String handleState) {
        this.handleState = handleState;
    }

    public int getHandleStateCode() {
        return handleStateCode;
    }

    public void setHandleStateCode(int handleStateCode) {
        this.handleStateCode = handleStateCode;
    }

    public String getSaleListPayment() {
        return saleListPayment;
    }

    public void setSaleListPayment(String saleListPayment) {
        this.saleListPayment = saleListPayment;
    }

    public int getSaleListPaymentCode() {
        return saleListPaymentCode;
    }

    public void setSaleListPaymentCode(int saleListPaymentCode) {
        this.saleListPaymentCode = saleListPaymentCode;
    }

    public String getSaleListRemarks() {
        return saleListRemarks;
    }

    public void setSaleListRemarks(String saleListRemarks) {
        this.saleListRemarks = saleListRemarks;
    }

    public String getReceiptDateTime() {
        return receiptDateTime;
    }

    public void setReceiptDateTime(String receiptDateTime) {
        this.receiptDateTime = receiptDateTime;
    }

    public String getSendDateTime() {
        return sendDateTime;
    }

    public void setSendDateTime(String sendDateTime) {
        this.sendDateTime = sendDateTime;
    }

    public int getSaleListNumber() {
        return saleListNumber;
    }

    public void setSaleListNumber(int saleListNumber) {
        this.saleListNumber = saleListNumber;
    }

    public int getSaleListCashier() {
        return saleListCashier;
    }

    public void setSaleListCashier(int saleListCashier) {
        this.saleListCashier = saleListCashier;
    }

    public double getActuallyReceived() {
        return actuallyReceived;
    }

    public void setActuallyReceived(double actuallyReceived) {
        this.actuallyReceived = actuallyReceived;
    }

    public int getMachineNum() {
        return machineNum;
    }

    public void setMachineNum(int machineNum) {
        this.machineNum = machineNum;
    }

    public int getShipping_method() {
        return shipping_method;
    }

    public void setShipping_method(int shipping_method) {
        this.shipping_method = shipping_method;
    }

    public int getDelivery_type() {
        return delivery_type;
    }

    public void setDelivery_type(int delivery_type) {
        this.delivery_type = delivery_type;
    }

    public Object getPeisong_money() {
        return peisong_money;
    }

    public void setPeisong_money(Object peisong_money) {
        this.peisong_money = peisong_money;
    }

    public Object getCoupon_amount() {
        return coupon_amount;
    }

    public void setCoupon_amount(Object coupon_amount) {
        this.coupon_amount = coupon_amount;
    }

    public double getCard_deduction() {
        return card_deduction;
    }

    public void setCard_deduction(double card_deduction) {
        this.card_deduction = card_deduction;
    }

    public double getBeans_money() {
        return beans_money;
    }

    public void setBeans_money(double beans_money) {
        this.beans_money = beans_money;
    }

    public String getDelivery_status() {
        return delivery_status;
    }

    public void setDelivery_status(String delivery_status) {
        this.delivery_status = delivery_status;
    }

    public String getCusProtrait() {
        return cusProtrait;
    }

    public void setCusProtrait(String cusProtrait) {
        this.cusProtrait = cusProtrait;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public List<?> getPayMethods() {
        return payMethods;
    }

    public void setPayMethods(List<?> payMethods) {
        this.payMethods = payMethods;
    }

    public List<ListDetailBean> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<ListDetailBean> listDetail) {
        this.listDetail = listDetail;
    }

    public static class ListDetailBean {
        /**
         * saleListDetailId : 6303
         * goodsBarcode : 6938315016123
         * goodsName : 抽纸
         * goodsPicturepath : /image/1548662488097/8256a36a-3dd1-4d75-8f6b-fe52920aa433.jpg
         * saleListDetailCount : 1.0
         * saleListDetailPrice : 3.0
         * subTotal : 3.0
         * goods_unit : null
         * goods_purprice : null
         */

        private int saleListDetailId;
        private String goodsBarcode;
        private String goodsName;
        private String goodsPicturepath;
        private double saleListDetailCount;
        private double saleListDetailPrice;
        private double subTotal;
        private Object goods_unit;
        private Object goods_purprice;

        public int getSaleListDetailId() {
            return saleListDetailId;
        }

        public void setSaleListDetailId(int saleListDetailId) {
            this.saleListDetailId = saleListDetailId;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public double getSaleListDetailCount() {
            return saleListDetailCount;
        }

        public void setSaleListDetailCount(double saleListDetailCount) {
            this.saleListDetailCount = saleListDetailCount;
        }

        public double getSaleListDetailPrice() {
            return saleListDetailPrice;
        }

        public void setSaleListDetailPrice(double saleListDetailPrice) {
            this.saleListDetailPrice = saleListDetailPrice;
        }

        public double getSubTotal() {
            return subTotal;
        }

        public void setSubTotal(double subTotal) {
            this.subTotal = subTotal;
        }

        public Object getGoods_unit() {
            return goods_unit;
        }

        public void setGoods_unit(Object goods_unit) {
            this.goods_unit = goods_unit;
        }

        public Object getGoods_purprice() {
            return goods_purprice;
        }

        public void setGoods_purprice(Object goods_purprice) {
            this.goods_purprice = goods_purprice;
        }
    }
}
