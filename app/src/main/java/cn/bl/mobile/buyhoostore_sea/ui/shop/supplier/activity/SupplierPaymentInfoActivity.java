package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.ImgBigActivity;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ImgAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.activity.GouXInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierGouXAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierPaymentInfoData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-供货商管理-详情-还款详情
 * Created by jingang on 2023/9/4
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierPaymentInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.tvRepaymentValue)
    TextView tvRepaymentValue;
    @BindView(R.id.tvUserValue)
    TextView tvUserValue;
    @BindView(R.id.tvUser)
    TextView tvUser;
    @BindView(R.id.tvMoneyValue)
    TextView tvMoneyValue;
    @BindView(R.id.tvMoney)
    TextView tvMoney;
    @BindView(R.id.tvRemarksValue)
    TextView tvRemarksValue;
    @BindView(R.id.tvRemarks)
    TextView tvRemarks;
    @BindView(R.id.tvImgValue)
    TextView tvImgValue;
    @BindView(R.id.rvImg)
    RecyclerView rvImg;
    @BindView(R.id.tvOrderValue)
    TextView tvOrderValue;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String supplierUnique, id;

    //还款凭证
    private ImgAdapter imgAdapter;
    private List<String> imgList = new ArrayList<>();

    //订单
    private SupplierGouXAdapter mAdapter;
    private List<GouXOrderListData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier_payment_info;
    }

    @Override
    public void initViews() {
        supplierUnique = getIntent().getStringExtra("unique");
        id = getIntent().getStringExtra("id");
        setAdapter();
    }

    @Override
    public void initData() {
        getPaymentInfo();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("repayment")+getLanguageValue("details"));
        tvRepaymentValue.setText(getLanguageValue("repayment")+getLanguageValue("detail"));
        tvUserValue.setText(getLanguageValue("repayment")+getLanguageValue("people"));
        tvMoneyValue.setText(getLanguageValue("repayment")+getLanguageValue("amount"));
        tvRemarksValue.setText(getLanguageValue("repayment")+getLanguageValue("remark"));
        tvImgValue.setText(getLanguageValue("repaymentVoucher"));
        tvOrderValue.setText(getLanguageValue("repayment")+getLanguageValue("order"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //还款凭证
        rvImg.setLayoutManager(new GridLayoutManager(this, 3));
        imgAdapter = new ImgAdapter(this);
        rvImg.setAdapter(imgAdapter);
        imgAdapter.setOnItemClickListener((view, position) -> {
            //查看图片
            startActivity(new Intent(this, ImgBigActivity.class)
                    .putExtra("img", (Serializable) imgList)
                    .putExtra("index", position)
            );
        });

        //订单
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new SupplierGouXAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierGouXAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(TAG, GouXInfoActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getId()))
                );
            }

            @Override
            public void onCheckClick(View view, int position) {

            }

            @Override
            public void onKefuClick(View view, int position) {
                //打电话
                mobile = dataList.get(position).getSupplierPhone();
                if (PermissionUtils.checkPermissionsGroup(TAG, 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(TAG, Constants.PERMISSION, 4);
                }
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getPaymentInfo();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(SupplierPaymentInfoData data) {
        if (data == null) {
            return;
        }
        //还款明细
        tvUser.setText(data.getCreateBy() + " " + data.getCreateTime());
        tvMoney.setText(DFUtils.getNum2(data.getPaymentMoney()));
        if (TextUtils.isEmpty(data.getRemark())) {
            tvRemarks.setText(getLanguageValue("none"));
        } else {
            tvRemarks.setText(data.getRemark());
        }
        imgList.clear();
        if (data.getVoucherPicturePath() != null) {
            imgList.addAll(data.getVoucherPicturePath());
            imgAdapter.setDataList(imgList);
        }

        //订单
        dataList.clear();
        dataList.addAll(data.getBillList());
        if (dataList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            recyclerView.setVisibility(View.GONE);
            linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this,
                getLanguageValue("confirmCall")+":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    /**
     * 付款详情
     */
    private void getPaymentInfo() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        map.put("paymentId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoPaymentInfo(),
                map,
                SupplierPaymentInfoData.class,
                new RequestListener<SupplierPaymentInfoData>() {
                    @Override
                    public void success(SupplierPaymentInfoData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });

    }
}
