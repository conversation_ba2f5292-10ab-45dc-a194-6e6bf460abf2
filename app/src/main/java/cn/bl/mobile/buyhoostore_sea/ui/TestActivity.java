package cn.bl.mobile.buyhoostore_sea.ui;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.app.MyApplication;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;

/**
 * Describe:测试（选择接口地址）
 * Created by jingang on 2023/4/11
 */
@SuppressLint("NonConstantResourceId")
public class TestActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etInput)
    EditText etInput;
    @BindView(R.id.ivCapture)
    ImageView ivCapture;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_test;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText("选择域名");
    }

    @OnClick({R.id.ivBack, R.id.butInput, R.id.butDebug, R.id.butRelease,R.id.butDev,
            R.id.butLocation, R.id.butCamera, R.id.butRead, R.id.butCall, R.id.butInfo, R.id.butCameraAndRead,
            R.id.butCapture, R.id.butIdentify})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.butInput:
                //输入
                if (TextUtils.isEmpty(etInput.getText().toString().trim())) {
                    showMessage("请输入域名");
                    return;
                }
                SharedUtils.put("testInput", etInput.getText().toString().trim());
                SharedUtils.put("test", 2);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butDebug:
                //测试域名
                SharedUtils.put("testInput", "");
                SharedUtils.put("test", 0);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butRelease:
                //正式域名
                SharedUtils.put("testInput", "");
                SharedUtils.put("test", 1);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butDev:
                //开发域名
                SharedUtils.put("testInput", "");
                SharedUtils.put("test", 3);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butLocation:
                //权限：位置
                showPermission(1);
                break;
            case R.id.butCamera:
                //权限：相机
                showPermission(2);
                break;
            case R.id.butRead:
                //权限：读写
                showPermission(3);
                break;
            case R.id.butCall:
                //权限：打电话
                showPermission(4);
                break;
            case R.id.butInfo:
                //权限：手机信息
                showPermission(5);
                break;
            case R.id.butCameraAndRead:
                //权限：相机、读写
                showPermission(0);
                break;
            case R.id.butCapture:
                //截图
                screenshotView(view);
                break;
            case R.id.butIdentify:
                //图像识别
                break;
        }
    }

    private void showPermission(int type) {
        if (PermissionUtils.checkPermissionsGroup(this, type)) {

        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, type);
        }
    }

    /*****************************截图start*****************************/
    private String bitmapString;

    // 开始截屏
    private byte[] screenshotView(View view) {
        // ⬇⬇⬇ 可直接放入点击事件中 ⬇⬇⬇
//        View view = getWindow().getDecorView(); // view可以替换成你需要截图的控件，如父控件 RelativeLayout，LinearLayout
        // view.setDrawingCacheEnabled(true); // 设置缓存，可用于实时截图
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        view.draw(canvas);
        // view.setDrawingCacheEnabled(false); // 清空缓存，可用于实时截图

        bitmapString = getBitmapString(bitmap); // 位图转 Base64 String
        byte[] drawByte = getBitmapByte(bitmap); // 位图转为 Byted

        Log.d("111111 合并>>", "bitmapString ====:" + bitmapString);
        ivCapture.setImageBitmap(bitmap); // ImageView控件直接图片展示截好的图片
        // ⬆⬆⬆ 可直接放入点击事件中 ⬆⬆⬆

        return drawByte;
    }

    // 位图转 Base64 String
    private String getBitmapString(Bitmap bitmap) {
        String result = null;
        ByteArrayOutputStream out = null;
        try {
            if (bitmap != null) {
                out = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out); // 质量压缩方法,这里100表示不压缩,把压缩后的数据存放到baos中 "quality:图片质量"

                out.flush();
                out.close();

                byte[] bitmapBytes = out.toByteArray();
                result = Base64.encodeToString(bitmapBytes, Base64.DEFAULT); // Base64.DEFAULT会自动换行，传给前端会报错，所以要用Base64.NO_WRAP
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.flush();
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    // 位图转 Byte
    private static byte[] getBitmapByte(Bitmap bitmap) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // 参数1转换类型，参数2压缩质量，参数3字节流资源
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, out); // 质量压缩方法,这里100表示不压缩,把压缩后的数据存放到baos中 "quality:图片质量"
        try {
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }
    /*****************************截图end*****************************/

    /*****************************图像识别start*****************************/



    /*****************************图像识别end*****************************/

}
