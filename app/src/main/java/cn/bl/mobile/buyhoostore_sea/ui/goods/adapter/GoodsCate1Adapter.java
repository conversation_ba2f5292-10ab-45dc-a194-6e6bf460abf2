package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateChildData;

/**
 * Describe:商品分类-二级（适配器）
 * Created by jingang on 2023/3/20
 */
public class GoodsCate1Adapter extends BaseAdapter<CateChildData> {
    public GoodsCate1Adapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_cate1;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivIcon, ivMore;
        TextView tvName, tvDefault, tvEnable;
        ivIcon = holder.getView(R.id.ivItemIcon);
        ivMore = holder.getView(R.id.ivItemMore);
        tvName = holder.getView(R.id.tvItemName);
        tvDefault = holder.getView(R.id.tvItemDefault);
        tvEnable = holder.getView(R.id.tvItemEnable);
        RecyclerView rvCate = holder.getView(R.id.rvItemCate);
        tvEnable.setText(getLanguageValue("deactivated"));

        tvName.setText(mDataList.get(position).getKindName());
        tvEnable.setVisibility(mDataList.get(position).getValid_type() == 2 ? View.VISIBLE : View.GONE);
//        //是否可编辑，1、不可编辑；2、可编辑
//        if (TextUtils.equals(mDataList.get(position).getEditType(), "2")) {
//            ivMore.setVisibility(View.VISIBLE);
//            tvDefault.setVisibility(View.GONE);
//        } else {
//            ivMore.setVisibility(View.GONE);
//            tvDefault.setVisibility(View.VISIBLE);
//        }
        if (mDataList.get(position).getKindDetail() == null) {
            rvCate.setVisibility(View.GONE);
            ivIcon.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getKindDetail().isEmpty()) {
                rvCate.setVisibility(View.GONE);
                ivIcon.setVisibility(View.GONE);
            } else {
                rvCate.setVisibility(View.VISIBLE);
                ivIcon.setVisibility(View.VISIBLE);
                GoodsCate2Adapter adapter = new GoodsCate2Adapter(mContext);
                rvCate.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getKindDetail());
                adapter.setListener(new GoodsCate2Adapter.MyListener() {
                    @Override
                    public void onItemClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildItemClick(view, position, position1);
                        }
                    }

                    //                    @Override
//                    public void onEditClick(View view, int position1) {
//                        if (listener != null) {
//                            listener.onChildEditClick(view, position, position1);
//                        }
//                    }
//
//                    @Override
//                    public void onDelClick(View view, int position1) {
//                        if (listener != null) {
//                            listener.onChildDelClick(view, position, position1);
//                        }
//                    }
                    @Override
                    public void onMoreClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildMoreClick(view, position, position1);
                        }
                    }
                });
            }
        }
        if (mDataList.get(position).isCheck()) {
            ivIcon.setImageResource(R.mipmap.ic_arrow011);
            rvCate.setVisibility(View.VISIBLE);
        } else {
            ivIcon.setImageResource(R.mipmap.ic_arrow010);
            rvCate.setVisibility(View.GONE);
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivIcon.setOnClickListener(v -> listener.onOpenClick(v, position));
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onOpenClick(View view, int position);

        void onMoreClick(View view, int position);

        void onChildItemClick(View view, int position, int position1);

//        void onChildEditClick(View view, int position, int position1);
//
//        void onChildDelClick(View view, int position, int position1);

        void onChildMoreClick(View view, int position, int position1);
    }
}
