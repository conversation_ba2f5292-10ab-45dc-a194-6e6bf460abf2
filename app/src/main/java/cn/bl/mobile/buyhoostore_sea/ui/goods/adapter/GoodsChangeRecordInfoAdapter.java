package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsChangeRecordData;

/**
 * Describe:商品变更记录-详情（适配器）
 * Created by jingang on 2023/8/23
 */
public class GoodsChangeRecordInfoAdapter extends BaseAdapter<GoodsChangeRecordData.RecordDetailBean> {

    public GoodsChangeRecordInfoAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_change_record_info;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvCountBefore, tvCountAfter;
        tvName = holder.getView(R.id.tvItemName);
        tvCountBefore = holder.getView(R.id.tvItemCountBefore);
        tvCountAfter = holder.getView(R.id.tvItemCountAfter);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f7));
        }
        tvName.setText(mDataList.get(position).getGoodsOperClass());
        tvCountBefore.setText(mDataList.get(position).getValueSource());
        tvCountAfter.setText(mDataList.get(position).getValueNew());
    }
}
