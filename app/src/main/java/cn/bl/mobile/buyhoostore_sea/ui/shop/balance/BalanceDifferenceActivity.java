package cn.bl.mobile.buyhoostore_sea.ui.shop.balance;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean.GapListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-余额（差价）
 * Created by jingang on 2023/4/24
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class BalanceDifferenceActivity extends BaseActivity {
    private BalanceDifferenceActivity TAG = BalanceDifferenceActivity.this;
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvBalance)
    TextView tvBalance;
    @BindView(R.id.tvBalanceValue)
    TextView tvBalanceValue;
    @BindView(R.id.tvBalanceIntroduce)
    TextView tvBalanceIntroduce;
    @BindView(R.id.tvRecord)
    TextView tvRecord;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private BalanceDifferenceAdapter mAdapter;
    private List<GapListData.DataBean.GapListBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_balance_difference;
    }

    @Override
    public void initViews() {
        setAdapter();
    }

    @Override
    protected void onResume() {
        super.onResume();
        getShopInfo();
        getShopGapList();
    }

    @OnClick({R.id.ivBack, R.id.tvBalanceIntroduce})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvBalanceIntroduce:
                //余额说明
                showDialogBalanceIntroduce();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("balance") + "(" + getLanguageValue("priceDiffer") + ")");
        tvBalanceValue.setText(getLanguageValue("ok") + getLanguageValue("deduction") + getLanguageValue("balance"));
        tvBalanceIntroduce.setText(getLanguageValue("balance") + getLanguageValue("explain"));
        tvRecord.setText(getLanguageValue("balance") + getLanguageValue("record"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new BalanceDifferenceAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getShopGapList();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getShopGapList();
        });
        mAdapter.setOnItemClickListener((view, position) -> {
            //gap_type： 差价产生方式：1下单、2确认收货、3订单退款、4订单取消、5筐押金退款
            startActivity(new Intent(this, BalanceDifferenceOrderActivity.class)
                    .putExtra("no", dataList.get(position).getOrder_no())
            );
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GapListData.DataBean data) {
        tvBalance.setText(DFUtils.getNum2(data.getShop_balance()));
        if (page == 1) {
            smartRefreshLayout.finishRefresh();
            dataList.clear();
        } else {
            smartRefreshLayout.finishLoadMore();
        }
        dataList.addAll(data.getGap_list());
        if (dataList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            recyclerView.setVisibility(View.GONE);
            linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * dialog（余额说明）
     */
    @SuppressLint({"SetTextI18n", "MissingInflatedId"})
    private void showDialogBalanceIntroduce() {
        View view = LayoutInflater.from(this).inflate(R.layout.dialog_balance_introduce, null);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setView(view);
        TextView tvTitle = view.findViewById(R.id.tvDialogTitle);
        TextView tvTips = view.findViewById(R.id.tvDialogTips);
        TextView tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        tvTitle.setText(getLanguageValue("balance") + getLanguageValue("explain"));
        tvTips.setText(getLanguageValue("balanceWhen"));
        tvConfirm.setText(getLanguageValue("gotIt"));

        final AlertDialog dialog = builder.create();
        Window window = dialog.getWindow();
        window.setGravity(Gravity.CENTER);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        window.setBackgroundDrawableResource(android.R.color.transparent);
        dialog.show();
        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> dialog.dismiss());
    }

    /**
     * 获取店铺信息
     */
    private void getShopInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getShopInfoUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "店铺信息  = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        ShopInfoResponseModel data = new Gson().fromJson(s, ShopInfoResponseModel.class);
                        if (data.getStatus() == 1) {
                            Glide.with(TAG)
                                    .load(StringUtils.handledImgUrl(data.getData().getShopImagePath()))
                                    .apply(new RequestOptions().error(R.mipmap.ic_default_head))
                                    .into(ivHead);
                            tvShopName.setText(data.getData().getShopName());
                        }
                    }
                });
    }

    /**
     * 订单差价列表及店铺店铺差价信息
     */
    private void getShopGapList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("page", page);
        map.put("limit", Constants.limit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getShopGapList(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        GapListData data = new Gson().fromJson(s, GapListData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setUI(data.getData());
                        } else {
                            if (page == 1) {
                                smartRefreshLayout.finishRefresh();
                                dataList.clear();
                                mAdapter.clear();
                                recyclerView.setVisibility(View.GONE);
                                linEmpty.setVisibility(View.VISIBLE);
                            } else {
                                smartRefreshLayout.finishLoadMore();
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            mAdapter.clear();
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                    }
                });
    }
}
