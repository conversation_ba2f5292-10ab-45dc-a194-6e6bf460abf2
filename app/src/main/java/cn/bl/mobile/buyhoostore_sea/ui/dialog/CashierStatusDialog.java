package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.CashierStatusData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（宁宇收银-收银中...）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class CashierStatusDialog extends BaseDialog {
    @BindView(R.id.ivDialogImg)
    ImageView ivImg;
    @BindView(R.id.tvDialogMoney)
    TextView tvMoney;
    @BindView(R.id.tvDialogStatus)
    TextView tvStatus;

    private static String no;
    private static double money;

    public static void showDialog(Context context, String no, double money, MyListener listener) {
        CashierStatusDialog.listener = listener;
        CashierStatusDialog.no = no;
        CashierStatusDialog.money = money;
        CashierStatusDialog dialog = new CashierStatusDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.setCancelable(false);
        dialog.show();
    }

    public CashierStatusDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_cashier_status);
        ButterKnife.bind(this);
        tvStatus.setText(getLanguageValue("inCash") + "...");

        Glide.with(context)
                .asGif()
                .load(R.drawable.cashiering)
                .into(ivImg);
        tvMoney.setText(getLanguageValue("cashiering") + getLanguageValue("amount") + ":" + DFUtils.getNum2(money));
        getPayStatus();
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(CashierStatusData data) {
        if (TextUtils.isEmpty(data.getTrade_state())) {
            if (listener != null) {
                listener.onClick(getLanguageValue("cashierFail"), 1);
                dismiss();
            }
            return;
        }
        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
        switch (data.getTrade_state()) {
            case "SUCCESS":
                showMessage(getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(money));
                if (listener != null) {
                    listener.onClick(getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(money), 0);
                    dismiss();
                }
                break;
            case "DOING":
            case "USERPAYING":
                new Handler(Looper.getMainLooper()).postDelayed(this::getPayStatus, 2000);
                break;
            default:
                if (listener != null) {
                    listener.onClick(getLanguageValue("cashierFail"), 1);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 扫码支付结果查询
     */
    private void getPayStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("out_trade_no", no);
        map.put("pay_type", 13);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getNingYuPayStatus(),
                map,
                CashierStatusData.class,
                new RequestListener<CashierStatusData>() {
                    @Override
                    public void success(CashierStatusData data) {
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        if (listener != null) {
                            listener.onClick(msg, 1);
                            dismiss();
                        }
                    }
                });
    }


    private static MyListener listener;

    public interface MyListener {

        /**
         * @param msg
         * @param status 0.成功 1.失败
         */
        void onClick(String msg, int status);
    }
}
