package cn.bl.mobile.buyhoostore_sea.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.bean.ReXiaoBean;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Created by Administrator on 2017/7/24 0024.
 */
public class GoodHotCakeAdapter extends BaseAdapter{
    private ViewHolder holder;
    private List<ReXiaoBean.DataBean> list;
    private Context context;
    private ReXiaoBean.DataBean categoryBean;
    public GoodHotCakeAdapter(Context context, List<ReXiaoBean.DataBean> list) {
        this.list = list;
        this.context = context;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        Log.e("TAG",position+"position");
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(
                    R.layout.item_goodsrexiao_tab, null);
            holder.rexiao_index = (TextView) convertView.findViewById(R.id.rexiao_index);
            holder.rx_goodsimg = (ImageView) convertView.findViewById(R.id.rx_goodsimg);
            holder.goods_name = (TextView) convertView.findViewById(R.id.goods_name);
            holder.rx_number = (TextView) convertView.findViewById(R.id.rx_number);
            holder.rx_top = (TextView) convertView.findViewById(R.id.rx_top);
            holder.rx_state = (ImageView) convertView.findViewById(R.id.rx_state);

            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        categoryBean = list.get(position);
        int index = position+1;
        holder.rexiao_index.setText(index+"");
        String imageUrl= StringUtils.handledImgUrl(categoryBean.getPicture_name());
        ImageLoader.getInstance().displayImage(imageUrl,holder.rx_goodsimg);
        holder.goods_name.setText(categoryBean.getGoods_name()+"");
        holder.rx_number.setText(categoryBean.getSaleCount()+"");
        holder.rx_top.setText(categoryBean.getProportion_no());
        String status = categoryBean.getExit_status()+"";
        if (status.equals("-1")){
            holder.rx_state.setVisibility(View.GONE);
        }else {
            holder.rx_state.setVisibility(View.VISIBLE);
        }
        return convertView;
    }

    private class ViewHolder {

        TextView rexiao_index;
        ImageView rx_goodsimg;
        TextView goods_name;
        TextView rx_number;
        TextView rx_top;
        ImageView rx_state;


    }


}
