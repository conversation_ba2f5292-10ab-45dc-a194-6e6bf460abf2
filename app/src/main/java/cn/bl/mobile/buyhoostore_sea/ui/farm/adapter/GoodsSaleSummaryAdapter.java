package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.GoodsSaleData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:货品销售汇总（适配器）
 * Created by jingang on 2023/5/28
 */
public class GoodsSaleSummaryAdapter extends BaseAdapter<GoodsSaleData.DataBean> {

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public GoodsSaleSummaryAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_sale_summary;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvChengType, tvSaleType, tvCount, tvPrice, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvChengType = holder.getView(R.id.tvItemChengType);
        tvSaleType = holder.getView(R.id.tvItemSaleType);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTotal = holder.getView(R.id.tvItemTotal);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvChengType.setText(mDataList.get(position).getGoodsChengType());
        tvSaleType.setText(mDataList.get(position).getGoodsType());
        tvCount.setText(DFUtils.getNum2(mDataList.get(position).getCount()));
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getPercentage()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSum()));
    }
}
