package cn.bl.mobile.buyhoostore_sea.bean;


public class UpdateVersionModel {
    private Data data = new Data();
    private String msg;
    private int status;
    private Object total = new Object();

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public static class Data {
        /**
         * 是否强制更新
         */
        private   int update_install;
        /**
         * 下载地址
         */
        private   String update_url;
        /**
         * 版本
         */
        private  String update_version;
        /**
         * 版本
         */
        private  int update_id;
        /**
         * 版本code
         */
        private  int code;

        /**
         * 升级描述
         */
        private   String update_des;
        /**
         * 升级日主
         */
        private   String update_log;

        public int getUpdate_install() {
            return update_install;
        }

        public void setUpdate_install(int update_install) {
            this.update_install = update_install;
        }

        public String getUpdate_url() {
            return update_url;
        }

        public void setUpdate_url(String update_url) {
            this.update_url = update_url;
        }

        public String getUpdate_version() {
            return update_version;
        }

        public void setUpdate_version(String update_version) {
            this.update_version = update_version;
        }

        public int getUpdate_id() {
            return update_id;
        }

        public void setUpdate_id(int update_id) {
            this.update_id = update_id;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getUpdate_des() {
            return update_des;
        }

        public void setUpdate_des(String update_des) {
            this.update_des = update_des;
        }

        public String getUpdate_log() {
            return update_log;
        }

        public void setUpdate_log(String update_log) {
            this.update_log = update_log;
        }
    }
}
