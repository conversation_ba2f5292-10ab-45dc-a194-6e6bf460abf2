package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:
 * Created by jingang on 2023/5/18
 */
public class GoodsSpecsAdapter extends BaseAdapter<String> {

    public GoodsSpecsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_specs;
    }

    @Override
    public int getItemCount() {
        return 3;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {

    }
}
