package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.OrderListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:农批-订单（适配器）
 * Created by jingang on 2023/5/25
 */
public class NPBaiTiaoRecordAdapter extends BaseAdapter<OrderListData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public NPBaiTiaoRecordAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_bai_tiao_record;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvType, tvName, tvPrice, tvTime,tvRemark;
        tvNo = holder.getView(R.id.tvItemNo);
        tvType = holder.getView(R.id.tvItemType);
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTime = holder.getView(R.id.tvItemTime);
        tvRemark = holder.getView(R.id.tvRemark);

        tvNo.setText(String.valueOf(mDataList.get(position).getSaleListUnique()));
//        tvType.setText(mDataList.get(position).getSaleListPayment());
        tvName.setText(mDataList.get(position).getSaleListName());
        tvPrice.setText("FM" + DFUtils.getNum2(mDataList.get(position).getSaleListTotal()));
        tvTime.setText(mDataList.get(position).getDateTime() + " " + mDataList.get(position).getStaffName());
        tvRemark.setText("备注");
        switch (mDataList.get(position).getHandleStateCode()) {
            case 4:
                lin.setBackgroundResource(R.drawable.shape_white_8);
                tvType.setText("已结清");
                tvType.setBackgroundResource(R.drawable.shape_33cc67_tm33_4);
                tvType.setTextColor(mContext.getResources().getColor(R.color.green));
                break;
            case 5:
                lin.setBackgroundResource(R.drawable.shape_e1_8);
                tvType.setText("已作废");
                tvType.setBackgroundResource(R.drawable.shape_53_tm33_4);
                tvType.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            default:
                tvType.setText("");
                break;
        }
    }
}
