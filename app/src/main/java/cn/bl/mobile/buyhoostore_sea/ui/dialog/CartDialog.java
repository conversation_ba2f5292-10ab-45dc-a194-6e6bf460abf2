package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.CartDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

import static android.content.Context.INPUT_METHOD_SERVICE;

/**
 * Describe:dialog（快速收银-购物车）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class CartDialog extends BaseDialog {
    @BindView(R.id.etDialogTest)
    EditText etTest;
    @BindView(R.id.tvDialogTips)
    TextView tvDialogTips;
    @BindView(R.id.tvDialogDel)
    TextView tvDialogDel;
    @BindView(R.id.rvDialog)
    RecyclerView recyclerView;
    @BindView(R.id.tvDialogCartNum)
    TextView tvCartNum;
    @BindView(R.id.tvDialogTotal)
    TextView tvTotal;
    @BindView(R.id.tvDialogCount)
    TextView tvCount;
    @BindView(R.id.tvDialogCash)
    TextView tvDialogCash;
    @BindView(R.id.tvDialogCustom)
    TextView tvDialogCustom;

    private static CartDialog dialog;
    private static List<GoodsData> dataList;//购物车列表
    private CartDialogAdapter mAdapter;
    private double total;//总价

    public static void showDialog(Context context, List<GoodsData> cartList, MyListener listener) {
        CartDialog.listener = listener;
        CartDialog.dataList = cartList;
        dialog = new CartDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.setCancelable(true);
        dialog.show();
    }

    public CartDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_cart);
        ButterKnife.bind(this);
        tvDialogTips.setText(getLanguageValue("selected") + getLanguageValue("commodity"));
        tvDialogDel.setText(getLanguageValue("clear"));
        tvDialogCash.setText(getLanguageValue("cash"));
        tvDialogCustom.setText(getLanguageValue("member"));
        etTest.requestFocus();
        etTest.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                    (event != null && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                // 当点击软键盘上的“完成”按钮或按下硬件/虚拟Enter键时执行以下操作
                Log.e("111111", "扫码枪 = " + etTest.getText().toString());
                setResultCode(etTest.getText().toString().trim());
                etTest.setText("");

                return true; // 表示已处理该事件
            }
            return false;
        });
        //获取焦点不弹出软键盘
        etTest.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
    }

    /**
     * 得到扫码结果
     *
     * @param code
     */
    private void setResultCode(String code) {
        Log.e(tag, "扫码结果 = " + code);
        if (TextUtils.isEmpty(code)) {
            return;
        }
        if (code.length() < 2) {
            return;
        }
        String chooseCode = code.substring(0, 2);
        switch (chooseCode) {
            case "62":
            case "28":
            case "13":
                if (code.length() == 18 || ("62".equals(chooseCode) && code.length() == 19)) {
                    //支付码
                    if (listener != null) {
                        listener.onScanResult(code);
                        dismiss();
                    }
                } else {
                    showMessage(getLanguageValue("showPaymentCodeCorrect"));
                }
                break;
            default:
                showMessage(getLanguageValue("showPaymentCodeCorrect"));
                break;
        }
    }

    /**
     * 是否显示
     *
     * @return
     */
    public static boolean isShow() {
        if (dialog == null) {
            return false;
        } else {
            return dialog.isShowing();
        }
    }

    /**
     * 消除
     */
    public static void dismissDialog() {
        if (dialog != null) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
        setAdapter();
    }

    @OnClick({R.id.tvDialogDel, R.id.relDialogCart, R.id.tvDialogCash, R.id.tvDialogCustom})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogDel:
                //清空
                dataList.clear();
                mAdapter.clear();
                if (listener != null) {
                    listener.onCartList(dataList);
                    dismiss();
                }
                break;
            case R.id.relDialogCart:
                dismiss();
                break;
            case R.id.tvDialogCash:
                //现金
                if (isQuicklyClick()) {
                    return;
                }
                if (listener != null) {
                    listener.onCashClick();
                    dismiss();
                }
                break;
            case R.id.tvDialogCustom:
                //会员
                if (isQuicklyClick()) {
                    return;
                }
                if (listener != null) {
                    listener.onCustomClick();
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new CartDialogAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        if (dataList != null) {
            mAdapter.setDataList(dataList);
        }
        mAdapter.setListener(new CartDialogAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {

            }

            @Override
            public void onSubClick(View view, int position) {
                int cartNum = dataList.get(position).getCartNum();
                if (cartNum > 1) {
                    dataList.get(position).setCartNum(cartNum - 1);
                    mAdapter.notifyItemChanged(position);
                } else {
                    dataList.remove(position);
                    mAdapter.remove(position);
                }
                if (listener != null) {
                    listener.onCartList(dataList);
                }
                getTotal();
            }

            @Override
            public void onAddClick(View view, int position) {
                if ((total + dataList.get(position).getGoodsSalePrice()) > Constants.CASHIER_MAX_MONEY) {
                    showMessage(getLanguageValue("cartTotal") + "RM" + Constants.CASHIER_MAX_MONEY);
                    return;
                }
                int cartNum = dataList.get(position).getCartNum();
                dataList.get(position).setCartNum(cartNum + 1);
                mAdapter.notifyItemChanged(position);
                if (listener != null) {
                    listener.onCartList(dataList);
                }
                getTotal();
            }
        });
        getTotal();
    }

    /**
     * 小计
     */
    private void getTotal() {
        if (dataList.size() < 1) {
            if (listener != null) {
                listener.onCartList(dataList);
            }
            dismiss();
            return;
        }
        int num = 0;
        total = 0;
        for (int i = 0; i < dataList.size(); i++) {
            num = num + dataList.get(i).getCartNum();
            total = total + dataList.get(i).getCartNum() * dataList.get(i).getGoodsSalePrice();
        }
        if (num > 0) {
            tvCartNum.setVisibility(View.VISIBLE);
            if (num > 99) {
                tvCartNum.setText("99+");
            } else {
                tvCartNum.setText(String.valueOf(num));
            }
        } else {
            tvCartNum.setVisibility(View.GONE);
        }
        tvTotal.setText("RM" + DFUtils.getNum2(total));
        tvCount.setText(dataList.size() + getLanguageValue("types") + getLanguageValue("commodity"));
    }

    private static MyListener listener;

    public interface MyListener {
        void onCartList(List<GoodsData> dataList);

        void onCashClick();

        void onCustomClick();

        void onScanResult(String result);
    }
}
