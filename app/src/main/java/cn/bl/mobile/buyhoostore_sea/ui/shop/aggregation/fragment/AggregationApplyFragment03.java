package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseFragment;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:赊销审核（店面照）
 * Created by jingang on 2023/2/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyFragment03 extends BaseFragment {
    @BindView(R.id.linBg0)
    LinearLayout linBg0;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.ivCamera0)
    ImageView ivCamera0;
    @BindView(R.id.tvImg0Value)
    TextView tvImg0Value;
    @BindView(R.id.linBg1)
    LinearLayout linBg1;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.ivCamera1)
    ImageView ivCamera1;
    @BindView(R.id.tvImg1Value)
    TextView tvImg1Value;
    @BindView(R.id.linBg2)
    LinearLayout linBg2;
    @BindView(R.id.ivImg2)
    ImageView ivImg2;
    @BindView(R.id.ivCamera2)
    ImageView ivCamera2;
    @BindView(R.id.tvImg2Value)
    TextView tvImg2Value;
    @BindView(R.id.tvPrevious)
    TextView tvPrevious;
    @BindView(R.id.tvNext)
    TextView tvNext;

    private int type,//类型：0.门头照 1.店内照 2.收银台照
            status;
    private String shop0, shop1, shop2;

    public AggregationApplyFragment03(int status, String shop0, String shop1, String shop2) {
        this.status = status;
        this.shop0 = shop0;
        this.shop1 = shop1;
        this.shop2 = shop2;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        if(status == 1){
            linBg0.setEnabled(false);
            ivCamera0.setVisibility(View.GONE);
            linBg1.setEnabled(false);
            ivCamera1.setVisibility(View.GONE);
            linBg2.setEnabled(false);
            ivCamera2.setVisibility(View.GONE);
            tvNext.setVisibility(View.GONE);
        }else{
            linBg0.setEnabled(true);
            ivCamera0.setVisibility(View.VISIBLE);
            linBg1.setEnabled(true);
            ivCamera1.setVisibility(View.VISIBLE);
            linBg2.setEnabled(true);
            ivCamera2.setVisibility(View.VISIBLE);
            tvNext.setVisibility(View.VISIBLE);
        }
        setUI();
    }

    @Override
    public void initData() {

    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_aggregation_apply03;
    }

    @OnClick({R.id.linBg0, R.id.linBg1, R.id.linBg2, R.id.tvPrevious, R.id.tvNext})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linBg0:
                //门头照
                type = 0;
                getImg();
                break;
            case R.id.linBg1:
                //店内照
                type = 1;
                getImg();
                break;
            case R.id.linBg2:
                //收银台照
                type = 2;
                getImg();
                break;
            case R.id.tvPrevious:
                //上一步
                if (listener != null) {
                    listener.onPreviousClick();
                }
                break;
            case R.id.tvNext:
                //提交信息
                if (listener != null) {
                    listener.onNextClick();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvImg0Value.setText(getLanguageValue("shoot")+getLanguageValue("doorPhoto"));
        tvImg1Value.setText(getLanguageValue("shoot")+getLanguageValue("insidePhoto"));
        tvImg2Value.setText(getLanguageValue("shoot")+getLanguageValue("cashierPhoto"));
        tvPrevious.setText(getLanguageValue("previousStep"));
        tvNext.setText(getLanguageValue("nextStep"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(AggregationApplyFragment03.this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvCamera.setText(getLanguageValue("photograph"));
        tvAlbum.setText(getLanguageValue("phoneSelect"));
        tvCancel.setText(getLanguageValue("cancel"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(getActivity())
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        switch (type) {
//                            case 0:
//                                shop0 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop0", shop0));
//                                break;
//                            case 1:
//                                shop1 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop1", shop1));
//                                break;
//                            case 2:
//                                shop2 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop2", shop2));
//                                break;
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(getActivity())
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        switch (type) {
//                            case 0:
//                                shop0 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop0", shop0));
//                                break;
//                            case 1:
//                                shop1 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop1", shop1));
//                                break;
//                            case 2:
//                                shop2 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop2", shop2));
//                                break;
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                getActivity().runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                getActivity().runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            switch (type) {
                                case 0:
                                    shop0 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop0", shop0));
                                    break;
                                case 1:
                                    shop1 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop1", shop1));
                                    break;
                                case 2:
                                    shop2 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("shop2", shop2));
                                    break;
                            }
                            setUI();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //门头照
        if (TextUtils.isEmpty(shop0)) {
            ivImg0.setVisibility(View.GONE);
        } else {
            ivImg0.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(shop0)
                    .into(ivImg0);
        }
        //店内照
        if (TextUtils.isEmpty(shop1)) {
            ivImg1.setVisibility(View.GONE);
        } else {
            ivImg1.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(shop1)
                    .into(ivImg1);
        }
        //收银台照
        if (TextUtils.isEmpty(shop2)) {
            ivImg2.setVisibility(View.GONE);
        } else {
            ivImg2.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(shop2)
                    .into(ivImg2);
        }
        if (TextUtils.isEmpty(shop0)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(shop1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(shop2)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        tvNext.setBackgroundResource(R.drawable.shape_blue_22);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onPreviousClick();

        void onNextClick();
    }
}
