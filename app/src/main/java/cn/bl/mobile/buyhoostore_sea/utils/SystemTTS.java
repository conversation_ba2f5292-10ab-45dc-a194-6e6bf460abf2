package cn.bl.mobile.buyhoostore_sea.utils;

import android.content.Context;
import android.media.AudioManager;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;

import com.yxl.commonlibrary.base.BaseApplication;

import java.util.Locale;

import kotlin.jvm.Volatile;

public class SystemTTS extends UtteranceProgressListener implements TTS {
    private Context context;

    public SystemTTS(Context context) {
        this.context = context;
        textToSpeech = new TextToSpeech(context, status -> {
            Log.e("111111","status = "+status);
            //系统语音初始化成功
            if (status == TextToSpeech.SUCCESS) {
                int languageResult = textToSpeech.setLanguage(Locale.CHINA);
                textToSpeech.setPitch(1.0f);//设置音调，值越大声音越尖（女生），值越小则变成男声,1.0是常规
                textToSpeech.setSpeechRate(1.0f);
                textToSpeech.setOnUtteranceProgressListener(SystemTTS.this);
                //textToSpeech.setOnUtteranceCompletedListener(this)
                if (languageResult == TextToSpeech.LANG_MISSING_DATA || languageResult == TextToSpeech.LANG_NOT_SUPPORTED) {
                    //系统不支持中文播报
                    isSuccess = false;
                }
            }
        });
    }

    TextToSpeech textToSpeech;
    boolean isSuccess = true;

    @Volatile
    private static volatile SystemTTS instance;

    public static SystemTTS getInstance(Context context) {
        if (instance == null) {
            synchronized (SystemTTS.class) {
                if (instance == null) {
                    instance = new SystemTTS(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void onStart(String utteranceId) {

    }

    @Override
    public void onDone(String utteranceId) {

    }

    @Override
    public void onError(String utteranceId) {

    }

    @Override
    public void playText(String playText) {
        Log.e("111111", "playText = "+playText);
        if (!isSuccess) {
            ToastUtil.showToast(BaseApplication.getInstance(), "系统不支持中文播报");
            return;
        }
//        if(textToSpeech.isSpeaking()){
//            textToSpeech.stop();
//        }
//        textToSpeech.speak(playText, TextToSpeech.QUEUE_FLUSH, null, null);

        AudioManager audioManager = (AudioManager) BaseApplication.getInstance().getSystemService(Context.AUDIO_SERVICE);
        int result = audioManager.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK);
        if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            // 现在有音频焦点，可以播放TTS
            if (isSuccess && playText != null) {
                textToSpeech.speak(playText, TextToSpeech.QUEUE_FLUSH, null, null);
            }
        } else {
            Log.w("111111", "Audio focus request denied");
        }
    }

    @Override
    public void stopSpeak() {
        textToSpeech.stop();
    }
}
