package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import com.google.gson.Gson;
import com.jaredrummler.materialspinner.MaterialSpinner;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.PersionPersonBean;
import cn.bl.mobile.buyhoostore_sea.bean.PowerBean;
import cn.bl.mobile.buyhoostore_sea.bean.ShopBean;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * @Author: Great Han
 * @Description: 权限设置
 * @Date: 8:50 2019/7/2
 */

/**
 * 店铺-店铺设置-权限设置
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class PermissionSetActivity extends BaseActivity implements OnClickListener {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvZero)
    TextView tvZero;
    @BindView(R.id.tvSupplier)
    TextView tvSupplier;
    @BindView(R.id.tvStaff)
    TextView tvStaff;
    @BindView(R.id.tvPrice)
    TextView tvPrice;
    @BindView(R.id.tvStock)
    TextView tvStock;
    @BindView(R.id.tvCate)
    TextView tvCate;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvInPrice)
    TextView tvInPrice;
    @BindView(R.id.tvBuy)
    TextView tvBuy;
    @BindView(R.id.tvGoodsAdd)
    TextView tvGoodsAdd;
    @BindView(R.id.tvGoodsDel)
    TextView tvGoodsDel;
    @BindView(R.id.btn_update)
    Button btn_update;

    private CheckBox cb_moling, cb_gonghuoshang, cb_xiugaijiage, cb_shangpinkucun, cb_shangpinfenlei, cb_spmcheng, spjjia, spcgou, spzjia, spschu;
    SharedPreferences sp = null;
    private String userId, managerUnique, staffPosition;
    ShopBean shopBean;
    PersionPersonBean persionPersonBean;
    MaterialSpinner spinner_dianpu, spinner_yuangong;
    PowerBean powerBean;
    private int staffId;//员工编号
    String ml, ghs, xgjg, spkc, spfl, spmc, spjj, spcg, spzj, spsc;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_permission;
    }

    @Override
    public void initViews() {
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        userId = sp.getString("shopId", "");
        managerUnique = sp.getString("managerUnique", "");
        staffPosition = sp.getString("staffPosition", "");
        getShopList();
        cb_moling = (CheckBox) findViewById(R.id.cb_moling);
        cb_gonghuoshang = (CheckBox) findViewById(R.id.cb_gonghuoshang);
        cb_xiugaijiage = (CheckBox) findViewById(R.id.cb_xiugaijiage);
        cb_shangpinkucun = (CheckBox) findViewById(R.id.cb_shangpinkucun);
        cb_shangpinfenlei = (CheckBox) findViewById(R.id.cb_shangpinfenlei);
        cb_spmcheng = (CheckBox) findViewById(R.id.cb_spmcheng);
        spjjia = (CheckBox) findViewById(R.id.spjjia);
        spcgou = (CheckBox) findViewById(R.id.spcgou);
        spzjia = (CheckBox) findViewById(R.id.spzjia);
        spschu = (CheckBox) findViewById(R.id.spschu);
        spinner_dianpu = (MaterialSpinner) findViewById(R.id.spinner_dianpu);
        spinner_yuangong = (MaterialSpinner) findViewById(R.id.spinner_yuangong);
        spinner_dianpu.setOnItemSelectedListener((view, position, id, item) -> {
            userId = shopBean.getData().get(position).getShopUnique();
            getpersonList();
        });
        spinner_yuangong.setOnItemSelectedListener((view, position, id, item) -> {
            staffId = persionPersonBean.getData().get(position).getStaffId();
            getpersonpersion();
        });
        btn_update.setOnClickListener(this);
    }

    @OnClick(R.id.ivBack)
    public  void onViewClicked(View view){
        switch (view.getId()){
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("permissions")+getLanguageValue("setUp"));
        tvZero.setText(getLanguageValue("wipeZero"));
        tvSupplier.setText(getLanguageValue("supplier"));
        tvStaff.setText(getLanguageValue("staff")+getLanguageValue("administration"));
        tvPrice.setText(getLanguageValue("modification")+getLanguageValue("price"));
        tvStock.setText(getLanguageValue("commodity")+getLanguageValue("inventory"));
        tvCate.setText(getLanguageValue("commodity")+getLanguageValue("classification"));
        tvName.setText(getLanguageValue("commodity")+getLanguageValue("name"));
        tvInPrice.setText(getLanguageValue("commodity")+getLanguageValue("purchasePrice"));
        tvBuy.setText(getLanguageValue("commodity")+getLanguageValue("procurement"));
        tvGoodsAdd.setText(getLanguageValue("commodity")+getLanguageValue("addTo"));
        tvGoodsDel.setText(getLanguageValue("commodity")+getLanguageValue("delete"));
        btn_update.setText(getLanguageValue("preservation"));
    }

    /**
     * 获取店铺列表信息
     *
     * @param
     */
    public void getShopList() {
        Map<String, Object> params = new HashMap<>();
        params.put("managerUnique", managerUnique);
        params.put("shopUnique", userId);
        params.put("staffPosition", staffPosition);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getshoplistTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "获取店铺列表信息 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusInfo2 = 2;
                        JSONObject objectInfo2;
                        try {
                            objectInfo2 = new JSONObject(s);
                            statusInfo2 = objectInfo2.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (statusInfo2 == 1) {
                            Gson gson1 = new Gson();
                            shopBean = gson1.fromJson(s, ShopBean.class);
                            List<String> ShopAllName = new ArrayList<>();
                            for (int t = 0; t < shopBean.getData().size(); t++) {
                                ShopAllName.add(shopBean.getData().get(t).getShopName());
                            }
                            int t = shopBean.getData().size();
                            if (t > 0) {
                                String[] classifyNameArr = ShopAllName.toArray(new String[t]);
                                spinner_dianpu.setItems(classifyNameArr);
                            }
                            userId = shopBean.getData().get(0).getShopUnique();
                            getpersonList();
                        }
                    }
                });
    }

    /**
     * 查询员工列表
     *
     * @param
     */
    public void getpersonList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", userId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getpersonlistTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "查询员工列表 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusInfo3 = 2;
                        JSONObject objectInfo3;
                        try {
                            objectInfo3 = new JSONObject(s);
                            statusInfo3 = objectInfo3.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (statusInfo3 == 1) {
                            Gson gson1 = new Gson();
                            persionPersonBean = gson1.fromJson(s, PersionPersonBean.class);
                            List<String> PersionAllName = new ArrayList<String>();
                            for (int t = 0; t < persionPersonBean.getData().size(); t++) {
                                PersionAllName.add(persionPersonBean.getData().get(t).getStaffName());
                            }
                            int t = persionPersonBean.getData().size();
                            if (t > 0) {
                                String[] classifyNameArr = PersionAllName.toArray(new String[t]);
                                spinner_yuangong.setItems(classifyNameArr);
                            }
                            staffId = persionPersonBean.getData().get(0).getStaffId();
                            getpersonpersion();
                        }
                    }
                });
    }

    /**
     * 根据员工编号查权限
     *
     * @param
     */
    public void getpersonpersion() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", userId);
        params.put("staffId", staffId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getpersonlistTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "根据员工编号查权限 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusInfo4 = 2;
                        JSONObject objectInfo4;
                        try {
                            objectInfo4 = new JSONObject(s);
                            statusInfo4 = objectInfo4.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (statusInfo4 == 1) {
                            Gson gson1 = new Gson();
                            powerBean = gson1.fromJson(s, PowerBean.class);
                            ml = powerBean.getData().get(0).getPowerChange();
                            ghs = powerBean.getData().get(0).getPowerSupplier();
                            spsc = powerBean.getData().get(0).getPowerDelete();
                            xgjg = powerBean.getData().get(0).getPowerPrice();
                            spkc = powerBean.getData().get(0).getPowerCount();
                            spfl = powerBean.getData().get(0).getPowerKind();
                            spmc = powerBean.getData().get(0).getPowerName();
                            spjj = powerBean.getData().get(0).getPowerInPrice();
                            spcg = powerBean.getData().get(0).getPowerPur();
                            spzj = powerBean.getData().get(0).getPowerAdd();
                            if ("1".equals(ml)) {
                                cb_moling.setChecked(true);
                            } else if ("2".equals(ml)) {
                                cb_moling.setChecked(false);
                            }

                            if ("1".equals(ghs)) {
                                cb_gonghuoshang.setChecked(true);
                            } else if ("2".equals(ghs)) {
                                cb_gonghuoshang.setChecked(false);
                            }

                            if ("1".equals(xgjg)) {
                                cb_xiugaijiage.setChecked(true);
                            } else if ("2".equals(xgjg)) {
                                cb_xiugaijiage.setChecked(false);
                            }

                            if ("1".equals(spkc)) {
                                cb_shangpinkucun.setChecked(true);
                            } else if ("2".equals(spkc)) {
                                cb_shangpinkucun.setChecked(false);
                            }

                            if ("1".equals(spfl)) {
                                cb_shangpinfenlei.setChecked(true);
                            } else if ("2".equals(spfl)) {
                                cb_shangpinfenlei.setChecked(false);
                            }
                            if ("1".equals(spmc)) {
                                cb_spmcheng.setChecked(true);
                            } else if ("2".equals(spmc)) {
                                cb_spmcheng.setChecked(false);
                            }

                            if ("1".equals(spjj)) {
                                spjjia.setChecked(true);
                            } else if ("2".equals(spjj)) {
                                spjjia.setChecked(false);
                            }

                            if ("1".equals(spcg)) {
                                spcgou.setChecked(true);
                            } else if ("2".equals(spcg)) {
                                spcgou.setChecked(false);
                            }
                            if ("1".equals(spzj)) {
                                spzjia.setChecked(true);
                            } else if ("2".equals(spzj)) {
                                spzjia.setChecked(false);
                            }

                            if ("1".equals(spsc)) {
                                spschu.setChecked(true);
                            } else if ("2".equals(spsc)) {
                                spschu.setChecked(false);
                            }
                        }
                    }
                });
    }

    /**
     * 员工权限更新
     *
     * @param
     */
    public void updatepersion() {
        Map<String, Object> params = new HashMap<>();
        params.put("staffId", staffId);
        params.put("powerChange", ml);
        params.put("powerSupplier", ghs);
        params.put("powerDelete", spsc);
        params.put("powerPur", spcg);
        params.put("powerAdd", spzj);
        params.put("powerPrice", xgjg);
        params.put("powerName", spmc);
        params.put("powerCount", spkc);
        params.put("powerInPrice", spjj);
        params.put("powerKind", spfl);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.updatepersonlistTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111","员工权限更新 = "+s);
                        if(TextUtils.isEmpty(s)){
                            return;
                        }
                        int statusInfo5 = 2;
                        JSONObject objectInfo5;
                        try {
                            objectInfo5 = new JSONObject(s);
                            statusInfo5 = objectInfo5.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (statusInfo5 == 1) {
                            showMessage(getLanguageValue("modification")+getLanguageValue("succeed"));
                            finish();
                        }
                    }
                });
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.cb_songshui:
                break;
            case R.id.cb_kuaidi:
                break;
            case R.id.cb_jiazheng:
                break;
            case R.id.cb_guoshu:
                break;
            case R.id.cb_xianhua:
                break;
            case R.id.cb_dangao:
                break;
            case R.id.cb_xiyi:
                break;
            case R.id.btn_update:
                SendUpdate();
                break;
        }
    }

    /**
     * 修改权限   ml,ghs,xgjg,spkc,spfl,spmc,spjj,spcg,spzj,spsc
     */

    private void SendUpdate() {

        if (cb_moling.isChecked()) {
            ml = "1";
        } else {
            ml = "2";
        }

        if (cb_gonghuoshang.isChecked()) {
            ghs = "1";
        } else {
            ghs = "2";
        }

        if (cb_xiugaijiage.isChecked()) {
            xgjg = "1";
        } else {
            xgjg = "2";
        }

        if (cb_shangpinkucun.isChecked()) {
            spkc = "1";
        } else {
            spkc = "2";
        }

        if (cb_shangpinfenlei.isChecked()) {
            spfl = "1";
        } else {
            spfl = "2";
        }
        if (cb_spmcheng.isChecked()) {
            spmc = "1";
        } else {
            spmc = "2";
        }

        if (spjjia.isChecked()) {
            spjj = "1";
        } else {
            spjj = "2";
        }

        if (spcgou.isChecked()) {
            spcg = "1";
        } else {
            spcg = "2";
        }
        if (spzjia.isChecked()) {
            spzj = "1";
        } else {
            spzj = "2";
        }

        if (spschu.isChecked()) {
            spsc = "1";
        } else {
            spsc = "2";
        }

        updatepersion();
    }
}
