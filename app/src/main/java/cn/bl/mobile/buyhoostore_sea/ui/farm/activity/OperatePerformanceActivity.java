package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:农批-报表-经营业绩
 * Created by jingang on 2023/5/28
 */
@SuppressLint("NonConstantResourceId")
public class OperatePerformanceActivity extends BaseActivity {
    @BindView(R.id.tvDay0)
    TextView tvDay0;
    @BindView(R.id.tvDay1)
    TextView tvDay1;
    @BindView(R.id.tvDay2)
    TextView tvDay2;

    private int day;//0.今日 1.近7日 2.近14日

    @Override
    protected int getLayoutId() {
        return R.layout.activity_operate_performance;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
    }

    @OnClick({R.id.ivBack, R.id.tvDay0, R.id.tvDay1, R.id.tvDay2, R.id.tvScreen})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvDay0:
                //今日
                if (day != 0) {
                    day = 0;
                    tvDay0.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay1.setBackgroundResource(0);
                    tvDay2.setBackgroundResource(0);
                }
                break;
            case R.id.tvDay1:
                //近7日
                if (day != 1) {
                    day = 1;
                    tvDay1.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay0.setBackgroundResource(0);
                    tvDay2.setBackgroundResource(0);
                }
                break;
            case R.id.tvDay2:
                //近14日
                if (day != 2) {
                    day = 2;
                    tvDay2.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay1.setBackgroundResource(0);
                    tvDay0.setBackgroundResource(0);
                }
                break;
            case R.id.tvScreen:
                //筛选
                break;
        }
    }

}
