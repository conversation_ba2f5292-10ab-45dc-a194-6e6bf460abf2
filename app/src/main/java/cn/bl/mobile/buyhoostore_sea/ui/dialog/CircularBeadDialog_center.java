package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;


public class CircularBeadDialog_center extends Dialog {
	private static int default_width = 300; //默认宽度
	private static int default_height = 120;//默认高度
	public CircularBeadDialog_center(Context context, View layout, int style) {
		//this(context, default_width, default_height, layout, style);
        super(context, style);
        setContentView(layout);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.gravity = Gravity.CENTER;
        window.setAttributes(params);
	}

	public CircularBeadDialog_center(Context context, int width, int height, View layout, int style) {
		super(context, style);
		setContentView(layout);
		Window window = getWindow();
		WindowManager.LayoutParams params = window.getAttributes();
		params.gravity = Gravity.CENTER;
        params.width = width;
        params.height = height;
		window.setAttributes(params);
	}
}
