package cn.bl.mobile.buyhoostore_sea.ui.login;

import org.json.JSONException;
import org.json.JSONObject;

import android.annotation.SuppressLint;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 忘记密码
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ForgetPwdActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etAccount)
    EditText etAccount;
    @BindView(R.id.etCode)
    EditText etCode;
    @BindView(R.id.tvCode)
    TextView tvCode;
    @BindView(R.id.etPwd)
    EditText etPwd;
    @BindView(R.id.etPwd1)
    EditText etPwd1;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String account, code, pwd, pwd1;
    private String session_id, token;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_forget_pwd;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText(getLanguageValue("forget") + getLanguageValue("password"));
    }


    @OnClick({R.id.ivBack, R.id.tvCode, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCode:
                //获取验证码
                account = etAccount.getText().toString().trim();
                if (TextUtils.isEmpty(account)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("regist") + getLanguageValue("phoneNum"));
                    return;
                }
//                if (account.length() < 11) {
//                    showMessage(getLanguageValue("enterCorrectPhone"));
//                    return;
//                }
                if (!isRun) {
                    getSendCode();
                }
                break;
            case R.id.tvConfirm:
                //提交
                account = etAccount.getText().toString().trim();
                code = etCode.getText().toString().trim();
                pwd = etPwd.getText().toString().trim();
                pwd1 = etPwd1.getText().toString().trim();
                if (TextUtils.isEmpty(account)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("regist") + getLanguageValue("phoneNum"));
                    return;
                }
//                if (account.length() < 11) {
//                    showMessage(getLanguageValue("enterCorrectPhone"));
//                    return;
//                }
                if (TextUtils.isEmpty(code)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("captcha"));
                    return;
                }
                if (TextUtils.isEmpty(pwd)) {
                    showMessage(getLanguageValue("passwordNew"));
                    return;
                }
                if (TextUtils.isEmpty(pwd1)) {
                    showMessage(getLanguageValue("passwordNewAgain"));
                    return;
                }
                if (!TextUtils.equals(pwd, pwd1)) {
                    showMessage(getLanguageValue("passwordInconsistent"));
                    return;
                }
                getCheckCode();
                break;
        }
    }

    @Override
    public void setText() {
        etAccount.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("regist") + getLanguageValue("phoneNum"));
        etCode.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("captcha"));
        tvCode.setText(getLanguageValue("get") + getLanguageValue("captcha"));
        etPwd.setHint(getLanguageValue("passwordNew"));
        etPwd1.setHint(getLanguageValue("passwordNewAgain"));
        tvTips.setText(getLanguageValue("passwordPrompt"));
        tvConfirm.setText(getLanguageValue("submit"));
    }


    //倒计时
    private boolean isRun = false;//倒计时线程是否运行

    /**
     * 验证码倒计时
     */
    public void time() {
        if (!isRun) {
            new CountdownThread(60000, 1000).start();// 构造CountDownTimer对象
            isRun = true;
        }
    }

    /**
     * 倒计时
     */
    class CountdownThread extends CountDownTimer {
        public CountdownThread(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @SuppressLint("SetTextI18n")
        @Override
        public void onTick(long millisUntilFinished) {
            tvCode.setText((millisUntilFinished / 1000) + "s");
        }

        @Override
        public void onFinish() {
            //倒计时结束时触发
            tvCode.setText(getLanguageValue("revalidate"));
            isRun = false;
        }
    }

    // 发送验证码
    public void getSendCode() {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getSendCodeUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "发送验证码 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            session_id = data.getData().toString();
                            Log.e(tag, "session_id = " + session_id);
                            time();
                        }
                    }
                });
    }

    //验证手机和验证码是否匹配
    public void getCheckCode() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("smsCode", code);
        params.put("sessionId", session_id);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getCheckPwdUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "验证手机和验证码是否匹配 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusCheck;
                        String msgCheck;
                        try {
                            JSONObject objectCheck = new JSONObject(s);
                            statusCheck = objectCheck.getInt("status");
                            msgCheck = objectCheck.getString("msg");
                            if (statusCheck == Constants.SUCCESS_CODE) {
                                try {
                                    token = objectCheck.getString("msg");
                                    getSetNewPwd();
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            } else {
                                hideDialog();
                                showMessage(msgCheck);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    // 设置新密码
    public void getSetNewPwd() {
        Map<String, Object> params = new HashMap<>();
        params.put("token", token);
        params.put("staffAccount", account);
        params.put("newStaffPwd", pwd);
        params.put("sessionId", session_id);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getSetNewPwdUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "设置新密码 = " + s);
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusCheck = 2;
                        String msgCheck = "";
                        JSONObject objectCheck;
                        try {
                            objectCheck = new JSONObject(s);
                            statusCheck = objectCheck.getInt("status");
                            msgCheck = objectCheck.getString("msg");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        showMessage(msgCheck);
                        if (statusCheck == Constants.SUCCESS_CODE) {
                            finish();
                        }
                    }
                });
    }

}
