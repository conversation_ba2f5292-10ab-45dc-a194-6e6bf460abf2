package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.bean.AggregateQualificationsData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment.AggregationApplyFragment00;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment.AggregationApplyFragment02;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment.AggregationApplyFragment03;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment.AggregationApplyFragment10;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment.AggregationApplyFragment11;
import cn.bl.mobile.buyhoostore_sea.utils.RegularUtils;
import cn.bl.mobile.buyhoostore_sea.view.NoScrollViewPager;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-聚合码-非法人申请
 * Created by jingang on 2023/2/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyActivity1 extends BaseActivity {
    private AggregationApplyActivity1 TAG = AggregationApplyActivity1.this;
    @BindView(R.id.tvTitle)
    TextView tvTitle;

    @BindView(R.id.ivIcon0)
    ImageView ivIcon0;
    @BindView(R.id.v01)
    View v01;
    @BindView(R.id.v10)
    View v10;
    @BindView(R.id.ivIcon1)
    ImageView ivIcon1;
    @BindView(R.id.v11)
    View v11;
    @BindView(R.id.v20)
    View v20;
    @BindView(R.id.ivIcon2)
    ImageView ivIcon2;
    @BindView(R.id.v21)
    View v21;
    @BindView(R.id.v30)
    View v30;
    @BindView(R.id.ivIcon3)
    ImageView ivIcon3;
    @BindView(R.id.v31)
    View v31;
    @BindView(R.id.v40)
    View v40;
    @BindView(R.id.ivIcon4)
    ImageView ivIcon4;

    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.tvType2)
    TextView tvType2;
    @BindView(R.id.tvType3)
    TextView tvType3;
    @BindView(R.id.tvType4)
    TextView tvType4;

    @BindView(R.id.viewPager)
    NoScrollViewPager viewPager;

    private List<Fragment> fragmentList = new ArrayList<>();
    private AggregationApplyFragment10 fragment10;
    private AggregationApplyFragment00 fragment0;
    private AggregationApplyFragment11 fragment1;
    private AggregationApplyFragment02 fragment2;
    private AggregationApplyFragment03 fragment3;
    private int pos;

    private int status;
    private String guideImage,//授权书示例
            helibao,//合利宝授权书
            ruiyinxin;//瑞银信授权书

    //授权书
    private String auth0, auth1;
    //营业执照
    private String business0, business1,
            business,//商户名称
            business_code,//营业执照号
            business_address;//地址信息
    //身份证
    private String idcard0, idcard1, idcard2, idcard3, idcard4,
            idcard_name,//真实姓名（法人）
            idcard_code,//身份证（法人）
            idcard_name1,//真实姓名（持卡人）
            idcard_code1;//身份证（持卡人）
    //银行卡
    private String bank0, bank1,
            bank_code,//银行卡号
            bank_name;//开户行
    private String shop0, shop1, shop2;//店面照

    @Override
    protected int getLayoutId() {
        return R.layout.activity_aggregation_apply1;
    }

    @Override
    public void initViews() {
        status = getIntent().getIntExtra("status", 0);
        guideImage = getIntent().getStringExtra("guideImage");
        helibao = getIntent().getStringExtra("helibao");
        ruiyinxin = getIntent().getStringExtra("ruiyinxin");
    }

    @Override
    public void initData() {
        getQualifications();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                postSaveQualifications(1);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        String msg = event.getMsg();
        switch (event.getTypes()) {
            //授权书
            case "auth0":
                //广州合利宝
                auth0 = msg;
                break;
            case "auth1":
                auth1 = msg;
                break;
            //营业执照
            case "business0":
                //营业执照
                business0 = msg;
                break;
            case "business1":
                //手持营业执照
                business1 = msg;
                break;
            case "business":
                business = msg;
                break;
            case "business_code":
                business_code = msg;
                break;
            case "business_address":
                business_address = msg;
                break;
            //身份证
            case "idcard0":
                idcard0 = msg;
                break;
            case "idcard1":
                idcard1 = msg;
                break;
            case "idcard2":
                idcard2 = msg;
                break;
            case "idcard3":
                idcard3 = msg;
                break;
            case "idcard4":
                idcard4 = msg;
                break;
            case "idcard_name":
                idcard_name = msg;
                break;
            case "idcard_code":
                idcard_code = msg;
                break;
            case "idcard_name1":
                idcard_name1 = msg;
                break;
            case "idcard_code1":
                idcard_code1 = msg;
                break;
            //银行卡
            case "bank0":
                bank0 = msg;
                break;
            case "bank1":
                bank1 = msg;
                break;
            case "bank_code":
                bank_code = msg;
                break;
            case "bank_name":
                bank_name = msg;
                break;
            //店面照
            case "shop0":
                shop0 = msg;
                break;
            case "shop1":
                shop1 = msg;
                break;
            case "shop2":
                shop2 = msg;
                break;
        }
        setUI();
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("applyOpenAggcode"));
        tvType0.setText(getLanguageValue("businessLicense"));
        tvType1.setText(getLanguageValue("idNumber"));
        tvType2.setText(getLanguageValue("bankCard"));
        tvType3.setText(getLanguageValue("shop")+getLanguageValue("photo"));
    }

    /**
     * 设置fragment
     */
    private void setFragment() {
        setUI();
        fragmentList.clear();
        //授权书
        fragment10 = new AggregationApplyFragment10(status, auth0, auth1, guideImage, helibao, ruiyinxin);
        fragment10.setListener(() -> {
            viewPager.setCurrentItem(viewPager.getCurrentItem() + 1);
        });
        //营业执照
        fragment0 = new AggregationApplyFragment00(status, business0, business1, business, business_code, business_address,false);
        fragment0.setListener(new AggregationApplyFragment00.MyListener() {
            @Override
            public void onPreviousClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() - 1);
            }

            @Override
            public void onNextClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() + 1);
            }
        });
        //身份证
        fragment1 = new AggregationApplyFragment11(status, idcard0, idcard1, idcard2, idcard3, idcard4, idcard_name, idcard_code, idcard_name1, idcard_code1);
        fragment1.setListener(new AggregationApplyFragment11.MyListener() {
            @Override
            public void onPreviousClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() - 1);
            }

            @Override
            public void onNextClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() + 1);
            }
        });
        //银行卡
        fragment2 = new AggregationApplyFragment02(status, bank0, bank1, bank_code, bank_name);
        fragment2.setListener(new AggregationApplyFragment02.MyListener() {
            @Override
            public void onPreviousClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() - 1);
            }

            @Override
            public void onNextClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() + 1);
            }
        });
        //店面照
        fragment3 = new AggregationApplyFragment03(status, shop0, shop1, shop2);
        fragment3.setListener(new AggregationApplyFragment03.MyListener() {
            @Override
            public void onPreviousClick() {
                viewPager.setCurrentItem(viewPager.getCurrentItem() - 1);
            }

            @Override
            public void onNextClick() {
                //授权书
                if (TextUtils.isEmpty(auth0)) {
                    showMessage(getLanguageValue("uploadHelipay"));
                    return;
                }
                if (TextUtils.isEmpty(auth1)) {
                    showMessage(getLanguageValue("uploadRuipay"));
                    return;
                }
                //营业执照
                if (TextUtils.isEmpty(business0)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("businessLicense"));
                    return;
                }
                if (TextUtils.isEmpty(business1)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("handHeld")+getLanguageValue("businessLicense"));
                    return;
                }
                if (TextUtils.isEmpty(business)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("merchant")+getLanguageValue("name"));
                    return;
                }
                if (TextUtils.isEmpty(business_code)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("businessLicense")+getLanguageValue("number"));
                    return;
                }
                if (TextUtils.isEmpty(business_address)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("address")+getLanguageValue("information"));
                    return;
                }
                //身份证
                if (TextUtils.isEmpty(idcard0)) {
                    showMessage(getLanguageValue("uploadLegalPersonFace"));
                    return;
                }
                if (TextUtils.isEmpty(idcard1)) {
                    showMessage(getLanguageValue("uploadLegalPersonEmblem"));
                    return;
                }
                if (TextUtils.isEmpty(idcard2)) {
                    showMessage(getLanguageValue("uploadLegalPersonHeld"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_name)) {
                    showMessage(getLanguageValue("uploadLegalPersonRealname"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_code)) {
                    showMessage(getLanguageValue("enterLegalPersonId"));
                    return;
                }
                if(!RegularUtils.isValidatedAllIdcard(idcard_code)){
                    showMessage(getLanguageValue("enterLegalPersonIdCorrect"));
                    return;
                }
                if (TextUtils.isEmpty(idcard3)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("identityWitnessFace"));
                    return;
                }
                if (TextUtils.isEmpty(idcard4)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("indentityEmblem"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_name1)) {
                    showMessage(getLanguageValue("enterApplicantRealname"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_code1)) {
                    showMessage(getLanguageValue("enterApplicantId"));
                    return;
                }
                if(!RegularUtils.isValidatedAllIdcard(idcard_code)){
                    showMessage(getLanguageValue("enterApplicantIdCorrect"));
                    return;
                }
                //银行卡
                if (TextUtils.isEmpty(bank0)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("bankCardFrontPhoto"));
                    return;
                }
                if (TextUtils.isEmpty(bank1)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("bankCardPhoto"));
                    return;
                }
                if (TextUtils.isEmpty(bank_code)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("bankCard"));
                    return;
                }
                if (TextUtils.isEmpty(bank_name)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("bankDeposit"));
                    return;
                }
                //店面照
                if (TextUtils.isEmpty(shop0)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("insidePhoto"));
                    return;
                }
                if (TextUtils.isEmpty(shop1)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("insidePhoto"));
                    return;
                }
                if (TextUtils.isEmpty(shop2)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("cashierPhoto"));
                    return;
                }
                postSaveQualifications(2);
            }
        });
        fragmentList.add(fragment10);
        fragmentList.add(fragment0);
        fragmentList.add(fragment1);
        fragmentList.add(fragment2);
        fragmentList.add(fragment3);
        viewPager.setAdapter(new Adapter(getSupportFragmentManager(), fragmentList));
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                pos = position;
                setUI();
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });
        viewPager.setNoScroll(true);
    }

    /**
     * 更新UI（有没有好办法）
     */
    private void setUI() {
        Log.e(tag, "pos = " + pos);
        switch (pos) {
            case 0:
                //授权书
                if (!TextUtils.isEmpty(auth0) && !TextUtils.isEmpty(auth1)) {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v01.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //营业执照
                if (!TextUtils.isEmpty(business0) && !TextUtils.isEmpty(business1)
                        && !TextUtils.isEmpty(business) && !TextUtils.isEmpty(business_code) &&  !TextUtils.isEmpty(business_address)) {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v10.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v11.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //身份证
                if (!TextUtils.isEmpty(idcard0) && !TextUtils.isEmpty(idcard1) && !TextUtils.isEmpty(idcard2) && !TextUtils.isEmpty(idcard_name) && !TextUtils.isEmpty(idcard_code)
                        && !TextUtils.isEmpty(idcard3) && !TextUtils.isEmpty(idcard4) && !TextUtils.isEmpty(idcard_name1) && !TextUtils.isEmpty(idcard_code1)) {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v20.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v21.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //银行卡
                if (!TextUtils.isEmpty(bank0) && !TextUtils.isEmpty(bank1) && !TextUtils.isEmpty(bank_code) && !TextUtils.isEmpty(bank_name)) {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v30.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v31.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //店面照
                if (!TextUtils.isEmpty(shop0) && !TextUtils.isEmpty(shop1) && !TextUtils.isEmpty(shop2)) {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v40.setBackgroundColor(getResources().getColor(R.color.color_d0));
                tvType0.setTextColor(getResources().getColor(R.color.blue));
                tvType1.setTextColor(getResources().getColor(R.color.color_999));
                tvType2.setTextColor(getResources().getColor(R.color.color_999));
                tvType3.setTextColor(getResources().getColor(R.color.color_999));
                tvType4.setTextColor(getResources().getColor(R.color.color_999));
                break;
            case 1:
                //授权书
                if (!TextUtils.isEmpty(auth0) && !TextUtils.isEmpty(auth1)) {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v01.setBackgroundColor(getResources().getColor(R.color.blue));
                //营业执照
                if (!TextUtils.isEmpty(business0) && !TextUtils.isEmpty(business1)
                        && !TextUtils.isEmpty(business) && !TextUtils.isEmpty(business_code)  && !TextUtils.isEmpty(business_address)) {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v10.setBackgroundColor(getResources().getColor(R.color.blue));
                v11.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //身份证
                if (!TextUtils.isEmpty(idcard0) && !TextUtils.isEmpty(idcard1) && !TextUtils.isEmpty(idcard2) && !TextUtils.isEmpty(idcard_name) && !TextUtils.isEmpty(idcard_code)
                        && !TextUtils.isEmpty(idcard3) && !TextUtils.isEmpty(idcard4) && !TextUtils.isEmpty(idcard_name1) && !TextUtils.isEmpty(idcard_code1)) {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v20.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v21.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //银行卡
                if (!TextUtils.isEmpty(bank0) && !TextUtils.isEmpty(bank1) && !TextUtils.isEmpty(bank_code) && !TextUtils.isEmpty(bank_name)) {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v30.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v31.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //店面照
                if (!TextUtils.isEmpty(shop0) && !TextUtils.isEmpty(shop1) && !TextUtils.isEmpty(shop2)) {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v40.setBackgroundColor(getResources().getColor(R.color.color_d0));
                tvType0.setTextColor(getResources().getColor(R.color.blue));
                tvType1.setTextColor(getResources().getColor(R.color.blue));
                tvType2.setTextColor(getResources().getColor(R.color.color_999));
                tvType3.setTextColor(getResources().getColor(R.color.color_999));
                tvType4.setTextColor(getResources().getColor(R.color.color_999));
                break;
            case 2:
                //授权书
                if (!TextUtils.isEmpty(auth0) && !TextUtils.isEmpty(auth1)) {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v01.setBackgroundColor(getResources().getColor(R.color.blue));
                //营业执照
                if (!TextUtils.isEmpty(business0) && !TextUtils.isEmpty(business1)
                        && !TextUtils.isEmpty(business) && !TextUtils.isEmpty(business_code)  && !TextUtils.isEmpty(business_address)) {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v10.setBackgroundColor(getResources().getColor(R.color.blue));
                v11.setBackgroundColor(getResources().getColor(R.color.blue));
                //身份证
                if (!TextUtils.isEmpty(idcard0) && !TextUtils.isEmpty(idcard1) && !TextUtils.isEmpty(idcard2) && !TextUtils.isEmpty(idcard_name) && !TextUtils.isEmpty(idcard_code)
                        && !TextUtils.isEmpty(idcard3) && !TextUtils.isEmpty(idcard4) && !TextUtils.isEmpty(idcard_name1) && !TextUtils.isEmpty(idcard_code1)) {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v20.setBackgroundColor(getResources().getColor(R.color.blue));
                v21.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //银行卡
                if (!TextUtils.isEmpty(bank0) && !TextUtils.isEmpty(bank1) && !TextUtils.isEmpty(bank_code) && !TextUtils.isEmpty(bank_name)) {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v30.setBackgroundColor(getResources().getColor(R.color.color_d0));
                v31.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //店面照
                if (!TextUtils.isEmpty(shop0) && !TextUtils.isEmpty(shop1) && !TextUtils.isEmpty(shop2)) {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v40.setBackgroundColor(getResources().getColor(R.color.color_d0));
                tvType0.setTextColor(getResources().getColor(R.color.blue));
                tvType1.setTextColor(getResources().getColor(R.color.blue));
                tvType2.setTextColor(getResources().getColor(R.color.blue));
                tvType3.setTextColor(getResources().getColor(R.color.color_999));
                tvType4.setTextColor(getResources().getColor(R.color.color_999));
                break;
            case 3:
                //授权书
                if (!TextUtils.isEmpty(auth0) && !TextUtils.isEmpty(auth1)) {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v01.setBackgroundColor(getResources().getColor(R.color.blue));
                //营业执照
                if (!TextUtils.isEmpty(business0) && !TextUtils.isEmpty(business1)
                        && !TextUtils.isEmpty(business) && !TextUtils.isEmpty(business_code)  && !TextUtils.isEmpty(business_address)) {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v10.setBackgroundColor(getResources().getColor(R.color.blue));
                v11.setBackgroundColor(getResources().getColor(R.color.blue));
                //身份证
                if (!TextUtils.isEmpty(idcard0) && !TextUtils.isEmpty(idcard1) && !TextUtils.isEmpty(idcard2) && !TextUtils.isEmpty(idcard_name) && !TextUtils.isEmpty(idcard_code)
                        && !TextUtils.isEmpty(idcard3) && !TextUtils.isEmpty(idcard4) && !TextUtils.isEmpty(idcard_name1) && !TextUtils.isEmpty(idcard_code1)) {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v20.setBackgroundColor(getResources().getColor(R.color.blue));
                v21.setBackgroundColor(getResources().getColor(R.color.blue));
                //银行卡
                if (!TextUtils.isEmpty(bank0) && !TextUtils.isEmpty(bank1) && !TextUtils.isEmpty(bank_code) && !TextUtils.isEmpty(bank_name)) {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v30.setBackgroundColor(getResources().getColor(R.color.blue));
                v31.setBackgroundColor(getResources().getColor(R.color.color_d0));
                //店面照
                if (!TextUtils.isEmpty(shop0) && !TextUtils.isEmpty(shop1) && !TextUtils.isEmpty(shop2)) {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan002);
                }
                v40.setBackgroundColor(getResources().getColor(R.color.color_d0));
                tvType0.setTextColor(getResources().getColor(R.color.blue));
                tvType1.setTextColor(getResources().getColor(R.color.blue));
                tvType2.setTextColor(getResources().getColor(R.color.blue));
                tvType3.setTextColor(getResources().getColor(R.color.blue));
                tvType4.setTextColor(getResources().getColor(R.color.color_999));
                break;
            case 4:
                //授权书
                if (!TextUtils.isEmpty(auth0) && !TextUtils.isEmpty(auth1)) {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon0.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v01.setBackgroundColor(getResources().getColor(R.color.blue));
                //营业执照
                if (!TextUtils.isEmpty(business0) && !TextUtils.isEmpty(business1)
                        && !TextUtils.isEmpty(business) && !TextUtils.isEmpty(business_code)  && !TextUtils.isEmpty(business_address)) {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon1.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v10.setBackgroundColor(getResources().getColor(R.color.blue));
                v11.setBackgroundColor(getResources().getColor(R.color.blue));
                //身份证
                if (!TextUtils.isEmpty(idcard0) && !TextUtils.isEmpty(idcard1) && !TextUtils.isEmpty(idcard2) && !TextUtils.isEmpty(idcard_name) && !TextUtils.isEmpty(idcard_code)
                        && !TextUtils.isEmpty(idcard3) && !TextUtils.isEmpty(idcard4) && !TextUtils.isEmpty(idcard_name1) && !TextUtils.isEmpty(idcard_code1)) {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon2.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v20.setBackgroundColor(getResources().getColor(R.color.blue));
                v21.setBackgroundColor(getResources().getColor(R.color.blue));
                //银行卡
                if (!TextUtils.isEmpty(bank0) && !TextUtils.isEmpty(bank1) && !TextUtils.isEmpty(bank_code) && !TextUtils.isEmpty(bank_name)) {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon3.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v30.setBackgroundColor(getResources().getColor(R.color.blue));
                v31.setBackgroundColor(getResources().getColor(R.color.blue));
                //店面照
                if (!TextUtils.isEmpty(shop0) && !TextUtils.isEmpty(shop1) && !TextUtils.isEmpty(shop2)) {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan003);
                } else {
                    ivIcon4.setImageResource(R.mipmap.ic_credit_apply_yuan001);
                }
                v40.setBackgroundColor(getResources().getColor(R.color.blue));
                tvType0.setTextColor(getResources().getColor(R.color.blue));
                tvType1.setTextColor(getResources().getColor(R.color.blue));
                tvType2.setTextColor(getResources().getColor(R.color.blue));
                tvType3.setTextColor(getResources().getColor(R.color.blue));
                tvType4.setTextColor(getResources().getColor(R.color.blue));
                break;
        }
    }

    /**
     * 聚合码查询资质信息
     */
    private void getQualifications() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAggregateQualifications(),
                map,
                AggregateQualificationsData.DataBean.class,
                new RequestListener<AggregateQualificationsData.DataBean>() {
                    @Override
                    public void success(AggregateQualificationsData.DataBean data) {
                        auth0 = data.getHelibaoAuth();
                        auth1 = data.getRuiyinxinAuth();

                        business0 = data.getLicense();
                        business1 = data.getLicenseHand();
                        business = data.getLicenseMerchantName();
                        business_code = data.getLicenseCode();
                        business_address = data.getLicenseAddress();

                        idcard0 = data.getLegalIdCardPortrait();
                        idcard1 = data.getLegalIdCardEmblem();
                        idcard2 = data.getLegalIdCardHand();
                        idcard3 = data.getCardholderIdCardPortrait();
                        idcard4 = data.getCardholderIdCardEmblem();
                        idcard_name = data.getLegalName();
                        idcard_code = data.getLegalIdCardCode();
                        idcard_name1 = data.getCardholderName();
                        idcard_code1 = data.getCardholderIdCardCode();

                        bank0 = data.getBankCardFront();
                        bank1 = data.getBankCardBack();
                        bank_code = data.getBankCode();
                        bank_name = data.getOpenBank();

                        shop0 = data.getShopDoorhead();
                        shop1 = data.getShopInside();
                        shop2 = data.getShopCashier();
                        setFragment();
                    }
                });
    }

    /**
     * 保存聚合码资质信息
     */
    private void postSaveQualifications(int saveType) {
        if (saveType == 1 && status != 0) {
            finish();
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("stafferId", getStaff_id());
        map.put("saveType", saveType);
        map.put("aggregateApplyType", 2);//申请人类型1法人申请2非法人申请
        //授权书
        map.put("helibaoAuth", auth0);
        map.put("ruiyinxinAuth", auth1);
        //营业执照
        map.put("license", business0);
        map.put("licenseHand", business1);
        map.put("licenseMerchantName", business);
        map.put("licenseCode", business_code);
        map.put("licenseAddress", business_address);
        //身份证
        map.put("legalIdCardPortrait", idcard0);
        map.put("legalIdCardEmblem", idcard1);
        map.put("legalIdCardHand", idcard2);
        map.put("cardholderIdCardPortrait", idcard3);
        map.put("cardholderIdCardEmblem", idcard4);
        map.put("legalIdCardCode", idcard_code);
        map.put("legalName", idcard_name);
        map.put("cardholderName", idcard_name1);
        map.put("cardholderIdCardCode", idcard_code1);
        //银行卡
        map.put("bankCardFront", bank0);
        map.put("bankCardBack", bank1);
        map.put("bankCode", bank_code);
        map.put("openBank", bank_name);
        //店面照
        map.put("shopDoorhead", shop0);
        map.put("shopInside", shop1);
        map.put("shopCashier", shop2);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getAggregateSaveQualifications(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        if (saveType == 1) {
                            finish();
                        } else {
                            BaseData data = new Gson().fromJson(s, BaseData.class);
                            if (data.getStatus() == Constants.SUCCESS_CODE) {
                                showMessage(data.getMsg());
                                startActivity(new Intent(TAG, AggregationApplyStateActivity.class)
                                        .putExtra("status", -1)
                                        .putExtra("applyType", 2)
                                );
                                setResult(Constants.CREDIT, new Intent());
                                finish();
                            }
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constants.CREDIT) {
            setResult(Constants.CREDIT, new Intent());
            finish();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        postSaveQualifications(1);
        return false;
    }

    /**
     * ViewPage适配器
     */
    public class Adapter extends FragmentPagerAdapter {
        private List<Fragment> mFragments;

        public Adapter(FragmentManager fm, List<Fragment> fragments) {
            super(fm);
            mFragments = fragments;
        }

        @Override
        public Fragment getItem(int i) {
            return mFragments.get(i);
        }

        @Override
        public int getCount() {
            return mFragments.size();
        }
    }

}
