package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.graphics.Color;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.king.keyboard.KingKeyboard;
import com.king.keyboard.KingKeyboardView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;

import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmOrderSubmitGoodsData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.NPCustomerDialog;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmCate1Adapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmOrderSubmitGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmGoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批
 * Created by jingang on 2023/5/24
 */
@SuppressLint("NonConstantResourceId")
public class FarmActivity extends BaseActivity {
    private FarmActivity TAG = FarmActivity.this;
    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvCustom)
    TextView tvCustom;
    @BindView(R.id.rvCate)
    RecyclerView rvCate;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.tvCount)
    TextView tvCount;

    private int type;//1.自营 2.代卖
    private String keywords;

    //分类（自营）
    private FarmCateAdapter cateAdapter;
    private List<CateData> cateList = new ArrayList<>();
    //分类（代卖）
    private FarmCate1Adapter cateAdapter1;
    private List<SupplierData> cateList1 = new ArrayList<>();
    private String classUnique;

    //商品
    private FarmGoodsAdapter mAdapter;
    private List<FarmGoodsData.GoodsListBean> dataList = new ArrayList<>();

    //选择商品
    private List<FarmOrderSubmitGoodsData> goodsList = new ArrayList<>();

    private MemberBean.DataBean mMemberBean;
    private String cusUnique, cusName;
    private KingKeyboard kingKeyboard;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_farm;
    }

    @Override
    public void initViews() {
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            page = 1;
            getGoods();
            return true;
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getCate();
    }

    @OnClick({R.id.ivBack, R.id.tvOrder, R.id.tvType0, R.id.tvType1,
            R.id.linCustom,
            R.id.linCateAdd, R.id.ivAdd,
            R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvOrder:
                //订单中心
                goToActivity(FarmOrderActivity.class);
                break;
            case R.id.tvType0:
                //自营
                if (type != 1) {
                    type = 1;
                    tvType0.setBackgroundResource(0);
                    tvType0.setTextColor(getResources().getColor(R.color.white));
                    tvType1.setBackgroundResource(R.drawable.shape_white_4);
                    tvType1.setTextColor(getResources().getColor(R.color.green));
                    getCate();
                }
                break;
            case R.id.tvType1:
                //代卖
                if (type != 2) {
                    type = 2;
                    tvType1.setBackgroundResource(0);
                    tvType1.setTextColor(getResources().getColor(R.color.white));
                    tvType0.setBackgroundResource(R.drawable.shape_white_4);
                    tvType0.setTextColor(getResources().getColor(R.color.green));
                    getSupplierList();
                }
                break;
            case R.id.linCustom:
                //客户
                NPCustomerDialog dialog = new NPCustomerDialog(this);
                if (mMemberBean != null) {
                    dialog.setCusUnique(mMemberBean.getCus_unique());
                }
                dialog.setonDialogClickListener(memberBean -> {
                    mMemberBean = memberBean;
                    cusUnique = memberBean.getCus_unique();
                    cusName = memberBean.getCusName();
                    tvCustom.setText(cusName);
                });
                new XPopup.Builder(this)
                        .popupAnimation(PopupAnimation.ScrollAlphaFromBottom)
                        .offsetY(200)
                        .isDestroyOnDismiss(true)
                        .asCustom(dialog)
                        .show();
                break;
            case R.id.linCateAdd:
                //添加分类
                goToActivity(GoodsCateActivity.class);
                break;
            case R.id.ivAdd:
                //添加
                startActivityForResult(new Intent(this, FarmAddGoodsActivity.class), Constants.ADD);
                break;
            case R.id.tvConfirm:
                //确认
                if (goodsList.size() < 1) {
                    showMessage("请添加商品");
                    return;
                }
                showDialogGoods();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_CATE:
                getCate();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //分类（自营）
        rvCate.setLayoutManager(new LinearLayoutManager(this));
        cateAdapter = new FarmCateAdapter(this);
//        rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < cateList.size(); i++) {
                    cateList.get(i).setCheck(false);
                }
                cateList.get(position).setCheck(true);
                cateAdapter.setDataList(cateList);
                classUnique = cateList.get(position).getGroupUnique();
                page = 1;
                getGoods();
            }
        });
        //分类（代卖）
        cateAdapter1 = new FarmCate1Adapter(this);
        cateAdapter1.setOnItemClickListener((view, position) -> {
            if (!cateList1.get(position).isCheck()) {
                for (int i = 0; i < cateList1.size(); i++) {
                    cateList1.get(i).setCheck(false);
                }
                cateList1.get(position).setCheck(true);
                cateAdapter1.setDataList(cateList1);
                classUnique = cateList1.get(position).getSupplier_unique();
                page = 1;
                getGoods();
            }
        });

        //商品
        recyclerView.setLayoutManager(new GridLayoutManager(this, 2));
        mAdapter = new FarmGoodsAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getGoods();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getGoods();
        });
        mAdapter.setListener(new FarmGoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                if (dataList.get(position).isCheck()) {
                    showMessage("该商品已选");
                    return;
                }
                showDialogOperation(dataList.get(position), position);
            }

            @Override
            public void onItemLongClick(View view, int position) {
                //长按编辑
                startActivity(new Intent(FarmActivity.this, FarmAddGoodsActivity.class)
                        .putExtra("data", dataList.get(position))
                );
            }
        });
    }

    private String count, price;
    private double total;

    /**
     * dialog（操作）
     *
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void showDialogOperation(FarmGoodsData.GoodsListBean data, int position) {
        count = "0";
        price = "0";
        TextView tvName, tvChengType, tvSaleType, tvKucun, tvTotal;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_operation_farm, null);
        dialog.setContentView(view);

        tvName = view.findViewById(R.id.tvDialogName);
        tvChengType = view.findViewById(R.id.tvDialogChengType);
        tvSaleType = view.findViewById(R.id.tvDialogSaleType);
        tvKucun = view.findViewById(R.id.tvDialogKucun);
        LinearLayout linCount = view.findViewById(R.id.linDialogCount);
        EditText etCount = view.findViewById(R.id.etDialogCount);
        LinearLayout linPrice = view.findViewById(R.id.linDialogPrice);
        EditText etPrice = view.findViewById(R.id.etDialogPrice);
        tvTotal = view.findViewById(R.id.tvDialogTotal);

        LinearLayout keyboardParent = view.findViewById(R.id.keyboardParent);
        tvName.setText(data.getGoodsName());
        tvChengType.setText(data.getGoodsChengType());
        tvSaleType.setText(data.getGoodsSaleType());
        tvKucun.setText("库存:" + DFUtils.getNum2(data.getGoodsCount()) + data.getGoodsUnit());
        etCount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                count = s.toString().trim();
                if (TextUtils.isEmpty(price)) {
                    tvTotal.setText("0.00");
                    return;
                }
                if (TextUtils.isEmpty(count)) {
                    tvTotal.setText("0.00");
                    return;
                }
                tvTotal.setText(DFUtils.getNum2(Double.parseDouble(count) * Double.parseDouble(price)));
            }
        });

        etPrice.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                price = s.toString().trim();
                if (TextUtils.isEmpty(price)) {
                    tvTotal.setText("0.00");
                    return;
                }
                if (TextUtils.isEmpty(count)) {
                    tvTotal.setText("0.00");
                    return;
                }
                tvTotal.setText(DFUtils.getNum2(Double.parseDouble(count) * Double.parseDouble(price)));
            }
        });

        etCount.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                linCount.setBackgroundResource(R.drawable.shape_33cc67_kuang_4);
                linPrice.setBackgroundResource(R.drawable.shape_f2_4);
            }
        });
        etPrice.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                linPrice.setBackgroundResource(R.drawable.shape_33cc67_kuang_4);
                linCount.setBackgroundResource(R.drawable.shape_f2_4);
            }
        });

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        kingKeyboard = new KingKeyboard(dialog, keyboardParent);
        KingKeyboardView.Config config = kingKeyboard.getKeyboardViewConfig();
        if (config != null) {
            config.setKeyDoneText("确定");
            config.setKeySpecialTextColor(Color.WHITE);
            config.setSpecialKeyBackground(ContextCompat.getDrawable(this, R.drawable.shape_btn_blue));
            config.setKeyBackground(ContextCompat.getDrawable(this, R.drawable.shape_keyboard_key));
            config.setKeyTextColor(Color.WHITE);
            config.setDeleteDrawable(ContextCompat.getDrawable(this, R.mipmap.ic_close004));
            config.setKeyRightTopText("下一步");
        }
        kingKeyboard.setKeyboardCustom(R.xml.custom_keyboard_number_decimal);
        kingKeyboard.register(etCount, KingKeyboard.KeyboardType.CUSTOM);
        kingKeyboard.register(etPrice, KingKeyboard.KeyboardType.CUSTOM);
        new Handler().postDelayed(() -> {
            etCount.requestFocus();
        }, 100);
        dialog.show();
        kingKeyboard.setOnKeyDoneListener((editText, primaryCode) -> {
            if (TextUtils.isEmpty(etCount.getText().toString()) || Double.parseDouble(etCount.getText().toString()) == 0) {
                showMessage("请输重量（斤）");
                return;
            }
            if (TextUtils.isEmpty(etPrice.getText().toString()) || Double.parseDouble(etPrice.getText().toString()) == 0) {
                showMessage("请输入单价");
                return;
            }
            goodsList.add(new FarmOrderSubmitGoodsData(data.getGoodsBarcode(),
                    Double.parseDouble(count),
                    Double.parseDouble(price),
                    data.getGoodsName(),
                    data.getGoodsChengType(),
                    data.getGoodsSaleType(),
                    data.getGoodsUnit()));
            dataList.get(position).setCheck(true);
            count = "0";
            price = "0";
            getTotal();
            kingKeyboard.hide();
            kingKeyboard.onDestroy();
            dialog.dismiss();
        });

        kingKeyboard.setOnKeyExtraListener((editText, primaryCode) -> {
            if (primaryCode == KingKeyboard.KEYCODE_KING_RIGHT_TOP) { // 点击下一项
                if (editText == etCount) {
                    etPrice.requestFocus();
                    linPrice.setBackgroundResource(R.drawable.shape_33cc67_kuang_4);
                    linCount.setBackgroundResource(R.drawable.shape_f2_4);
                } else {
                    etCount.requestFocus();
                    linCount.setBackgroundResource(R.drawable.shape_33cc67_kuang_4);
                    linPrice.setBackgroundResource(R.drawable.shape_f2_4);
                }
            }
        });
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> {
            dialog.dismiss();
        });
    }

    /**
     * dialog（商品）
     */
    @SuppressLint("SetTextI18n")
    private void showDialogGoods() {
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_goods_farm, null);
        dialog.setContentView(view);

        RecyclerView rvGoods = view.findViewById(R.id.rvDialogGoods);
        TextView tvTotal = view.findViewById(R.id.tvDialogTotal);
        TextView tvCount = view.findViewById(R.id.tvDialogCount);

        tvTotal.setText(DFUtils.getNum2(total));
        tvCount.setText("RM (" + goodsList.size() + "种)");

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(this) / 2);
        dialog.show();

        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> {
            //确认
            if (goodsList.size() < 1) {
                showMessage("请选择货品");
                return;
            }
            JSONObject object;
            JSONArray array = new JSONArray();
            try {
                for (int i = 0; i < goodsList.size(); i++) {
                    object = new JSONObject();
                    object.put("goodsBarcode", goodsList.get(i).getGoodsBarcode());
                    object.put("goodsCount", goodsList.get(i).getGoodsCount());
                    object.put("goodsPrice", goodsList.get(i).getGoodsPrice());
                    object.put("goodsName", goodsList.get(i).getGoodsName());
                    array.put(object);
                }
            } catch (Exception ignored) {
            }
            startActivity(new Intent(this, FarmOrderSubmitActivity.class)
                    .putExtra("total", total)
                    .putExtra("json", array.toString())
                    .putExtra("cusUnique", cusUnique)
                    .putExtra("cusName", cusName)
            );
            goodsList.clear();
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setCheck(false);
            }
            getTotal();
            dialog.dismiss();
        });
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        FarmOrderSubmitGoodsAdapter adapter = new FarmOrderSubmitGoodsAdapter(this);
        rvGoods.setAdapter(adapter);
        adapter.setDataList(goodsList);
        adapter.setOnItemClickListener((view1, position) -> {
            for (int i = 0; i < dataList.size(); i++) {
                if (dataList.get(i).getGoodsBarcode().equals(goodsList.get(position).getGoodsBarcode())) {
                    dataList.get(i).setCheck(false);
                }
            }
            goodsList.remove(position);
            adapter.remove(position);
            getTotal();
            tvTotal.setText(DFUtils.getNum2(total));
            tvCount.setText("RM (" + goodsList.size() + "种)");
            if (goodsList.size() < 1) {
                dialog.dismiss();
            }
        });
    }

    /**
     * 小计
     */
    @SuppressLint("SetTextI18n")
    private void getTotal() {
        tvCount.setText("RM (" + goodsList.size() + "种)");
        total = 0;
        for (int i = 0; i < goodsList.size(); i++) {
            total = total + goodsList.get(i).getGoodsCount() * goodsList.get(i).getGoodsPrice();
        }
        tvTotal.setText(DFUtils.getNum2(total));
    }

    /**
     * popWindow（客户）
     *
     * @param showView
     */
    private void showPopCustom(View showView) {
        View view = View.inflate(this, R.layout.pop_goods_custom, null);
        final PopupWindow popWindow = new PopupWindow(view, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, true);
        popWindow.showAsDropDown(showView);
    }

    /**
     * 查询会员列表
     */
    private void getRemember() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("pages", page);
        map.put("perpage", "100000");
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getRemember(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {

                    }
                });
    }

    /**
     * 分类（自营：自定义商品分类查询）
     */
    private void getCate() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("status", 1);//1.启用 2.禁用
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsCate(),
                params,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        rvCate.setAdapter(cateAdapter);
                        cateList.clear();
                        cateList.addAll(list);
                        if (cateList.size() > 0) {
                            cateList.get(0).setCheck(true);
                            classUnique = cateList.get(0).getGroupUnique();
                        }
                        cateAdapter.setDataList(cateList);
                        page = 1;
                        getGoods();
                    }
                });
    }

    /**
     * 分类（代卖：供货商列表）
     */
    private void getSupplierList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("page", page);
        map.put("limit", 100000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getQuerySupplierList(),
                map,
                SupplierData.class,
                new RequestListListener<SupplierData>() {
                    @Override
                    public void onResult(List<SupplierData> supplierData) {
                        rvCate.setAdapter(cateAdapter1);
                        cateList1.clear();
                        cateList1.addAll(supplierData);
                        if (cateList1.size() > 0) {
                            cateList1.get(0).setCheck(true);
                            classUnique = cateList1.get(0).getSupplier_unique();
                        }
                        cateAdapter1.setDataList(cateList1);
                        page = 1;
                        getGoods();
                    }
                });
    }

    /**
     * 农批产品信息查询
     */
    private void getGoods() {
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("classUnique", classUnique);
        map.put("goodsType", type);
        map.put("goodsMsg", keywords);
        map.put("pages", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getFarmGoodsList(),
                map,
                FarmGoodsData.class,
                new RequestListener<FarmGoodsData>() {
                    @Override
                    public void success(FarmGoodsData farmGoodsData) {
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(farmGoodsData.getGoodsList());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.CATE:
                    //添加分类
                    getCate();
                    break;
                case Constants.ADD:
                    //添加商品
                    page = 1;
                    getGoods();
                    break;
            }
        }
    }
}
