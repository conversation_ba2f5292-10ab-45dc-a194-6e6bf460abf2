package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:调拨单（实体类）
 * Created by jingang on 2023/5/24
 */
public class AllotData implements Serializable {
    /**
     * purchaseListId : 14
     * purchaseListUnique : 2905349835368373
     * purchaseListRemark : 测试
     * purchaseListDate : 1529100298000
     * storehouseInId : 1518144027422
     * storehouseOutId : 8302016134121
     * userId : 12377
     * allocationStatus : 1
     * recipientsUserId : null
     * recipientsUserIdName : null
     * recipientsTime : null
     * userName : null
     * detailInfoList : null
     */

    private int purchaseListId;
    private String purchaseListUnique;
    private String purchaseListRemark;//备注
    private long purchaseListDate;//申请时间
    private long storehouseInId;//申请时间
    private long storehouseOutId;//掉出方id
    private int userId;//申请用户id
    private int allocationStatus;
    private int recipientsUserId;//接收方用户id
    private String recipientsUserIdName;//接收方用户名称
    private long recipientsTime;//接收时间
    private String userName;//申请用户名称
    private String storehouseInName;//拨出店面
    private String storehouseOutName;//拨入店面
    private List<DetailInfoListBean> detailInfoList;

    public int getPurchaseListId() {
        return purchaseListId;
    }

    public void setPurchaseListId(int purchaseListId) {
        this.purchaseListId = purchaseListId;
    }

    public String getPurchaseListUnique() {
        return purchaseListUnique;
    }

    public void setPurchaseListUnique(String purchaseListUnique) {
        this.purchaseListUnique = purchaseListUnique;
    }

    public String getPurchaseListRemark() {
        return purchaseListRemark;
    }

    public void setPurchaseListRemark(String purchaseListRemark) {
        this.purchaseListRemark = purchaseListRemark;
    }

    public long getPurchaseListDate() {
        return purchaseListDate;
    }

    public void setPurchaseListDate(long purchaseListDate) {
        this.purchaseListDate = purchaseListDate;
    }

    public long getStorehouseInId() {
        return storehouseInId;
    }

    public void setStorehouseInId(long storehouseInId) {
        this.storehouseInId = storehouseInId;
    }

    public long getStorehouseOutId() {
        return storehouseOutId;
    }

    public void setStorehouseOutId(long storehouseOutId) {
        this.storehouseOutId = storehouseOutId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(int allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public int getRecipientsUserId() {
        return recipientsUserId;
    }

    public void setRecipientsUserId(int recipientsUserId) {
        this.recipientsUserId = recipientsUserId;
    }

    public String getRecipientsUserIdName() {
        return recipientsUserIdName;
    }

    public void setRecipientsUserIdName(String recipientsUserIdName) {
        this.recipientsUserIdName = recipientsUserIdName;
    }

    public long getRecipientsTime() {
        return recipientsTime;
    }

    public void setRecipientsTime(long recipientsTime) {
        this.recipientsTime = recipientsTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStorehouseInName() {
        return storehouseInName;
    }

    public void setStorehouseInName(String storehouseInName) {
        this.storehouseInName = storehouseInName;
    }

    public String getStorehouseOutName() {
        return storehouseOutName;
    }

    public void setStorehouseOutName(String storehouseOutName) {
        this.storehouseOutName = storehouseOutName;
    }

    public List<DetailInfoListBean> getDetailInfoList() {
        return detailInfoList;
    }

    public void setDetailInfoList(List<DetailInfoListBean> detailInfoList) {
        this.detailInfoList = detailInfoList;
    }

    public static class DetailInfoListBean implements Serializable{
        private int purchaseListDetailId;
        private String purchaseListUnique;
        private String goodsName;
        private String goodsBarcode;
        private double purchaseListDetailCount;
        private double goodsInPrice;//商品进价
        private double goodsSalePrice;//售价
        private String goodsSpec;
        private String goodsUnit;
        private String goodsPicturePath;

        public DetailInfoListBean() {
        }

        public DetailInfoListBean(String goodsName, String goodsBarcode, double purchaseListDetailCount, double goodsInPrice, double goodsSalePrice, String goodsSpec, String goodsUnit, String goodsPicturePath) {
            this.goodsName = goodsName;
            this.goodsBarcode = goodsBarcode;
            this.purchaseListDetailCount = purchaseListDetailCount;
            this.goodsInPrice = goodsInPrice;
            this.goodsSalePrice = goodsSalePrice;
            this.goodsSpec = goodsSpec;
            this.goodsUnit = goodsUnit;
            this.goodsPicturePath = goodsPicturePath;
        }

        public int getPurchaseListDetailId() {
            return purchaseListDetailId;
        }

        public void setPurchaseListDetailId(int purchaseListDetailId) {
            this.purchaseListDetailId = purchaseListDetailId;
        }

        public String getPurchaseListUnique() {
            return purchaseListUnique;
        }

        public void setPurchaseListUnique(String purchaseListUnique) {
            this.purchaseListUnique = purchaseListUnique;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getPurchaseListDetailCount() {
            return purchaseListDetailCount;
        }

        public void setPurchaseListDetailCount(double purchaseListDetailCount) {
            this.purchaseListDetailCount = purchaseListDetailCount;
        }

        public double getGoodsInPrice() {
            return goodsInPrice;
        }

        public void setGoodsInPrice(double goodsInPrice) {
            this.goodsInPrice = goodsInPrice;
        }

        public double getGoodsSalePrice() {
            return goodsSalePrice;
        }

        public void setGoodsSalePrice(double goodsSalePrice) {
            this.goodsSalePrice = goodsSalePrice;
        }

        public String getGoodsSpec() {
            return goodsSpec;
        }

        public void setGoodsSpec(String goodsSpec) {
            this.goodsSpec = goodsSpec;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public String getGoodsPicturePath() {
            return goodsPicturePath;
        }

        public void setGoodsPicturePath(String goodsPicturePath) {
            this.goodsPicturePath = goodsPicturePath;
        }
    }
}
