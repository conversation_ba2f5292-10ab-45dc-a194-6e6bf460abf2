package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.text.Editable;
import android.text.InputType;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.leaf.library.StatusBarUtil;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.SystemUtils;


import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.ui.WebActivity;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PermissionDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:注册
 * Created by jingang on 2023/3/6
 */
@SuppressLint("NonConstantResourceId")
public class RegisterActivity extends BaseActivity {
    @BindView(R.id.tvTitles)
    TextView tvTitle;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.linAccount)
    LinearLayout linAccount;
    @BindView(R.id.tvAccount)
    TextView tvAccount;
    @BindView(R.id.etAccount)
    EditText etAccount;
    @BindView(R.id.tvPwd)
    TextView tvPwd;
    @BindView(R.id.etPwd)
    EditText etPwd;
    @BindView(R.id.ivEye)
    ImageView ivEye;
    @BindView(R.id.linShop)
    LinearLayout linShop;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.etShopName)
    EditText etShopName;
    @BindView(R.id.tvShopAddress)
    TextView tvShopAddress;
    @BindView(R.id.tvShopAddressSelect)
    TextView tvShopAddressSelect;
    @BindView(R.id.linMobile)
    LinearLayout linMobile;
    @BindView(R.id.tvMobile)
    TextView tvMobile;
    @BindView(R.id.etMobile)
    EditText etMobile;
    @BindView(R.id.tvRegisterMobile)
    TextView tvRegisterMobile;
    @BindView(R.id.tvPrevious)
    TextView tvPrevious;
    @BindView(R.id.tvNext)
    TextView tvNext;
    @BindView(R.id.ivPrivacy)
    ImageView ivPrivacy;
    @BindView(R.id.tvPrivacy)
    TextView tvPrivacy;

    private boolean isEye;//密码可见
    private int type;//0.账号 1.店铺名称 2.联系电话
    private String account, pwd,
            shopName,
            mobile,
            address, lat, lng, province, city, district, area;
    private boolean isPrivacy;//同意用户协议

    @Override
    protected int getLayoutId() {
        return R.layout.activity_register;
    }

    @Override
    public void initViews() {
        StatusBarUtil.setTransparentForWindow(this);
        StatusBarUtil.setDarkMode(this);
        initWatcher();
    }

    @OnClick({R.id.ivBack, R.id.ivEye, R.id.tvShopAddressSelect, R.id.tvRegisterMobile, R.id.tvPrevious, R.id.tvNext, R.id.ivPrivacy})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivEye:
                //密码可见
                isEye = !isEye;
                if (isEye) {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);//设置密码可见
                    ivEye.setImageResource(R.drawable.open_eye);
                } else {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
                    ivEye.setImageResource(R.drawable.close_eye);
                }
                break;
            case R.id.tvShopAddressSelect:
                //选择店铺位置
                getLocationManage();
                break;
            case R.id.tvRegisterMobile:
                //同注册账号
                etMobile.setText(account);
                break;
            case R.id.tvPrevious:
                //上一步
                if (type == 2) {
                    type = 1;
                    tvTitle.setText(getLanguageValue("shop" + getLanguageValue("name")));
                    tvTips.setText(getLanguageValue("storeNameReality"));
                    linAccount.setVisibility(View.GONE);
                    linShop.setVisibility(View.VISIBLE);
                    linMobile.setVisibility(View.GONE);
                    tvPrevious.setVisibility(View.VISIBLE);
                } else {
                    type = 0;
                    tvTitle.setText(getLanguageValue("accountRegistration"));
                    tvTips.setText(getLanguageValue("phonneLogin"));
                    linAccount.setVisibility(View.VISIBLE);
                    linShop.setVisibility(View.GONE);
                    linMobile.setVisibility(View.GONE);
                    tvPrevious.setVisibility(View.GONE);
                }
                setTextBg();
                break;
            case R.id.tvNext:
                //下一步
                hideSoftInput(this);
                if (!isPrivacy) {
                    showMessage(getLanguageValue("consentClause"));
                    return;
                }
                if (type == 0) {
                    if (TextUtils.isEmpty(account)) {
                        showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("phoneNum"));
                        return;
                    }
//                    if (account.length() < 11) {
//                        showMessage(getLanguageValue("enterCorrectPhone"));
//                        return;
//                    }
                    if (TextUtils.isEmpty(pwd)) {
                        showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("login") + getLanguageValue("password"));
                        return;
                    }
                    postVerify_user();
                } else if (type == 1) {
                    if (TextUtils.isEmpty(shopName)) {
                        showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shop") + getLanguageValue("name"));
                        return;
                    }
                    if (TextUtils.isEmpty(address) || TextUtils.isEmpty(lat) || TextUtils.isEmpty(lng)) {
                        showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("shop") + getLanguageValue("location"));
//                        showMessage("请选择店铺位置");
                        return;
                    }
                    postVerify_shop();
                } else {
                    if (TextUtils.isEmpty(mobile)) {
                        showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("contact") + getLanguageValue("phoneNum"));
                        return;
                    }
                    startActivityForResult(new Intent(this, RegisterBusinessActivity.class)
                                    .putExtra("account", account)
                                    .putExtra("pwd", pwd)
                                    .putExtra("shopName", shopName)
                                    .putExtra("mobile", mobile)
                                    .putExtra("address", address)
                                    .putExtra("lat", lat)
                                    .putExtra("lng", lng)
                                    .putExtra("province", province)
                                    .putExtra("city", city)
                                    .putExtra("district", district)
                                    .putExtra("area", area)
                            , Constants.REGISTER
                    );
                }
                break;
            case R.id.ivPrivacy:
                //同意用户协议
                isPrivacy = !isPrivacy;
                if (isPrivacy) {
                    ivPrivacy.setImageResource(R.mipmap.ic_chosen001);
                } else {
                    ivPrivacy.setImageResource(R.mipmap.ic_chose001);
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("accountRegistration"));
        tvTips.setText(getLanguageValue("phonneLogin"));
        tvAccount.setText(getLanguageValue("phoneNum"));
        etAccount.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("phoneNum"));
        tvPwd.setText(getLanguageValue("password"));
        etPwd.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("login") + getLanguageValue("password"));
        tvShopName.setText(getLanguageValue("shop") + getLanguageValue("name"));
        etShopName.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shop") + getLanguageValue("name"));
        tvShopAddress.setText(getLanguageValue("shop") + getLanguageValue("location"));
        tvShopAddressSelect.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("shop") + getLanguageValue("location"));
        tvMobile.setText(getLanguageValue("phone"));
        etMobile.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("phone"));
        tvRegisterMobile.setText(getLanguageValue("sameRegisterPhone"));
        tvPrevious.setText(getLanguageValue("previousStep"));
        tvNext.setText(getLanguageValue("nextStep"));
//        tvPrivacy.setText(getLanguageValue("readAgree"));

        privacy_str0 = getRstr(R.string.agree_privacy_tips1);
        privacy_str1 = getRstr(R.string.agree_privacy_tips2);
        privacy_str2 = getRstr(R.string.agree_privacy_tips3);
        privacy_str3 = getRstr(R.string.agree_privacy_tips4);
        tvPrivacy.setText(privacy_str0 + privacy_str1 + privacy_str2 + privacy_str3);
        setSpannableText();
    }

    /**
     * 定位服务
     */
    private void getLocationManage() {
        if (!SystemUtils.hasGPSDevice(this)) {
            getStorageManage();
            return;
        }
        if (SystemUtils.isLocationEnabled(this)) {
            getStorageManage();
        } else {
            PermissionDialog.showDialog(this,
                    getLanguageValue("locationService"),
                    type -> {
                        if (type == 0) {
                            startActivityForResult(new Intent().setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                                    , Constants.LOCATION_ENABLED
                            );
                        }
                    });
        }
    }

    /**
     * 存储服务(所有文件访问)
     */
    private void getStorageManage() {
        if (SystemUtils.hasAllFilesAccessPermission(this)) {
            getLocationPermission();
        } else {
            PermissionDialog.showDialog(this,
                    getLanguageValue("allFillServiceDesc"),
                    type -> {
                        if (type == 0) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                startActivityForResult(new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                                                .setData(Uri.parse("package:" + getApplicationContext().getPackageName()))
                                        , Constants.LOCATION_STORAGE_MANAGE);
                            } else {
                                getLocationPermission();
                            }
                        }
                    });
        }
    }

    /**
     * 定位权限
     */
    private void getLocationPermission() {
        if (PermissionUtils.checkPermissionsGroup(this, 1)) {
            startActivityForResult(new Intent(this, PoiActivity.class),
                    Constants.CHOOSE_LOCATION);
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
        }
    }

    private String privacy_str0,
            privacy_str1,
            privacy_str2,
            privacy_str3;

    /**
     * TextView设置多种颜色及部分点击事件
     */
    public void setSpannableText() {
        SpannableString spannableString = new SpannableString(tvPrivacy.getText().toString().trim());
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(RegisterActivity.this, getLanguageValue("userServiceAgreement"), ZURL.CONSTANT_shopDoc);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 6, 14, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(RegisterActivity.this, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 14, 20, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(RegisterActivity.this, getLanguageValue("userServiceAgreement"), ZURL.CONSTANT_shopDoc);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, privacy_str0.length(), (privacy_str0 + privacy_str1).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(RegisterActivity.this, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, (privacy_str0 + privacy_str1).length(), (privacy_str0 + privacy_str1 + privacy_str2).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvPrivacy.setMovementMethod(LinkMovementMethod.getInstance());//设置可点击状态
        tvPrivacy.setText(spannableString);
    }

    /**
     * 监听editview输入
     */
    private void initWatcher() {
        etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                setTextBg();
            }
        });
        etPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                pwd = s.toString().trim();
                setTextBg();
            }
        });
        etShopName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                shopName = s.toString().trim();
                setTextBg();
            }
        });
        etMobile.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mobile = s.toString().trim();
                setTextBg();
            }
        });
    }

    /**
     * 根据不同输入判断各控件显示样式
     */
    private void setTextBg() {
        if (type == 0) {
            if (TextUtils.isEmpty(account)) {
                tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
                return;
            }
            if (TextUtils.isEmpty(pwd)) {
                tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
                return;
            }
            tvNext.setBackgroundResource(R.drawable.shape_blue_22);
        } else if (type == 1) {
            if (TextUtils.isEmpty(shopName)) {
                tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
                return;
            }
//            if (TextUtils.isEmpty(address)) {
//                tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
//                return;
//            }
            tvNext.setBackgroundResource(R.drawable.shape_blue_22);
        } else {
            if (TextUtils.isEmpty(mobile)) {
                tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
                return;
            }
            tvNext.setBackgroundResource(R.drawable.shape_blue_22);
        }
    }

    /**
     * 验证注册手机号和密码
     */
    private void postVerify_user() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("manager_account", account);
        map.put("manager_pwd", pwd);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getVerifyUser(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showMessage(data.getMsg());
                            return;
                        }
                        type = 1;
                        tvTitle.setText(getLanguageValue("shop") + getLanguageValue("name"));
                        tvTips.setText(getLanguageValue("storeNameReality"));
                        linAccount.setVisibility(View.GONE);
                        linShop.setVisibility(View.VISIBLE);
                        linMobile.setVisibility(View.GONE);
                        tvPrevious.setVisibility(View.VISIBLE);
                        isPrivacy = false;
                        ivPrivacy.setImageResource(R.mipmap.ic_chose001);
                        setTextBg();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 校验店铺名称
     */
    private void postVerify_shop() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shop_name", navticeEncode(shopName));
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getVerifyShop(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showMessage(data.getMsg());
                            return;
                        }
                        type = 2;
                        tvTitle.setText(getLanguageValue("phoneNum"));
                        tvTips.setText(getLanguageValue("contactToStaff"));
                        linAccount.setVisibility(View.GONE);
                        linShop.setVisibility(View.GONE);
                        linMobile.setVisibility(View.VISIBLE);
                        tvPrevious.setVisibility(View.VISIBLE);
                        isPrivacy = false;
                        ivPrivacy.setImageResource(R.mipmap.ic_chose001);
                        setTextBg();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 转码
     *
     * @param url
     * @return
     */
    private String navticeEncode(String url) {
        String resulUrl = "";
        try {
            resulUrl = URLEncoder.encode(url, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resulUrl;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    startActivityForResult(new Intent(this, PoiActivity.class),
                            Constants.CHOOSE_LOCATION);
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.CHOOSE_LOCATION:
                //选择定位
                if (data != null) {
                    address = data.getStringExtra("address");
                    lat = data.getStringExtra("lat");
                    lng = data.getStringExtra("lng");
                    province = data.getStringExtra("province");
                    city = data.getStringExtra("city");
                    district = data.getStringExtra("district");
                    area = data.getStringExtra("area");
                    tvShopAddressSelect.setText(address);
                    Log.e(tag, "province = " + province + " city = " + city + " district = " + district + " area = " + area + " address = " + address);
                    setTextBg();
                }
                break;
            case Constants.REGISTER:
                if (data != null) {
                    finish();
                }
                break;
            case Constants.LOCATION_ENABLED:
                //定位服务开关
                if (SystemUtils.isLocationEnabled(this)) {
                    getStorageManage();
                } else {
                    showMessage(getLanguageValue("locationServiceNotTurn"));
                }
                break;
            case Constants.LOCATION_STORAGE_MANAGE:
                //存储开关
                if (SystemUtils.hasAllFilesAccessPermission(this)) {
                    getLocationPermission();
                } else {
                    showMessage(getLanguageValue("allFillServiceNotTurn"));
                }
                break;
        }
    }
}
