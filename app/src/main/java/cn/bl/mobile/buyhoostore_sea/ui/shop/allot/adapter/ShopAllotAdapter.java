package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.ShopData;

/**
 * Describe:店铺列表-调拨单筛选（适配器）
 * Created by jingang on 2023/5/31
 */
public class ShopAllotAdapter extends BaseAdapter<ShopData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public ShopAllotAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_screen_string;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvName = holder.getView(R.id.tvItemName);
        if (mDataList.get(position).isCheck()) {
            tvName.setBackgroundResource(R.drawable.shape_blue_tm16_4);
            tvName.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_f2_4);
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_333));
        }
        tvName.setText(mDataList.get(position).getShopName());
    }
}
