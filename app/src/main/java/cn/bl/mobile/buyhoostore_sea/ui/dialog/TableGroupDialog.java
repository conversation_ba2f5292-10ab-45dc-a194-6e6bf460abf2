package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（桌台分类-编辑、新增）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class TableGroupDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogNameValue)
    TextView tvNameValue;
    @BindView(R.id.etDialogName)
    EditText etName;
    @BindView(R.id.tvDialogDel)
    TextView tvDel;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    private static String name;

    public static void showDialog(Context context, String name, MyListener listener) {
        TableGroupDialog.name = name;
        TableGroupDialog.listener = listener;
        TableGroupDialog dialog = new TableGroupDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public TableGroupDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_table_group);
        ButterKnife.bind(this);
        tvNameValue.setText(getLanguageValue("classification")+getLanguageValue("name"));
        etName.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("classification")+getLanguageValue("name"));
        tvDel.setText(getLanguageValue("delete"));
        tvConfirm.setText(getLanguageValue("preservation"));
        if (TextUtils.isEmpty(name)) {
            tvTitle.setText(getLanguageValue("add")+getLanguageValue("classification"));
            tvDel.setVisibility(View.GONE);
        } else {
            tvTitle.setText(getLanguageValue("editor")+getLanguageValue("classification"));
            etName.setText(name);
            tvDel.setVisibility(View.VISIBLE);
        }
    }

    @OnClick({R.id.ivDialogClose, R.id.tvDialogDel, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogDel:
                if (listener != null) {
                    listener.onDelClick();
                    dismiss();
                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                name = etName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("classification")+getLanguageValue("name"));
                    return;
                }
                if (listener != null) {
                    listener.onClick(name);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    @Override
    public void dismiss() {
        View view = getCurrentFocus();
        if (view instanceof TextView) {
            InputMethodManager mInputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.RESULT_UNCHANGED_SHOWN);
        }
        super.dismiss();
    }

    private static MyListener listener;

    public interface MyListener {
        void onDelClick();

        void onClick(String name);
    }
}
