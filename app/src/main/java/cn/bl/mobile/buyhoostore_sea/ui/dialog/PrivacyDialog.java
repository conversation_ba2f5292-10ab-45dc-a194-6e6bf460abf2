package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（隐私政策）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class PrivacyDialog extends BaseDialog {
    @BindView(R.id.tvDialogTips)
    TextView tvTips;
    @BindView(R.id.tvDialogContent)
    TextView tvContent;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;

    public static void showDialog(Context context, MyListener listener) {
        PrivacyDialog.listener = listener;
        PrivacyDialog dialog = new PrivacyDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PrivacyDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_privacy);
        ButterKnife.bind(this);
        tvTips.setText(getLanguageValue("tips"));
        tvConfirm.setText(getLanguageValue("agree"));
        tvCancel.setText(getLanguageValue("disagree"));

        SpannableString spannableString = new SpannableString(getLanguageValue("privacyPolicyDetail0")+"\n"
                +getLanguageValue("privacyPolicyDetail1")+"\n"
                +getLanguageValue("privacyPolicyDetail2")+"\n"
                +getLanguageValue("privacyPolicyDetail3")+"\n"
                +getLanguageValue("privacyPolicyDetail4")+"\n"
                +getLanguageValue("privacyPolicyDetail5")+"\n"

        );
//        int start = spannableString.toString().indexOf("了《隐私权限政策》");
//        ClickableSpan clickableSpan = new ClickableSpan() {
//            @Override
//            public void onClick(@NonNull View widget) {
//                if (listener != null) {
//                    listener.onPrivacyClick();
//                }
//            }
//
//            @SuppressLint("ResourceAsColor")
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                ds.setColor(Color.BLUE);
//                ds.setUnderlineText(false);
//            }
//        };
//        spannableString.setSpan(clickableSpan, start + 1, start + 9, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        tvContent.setHighlightColor(Color.TRANSPARENT);
        tvContent.setText(spannableString);
    }

    @OnClick({R.id.tvDialogConfirm, R.id.tvDialogCancel})
    public void onViewClicked(View view) {
        if (listener != null) {
            switch (view.getId()) {
                case R.id.tvDialogConfirm:
                    //同意
                    listener.onClick(1);
                    break;
                case R.id.tvDialogCancel:
                    //不同意
                    listener.onClick(0);
                    break;
            }
            dismiss();
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }


    private static MyListener listener;

    public interface MyListener {
        void onPrivacyClick();

        void onClick(int type);
    }
}
