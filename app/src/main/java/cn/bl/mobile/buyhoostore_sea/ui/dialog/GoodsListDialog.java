package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.GoodsDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

import static android.content.Context.INPUT_METHOD_SERVICE;

/**
 * Describe:dialog（快速收银-商品列表）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsListDialog extends BaseDialog {
    @BindView(R.id.etDialogTest)
    EditText etTest;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.linDialogLoading)
    LinearLayout linLoading;

    private static GoodsListDialog dialog;
    private static String cateUnique;
    private static double total;//购物车总价
    private static List<GoodsData> cartList;//购物车列表
    private List<GoodsData> dataList = new ArrayList<>();
    private GoodsDialogAdapter mAdapter;

    public static void showDialog(Context context, String unique, List<GoodsData> cartList, double total, MyListener listener) {
        GoodsListDialog.listener = listener;
        GoodsListDialog.cateUnique = unique;
        GoodsListDialog.cartList = cartList;
        GoodsListDialog.total = total;
        dialog = new GoodsListDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.setCancelable(true);
        dialog.show();
    }

    public GoodsListDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_goods_list);
        ButterKnife.bind(this);
        etTest.requestFocus();
        etTest.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                    (event != null && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                // 当点击软键盘上的“完成”按钮或按下硬件/虚拟Enter键时执行以下操作
                Log.e("111111", "扫码枪 = " + etTest.getText().toString());
                setResultCode(etTest.getText().toString().trim());
                etTest.setText("");

                return true; // 表示已处理该事件
            }
            return false;
        });
        //获取焦点不弹出软键盘
        etTest.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
    }

    /**
     * 得到扫码结果
     *
     * @param code
     */
    private void setResultCode(String code) {
        Log.e(tag, "扫码结果 = " + code);
        if (TextUtils.isEmpty(code)) {
            return;
        }
        if (code.length() < 2) {
            return;
        }
        String chooseCode = code.substring(0, 2);
        switch (chooseCode) {
            case "62":
            case "28":
            case "13":
                if (code.length() == 18 || ("62".equals(chooseCode) && code.length() == 19)) {
                    //支付码
                    if (listener != null) {
                        listener.onScanResult(code);
                        dismiss();
                    }
                } else {
                    showMessage(getLanguageValue("showPaymentCodeCorrect"));
                }
                break;
            default:
                showMessage(getLanguageValue("showPaymentCodeCorrect"));
                break;
        }
    }

    /**
     * 是否显示
     *
     * @return
     */
    public static boolean isShow() {
        if (dialog == null) {
            return false;
        } else {
            return dialog.isShowing();
        }
    }

    /**
     * 消除
     */
    public static void dismissDialog() {
        if (dialog != null) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        setAdapter();
        getGoodsList();
    }

    @OnClick({R.id.linDialog})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linDialog:
                dismiss();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new GoodsDialogAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if ((total + dataList.get(position).getGoodsSalePrice()) > Constants.CASHIER_MAX_MONEY) {
                showMessage(getLanguageValue("cartTotal") + "RM" + Constants.CASHIER_MAX_MONEY);
                return;
            }
            dataList.get(position).setCartNum(dataList.get(position).getCartNum() + 1);
            mAdapter.notifyItemChanged(position);
            total = total + dataList.get(position).getGoodsSalePrice();
            if (listener != null) {
                listener.onClick(dataList.get(position));
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsList();
            }
        });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        linLoading.setVisibility(View.VISIBLE);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("groupUnique", cateUnique);
        map.put("pageIndex", page);
        map.put("pageSize", 20);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSelectGoods(),
                map,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> list) {
                        linLoading.setVisibility(View.GONE);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        for (int i = 0; i < cartList.size(); i++) {
                            for (int j = 0; j < dataList.size(); j++) {
                                if (cartList.get(i).getGoodsId().equals(dataList.get(j).getGoodsId())) {
                                    dataList.get(j).setCartNum(cartList.get(i).getCartNum());
                                }
                            }
                        }
                        Log.e(tag, "size = " + dataList.size());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        linLoading.setVisibility(View.GONE);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {

        void onClick(GoodsData data);

        void onScanResult(String result);
    }
}
