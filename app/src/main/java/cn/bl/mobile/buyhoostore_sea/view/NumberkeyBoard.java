package cn.bl.mobile.buyhoostore_sea.view;

import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.Nullable;

import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:
 * Created by jingang on 2023/3/4
 */
public class NumberkeyBoard extends LinearLayout {
    private String resultStr = "";
    private Context mContext;
    private OnMValueChangedListener onMValueChangedListener;

    public void setOnMValueChangedListener(OnMValueChangedListener onMValueChangedListener) {
        this.onMValueChangedListener = onMValueChangedListener;
    }

    public interface OnMValueChangedListener {
        void onChange(String var1);
    }

    public NumberkeyBoard(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.num_lock_layout, this);
        new Handler().postDelayed(() -> {
            initListener();
        }, 10);
    }

    private void initListener() {
        class buttonsOnClickListener implements OnClickListener {
            @Override
            public void onClick(View v) {
                switch (v.getId()) {
                    case R.id.numLockOneBtn:
                        addNumberToResult("1");
                        break;
                    case R.id.numLockTwoBtn:
                        addNumberToResult("2");
                        break;
                    case R.id.numLockThreeBtn:
                        addNumberToResult("3");
                        break;
                    case R.id.numLockFourBtn:
                        addNumberToResult("4");
                        break;
                    case R.id.numLockFiveBtn:
                        addNumberToResult("5");
                        break;
                    case R.id.numLockSixBtn:
                        addNumberToResult("6");
                        break;
                    case R.id.numLockSevenBtn:
                        addNumberToResult("7");
                        break;
                    case R.id.numLockEightBtn:
                        addNumberToResult("8");
                        break;
                    case R.id.numLockNineBtn:
                        addNumberToResult("9");
                        break;
                    case R.id.numLockZeroBtn:
                        addNumberToResult("0");
                        break;
                    case R.id.numLockSpotBtn:
                        addNumberToResult(".");
                        break;
                    case R.id.numLockDeleteBtn:
                        reduceNumberToResult();
                        break;
                }
            }
        }
        findViewById(R.id.numLockOneBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockTwoBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockThreeBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockFourBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockFiveBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockSixBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockSevenBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockEightBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockNineBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockZeroBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockSpotBtn).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.numLockDeleteBtn).setOnClickListener(new buttonsOnClickListener());
    }


    //清除数字
    public void clearNumber() {
        resultStr = "";
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //增加数字
    private void addNumberToResult(String numberStr) {
        if (!TextUtils.isEmpty(resultStr)) {
            if (resultStr.contains(".")) {
                if (".".equals(numberStr)) {
                    return;
                } else {
                    //限制小数点后面最多有两位小数
                    int strLength = resultStr.substring(resultStr.indexOf(".")).length();
                    if (strLength == 3) {
                        return;
                    }
                }
            }
        }

        //第一次输入的是点，显示0.
        if ("".equals(resultStr) && ".".equals(numberStr)) {
            resultStr = "0";
            //小程序中关系网录入姓名手机号，但收银机上人脸识别不显示addNumberToResult
        }

        //数字前多次输入0，只显示一个0
        if (resultStr != null && !"".equals(resultStr) && "0".equals(resultStr) && "0".equals(numberStr)) {
            return;
        }
        resultStr += numberStr;

        if (null != onMValueChangedListener) {
            if (resultStr.length() > 9) {
                resultStr = resultStr.substring(0, 9);
                Toast.makeText(mContext, "金额不能大于9位", Toast.LENGTH_SHORT).show();
            }
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //擦除数字
    private void reduceNumberToResult() {
        if (resultStr.length() > 0) {
            resultStr = resultStr.substring(0, resultStr.length() - 1);
        }
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

}
