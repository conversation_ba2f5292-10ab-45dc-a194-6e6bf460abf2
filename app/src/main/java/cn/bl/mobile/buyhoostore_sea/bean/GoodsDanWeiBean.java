package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class GoodsDanWeiBean {


    /**
     * status : 1
     * data : [{"goods_unit_id":"单位ID","shop_unique":"店铺编号","goods_unit":"商品单位"}]
     */

    private int status;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * goods_unit_id : 单位ID
         * shop_unique : 店铺编号
         * goods_unit : 商品单位
         */

        private String goods_unit_id;
        private String shop_unique;
        private String goods_unit;

        public String getGoods_unit_id() {
            return goods_unit_id;
        }

        public void setGoods_unit_id(String goods_unit_id) {
            this.goods_unit_id = goods_unit_id;
        }

        public String getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(String shop_unique) {
            this.shop_unique = shop_unique;
        }

        public String getGoods_unit() {
            return goods_unit;
        }

        public void setGoods_unit(String goods_unit) {
            this.goods_unit = goods_unit;
        }
    }
}