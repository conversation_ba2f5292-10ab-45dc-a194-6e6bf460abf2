package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsChangeRecordAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsChangeRecordData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品-变更记录
 * Created by jingang on 2023/8/23
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsChangeRecordActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvDay0)
    TextView tvDay0;
    @BindView(R.id.tvDay1)
    TextView tvDay1;
    @BindView(R.id.tvDay2)
    TextView tvDay2;
    @BindView(R.id.tvDay3)
    TextView tvDay3;
    @BindView(R.id.tvDate)
    TextView tvDate;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int day = -1;//0.今天 1.昨天 2.本周 3.本月
    private String img, goodsBarcode, goodsName, startTime, endTime;

    private GoodsChangeRecordAdapter mAdapter;
    private List<GoodsChangeRecordData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_change_record;
    }

    @Override
    public void initViews() {
        img = getIntent().getStringExtra("img");
        goodsBarcode = getIntent().getStringExtra("goodsBarcode");
        goodsName = getIntent().getStringExtra("goodsName");
        setAdapter();
    }

    @Override
    public void initData() {
        startTime = DateUtils.getOldDate(0);
        endTime = DateUtils.getOldDate(0);
        tvDate.setText(startTime + " - " + endTime);
        getGoodsChangeRecord();
    }

    @OnClick({R.id.ivBack, R.id.ivClear, R.id.ivScan,
            R.id.tvDay0, R.id.tvDay1, R.id.tvDay2, R.id.tvDay3,
            R.id.linDate})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入内容
                etSearch.setText("");
                break;
            case R.id.ivScan:
                //扫一哈子
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.tvDay0:
                //今天
                if (day != 0) {
                    day = 0;
                    clearDay();
                    tvDay0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay0.setTextColor(getResources().getColor(R.color.blue));
                    startTime = DateUtils.getOldDate(0);
                    endTime = DateUtils.getOldDate(0);
                    tvDate.setText(startTime + " - " + endTime);
                    page = 1;
                    getGoodsChangeRecord();
                }
                break;
            case R.id.tvDay1:
                //昨天
                if (day != 1) {
                    day = 1;
                    clearDay();
                    tvDay1.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay1.setTextColor(getResources().getColor(R.color.blue));
                    startTime = DateUtils.getOldDate(-1);
                    endTime = DateUtils.getOldDate(0);
                    tvDate.setText(startTime + " - " + endTime);
                    page = 1;
                    getGoodsChangeRecord();
                }
                break;
            case R.id.tvDay2:
                //本周
                if (day != 2) {
                    day = 2;
                    clearDay();
                    tvDay2.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay2.setTextColor(getResources().getColor(R.color.blue));
//                    startTime = DateUtils.getOldDate(-7);
                    startTime = DateUtils.getWeekStartTime();
                    endTime = DateUtils.getOldDate(0);
                    tvDate.setText(startTime + " - " + endTime);
                    page = 1;
                    getGoodsChangeRecord();
                }
                break;
            case R.id.tvDay3:
                //本月
                if (day != 3) {
                    day = 3;
                    clearDay();
                    tvDay3.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay3.setTextColor(getResources().getColor(R.color.blue));
//                    startTime = DateUtils.getOldDate(-30);
                    startTime = DateUtils.getMonthStartTime();
                    endTime = DateUtils.getOldDate(0);
                    tvDate.setText(startTime + " - " + endTime);
                    page = 1;
                    getGoodsChangeRecord();
                }
                break;
            case R.id.linDate:
                //选择日期
                DateStartEndDialog.showDialog(this,
                        startTime,
                        endTime, startTime,
                        (startDate, endDate) -> {
                            clearDay();
                            day = -1;
                            startTime = startDate;
                            endTime = endDate;
                            if (TextUtils.isEmpty(startTime)) {
                                tvDate.setText("");
                            } else {
                                tvDate.setText(startTime + " - " + endTime);
                            }
                            page = 1;
                            getGoodsChangeRecord();
                        });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity")+getLanguageValue("change")+getLanguageValue("record"));
        tvDay0.setText(getLanguageValue("today"));
        tvDay1.setText(getLanguageValue("yesterday"));
        tvDay2.setText(getLanguageValue("thisWeek"));
        tvDay3.setText(getLanguageValue("thisMonth"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsChangeRecordAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setGoodsName(goodsName);
        mAdapter.setOnItemClickListener((view, position) -> {
            startActivity(new Intent(this, GoodsChangeRecordInfoActivity.class)
                    .putExtra("goodsBarcode", goodsBarcode)
                    .putExtra("id", String.valueOf(dataList.get(position).getId()))
                    .putExtra("img", img)
                    .putExtra("goodsName", goodsName)
            );
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsChangeRecord();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsChangeRecord();
            }
        });
    }

    /**
     * 清除日期选择样式
     */
    private void clearDay() {
        tvDay0.setBackgroundResource(R.drawable.shape_f7_4);
        tvDay0.setTextColor(getResources().getColor(R.color.color_666));
        tvDay1.setBackgroundResource(R.drawable.shape_f7_4);
        tvDay1.setTextColor(getResources().getColor(R.color.color_666));
        tvDay2.setBackgroundResource(R.drawable.shape_f7_4);
        tvDay2.setTextColor(getResources().getColor(R.color.color_666));
        tvDay3.setBackgroundResource(R.drawable.shape_f7_4);
        tvDay3.setTextColor(getResources().getColor(R.color.color_666));
    }

    /**
     * 商品变更记录
     */
    private void getGoodsChangeRecord() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        map.put("startDate", startTime);
        map.put("endDate", endTime);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getGoodsChangeRecord(),
                map,
                GoodsChangeRecordData.class,
                new RequestListListener<GoodsChangeRecordData>() {
                    @Override
                    public void onResult(List<GoodsChangeRecordData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    etSearch.setText(data.getStringExtra("result"));
                    //搜索
                    break;
            }
        }
    }

}
