package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.android.material.textfield.TextInputEditText;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.StringCallback;
import com.lzy.okgo.model.Response;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;


import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CommonMessageBean;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.evevtbus.EventManager;
import cn.bl.mobile.buyhoostore_sea.utils.KeyBoardUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 销售-退款单-拒绝原因（编辑、新增）
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AddCommonMessageActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etContent)
    TextInputEditText etContent;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private CommonMessageBean mCommonMessageBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_add_common_message;
    }

    @Override
    public void initViews() {
        if (null != mCommonMessageBean) {
            tvTitle.setText(getLanguageValue("editor")+getLanguageValue("skill"));
            etContent.setText(mCommonMessageBean.getMsg());
        } else {
            tvTitle.setText(getLanguageValue("add")+getLanguageValue("skill"));
        }
        etContent.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("commonSkill"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //保存
                if (etContent.getText().toString().length() < 1) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("commonSkill"));
                    return;
                }
                if (null != mCommonMessageBean) {
                    edit();
                } else {
                    add();
                }
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getItem(CommonMessageBean commonMessageBean) {
        mCommonMessageBean = commonMessageBean;
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        if (null != mCommonMessageBean) {
            EventBus.getDefault().removeStickyEvent(mCommonMessageBean);
        }
    }

    private void add() {
        Map<String,Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("msg", etContent.getText().toString());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAddRefundMsgUrL(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFUND_REFUSE_REASON));
                        EventBus.getDefault().post(new EventManager.RefreshCommonMessageList());
                        KeyBoardUtils.closeKeyboard(AddCommonMessageActivity.this);
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    private void edit() {
        OkGo.<String>post(ZURL.getHandelRefundMsgUrL())
                .tag(this)
                .params("delFlag", 1)
                .params("id", mCommonMessageBean.getId())
                .params("msg", etContent.getText().toString())
                .execute(new StringCallback() {
                    @Override
                    public void onSuccess(Response<String> response) {
                        JSONObject jsonObject = JSON.parseObject(response.body());
                        int status = jsonObject.getInteger("status");
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (status == 1) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFUND_REFUSE_REASON));
                            EventBus.getDefault().post(new EventManager.RefreshCommonMessageList());
                            KeyBoardUtils.closeKeyboard(AddCommonMessageActivity.this);
                            finish();
                        } else {
                            showMessage(jsonObject.getString("msg"));
                        }

                    }

                    @Override
                    public void onError(Response<String> response) {
                        super.onError(response);
                        showMessage(getLanguageValue("networkError"));
                    }
                });
    }

}

