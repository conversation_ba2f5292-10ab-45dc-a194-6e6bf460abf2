package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean;

import java.io.Serializable;

/**
 * Describe:供货商（实体类）
 * Created by jingang on 2023/8/21
 */
public class SupplierData implements Serializable {
    /**
     * supplierName : 演示供货商1
     * countPresent : 0
     * supplierUnique : 1476781086919
     * pgoodsStandard :
     * goodsPrice : 1
     * goodsUnit : 盒
     * goodsName : 香烟盒
     * goodsBarcode : 120211
     * goodsStandard :
     * pgoodsName :
     */

    private String supplierName;//供货商名称
    private double countPresent;//起赠的数量，为零为不赠
    private String supplierUnique;//供货商编号
    private double goodsPrice;
    private String goodsUnit;
    private String goodsName;
    private String goodsBarcode;
    private String goodsStandard;
    private String pgoodsName;
    private double pgoodsCount;
    private String pgoodsStandard;

    /*供货商管理*/
    private int id;
    private String contacts;//联系人
    private String contactMobile;//联系人电话
    private String bindFlagDesc;//绑定标识
    private String enableStatusDesc;//启用标识
    private int orderCount;//订单数
    private double debts;//总欠款
    private String address;//地址
    private int bindFlag;//绑定标记:0未绑定1已绑定
    private int enableStatus;//启用状态:1-启用2停用
    private int purchaseType;//采购类型:1-购销2自采
    private String supplierKindUnique;//分类编号
    private String supplierKindName;//分类名称

    public SupplierData() {
    }

    public SupplierData(String supplierName, String supplierUnique,int purchaseType) {
        this.supplierName = supplierName;
        this.supplierUnique = supplierUnique;
        this.purchaseType = purchaseType;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public double getCountPresent() {
        return countPresent;
    }

    public void setCountPresent(double countPresent) {
        this.countPresent = countPresent;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public double getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(double goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getPgoodsName() {
        return pgoodsName;
    }

    public void setPgoodsName(String pgoodsName) {
        this.pgoodsName = pgoodsName;
    }

    public double getPgoodsCount() {
        return pgoodsCount;
    }

    public void setPgoodsCount(double pgoodsCount) {
        this.pgoodsCount = pgoodsCount;
    }

    public String getPgoodsStandard() {
        return pgoodsStandard;
    }

    public void setPgoodsStandard(String pgoodsStandard) {
        this.pgoodsStandard = pgoodsStandard;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getBindFlagDesc() {
        return bindFlagDesc;
    }

    public void setBindFlagDesc(String bindFlagDesc) {
        this.bindFlagDesc = bindFlagDesc;
    }

    public String getEnableStatusDesc() {
        return enableStatusDesc;
    }

    public void setEnableStatusDesc(String enableStatusDesc) {
        this.enableStatusDesc = enableStatusDesc;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public double getDebts() {
        return debts;
    }

    public void setDebts(double debts) {
        this.debts = debts;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(int bindFlag) {
        this.bindFlag = bindFlag;
    }

    public int getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(int enableStatus) {
        this.enableStatus = enableStatus;
    }

    public int getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(int purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public String getSupplierKindName() {
        return supplierKindName;
    }

    public void setSupplierKindName(String supplierKindName) {
        this.supplierKindName = supplierKindName;
    }
}
