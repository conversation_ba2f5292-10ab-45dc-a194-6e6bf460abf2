package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.yxl.commonlibrary.base.BaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.OrderCountData;

import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerWhiteAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.dialog.SaleScreenDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:销售
 * Created by jingang on 2022/12/10
 */
@SuppressLint("NonConstantResourceId")
public class SaleFragment extends BaseFragment {
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvScreen)
    TextView tvScreen;
    @BindView(R.id.tvScreening)
    TextView tvScreening;
    @BindView(R.id.flRefund)
    FrameLayout flRefund;
    @BindView(R.id.tvRefund)
    TextView tvRefund;
    @BindView(R.id.tvRefundCount)
    TextView tvRefundCount;
    @BindView(R.id.tabLayout)
    TabLayout tabLayout;
    @BindView(R.id.viewPager)
    ViewPager viewPager;

    public static String keywords = "";
    private SharedPreferences sp;
    private String staffId;

    private SimpleFragmentPagerWhiteAdapter pagerAdapter;
    private List<String> titleList = new ArrayList<>();
    private List<Fragment> fragmentList = new ArrayList<>();
    private List<Integer> countList = new ArrayList<>();

    //筛选条件
    public static int time = -1, saleType = -1, saleListPayment = -1;
    public static String startTime, endTime;

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SaleFragment newInstance() {
        SaleFragment fragment = new SaleFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fm_safe;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        //是否展示已取消订单
        tvScreening.setVisibility(!TextUtils.isEmpty(SharedUtils.get(Constants.IS_SHOW_ORDER_CANCEL, IPreference.DataType.STRING)) ? View.VISIBLE : View.GONE);
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                if (TextUtils.isEmpty(str)) {
                    keywords = "";
                } else {
                    keywords = str;
                }
            }
        });
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("keywords"));
            return true;
        });
        sp = getActivity().getSharedPreferences("shop", Context.MODE_PRIVATE);
        staffId = sp.getString("staffId", "");
        setFragment();
    }

    @Override
    public void initData() {
        getOrderCount();
    }

    @OnClick({R.id.ivScan, R.id.tvScreen, R.id.flRefund})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivScan:
                //扫码
                startActivityForResult(new Intent(getActivity(), ScanActivity.class), Constants.SCAN);
                break;
            case R.id.tvScreen:
                //筛选
                SaleScreenDialog.showDialog(getActivity(),
                        time,
                        startTime,
                        endTime,
                        saleType,
                        saleListPayment,
                        (view1, isShowOrderCancel, time, startTime, endTime, saleType, saleListPayment) -> {
                            if (!isShowOrderCancel && TextUtils.isEmpty(startTime) && TextUtils.isEmpty(endTime) && saleType == -1 && saleListPayment == -1) {
                                tvScreening.setVisibility(View.GONE);
                            } else {
                                tvScreening.setVisibility(View.VISIBLE);
                            }
                            SaleFragment.time = time;
                            SaleFragment.startTime = startTime;
                            SaleFragment.endTime = endTime;
                            SaleFragment.saleType = saleType;
                            SaleFragment.saleListPayment = saleListPayment;
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("keywords"));
                        });
                break;
            case R.id.flRefund:
                goToActivity(RefundOrderActivity.class);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "keywords":
            case "state":
                getOrderCount();
                break;
        }
    }

    @Override
    public void setText() {
        etSearch.setHint(getLanguageValue("search")+getLanguageValue("order"));
        tvScreen.setText(getLanguageValue("filter"));
        tvScreening.setText(getLanguageValue("filtering"));
        tvRefund.setText(getLanguageValue("refund")+getLanguageValue("order"));
    }

    /**
     * 获取每种状态下的订单数量
     */
    private void getOrderCount() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("saleListMessage", keywords);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getshopsSaleListCount(),
                params,
                OrderCountData.class,
                new RequestListListener<OrderCountData>() {
                    @SuppressLint("SetTextI18n")
                    @Override
                    public void onResult(List<OrderCountData> dataList) {
                        countList.clear();
                        for (int i = 0; i < 8; i++) {
                            countList.add(0);
                        }
                        for (OrderCountData data : dataList) {
                            switch (data.getHandleState()) {
                                case 2:
                                    countList.set(1, data.getCount());
                                    break;
                                case 7:
                                    countList.set(2, data.getCount());
                                    break;
                                case 10:
                                    countList.set(3, data.getCount());
                                    break;
                                case 3:
                                    countList.set(4, data.getCount());
                                    break;
                                case 9:
                                    countList.set(5, data.getCount());
                                    break;
                                case 6:
                                    countList.set(6, data.getCount());
                                    break;
                                case 4:
                                    countList.set(7, data.getCount());
                                    break;
                                case -2:
                                    if (data.getCount() > 0) {
                                        tvRefundCount.setVisibility(View.VISIBLE);
                                        if (data.getCount() > 99) {
                                            tvRefundCount.setText("99+");
                                        } else {
                                            tvRefundCount.setText(String.valueOf(data.getCount()));
                                        }
                                    } else {
                                        tvRefundCount.setVisibility(View.GONE);
                                    }
                                    break;
                            }
                        }
                        setUpTabBadge();
                    }
                });
    }

    /**
     * 设置viewpage
     */
    private void setFragment() {
        keywords = "";
        time = -1;
        saleType = -1;
        saleListPayment = -1;
        startTime = "";
        endTime = "";
        titleList.clear();
        titleList.add(getLanguageValue("whole"));
//        titleList.add("待发货");
//        titleList.add("待骑手配送");
//        titleList.add("配送异常");
//        titleList.add("待收货");
//        titleList.add("待自提");
//        titleList.add("待评价");
//        titleList.add("已完成");
        titleList.add(getLanguageValue("toBeDelivered"));
        titleList.add(getLanguageValue("beDelivered"));
        titleList.add(getLanguageValue("deliveryException"));
        titleList.add(getLanguageValue("toBeReceived"));
        titleList.add(getLanguageValue("toBePickedUp"));
        titleList.add(getLanguageValue("beEvaluated"));
        titleList.add(getLanguageValue("completed"));

        countList.clear();
        for (int i = 0; i < 8; i++) {
            countList.add(0);
        }
        fragmentList.clear();
        for (int i = 0; i < titleList.size(); i++) {
            switch (i) {
                case 0:
                    fragmentList.add(OrderFragment.newInstance(-1, staffId));
                    break;
                case 1:
                    fragmentList.add(OrderFragment.newInstance(2, staffId));
                    break;
                case 2:
                    fragmentList.add(OrderFragment.newInstance(7, staffId));
                    break;
                case 3:
                    fragmentList.add(OrderFragment.newInstance(10, staffId));
                    break;
                case 4:
                    fragmentList.add(OrderFragment.newInstance(3, staffId));
                    break;
                case 5:
                    fragmentList.add(OrderFragment.newInstance(9, staffId));
                    break;
                case 6:
                    fragmentList.add(OrderFragment.newInstance(6, staffId));
                    break;
                case 7:
                    fragmentList.add(OrderFragment.newInstance(4, staffId));
                    break;
            }
        }

        pagerAdapter = new SimpleFragmentPagerWhiteAdapter(getActivity(), getChildFragmentManager(), fragmentList, titleList, countList);
        viewPager.setAdapter(pagerAdapter);
        viewPager.setCurrentItem(0);
        viewPager.setOffscreenPageLimit(titleList.size());
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        tabLayout.setupWithViewPager(viewPager);
        tabLayout.setSelectedTabIndicatorHeight(0);
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                updateTabTextView(tab, true);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                updateTabTextView(tab, false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        setUpTabBadge();

    }

    /**
     * 设置Tablayout上的标题的角标
     */
    private void setUpTabBadge() {
        for (int i = 0; i < fragmentList.size(); i++) {
            TabLayout.Tab tab = tabLayout.getTabAt(i);
            View customView = tab.getCustomView();
            if (customView != null) {
                ViewParent parent = customView.getParent();
                ViewGroup viewGroup = (ViewGroup) parent;
                if (parent != null) {
                    try {
                        viewGroup.removeView(customView);
                    } catch (Exception ignored) {
                    }
                }
            }
            // 更新CustomView
            tab.setCustomView(pagerAdapter.getTabItemView(i));
        }

        // 需加上以下代码,不然会出现更新Tab角标后,选中的Tab字体颜色不是选中状态的颜色
        TabLayout.Tab tabAt = tabLayout.getTabAt(tabLayout.getSelectedTabPosition());
        updateTabTextView(tabAt, true);
    }

    private void updateTabTextView(TabLayout.Tab tab, Boolean isSelect) {
        if (tab == null) {
            return;
        }
        if (tab.getCustomView() == null) {
            return;
        }
        TextView tabSelect = tab.getCustomView().findViewById(R.id.tvItemName);
        View line = tab.getCustomView().findViewById(R.id.vItem);
        if (isSelect) {
            //选中加粗
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.VISIBLE);
        } else {
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.GONE);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    etSearch.setText(data.getStringExtra("result"));
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("keywords"));
                    break;
            }
        }
    }
}
