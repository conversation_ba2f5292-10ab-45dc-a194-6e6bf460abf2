package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean;

import java.io.Serializable;
import java.util.List;

import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;

/**
 * Describe:供货商详情-付款记录-付款详情（实体类）
 * Created by jingang on 2023/9/21
 */
public class SupplierPaymentInfoData implements Serializable {
    /**
     * createId : 3586
     * createBy : 益农社服务员
     * paymentMoney : 100
     * remark : 哈哈哈
     * voucherPicturePath : ["https://file.buyhoo.cc/publicImage/2023/09/15/b74f9e5022744fc7b068fe32a525cd79.jpg"]
     * billList : [{"supplierUnique":null,"supplierName":null,"contactMobile":null,"statusDesc":null,"categoryCount":2,"purchaseAmount":121.5,"outstandingAmount":0,"goodsList":[{"goodsBarcode":"8960757619486","goodsName":"Raspberry elite","goodsImageUrl":"https://auth.ikeda10.xyz/ClothingShoesandJewelry","goodsCount":9},{"goodsBarcode":"23413252","goodsName":"Raspberry mini","goodsImageUrl":"https://www.kwokok7.cn/Food","goodsCount":6}]},{"supplierUnique":"d60054eac842485aa3fa31a4dcfc57f3","supplierName":"供应商甲","contactMobile":"131*********","statusDesc":"已完成","categoryCount":2,"purchaseAmount":121.5,"outstandingAmount":0,"goodsList":[{"goodsBarcode":"2221","goodsName":"Raspberry mini","goodsImageUrl":"https://www.kwokok7.cn/Food","goodsCount":6},{"goodsBarcode":"2222222222222221","goodsName":"Raspberry elite","goodsImageUrl":"https://auth.ikeda10.xyz/ClothingShoesandJewelry","goodsCount":9}]}]
     */

    private int createId;
    private String createTime;
    private String createBy;
    private double paymentMoney;
    private String remark;
    private List<String> voucherPicturePath;
    private List<GouXOrderListData> billList;

    public int getCreateId() {
        return createId;
    }

    public void setCreateId(int createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public double getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(double paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getVoucherPicturePath() {
        return voucherPicturePath;
    }

    public void setVoucherPicturePath(List<String> voucherPicturePath) {
        this.voucherPicturePath = voucherPicturePath;
    }

    public List<GouXOrderListData> getBillList() {
        return billList;
    }

    public void setBillList(List<GouXOrderListData> billList) {
        this.billList = billList;
    }

}
