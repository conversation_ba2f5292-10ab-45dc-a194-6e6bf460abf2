package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPlanData;

/**
 * Describe:补货单（适配器）
 * Created by jingang on 2023/9/6
 */
public class RestockAdapter extends BaseAdapter<RestockPlanData> {

    public RestockAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvStatus, tvTime, tvCount, tvDel, tvAgain, tvCancel, tvPreview;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvTime = holder.getView(R.id.tvItemTime);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);
        tvCount = holder.getView(R.id.tvItemCount);
        tvDel = holder.getView(R.id.tvItemDel);
        tvAgain = holder.getView(R.id.tvItemAgain);
        tvCancel = holder.getView(R.id.tvItemCancel);
        tvPreview = holder.getView(R.id.tvItemPreview);
        tvDel.setText(getLanguageValue("delete"));
        tvAgain.setText(getLanguageValue("replenishAgain"));
        tvCancel.setText(getLanguageValue("cancelReplenish"));
        tvPreview.setText(getLanguageValue("preview"));

        tvName.setText(mDataList.get(position).getShopRestockplanName());
        //补货计划状态:1待生成2已生成3已取消
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getLanguageValue("beGenerated"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                tvStatus.setBackgroundResource(R.drawable.shape_orange_22);
                tvDel.setVisibility(View.GONE);
                tvAgain.setVisibility(View.GONE);
                tvCancel.setVisibility(View.VISIBLE);
                tvPreview.setVisibility(View.VISIBLE);
                break;
            case 2:
                tvStatus.setText(getLanguageValue("generated"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                tvStatus.setBackgroundResource(R.drawable.shape_blue_22);
                tvDel.setVisibility(View.GONE);
                tvAgain.setVisibility(View.VISIBLE);
                tvCancel.setVisibility(View.GONE);
                tvPreview.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText(getLanguageValue("cancelled"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                tvStatus.setBackgroundResource(R.drawable.shape_f2_22);
                tvDel.setVisibility(View.VISIBLE);
                tvAgain.setVisibility(View.VISIBLE);
                tvCancel.setVisibility(View.GONE);
                tvPreview.setVisibility(View.GONE);
                break;
        }
        tvTime.setText(mDataList.get(position).getCreateTime());
        tvCount.setText(getLanguageValue("aTotalOf") + mDataList.get(position).getGoodsCount() + getLanguageValue("numberTypes"));
        if (mDataList.get(position).getGoodsList().size() > 0) {
            rvGoods.setVisibility(View.VISIBLE);
            rvGoods.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
            RestockGoodsAdapter adapter = new RestockGoodsAdapter(mContext);
            rvGoods.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getGoodsList());
            adapter.setOnItemClickListener((view, position1) -> {
                if (listener != null) {
                    listener.onItemClick(view, position);
                }
            });
        } else {
            rvGoods.setVisibility(View.GONE);
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            tvDel.setOnClickListener(v -> listener.onDelClick(v, position));
            tvAgain.setOnClickListener(v -> listener.onAgainClick(v, position));
            tvCancel.setOnClickListener(v -> listener.onCancelClick(v, position));
            tvPreview.setOnClickListener(v -> listener.onPreviewClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);

        void onAgainClick(View view, int position);

        void onCancelClick(View view, int position);

        void onPreviewClick(View view, int position);
    }
}
