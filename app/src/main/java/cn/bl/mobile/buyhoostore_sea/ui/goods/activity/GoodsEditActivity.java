package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.gengcon.www.jcprintersdk.callback.PrintCallback;
import com.google.gson.Gson;
import com.gprinter.command.LabelCommand;
import com.gprinter.utils.LogUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.tencent.bugly.proguard.T;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.function.Function;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.printer.PrinterSettingActivity;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrintUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.Printer;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.ThreadPoolManager;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.SupplierWebDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsWholesaleAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsWholesaleData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.location.LocationSelectActivity;
import cn.bl.mobile.buyhoostore_sea.utils.DecimalDigitsInputFilter;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCropEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CameraDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ChuRuSelectActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * @Author: Great Han
 * @Description: 接盘添加注释：商品编辑即商品详情页
 * @Date: 17:03 2019/6/13
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsEditActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;

    /*商品信息start*/
    @BindView(R.id.tvGoodsInfo)
    TextView tvGoodsInfo;
    @BindView(R.id.tvSupplierValue)
    TextView tvSupplierValue;
    @BindView(R.id.tvSupplier)
    TextView tvSupplier;//供货商
    @BindView(R.id.tvCateValue)
    TextView tvCateValue;
    @BindView(R.id.tvCate)
    TextView tvCate;//二级分类
    @BindView(R.id.linGoodsDate)
    LinearLayout linGoodsDate;
    @BindView(R.id.tvGoodsDateValue)
    TextView tvGoodsDateValue;
    @BindView(R.id.tvGoodsDate)
    TextView tvGoodsDate;
    @BindView(R.id.vGoodsDate)
    View vGoodsDate;//生产日期
    @BindView(R.id.tvGoodsLifeValue)
    TextView tvGoodsLifeValue;
    @BindView(R.id.etGoodsLife)
    EditText etGoodsLife;//保质期（天）
    @BindView(R.id.tvBrandValue)
    TextView tvBrandValue;
    @BindView(R.id.etBrand)
    EditText etBrand;//品牌
    //库存预警
    @BindView(R.id.tvWarningValue)
    TextView tvWarningValue;
    @BindView(R.id.ivWarning)
    ImageView ivWarning;
    @BindView(R.id.linWarning)
    LinearLayout linWarning;
    @BindView(R.id.tvWarningLowValue)
    TextView tvWarningLowValue;
    @BindView(R.id.etWarningLow)
    EditText etLow;
    @BindView(R.id.tvWarningTallValue)
    TextView tvWarningTallValue;
    @BindView(R.id.etWarningTall)
    EditText etTall;
    /*商品信息end*/

    /*基础包装start*/
    @BindView(R.id.tvSpecs0)
    TextView tvSpecs0;
    @BindView(R.id.tvClear0)
    TextView tvClear0;
    @BindView(R.id.linSpecs0)
    LinearLayout linSpecs0;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;//图片
    @BindView(R.id.tvBarcode0Value)
    TextView tvBarcode0Value;
    @BindView(R.id.ivBarcode0)
    ImageView ivBarcode0;
    @BindView(R.id.etBarcode0)
    EditText etBarcode0;
    @BindView(R.id.ivScan0)
    ImageView ivScan0;//商品条码
    @BindView(R.id.tvName0Value)
    TextView tvName0Value;
    @BindView(R.id.etName0)
    EditText etName0;//商品名称
    @BindView(R.id.tvChengTypeValue)
    TextView tvChengTypeValue;
    @BindView(R.id.tvChengType0)
    TextView tvChengType0;//计价类型：计件
    @BindView(R.id.tvChengType1)
    TextView tvChengType1;//计价类型：称重
    @BindView(R.id.tvUnit0Value)
    TextView tvUnit0Value;
    @BindView(R.id.tvUnit0)
    TextView tvUnit0;//商品单位
    @BindView(R.id.tvStock0Value)
    TextView tvStock0Value;
    @BindView(R.id.etStock0)
    EditText etStock0;//商品库存
    @BindView(R.id.tvIn0)
    TextView tvIn0;//入库
    @BindView(R.id.tvOut0)
    TextView tvOut0;//出库
    @BindView(R.id.linInPrice0)
    LinearLayout linInPrice0;
    @BindView(R.id.tvInPrice0Value)
    TextView tvInPrice0Value;
    @BindView(R.id.etInPrice0)
    EditText etInPrice0;//入库单价
    @BindView(R.id.linStockPrice0)
    LinearLayout linStockPrice0;
    @BindView(R.id.tvStockPrice0Value)
    TextView tvStockPrice0Value;
    @BindView(R.id.tvStockPrice0)
    TextView tvStockPrice0;//最近入库价
    @BindView(R.id.tvSalePrice0Value)
    TextView tvSalePrice0Value;
    @BindView(R.id.etSalePrice0)
    EditText etSalePrice0;//零售单价
    @BindView(R.id.tvMemberPrice0Value)
    TextView tvMemberPrice0Value;
    @BindView(R.id.etMemberPrice0)
    EditText etMemberPrice0;//会员单价
    @BindView(R.id.tvOnlinePrice0Value)
    TextView tvOnlinePrice0Value;
    @BindView(R.id.etOnlinePrice0)
    EditText etOnlinePrice0;//网购单价
    @BindView(R.id.tvUnderlinePrice0Value)
    TextView tvUnderlinePrice0Value;
    @BindView(R.id.etUnderlinePrice0)
    EditText etUnderlinePrice0;//划线单价
    @BindView(R.id.tvStartOrder0Value)
    TextView tvStartOrder0Value;
    @BindView(R.id.etStartOrder0)
    EditText etStartOrder0;//线上起订
    @BindView(R.id.tvWholesaleValue0)
    TextView tvWholesaleValue0;
    @BindView(R.id.ivWholesale0)
    ImageView ivWholesale0;
    //    @BindView(R.id.linWholesale0)
//    LinearLayout linWholesale0;
//    @BindView(R.id.tvWholesaleCount0Value)
//    TextView tvWholesaleCount0Value;
//    @BindView(R.id.etWholesaleCount0)
//    EditText etWholesaleCount0;//起批数量
//    @BindView(R.id.tvWholesalePrice0Value)
//    TextView tvWholesalePrice0Value;
//    @BindView(R.id.etWholesalePrice0)
//    EditText etWholesalePrice0;//批发单价
    @BindView(R.id.vWholesale0)
    View vWholesale0;
    @BindView(R.id.linWholesale0)
    LinearLayout linWholesale0;
    @BindView(R.id.rvWholesale0)
    RecyclerView rvWholesale0;
    @BindView(R.id.tvWholesaleAdd0)
    TextView tvWholesaleAdd0;

    @BindView(R.id.tvLocation0Value)
    TextView tvLocation0Value;
    @BindView(R.id.tvLocation0)
    TextView tvLocation0;//商品货位
    @BindView(R.id.tvSpecs0Value)
    TextView tvSpecs0Value;
    @BindView(R.id.etSpecs0)
    EditText etSpecs0;//商品规格
    @BindView(R.id.tvPrinter0Value)
    TextView tvPrinter0Value;
    @BindView(R.id.ivPrinterUp0)
    ImageView ivPrinterUp0;//收银机上架
    @BindView(R.id.tvPrinterUp0Value)
    TextView tvPrinterUp0Value;
    @BindView(R.id.ivPrinterDown0)
    ImageView ivPrinterDown0;//收银机下架
    @BindView(R.id.tvPrinterDown0Value)
    TextView tvPrinterDown0Value;
    @BindView(R.id.tvApplet0Value)
    TextView tvApplet0Value;
    @BindView(R.id.ivAppletUp0)
    ImageView ivAppletUp0;//小程序上架
    @BindView(R.id.tvAppletUp0Value)
    TextView tvAppletUp0Value;
    @BindView(R.id.ivAppletDown0)
    ImageView ivAppletDown0;//小程序下架
    @BindView(R.id.tvAppletDown0Value)
    TextView tvAppletDown0Value;
    @BindView(R.id.tvOpen0)
    TextView tvOpen0;
    @BindView(R.id.ivOpen0)
    ImageView ivOpen0;
    /*基础包装end*/

    /*中间包装start*/
    @BindView(R.id.linSpecs1)
    LinearLayout linSpecs1;
    @BindView(R.id.tvSpecs1)
    TextView tvSpecs1;
    @BindView(R.id.tvClear1)
    TextView tvClear1;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.tvBarcode1Value)
    TextView tvBarcode1Value;
    @BindView(R.id.ivBarcode1)
    ImageView ivBarcode1;
    @BindView(R.id.etBarcode1)
    EditText etBarcode1;
    @BindView(R.id.ivScan1)
    ImageView ivScan1;
    @BindView(R.id.tvName1Value)
    TextView tvName1Value;
    @BindView(R.id.etName1)
    EditText etName1;
    @BindView(R.id.tvUnit1Value)
    TextView tvUnit1Value;
    @BindView(R.id.tvUnit1)
    TextView tvUnit1;
    @BindView(R.id.tvCount1Value)
    TextView tvCount1Value;
    @BindView(R.id.etCount1)
    EditText etCount1;//单位换算=多少基础包装
    @BindView(R.id.linStock1)
    LinearLayout linStock1;
    @BindView(R.id.tvStock1Value)
    TextView tvStock1Value;
    @BindView(R.id.tvStock1)
    TextView tvStock1;//商品库存
    @BindView(R.id.tvIn1)
    TextView tvIn1;
    @BindView(R.id.tvOut1)
    TextView tvOut1;
    @BindView(R.id.tvStockTips1Value)
    TextView tvStockTips1Value;
    @BindView(R.id.linInPrice1)
    LinearLayout linInPrice1;
    @BindView(R.id.tvInPrice1Value)
    TextView tvInPrice1Value;
    @BindView(R.id.etInPrice1)
    EditText etInPrice1;
    @BindView(R.id.linStockPrice1)
    LinearLayout linStockPrice1;
    @BindView(R.id.tvStockPrice1Value)
    TextView tvStockPrice1Value;
    @BindView(R.id.tvStockPrice1)
    TextView tvStockPrice1;
    @BindView(R.id.tvSalePrice1Value)
    TextView tvSalePrice1Value;
    @BindView(R.id.etSalePrice1)
    EditText etSalePrice1;
    @BindView(R.id.tvMemberPrice1Value)
    TextView tvMemberPrice1Value;
    @BindView(R.id.etMemberPrice1)
    EditText etMemberPrice1;
    @BindView(R.id.tvOnlinePrice1Value)
    TextView tvOnlinePrice1Value;
    @BindView(R.id.etOnlinePrice1)
    EditText etOnlinePrice1;
    @BindView(R.id.tvUnderlinePrice1Value)
    TextView tvUnderlinePrice1Value;
    @BindView(R.id.etUnderlinePrice1)
    EditText etUnderlinePrice1;//划线单价
    @BindView(R.id.tvStartOrder1Value)
    TextView tvStartOrder1Value;
    @BindView(R.id.etStartOrder1)
    EditText etStartOrder1;
    @BindView(R.id.tvWholesaleValue1)
    TextView tvWholesaleValue1;
    @BindView(R.id.ivWholesale1)
    ImageView ivWholesale1;
    //    @BindView(R.id.linWholesale1)
//    LinearLayout linWholesale1;
//    @BindView(R.id.tvWholesaleCount1Value)
//    TextView tvWholesaleCount1Value;
//    @BindView(R.id.etWholesaleCount1)
//    EditText etWholesaleCount1;//起批数量
//    @BindView(R.id.tvWholesalePrice1Value)
//    TextView tvWholesalePrice1Value;
//    @BindView(R.id.etWholesalePrice1)
//    EditText etWholesalePrice1;//批发单价
    @BindView(R.id.vWholesale1)
    View vWholesale1;
    @BindView(R.id.linWholesale1)
    LinearLayout linWholesale1;
    @BindView(R.id.rvWholesale1)
    RecyclerView rvWholesale1;
    @BindView(R.id.tvWholesaleAdd1)
    TextView tvWholesaleAdd1;
    @BindView(R.id.tvLocation1Value)
    TextView tvLocation1Value;
    @BindView(R.id.tvLocation1)
    TextView tvLocation1;//商品货位
    @BindView(R.id.tvSpecs1Value)
    TextView tvSpecs1Value;
    @BindView(R.id.etSpecs1)
    EditText etSpecs1;
    @BindView(R.id.tvPrinter1Value)
    TextView tvPrinter1Value;
    @BindView(R.id.ivPrinterUp1)
    ImageView ivPrinterUp1;
    @BindView(R.id.tvPrinterUp1Value)
    TextView tvPrinterUp1Value;
    @BindView(R.id.ivPrinterDown1)
    ImageView ivPrinterDown1;
    @BindView(R.id.tvPrinterDown1Value)
    TextView tvPrinterDown1Value;
    @BindView(R.id.tvApplet1Value)
    TextView tvApplet1Value;
    @BindView(R.id.ivAppletUp1)
    ImageView ivAppletUp1;
    @BindView(R.id.tvAppletUp1Value)
    TextView tvAppletUp1Value;
    @BindView(R.id.ivAppletDown1)
    ImageView ivAppletDown1;
    @BindView(R.id.tvAppletDown1Value)
    TextView tvAppletDown1Value;
    @BindView(R.id.tvOpen1)
    TextView tvOpen1;
    @BindView(R.id.ivOpen1)
    ImageView ivOpen1;
    /*中间包装end*/

    /*最大包装start*/
    @BindView(R.id.tvSpecs2)
    TextView tvSpecs2;
    @BindView(R.id.tvClear2)
    TextView tvClear2;
    @BindView(R.id.linSpecs2)
    LinearLayout linSpecs2;
    @BindView(R.id.ivImg2)
    ImageView ivImg2;
    @BindView(R.id.tvBarcode2Value)
    TextView tvBarcode2Value;
    @BindView(R.id.ivBarcode2)
    ImageView ivBarcode2;
    @BindView(R.id.etBarcode2)
    EditText etBarcode2;
    @BindView(R.id.ivScan2)
    ImageView ivScan2;
    @BindView(R.id.tvName2Value)
    TextView tvName2Value;
    @BindView(R.id.etName2)
    EditText etName2;
    @BindView(R.id.tvUnit2Value)
    TextView tvUnit2Value;
    @BindView(R.id.tvUnit2)
    TextView tvUnit2;
    @BindView(R.id.tvCount2Value)
    TextView tvCount2Value;
    @BindView(R.id.etCount2)
    EditText etCount2;
    @BindView(R.id.linStock2)
    LinearLayout linStock2;
    @BindView(R.id.tvStock2Value)
    TextView tvStock2Value;
    @BindView(R.id.tvStock2)
    TextView tvStock2;
    @BindView(R.id.tvIn2)
    TextView tvIn2;
    @BindView(R.id.tvOut2)
    TextView tvOut2;
    @BindView(R.id.tvStockTips2Value)
    TextView tvStockTips2Value;
    @BindView(R.id.linInPrice2)
    LinearLayout linInPrice2;
    @BindView(R.id.tvInPrice2Value)
    TextView tvInPrice2Value;
    @BindView(R.id.etInPrice2)
    EditText etInPrice2;
    @BindView(R.id.linStockPrice2)
    LinearLayout linStockPrice2;
    @BindView(R.id.tvStockPrice2Value)
    TextView tvStockPrice2Value;
    @BindView(R.id.tvStockPrice2)
    TextView tvStockPrice2;
    @BindView(R.id.tvSalePrice2Value)
    TextView tvSalePrice2Value;
    @BindView(R.id.etSalePrice2)
    EditText etSalePrice2;
    @BindView(R.id.tvMemberPrice2Value)
    TextView tvMemberPrice2Value;
    @BindView(R.id.etMemberPrice2)
    EditText etMemberPrice2;
    @BindView(R.id.tvOnlinePrice2Value)
    TextView tvOnlinePrice2Value;
    @BindView(R.id.etOnlinePrice2)
    EditText etOnlinePrice2;
    @BindView(R.id.tvUnderlinePrice2Value)
    TextView tvUnderlinePrice2Value;
    @BindView(R.id.etUnderlinePrice2)
    EditText etUnderlinePrice2;//划线单价
    @BindView(R.id.tvStartOrder2Value)
    TextView tvStartOrder2Value;
    @BindView(R.id.etStartOrder2)
    EditText etStartOrder2;
    @BindView(R.id.tvWholesaleValue2)
    TextView tvWholesaleValue2;
    @BindView(R.id.ivWholesale2)
    ImageView ivWholesale2;
    //    @BindView(R.id.linWholesale2)
//    LinearLayout linWholesale2;
//    @BindView(R.id.tvWholesaleCount2Value)
//    TextView tvWholesaleCount2Value;
//    @BindView(R.id.etWholesaleCount2)
//    EditText etWholesaleCount2;//起批数量
//    @BindView(R.id.tvWholesalePrice2Value)
//    TextView tvWholesalePrice2Value;
//    @BindView(R.id.etWholesalePrice2)
//    EditText etWholesalePrice2;//批发单价
    @BindView(R.id.vWholesale2)
    View vWholesale2;
    @BindView(R.id.linWholesale2)
    LinearLayout linWholesale2;
    @BindView(R.id.rvWholesale2)
    RecyclerView rvWholesale2;
    @BindView(R.id.tvWholesaleAdd2)
    TextView tvWholesaleAdd2;
    @BindView(R.id.tvLocation2Value)
    TextView tvLocation2Value;
    @BindView(R.id.tvLocation2)
    TextView tvLocation2;//商品货位
    @BindView(R.id.tvSpecs2Value)
    TextView tvSpecs2Value;
    @BindView(R.id.etSpecs2)
    EditText etSpecs2;
    @BindView(R.id.tvPrinter2Value)
    TextView tvPrinter2Value;
    @BindView(R.id.ivPrinterUp2)
    ImageView ivPrinterUp2;
    @BindView(R.id.tvPrinterUp2Value)
    TextView tvPrinterUp2Value;
    @BindView(R.id.ivPrinterDown2)
    ImageView ivPrinterDown2;
    @BindView(R.id.tvPrinterDown2Value)
    TextView tvPrinterDown2Value;
    @BindView(R.id.tvApplet2Value)
    TextView tvApplet2Value;
    @BindView(R.id.ivAppletUp2)
    ImageView ivAppletUp2;
    @BindView(R.id.tvAppletUp2Value)
    TextView tvAppletUp2Value;
    @BindView(R.id.ivAppletDown2)
    ImageView ivAppletDown2;
    @BindView(R.id.tvAppletDown2Value)
    TextView tvAppletDown2Value;
    @BindView(R.id.tvOpen2)
    TextView tvOpen2;
    @BindView(R.id.ivOpen2)
    ImageView ivOpen2;
    /*最大包装end*/

    @BindView(R.id.tvDel)
    TextView tvDel;
    @BindView(R.id.tvCancel)
    TextView tvCancel;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private int type,//0.新增 1.编辑
            chengType,//计价类型 0.计件 1.称重
            warningStatus,//库存预警状态 0.关闭 1.开启
            imgType,//选择图片 0.基础规格 1.中规格 2.最大规格
            scanType,//扫码：0.基础规格 1.中规格 2.最大规格
            unitType,//选择单位 0.基础规格 1.中规格 2.最大规格
            goodsLife,//保质期（天）
            locationType;//选择货位 0.基础规格 1.中规格 2.最大规格

    private double warningLow,//库存预警下限
            warningTall;//库存预警上限

    private String img0, img1, img2,//商品图片
            goodsBarcode, goodsBarcode0, goodsBarcode1, goodsBarcode2,//商品编号
            supplierUnique,//供货商编号
            cateUnique,
            cateUnique1,//二级分类编号
            cateUnique2,
            goodsDate;//生产日期

    private boolean isSpecs1, isSpecs2,//是否含有改规格
            isOpen0 = true,//基础包装是否展开
            isOpen1,//中间规格是否展开
            isOpen2;//最大规格是否展开

    private SharedPreferences sharedPreferences;
    private SharedPreferencesUtil sharedPreferencesUtil;
    //商品库存  供货商   分类  名称 进价  售价  删除
    private String powerCount, powerSupplier, powerKind, powerName, powerInprice, powerPrice, powerDelete;

    private int printerType;//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
    private boolean isError,//是否打印错误
            isCancel;//是否取消打印
    private ArrayList<String> jsonList = new ArrayList<>(),//图像数据
            infoList = new ArrayList<>();//图像处理书记

    //批发单价
    private List<GoodsWholesaleData> wholesaleList0 = new ArrayList<>(),
            wholesaleList1 = new ArrayList<>(),
            wholesaleList2 = new ArrayList<>();
    private GoodsWholesaleAdapter wholesaleAdapter0, wholesaleAdapter1, wholesaleAdapter2;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_edit;
    }

    @Override
    public void initViews() {
        tvRight.setText(getLanguageValue("print"));
        type = getIntent().getIntExtra("type", 0);
        sharedPreferences = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        goodsBarcode = getIntent().getStringExtra("goodsBarcode");
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        powerCount = sharedPreferences.getString("power_count", "0");
        powerSupplier = sharedPreferences.getString("power_supplier", "0");
        powerKind = sharedPreferences.getString("power_kind", "0");
        powerName = sharedPreferences.getString("power_name", "0");
        powerInprice = sharedPreferences.getString("power_inprice", "0");
        powerPrice = sharedPreferences.getString("power_price", "0");
        powerDelete = sharedPreferences.getString("power_delete", "0");
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);

        etLow.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etTall.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etStock0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etInPrice0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etSalePrice0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etMemberPrice0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etOnlinePrice0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etStartOrder0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesaleCount0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesalePrice0.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etInPrice1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etSalePrice1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etMemberPrice1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etOnlinePrice1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etStartOrder1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesaleCount1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesalePrice1.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etInPrice2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etSalePrice2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etMemberPrice2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etOnlinePrice2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etStartOrder2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesaleCount2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
//        etWholesalePrice2.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});

        //焦点失去判断会员单价、网购单价是否有数据，没有则赋值零售单价
        etSalePrice0.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                if (TextUtils.isEmpty(etMemberPrice0.getText().toString().trim())) {
                    etMemberPrice0.setText(etSalePrice0.getText().toString().trim());
                }
                if (TextUtils.isEmpty(etOnlinePrice0.getText().toString().trim())) {
                    etOnlinePrice0.setText(etSalePrice0.getText().toString().trim());
                }
            }
        });
        etSalePrice1.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                if (TextUtils.isEmpty(etMemberPrice1.getText().toString().trim())) {
                    etMemberPrice1.setText(etSalePrice1.getText().toString().trim());
                }
                if (TextUtils.isEmpty(etOnlinePrice1.getText().toString().trim())) {
                    etOnlinePrice1.setText(etSalePrice1.getText().toString().trim());
                }
            }
        });
        etSalePrice2.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                if (TextUtils.isEmpty(etMemberPrice2.getText().toString().trim())) {
                    etMemberPrice2.setText(etSalePrice2.getText().toString().trim());
                }
                if (TextUtils.isEmpty(etOnlinePrice2.getText().toString().trim())) {
                    etOnlinePrice2.setText(etSalePrice2.getText().toString().trim());
                }
            }
        });

        //根据库存、单位换算计算当前包装库存（中间、最大包装）
        etCount1.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    tvStock1.setText("0");
                    return;
                }
                double count = Double.parseDouble(s.toString().trim());
                if (count == 0) {
                    tvStock1.setText("0");
                    return;
                }
                tvStock1.setText(String.valueOf((int) (goodsStock / count)));
            }
        });
        etCount2.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    tvStock2.setText("0");
                    return;
                }
                double count = Double.parseDouble(s.toString().trim());
                if (count == 0) {
                    tvStock2.setText("0");
                    return;
                }
                tvStock2.setText(String.valueOf((int) (goodsStock / count)));
            }
        });

        //默认上架
        ivPrinterUp0.setSelected(true);
        ivAppletUp0.setSelected(true);
        ivPrinterUp1.setSelected(true);
        ivAppletUp1.setSelected(true);
        ivPrinterUp2.setSelected(true);
        ivAppletUp2.setSelected(true);

        if (type == 1) {
            tvTitle.setText(getLanguageValue("editor") + getLanguageValue("commodity"));
            tvDel.setVisibility(View.VISIBLE);
            tvCancel.setVisibility(View.GONE);
            tvRight.setVisibility(View.VISIBLE);
            linGoodsDate.setVisibility(View.GONE);
            vGoodsDate.setVisibility(View.GONE);
            //最小规格条码不可编辑
            ivBarcode0.setVisibility(View.GONE);
            etBarcode0.setFocusable(false);
            etBarcode0.setFocusableInTouchMode(false);
            etBarcode0.setTextColor(getResources().getColor(R.color.color_999));
            ivScan0.setVisibility(View.GONE);
            //库存不可编辑
            etStock0.setFocusable(false);
            etStock0.setFocusableInTouchMode(false);
            etStock0.setTextColor(getResources().getColor(R.color.color_999));
            tvIn0.setVisibility(View.VISIBLE);
            tvOut0.setVisibility(View.VISIBLE);
            //
            linInPrice0.setVisibility(View.GONE);
            linStockPrice0.setVisibility(View.VISIBLE);
        } else {
            tvTitle.setText(getLanguageValue("addTo") + getLanguageValue("commodity"));
            tvDel.setVisibility(View.GONE);
            tvCancel.setVisibility(View.VISIBLE);
            tvRight.setVisibility(View.GONE);
            linGoodsDate.setVisibility(View.VISIBLE);
            vGoodsDate.setVisibility(View.VISIBLE);
            //最小规格条码可编辑
            ivBarcode0.setVisibility(View.VISIBLE);
            etBarcode0.setFocusable(true);
            etBarcode0.setFocusableInTouchMode(true);
            etBarcode0.setTextColor(getResources().getColor(R.color.color_333));
            ivScan0.setVisibility(View.VISIBLE);
            //库存可编辑
            etStock0.setFocusable(true);
            etStock0.setFocusableInTouchMode(true);
            etStock0.setTextColor(getResources().getColor(R.color.color_333));
            tvIn0.setVisibility(View.GONE);
            tvOut0.setVisibility(View.GONE);
            //
            linInPrice0.setVisibility(View.VISIBLE);
            linStockPrice0.setVisibility(View.GONE);
        }
        setAdapter();
    }

    @Override
    public void initData() {
        if (type == 1) {
            getGoodsInfo();
        } else {
            getWholesaleData(0);
            getWholesaleData(1);
            getWholesaleData(2);
        }
    }

    @OnClick({R.id.ivBack, R.id.tvRight,
            R.id.tvSupplier, R.id.tvCate, R.id.tvGoodsDate, R.id.ivWarning,
            R.id.tvClear0, R.id.ivImg0, R.id.ivBarcode0, R.id.ivScan0, R.id.tvChengType0, R.id.tvChengType1, R.id.tvUnit0, R.id.tvIn0, R.id.tvOut0, R.id.ivWholesale0, R.id.tvWholesaleAdd0, R.id.tvLocation0,
            R.id.ivPrinterUp0, R.id.ivPrinterDown0, R.id.ivAppletUp0, R.id.ivAppletDown0, R.id.linOpen0,
            R.id.tvClear1, R.id.ivImg1, R.id.ivBarcode1, R.id.ivScan1, R.id.tvUnit1, R.id.tvIn1, R.id.tvOut1, R.id.ivWholesale1, R.id.tvWholesaleAdd1, R.id.tvLocation1,
            R.id.ivPrinterUp1, R.id.ivPrinterDown1, R.id.ivAppletUp1, R.id.ivAppletDown1, R.id.linOpen1,
            R.id.tvClear2, R.id.ivImg2, R.id.ivBarcode2, R.id.ivScan2, R.id.tvUnit2, R.id.tvIn2, R.id.tvOut2, R.id.ivWholesale2, R.id.tvWholesaleAdd2, R.id.tvLocation2,
            R.id.ivPrinterUp2, R.id.ivPrinterDown2, R.id.ivAppletUp2, R.id.ivAppletDown2, R.id.linOpen2,
            R.id.tvDel, R.id.tvCancel, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                //打印
                setPrint();
                break;
            /*商品信息start*/
            case R.id.tvSupplier:
                //选择供货商
                if ("1".equals(powerSupplier)) {
//                    SupplierDialog.showDialog(this, goodsBarcode, data -> {
//                        supplierUnique = data.getSupplierUnique();
//                        tvSupplier.setText(data.getSupplierName());
//                    });
                    SupplierWebDialog.showDialog(this, goodsBarcode, data -> {
                        supplierUnique = data.getSupplier_unique();
                        tvSupplier.setText(data.getSupplier_name());
                    });
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
                break;
            case R.id.tvCate:
                //选择分类
                CateDialog.showDialog(this, cateUnique, cateUnique1, cateUnique2, (cateId, cateName, cateId1, cateName1, cateId2, cateName2) -> {
                    cateUnique = cateId;
                    cateUnique1 = cateId1;
                    cateUnique2 = cateId2;
                    if (TextUtils.isEmpty(cateId2)) {
                        tvCate.setText(cateName + "-" + cateName1);
                    } else {
                        tvCate.setText(cateName + "-" + cateName1 + "-" + cateName2);
                    }
                });
                break;
            case R.id.tvGoodsDate:
                //生产日期
                DateDialog.showDialog(this, goodsDate, date -> {
                    goodsDate = date;
                    tvGoodsDate.setText(date);
                });
                break;
            case R.id.ivWarning:
                //库存预警
                if (warningStatus == 0) {
                    linWarning.setVisibility(View.VISIBLE);
                    warningStatus = 1;
                    ivWarning.setSelected(true);
                } else {
                    linWarning.setVisibility(View.GONE);
                    warningStatus = 0;
                    ivWarning.setSelected(false);
                }
                break;
            /*商品信息end*/
            /*基础包装start*/
            case R.id.tvClear0:
                //清空
                setUIClear(0);
                break;
            case R.id.ivImg0:
                //选择图片
                getImg(0);
                break;
            case R.id.ivBarcode0:
                //生成条码（接口）
                getGoodsBarcode(0);
                break;
            case R.id.ivScan0:
                //扫码
                gotoScan(0);
                break;
            case R.id.tvChengType0:
                //计价类型 ：计件
                if (chengType != 0) {
                    chengType = 0;
                    tvChengType0.setBackgroundResource(R.drawable.shape_e5efff_22);
                    tvChengType0.setTextColor(getResources().getColor(R.color.blue));
                    tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                    tvChengType1.setBackgroundResource(R.drawable.shape_f7_22);
                    tvChengType1.setTextColor(getResources().getColor(R.color.color_666));
                    tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                }
                break;
            case R.id.tvChengType1:
                //计价类型：称重
                if (chengType != 1) {
                    chengType = 1;
                    tvChengType1.setBackgroundResource(R.drawable.shape_e5efff_22);
                    tvChengType1.setTextColor(getResources().getColor(R.color.blue));
                    tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                    tvChengType0.setBackgroundResource(R.drawable.shape_f7_22);
                    tvChengType0.setTextColor(getResources().getColor(R.color.color_666));
                    tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                }
                break;
            case R.id.tvUnit0:
                //单位选择
                gotoUnit(0);
                break;
            case R.id.tvIn0:
                //入库
                goToChuRu(goodsBarcode0, 1);
                break;
            case R.id.tvOut0:
                //出库
                goToChuRu(goodsBarcode0, 2);
                break;
            case R.id.ivWholesale0:
                //是否设置批发价
                if (wholesalePriceStatus0 == 0) {
                    wholesalePriceStatus0 = 1;
                    ivWholesale0.setSelected(true);
                    vWholesale0.setVisibility(View.VISIBLE);
                    linWholesale0.setVisibility(View.VISIBLE);
                } else {
                    wholesalePriceStatus0 = 0;
                    ivWholesale0.setSelected(false);
                    vWholesale0.setVisibility(View.GONE);
                    linWholesale0.setVisibility(View.GONE);
                }
                break;
            case R.id.tvWholesaleAdd0:
                //新增计价规则
                boolean isNull0 = false;
                String name0 = "";
                for (int i = 0; i < wholesaleList0.size(); i++) {
                    if (wholesaleList0.get(i).getWholesaleCount() == 0 || wholesaleList0.get(i).getWholesalePrice() == 0) {
                        isNull0 = false;
                        name0 = getLanguageValue("pleasePerfect") + getLanguageValue("pricingRules") + (i + 1);
                    } else {
                        isNull0 = true;
                    }
                }
                if (!isNull0) {
                    showMessage(name0);
                    return;
                }
                getWholesaleData(0);
                break;
            case R.id.tvLocation0:
                //商品货位
                locationType = 0;
                goToLocation(goodsPosition0, completePositionName0);
                break;
            case R.id.ivPrinterUp0:
                //收银机上架
                if (printerStatus0 != 1) {
                    printerStatus0 = 1;
                    ivPrinterUp0.setSelected(true);
                    ivPrinterDown0.setSelected(false);
                }
                break;
            case R.id.ivPrinterDown0:
                //收银机下架
                if (printerStatus0 != 2) {
                    printerStatus0 = 2;
                    ivPrinterDown0.setSelected(true);
                    ivPrinterUp0.setSelected(false);
                }
                break;
            case R.id.ivAppletUp0:
                //小程序上架
                if (appletStatus0 != 1) {
                    appletStatus0 = 1;
                    ivAppletUp0.setSelected(true);
                    ivAppletDown0.setSelected(false);
                }
                break;
            case R.id.ivAppletDown0:
                //小程序下架
                if (appletStatus0 != 2) {
                    appletStatus0 = 2;
                    ivAppletDown0.setSelected(true);
                    ivAppletUp0.setSelected(false);
                }
                break;
            case R.id.linOpen0:
                //展开/收起
                isOpen0 = !isOpen0;
                if (isOpen0) {
                    linSpecs0.setVisibility(View.VISIBLE);
                    tvOpen0.setText(getLanguageValue("putItAway"));
                    ivOpen0.setImageResource(R.mipmap.ic_arrow013);
                } else {
                    linSpecs0.setVisibility(View.GONE);
                    tvOpen0.setText(getLanguageValue("expand"));
                    ivOpen0.setImageResource(R.mipmap.ic_arrow014);
                }
                break;
            /*基础包装end*/

            /*中间包装start*/
            case R.id.tvClear1:
                //清空
                setUIClear(1);
                break;
            case R.id.ivImg1:
                //选择图片
                getImg(1);
                break;
            case R.id.ivBarcode1:
                //生成条码（接口）
                getGoodsBarcode(1);
                break;
            case R.id.ivScan1:
                //扫码
                gotoScan(1);
                break;
            case R.id.tvUnit1:
                //选择单位
                gotoUnit(1);
                break;
            case R.id.tvIn1:
                //入库
                goToChuRu(goodsBarcode1, 1);
                break;
            case R.id.tvOut1:
                //出库
                goToChuRu(goodsBarcode1, 2);
                break;
            case R.id.ivWholesale1:
                //是否设置批发价
                if (wholesalePriceStatus1 == 0) {
                    wholesalePriceStatus1 = 1;
                    ivWholesale1.setSelected(true);
                    vWholesale1.setVisibility(View.VISIBLE);
                    linWholesale1.setVisibility(View.VISIBLE);
                } else {
                    wholesalePriceStatus1 = 0;
                    ivWholesale1.setSelected(false);
                    vWholesale1.setVisibility(View.GONE);
                    linWholesale1.setVisibility(View.GONE);
                }
                break;
            case R.id.tvWholesaleAdd1:
                //新增计价规则
                boolean isNull1 = false;
                String name1 = "";
                for (int i = 0; i < wholesaleList1.size(); i++) {
                    if (wholesaleList1.get(i).getWholesaleCount() == 0 || wholesaleList1.get(i).getWholesalePrice() == 0) {
                        isNull1 = false;
                        name1 = getLanguageValue("pleasePerfect") + getLanguageValue("pricingRules") + (i + 1);
                    } else {
                        isNull1 = true;
                    }
                }
                if (!isNull1) {
                    showMessage(name1);
                    return;
                }
                getWholesaleData(1);
                break;
            case R.id.tvLocation1:
                //商品货位
                locationType = 1;
                goToLocation(goodsPosition1, completePositionName1);
                break;
            case R.id.ivPrinterUp1:
                //收银机上架（中规格）
                if (printerStatus1 != 1) {
                    printerStatus1 = 1;
                    ivPrinterUp1.setSelected(true);
                    ivPrinterDown1.setSelected(false);
                }
                break;
            case R.id.ivPrinterDown1:
                //收银机下架（中规格）
                if (printerStatus1 != 2) {
                    printerStatus1 = 2;
                    ivPrinterDown1.setSelected(true);
                    ivPrinterUp1.setSelected(false);
                }
                break;
            case R.id.ivAppletUp1:
                //小程序上架（中规格）
                if (appletStatus1 != 1) {
                    appletStatus1 = 1;
                    ivAppletUp1.setSelected(true);
                    ivAppletDown1.setSelected(false);
                }
                break;
            case R.id.ivAppletDown1:
                //小程序下架（中规格）
                if (appletStatus1 != 2) {
                    appletStatus1 = 2;
                    ivAppletDown1.setSelected(true);
                    ivAppletUp1.setSelected(false);
                }
                break;
            case R.id.linOpen1:
                //中间包装展开/收起
                isOpen1 = !isOpen1;
                if (isOpen1) {
                    linSpecs1.setVisibility(View.VISIBLE);
                    tvOpen1.setText(getLanguageValue("putItAway"));
                    ivOpen1.setImageResource(R.mipmap.ic_arrow013);
                } else {
                    linSpecs1.setVisibility(View.GONE);
                    tvOpen1.setText(getLanguageValue("expand"));
                    ivOpen1.setImageResource(R.mipmap.ic_arrow014);
                }
                break;
            /*中间包装end*/

            /*最大包装start*/
            case R.id.tvClear2:
                //清空
                setUIClear(2);
                break;
            case R.id.ivImg2:
                //选择照片
                getImg(2);
                break;
            case R.id.ivBarcode2:
                //生成条码（接口）
                getGoodsBarcode(2);
                break;
            case R.id.ivScan2:
                //扫码
                gotoScan(2);
                break;
            case R.id.tvUnit2:
                //选择单位
                gotoUnit(2);
                break;
            case R.id.tvIn2:
                //入库
                goToChuRu(goodsBarcode2, 1);
                break;
            case R.id.tvOut2:
                //出库
                goToChuRu(goodsBarcode2, 2);
                break;
            case R.id.ivWholesale2:
                //是否设置批发价
                if (wholesalePriceStatus2 == 0) {
                    wholesalePriceStatus2 = 1;
                    ivWholesale2.setSelected(true);
                    vWholesale2.setVisibility(View.VISIBLE);
                    linWholesale2.setVisibility(View.VISIBLE);
                } else {
                    wholesalePriceStatus2 = 0;
                    ivWholesale2.setSelected(false);
                    vWholesale2.setVisibility(View.GONE);
                    linWholesale2.setVisibility(View.GONE);
                }
                break;
            case R.id.tvWholesaleAdd2:
                //新增计价规则
                boolean isNull2 = false;
                String name2 = "";
                for (int i = 0; i < wholesaleList2.size(); i++) {
                    if (wholesaleList2.get(i).getWholesaleCount() == 0 || wholesaleList2.get(i).getWholesalePrice() == 0) {
                        isNull2 = false;
                        name2 = getLanguageValue("pleasePerfect") + getLanguageValue("pricingRules") + (i + 1);
                    } else {
                        isNull2 = true;
                    }
                }
                if (!isNull2) {
                    showMessage(name2);
                    return;
                }
                getWholesaleData(2);
                break;
            case R.id.tvLocation2:
                //商品货位
                locationType = 2;
                goToLocation(goodsPosition2, completePositionName2);
                break;
            case R.id.ivPrinterUp2:
                //收银机上架
                if (printerStatus2 != 1) {
                    printerStatus2 = 1;
                    ivPrinterUp2.setSelected(true);
                    ivPrinterDown2.setSelected(false);
                }
                break;
            case R.id.ivPrinterDown2:
                //收银机下架
                if (printerStatus2 != 2) {
                    printerStatus2 = 2;
                    ivPrinterDown2.setSelected(true);
                    ivPrinterUp2.setSelected(false);
                }
                break;
            case R.id.ivAppletUp2:
                //小程序上架
                if (appletStatus2 != 1) {
                    appletStatus2 = 1;
                    ivAppletUp2.setSelected(true);
                    ivAppletDown2.setSelected(false);
                }
                break;
            case R.id.ivAppletDown2:
                //小程序下架（
                if (appletStatus2 != 2) {
                    appletStatus2 = 2;
                    ivAppletDown2.setSelected(true);
                    ivAppletUp2.setSelected(false);
                }
                break;
            case R.id.linOpen2:
                //最大规格展开/收起
                isOpen2 = !isOpen2;
                if (isOpen2) {
                    linSpecs2.setVisibility(View.VISIBLE);
                    tvOpen2.setText(getLanguageValue("putItAway"));
                    ivOpen2.setImageResource(R.mipmap.ic_arrow013);
                } else {
                    linSpecs2.setVisibility(View.GONE);
                    tvOpen2.setText(getLanguageValue("expand"));
                    ivOpen2.setImageResource(R.mipmap.ic_arrow014);
                }
                break;
            /*最大包装end*/

            case R.id.tvDel:
                //删除
                if ("1".equals(powerDelete)) {
                    IAlertDialog.showDialog(this,
                            getLanguageValue("deleteProduct") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postGoodsDel();
                            });
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
                break;
            case R.id.tvCancel:
                //保存并继续
                if (isQuicklyClick()) {
                    return;
                }
                postGoodsEdit(0);
                break;
            case R.id.tvConfirm:
                //保存
                if (isQuicklyClick()) {
                    return;
                }
                postGoodsEdit(1);
                break;
        }
    }

    @Override
    public void setText() {
        /*商品信息*/
        tvGoodsInfo.setText(getLanguageValue("commodity") + getLanguageValue("information"));
        tvSupplierValue.setText(getLanguageValue("supplier"));
        tvSupplier.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("supplier"));
        tvCateValue.setText(getLanguageValue("commodity") + getLanguageValue("classification"));
        tvCate.setHint(getLanguageValue("please") + getLanguageValue("choose") + getLanguageValue("commodity") + getLanguageValue("classification"));
        tvGoodsDateValue.setText(getLanguageValue("dateOfManufacture"));
        tvGoodsDate.setHint(getLanguageValue("please") + getLanguageValue("choose") + getLanguageValue("dateOfManufacture"));
        tvGoodsLifeValue.setText(getLanguageValue("shelfLife") + "(" + getLanguageValue("god") + ")");
        etGoodsLife.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shelfLife"));
        tvBrandValue.setText(getLanguageValue("commodity") + getLanguageValue("brand"));
        etBrand.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("brand"));
        tvWarningValue.setText(getLanguageValue("inventory") + getLanguageValue("warn"));
        tvWarningLowValue.setText(getLanguageValue("alertBelow"));
        etLow.setHint(getLanguageValue("inventoryLow"));
        tvWarningTallValue.setText(getLanguageValue("alertRaise"));
        etTall.setHint(getLanguageValue("inventoryMax"));

        /*基础包装*/
        tvSpecs0.setText(getLanguageValue("basics") + getLanguageValue("package"));
        tvClear0.setText(getLanguageValue("clear"));
        tvBarcode0Value.setText(getLanguageValue("commodity") + getLanguageValue("barcode"));
        etBarcode0.setHint(getLanguageValue("please") + getLanguageValue("input") + "/" + getLanguageValue("scan") + getLanguageValue("commodity") + getLanguageValue("barcode"));
        tvName0Value.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        etName0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("name"));
        tvChengTypeValue.setText(getLanguageValue("pricing") + getLanguageValue("type"));
        tvChengType0.setText(getLanguageValue("piecework"));
        tvChengType1.setText(getLanguageValue("weigh"));
        tvUnit0Value.setText(getLanguageValue("commodity") + getLanguageValue("unit"));
        tvUnit0.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("unit"));
        tvStock0Value.setText(getLanguageValue("commodity") + getLanguageValue("inventory"));
        etStock0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("inventory"));
        tvIn0.setText(getLanguageValue("warehousing"));
        tvOut0.setText(getLanguageValue("outOfTheWarehouse"));
        tvInPrice0Value.setText(getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        etInPrice0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        tvStockPrice0Value.setText(getLanguageValue("latestReceiptPrice"));
        tvSalePrice0Value.setText(getLanguageValue("retailPrice"));
        etSalePrice0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("retailPrice"));
        tvMemberPrice0Value.setText(getLanguageValue("member") + getLanguageValue("unitPrice"));
        etMemberPrice0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("member") + getLanguageValue("unitPrice"));
        tvOnlinePrice0Value.setText(getLanguageValue("online") + getLanguageValue("unitPrice"));
        etOnlinePrice0.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("online") + getLanguageValue("unitPrice"));
        tvStartOrder0Value.setText(getLanguageValue("online") + getLanguageValue("upToOrder"));
        etStartOrder0.setHint(getLanguageValue("enterMinOrder"));
        tvWholesaleValue0.setText(getLanguageValue("isSetWholesalePrices"));
//        tvWholesaleCount0Value.setText(getLanguageValue("startingQuantity"));
//        etWholesaleCount0.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity"));
//        tvWholesalePrice0Value.setText(getLanguageValue("wholesaleUnitPrice"));
//        etWholesalePrice0.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice"));
        tvWholesaleAdd0.setText(getLanguageValue("add") + getLanguageValue("pricingRules"));
        tvUnderlinePrice0Value.setText(getLanguageValue("markingPrice"));
        etUnderlinePrice0.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("markingPrice"));
        tvLocation0Value.setText(getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvLocation0.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvSpecs0Value.setText(getLanguageValue("commodity") + getLanguageValue("spec"));
        etSpecs0.setHint(getLanguageValue("capacityWeightDraw"));
        tvPrinter0Value.setText(getLanguageValue("cashRegister") + getLanguageValue("onOffShelf"));
        tvPrinterUp0Value.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown0Value.setText(getLanguageValue("offTheShelf"));
        tvApplet0Value.setText(getLanguageValue("miniProgram") + getLanguageValue("onOffShelf"));
        tvAppletUp0Value.setText(getLanguageValue("onTheShelf"));
        tvAppletDown0Value.setText(getLanguageValue("offTheShelf"));
        tvOpen0.setText(getLanguageValue("putItAway"));

        /*中间包装*/
        tvSpecs1.setText(getLanguageValue("intermediatePackaging"));
        tvClear1.setText(getLanguageValue("clear"));
        tvBarcode1Value.setText(getLanguageValue("commodity") + getLanguageValue("barcode"));
        etBarcode1.setHint(getLanguageValue("please") + getLanguageValue("input") + "/" + getLanguageValue("scan") + getLanguageValue("commodity") + getLanguageValue("barcode"));
        tvName1Value.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        etName1.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("name"));
        tvUnit1Value.setText(getLanguageValue("commodity") + getLanguageValue("unit"));
        tvUnit1.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("unit"));
        tvCount1Value.setText(getLanguageValue("unitConversion"));
        etCount1.setHint(getLanguageValue("equalBasic"));
        tvStock1Value.setText(getLanguageValue("commodity") + getLanguageValue("inventory"));
        tvIn1.setText(getLanguageValue("warehousing"));
        tvOut1.setText(getLanguageValue("outOfTheWarehouse"));
        tvStockTips1Value.setText(getLanguageValue("roundBasicInventory"));
        tvInPrice1Value.setText(getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        etInPrice1.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        tvStockPrice1Value.setText(getLanguageValue("latestReceiptPrice"));
        tvSalePrice1Value.setText(getLanguageValue("retailPrice"));
        etSalePrice1.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("retailPrice"));
        tvMemberPrice1Value.setText(getLanguageValue("member") + getLanguageValue("unitPrice"));
        etMemberPrice1.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("member") + getLanguageValue("unitPrice"));
        tvOnlinePrice1Value.setText(getLanguageValue("online") + getLanguageValue("unitPrice"));
        etOnlinePrice1.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("online") + getLanguageValue("unitPrice"));
        tvStartOrder1Value.setText(getLanguageValue("online") + getLanguageValue("upToOrder"));
        etStartOrder1.setHint(getLanguageValue("enterMinOrder"));
        tvWholesaleValue1.setText(getLanguageValue("isSetWholesalePrices"));
//        tvWholesaleCount1Value.setText(getLanguageValue("startingQuantity"));
//        etWholesaleCount1.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity"));
//        tvWholesalePrice1Value.setText(getLanguageValue("wholesaleUnitPrice"));
//        etWholesalePrice1.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice"));
        tvWholesaleAdd1.setText(getLanguageValue("add") + getLanguageValue("pricingRules"));
        tvUnderlinePrice1Value.setText(getLanguageValue("markingPrice"));
        etUnderlinePrice1.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("markingPrice"));
        tvLocation1Value.setText(getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvLocation1.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvSpecs1Value.setText(getLanguageValue("commodity") + getLanguageValue("spec"));
        etSpecs1.setHint(getLanguageValue("capacityWeightDraw"));
        tvPrinter1Value.setText(getLanguageValue("cashRegister") + getLanguageValue("onOffShelf"));
        tvPrinterUp1Value.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown1Value.setText(getLanguageValue("offTheShelf"));
        tvApplet1Value.setText(getLanguageValue("miniProgram") + getLanguageValue("onOffShelf"));
        tvAppletUp1Value.setText(getLanguageValue("onTheShelf"));
        tvAppletDown1Value.setText(getLanguageValue("offTheShelf"));
        tvOpen1.setText(getLanguageValue("expand"));

        /*最大包装*/
        tvSpecs2.setText(getLanguageValue("max") + getLanguageValue("package"));
        tvClear2.setText(getLanguageValue("clear"));
        tvBarcode2Value.setText(getLanguageValue("commodity") + getLanguageValue("barcode"));
        etBarcode2.setHint(getLanguageValue("please") + getLanguageValue("input") + "/" + getLanguageValue("scan") + getLanguageValue("commodity") + getLanguageValue("barcode"));
        tvName2Value.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        etName2.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("name"));
        tvUnit2Value.setText(getLanguageValue("commodity") + getLanguageValue("unit"));
        tvUnit2.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("unit"));
        tvCount2Value.setText(getLanguageValue("unitConversion"));
        etCount2.setHint(getLanguageValue("equalBasic"));
        tvStock2Value.setText(getLanguageValue("commodity") + getLanguageValue("inventory"));
        tvIn2.setText(getLanguageValue("warehousing"));
        tvOut2.setText(getLanguageValue("outOfTheWarehouse"));
        tvStockTips2Value.setText(getLanguageValue("roundBasicInventory"));
        tvInPrice2Value.setText(getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        etInPrice2.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        tvStockPrice2Value.setText(getLanguageValue("latestReceiptPrice"));
        tvSalePrice2Value.setText(getLanguageValue("retailPrice"));
        etSalePrice2.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("retailPrice"));
        tvMemberPrice2Value.setText(getLanguageValue("member") + getLanguageValue("unitPrice"));
        etMemberPrice2.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("member") + getLanguageValue("unitPrice"));
        tvOnlinePrice2Value.setText(getLanguageValue("online") + getLanguageValue("unitPrice"));
        etOnlinePrice2.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("online") + getLanguageValue("unitPrice"));
        tvStartOrder2Value.setText(getLanguageValue("online") + getLanguageValue("upToOrder"));
        etStartOrder2.setHint(getLanguageValue("enterMinOrder"));
        tvWholesaleValue2.setText(getLanguageValue("isSetWholesalePrices"));
//        tvWholesaleCount2Value.setText(getLanguageValue("startingQuantity"));
//        etWholesaleCount2.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity"));
//        tvWholesalePrice2Value.setText(getLanguageValue("wholesaleUnitPrice"));
//        etWholesalePrice2.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice"));
        tvWholesaleAdd2.setText(getLanguageValue("add") + getLanguageValue("pricingRules"));
        tvUnderlinePrice2Value.setText(getLanguageValue("markingPrice"));
        etUnderlinePrice2.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("markingPrice"));
        tvLocation2Value.setText(getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvLocation2.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("cargoSpace"));
        tvSpecs2Value.setText(getLanguageValue("commodity") + getLanguageValue("spec"));
        etSpecs2.setHint(getLanguageValue("capacityWeightDraw"));
        tvPrinter2Value.setText(getLanguageValue("cashRegister") + getLanguageValue("onOffShelf"));
        tvPrinterUp2Value.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown2Value.setText(getLanguageValue("offTheShelf"));
        tvApplet2Value.setText(getLanguageValue("miniProgram") + getLanguageValue("onOffShelf"));
        tvAppletUp2Value.setText(getLanguageValue("onTheShelf"));
        tvAppletDown2Value.setText(getLanguageValue("offTheShelf"));
        tvOpen2.setText(getLanguageValue("expand"));

        tvDel.setText(getLanguageValue("delete"));
        tvCancel.setText(getLanguageValue("saveContinue"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //批发单价（基础包装）
        wholesaleAdapter0 = new GoodsWholesaleAdapter(this);
        rvWholesale0.setAdapter(wholesaleAdapter0);
        wholesaleAdapter0.setListener(new GoodsWholesaleAdapter.MyListener() {
            @Override
            public void onDelClick(int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("pricingRules") + "？",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            wholesaleList0.remove(position);
                            wholesaleAdapter0.remove(position);
                        });
            }

            @Override
            public void onCount(double count, int position) {
                //起批数量
                wholesaleList0.get(position).setWholesaleCount(count);
            }

            @Override
            public void onPrice(double price, int position) {
                //批发单价
                wholesaleList0.get(position).setWholesalePrice(price);
            }
        });

        //批发单价（中间包装）
        wholesaleAdapter1 = new GoodsWholesaleAdapter(this);
        rvWholesale1.setAdapter(wholesaleAdapter1);
        wholesaleAdapter1.setListener(new GoodsWholesaleAdapter.MyListener() {
            @Override
            public void onDelClick(int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("pricingRules") + "？",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            wholesaleList1.remove(position);
                            wholesaleAdapter1.remove(position);
                        });
            }

            @Override
            public void onCount(double count, int position) {
                //起批数量
                wholesaleList1.get(position).setWholesaleCount(count);
            }

            @Override
            public void onPrice(double price, int position) {
                //批发单价
                wholesaleList1.get(position).setWholesalePrice(price);
            }
        });

        //批发单价（最大包装）
        wholesaleAdapter2 = new GoodsWholesaleAdapter(this);
        rvWholesale2.setAdapter(wholesaleAdapter2);
        wholesaleAdapter2.setListener(new GoodsWholesaleAdapter.MyListener() {
            @Override
            public void onDelClick(int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("pricingRules") + "？",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            wholesaleList2.remove(position);
                            wholesaleAdapter2.remove(position);
                        });
            }

            @Override
            public void onCount(double count, int position) {
                //起批数量
                wholesaleList2.get(position).setWholesaleCount(count);
            }

            @Override
            public void onPrice(double price, int position) {
                //批发单价
                wholesaleList2.get(position).setWholesalePrice(price);
            }
        });
    }

    /**
     *
     */
    private void getWholesaleData(int type) {
        switch (type) {
            case 1:
                List<GoodsWholesaleData> wholesaleData1 = new ArrayList<>();
                wholesaleData1.add(new GoodsWholesaleData(0, 0));
                wholesaleList1.addAll(wholesaleData1);
                wholesaleAdapter1.setDataList(wholesaleList1);
                break;
            case 2:
                List<GoodsWholesaleData> wholesaleData2 = new ArrayList<>();
                wholesaleData2.add(new GoodsWholesaleData(0, 0));
                wholesaleList2.addAll(wholesaleData2);
                wholesaleAdapter2.setDataList(wholesaleList2);
                break;
            default:
                List<GoodsWholesaleData> wholesaleData0 = new ArrayList<>();
                wholesaleData0.add(new GoodsWholesaleData(0, 0));
                wholesaleList0.addAll(wholesaleData0);
                wholesaleAdapter0.setDataList(wholesaleList0);
                break;
        }
    }

    @SuppressLint("NewApi")
    public static <T> boolean isHaveWholesaleList(List<T> items, Function<T, ?> countExtractor) {
        Set<Object> counts = new HashSet<>();
        for (T item : items) {
            if (!counts.add(countExtractor.apply(item))) {
                return true; // 找到了重复的count值
            }
        }
        return false;
    }


    /**************************图片选择、上传start****************************/
    /**
     * 动态申请权限
     */
    public void getImg(int type) {
        imgType = type;
        if (PermissionUtils.checkPermissionsGroup(this, 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
        }
    }

    /**
     * 选择图片方式弹窗
     */
    private void showDialogCamera() {
        CameraDialog.showDialog(this, (view, type) -> {
            //type: 0.拍照 1.从相册选择
            if (type == 1) {
                pickPhoto();
            } else {
                takePhoto();
            }
        });
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .setCropEngine(new ImageFileCropEngine(this))
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCropEngine(new ImageFileCropEngine(this))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            Log.e(tag, "img = " + data.getData().getUrl());
                            switch (imgType) {
                                case 1:
                                    img1 = data.getData().getUrl();
                                    Glide.with(TAG)
                                            .load(data.getData().getUrl())
                                            .apply(new RequestOptions().error(R.mipmap.ic_camera002))
                                            .into(ivImg1);
                                    break;
                                case 2:
                                    img2 = data.getData().getUrl();
                                    Glide.with(TAG)
                                            .load(data.getData().getUrl())
                                            .apply(new RequestOptions().error(R.mipmap.ic_camera002))
                                            .into(ivImg2);
                                    break;
                                default:
                                    img0 = data.getData().getUrl();
                                    Glide.with(TAG)
                                            .load(data.getData().getUrl())
                                            .apply(new RequestOptions().error(R.mipmap.ic_camera002))
                                            .into(ivImg0);
                                    break;
                            }
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**************************图片选择、上传end****************************/

    /**
     * 跳转到扫码页
     *
     * @param type
     */
    private void gotoScan(int type) {
        scanType = type;
        startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
    }

    /**
     * 跳转到选择单位页
     *
     * @param type
     */
    private void gotoUnit(int type) {
        unitType = type;
        startActivityForResult(new Intent(this, GoodsUnitActivity.class), Constants.CHOOSE_UNIT);
    }

    /**
     * 跳转到出入库
     *
     * @param barcode
     * @param isRu
     */
    private void goToChuRu(String barcode, int isRu) {
        if ("1".equals(powerCount)) {
            if (TextUtils.isEmpty(barcode)) {
                showMessage(getLanguageValue("noBarcode"));
                return;
            }
            startActivity(new Intent(this, ChuRuSelectActivity.class)
                    .putExtra("result", barcode)
                    .putExtra("isRu", isRu)
            );
        } else {
            showMessage(getLanguageValue("noPermission"));
        }
    }

    /**
     * 跳转到商品货位选择
     */
    private void goToLocation(String ids, String names) {
        startActivityForResult(new Intent(this, LocationSelectActivity.class)
                        .putExtra("ids", ids)
                        .putExtra("names", names)
                , Constants.CHOOSE_LOCATION
        );
    }

    /**
     * 打印价签
     */
    private void setPrint() {
        if (TextUtils.isEmpty(etBarcode0.getText().toString().trim())) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("barcode") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(etName0.getText().toString().trim())) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("name") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(etSalePrice0.getText().toString().trim())) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("sellingPrice") + getLanguageValue("notEmpty"));
            return;
        }
        if (printerType == 0) {
            if (Printer.getPortManager() != null && Printer.getConnectState()) {
                ThreadPoolManager.getInstance().addTask(() -> {
                    try {
                        if (Printer.getPortManager() == null) {
                            runOnUiThread(() -> {
                                showMessage(getLanguageValue("connectPrinter"));
                            });
                            return;
                        }
                        boolean result = Printer.getPortManager().writeDataImmediately(getLabel());
                        runOnUiThread(() -> {
                            if (result) {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                            } else {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                            }
                        });
                        LogUtils.e("send result", result);
                    } catch (Exception e) {
                        runOnUiThread(() -> {
                            tipsDialog(getLanguageValue("print") + getLanguageValue("failed") + e.getMessage());
                        });
                    } finally {
                        if (Printer.getPortManager() == null) {
                            Printer.close();
                        }
                    }
                });
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        } else {
            if (PrintUtil.isConnection() == 0) {
                printLabel();
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        }
    }

    /**
     * 标签打印测试页
     *
     * @return
     */
    public Vector<Byte> getLabel() {
        int printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);
        LabelCommand tsc = new LabelCommand();
        // 设置标签尺寸宽高，按照实际尺寸设置 单位mm
        tsc.addUserCommand("\r\n");
        if (printType == 4) {
            tsc.addSize(50, 30);
        } else {
            tsc.addSize(70, 38);
        }
        // 设置标签间隙，按照实际尺寸设置，如果为无间隙纸则设置为0 单位mm
        tsc.addGap(2);
        //设置纸张类型为黑标，发送BLINE 指令不能同时发送GAP指令
//        tsc.addBline(2);
        // 设置打印方向
//        tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
        // 设置原点坐标
        tsc.addReference(0, 0);
        //设置浓度
        tsc.addDensity(LabelCommand.DENSITY.DNESITY4);
        // 撕纸模式开启
        tsc.addTear(LabelCommand.RESPONSE_MODE.ON);
        // 清除打印缓冲区
        tsc.addCls();
        //标签方向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //正向
            tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etName0.getText().toString().trim());
                    tsc.addText(415, 194,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(115, 154,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            etBarcode0.getText().toString().trim());
                    break;
                case 2:
                    tsc.addText(230, 16,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(88, 76,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            etName0.getText().toString().trim());
                    tsc.addText(400, 216,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(60, 185,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            etBarcode0.getText().toString().trim());
                    break;
                case 4:
                    tsc.addText(155, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(44, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            etName0.getText().toString().trim());
                    tsc.addText(255, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(35, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            25,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            etBarcode0.getText().toString().trim());
                    break;
            }
        } else {
            //反向
            tsc.addDirection(LabelCommand.DIRECTION.BACKWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etName0.getText().toString().trim());
                    tsc.addText(415, 194,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(115, 154,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            etBarcode0.getText().toString().trim());
                    break;
                case 2:
                    tsc.addText(270, 16,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(118, 73,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            etName0.getText().toString().trim());
                    tsc.addText(420, 216,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(100, 185,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            etBarcode0.getText().toString().trim());
                    break;
                case 4:
                    tsc.addText(195, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(84, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            etName0.getText().toString().trim());
                    tsc.addText(295, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            etSalePrice0.getText().toString().trim());
                    tsc.add1DBarcode(75, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            20,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            etBarcode0.getText().toString().trim());
                    break;
            }
        }
//        Bitmap b = BitmapFactory.decodeResource(GoodsAddUpdateActivity.this.getResources(), R.mipmap.mylogo);
//        // 绘制图片
//        tsc.drawImage(30, 100, 300, b);
//        Bitmap b2= BitmapFactory.decodeResource(context.getResources(), R.drawable.main_fenlei);
//        tsc.drawJPGImage(200,250,200,b2);
        //绘制二维码
//        tsc.addQRCode(30,250, LabelCommand.EEC.LEVEL_L, 5, LabelCommand.ROTATION.ROTATION_0, " www.smarnet.cc");
        // 绘制一维条码
        // 打印标签
        tsc.addPrint(1, 1);
        // 打印标签后 蜂鸣器响
        tsc.addSound(2, 100);
        //开启钱箱
//        tsc.addCashdrwer(LabelCommand.FOOT.F5, 255, 255);
        Vector<Byte> datas = tsc.getCommand();
        // 发送数据
        return datas;
    }

    /**
     * 提示弹框
     *
     * @param message
     */
    private void tipsDialog(String message) {
        //添加"Yes"按钮
        AlertDialog alertDialog = new AlertDialog
                .Builder(this)
                .setTitle(getLanguageValue("hint"))
                .setMessage(message)
                .setIcon(R.mipmap.mylogo).setPositiveButton(getString(R.string.confirm), (dialogInterface, i) -> {

                }).create();
        alertDialog.show();
    }

    /**
     * 打印标签（精臣）
     */
    private void printLabel() {
        // 检查是否连接了打印机
        if (PrintUtil.isConnection() != 0) {
            showMessage(getLanguageValue("printerNotConnect"));
            return;
        }
        isError = false;
        isCancel = false;
        jsonList.clear();
        infoList.clear();
        PrintUtil.getInstance().setTotalPrintQuantity(1);
        /*
         * 参数1：打印浓度 ，参数2:纸张类型 参数3:打印模式
         * 打印浓度 B50/B50W/T6/T7/T8 建议设置6或8，Z401/B32建议设置8，B3S/B21/B203/B1建议设置3
         */
        PrintUtil.getInstance().startPrintJob(4, 1, 1, new PrintCallback() {
            @Override
            public void onProgress(int pageIndex, int quantityIndex, HashMap<String, Object> hashMap) {
                // 更新打印进度
                String progressMessage = "打印进度:已打印到第" + pageIndex + "页,第" + quantityIndex + "份";
                Log.d(tag, "测试:" + progressMessage);
                // 处理打印完成情况
                if (pageIndex == 1 && quantityIndex == 1) {
                    Log.d(tag, "测试:onProgress: 结束打印");
                    //endJob，使用方法含义更明确的endPrintJob
                    runOnUiThread(() -> {
                        if (PrintUtil.getInstance().endPrintJob()) {
                            Log.d(tag, "结束打印成功");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                        } else {
                            Log.d(tag, "结束打印失败");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                        }
                    });

                }
            }


            @Override
            public void onError(int i) {

            }


            @Override
            public void onError(int errorCode, int printState) {
                Log.d(tag, "测试：报错");
                isError = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("printError") + " errorCode = " + errorCode + " printState = " + printState);
                });
            }

            @Override
            public void onCancelJob(boolean isSuccess) {
                Log.d(tag, "onCancelJob: " + isSuccess);
                isCancel = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("cancelPrint"));
                });
            }

            /**
             * SDK缓存空闲回调，可以在此处传入打印数据
             *
             * @param pageIndex 当前回调函数处理下一页的打印索引
             * @param bufferSize 缓存空间的大小
             */
            @Override
            public void onBufferFree(int pageIndex, int bufferSize) {
                // 如果出现错误、已取消打印，或 pageIndex 超过总页数，则返回
                if (isError || isCancel) {
                    return;
                }
                setPrinterData();
            }
        });
    }

    /**
     * 生成打印数据(精臣)
     */
    private void setPrinterData() {
        String printPrice;
        if (TextUtils.isEmpty(tvUnit0.getText().toString())) {
            printPrice = etSalePrice0.getText().toString();
        } else {
            printPrice = etSalePrice0.getText().toString() + "/" + tvUnit0.getText().toString();
        }
        float width = 70, height = 38;
        /*
         * 设置画布⼤⼩
         *
         * @param width 画布宽度(mm)
         * @param height 画布⾼度(mm)
         * @param orientation 画布旋转⻆度
         * @param fontDir 字体路径暂不可⽤，默认""即可
         *
         */
        PrintUtil.getInstance().drawEmptyLabel(width, height, 0, "");

        //标签方向：0.正向 1.反向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //标签样式 1. 2. 3.
            if (sharedPreferencesUtil.getInt(2, Constants.PRINT_TYPE) == 3) {
                /**
                 * 绘制⽂本
                 * @param x 位置x
                 * @param y 位置y
                 * @param width 宽
                 * @param height ⾼
                 * @param value 内容
                 * @param fontFamily 字体名称,未传输字体为空字符串时使⽤默认字体,暂时⽤默认字体
                 * @param fontSize 字体⼤⼩
                 * @param rotate 旋转
                 * @param textAlignHorizontal ⽔平对⻬⽅式：0:左对⻬ 1:居中对⻬ 2:右对⻬
                 * @param textAlignVertical 垂直 对⻬⽅式：0:顶对⻬ 1:垂直居中 2:底对⻬
                 * @param lineModel 1:宽⾼固定，内容⼤⼩⾃适应（字号/字符间距/⾏间距 按⽐例缩放）
                2:宽度固定，⾼度⾃适应
                3:宽⾼固定，超出内容后⾯加...
                4:宽⾼固定,超出内容直裁切
                6:宽⾼固定，内容超过预设宽⾼时⾃动缩⼩（字号/字符间距/⾏间距 按⽐例缩放）
                 * @param letterSpace 字⺟之间的标准间隔，单位mm
                 * @param lineSpace ⾏间距（倍距），单位mm
                 * @param mFontStyles 字体样式[加粗，斜体，下划线，删除下划线（预留）]
                 */
                PrintUtil.getInstance().drawLabelText(40f, 13f, 30f, 5f, etName0.getText().toString(), "", 6f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                /**
                 * 绘制⼀维码
                 *
                 * @param x ⽔平坐标
                 * @param y 垂直坐标
                 * @param width 宽度,单位mm
                 * @param height ⾼度,单位mm
                 * @param codeType ⼀维码类型20:CODE128 ,21:UPC-A,22:UPC-E,23:EAN8,24:EAN13,
                 * 25:CODE93,26:CODE39,27:CODEBAR, 28:ITF25
                 * @param value ⽂本内容
                 * @param fontSize ⽂本字号
                 * @param rotate 旋转⻆度，仅⽀持0,90,180,270
                 * @param textHeight ⽂本⾼度
                 * @param textPosition ⽂本位置，int,⼀维码⽂字识别码显示位置,0:下⽅显示,1:上⽅显
                示,2:不显示
                 */
                PrintUtil.getInstance().drawLabelBarCode(25f, 22f, 40f, 7f, 20, etBarcode0.getText().toString(), 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(40f, 27f, 30f, 7f, "RM" + printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            } else {
                PrintUtil.getInstance().drawLabelText(31f, 0f, 39f, 5f, getShop_name(), "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelText(12f, 7f, 55f, 5f, etName0.getText().toString(), "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelBarCode(10f, 23f, 28f, 8f, 20, etBarcode0.getText().toString(), 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(48f, 25f, 23f, 7f, printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            }
        } else {
            PrintUtil.getInstance().drawLabelText(0f, 30f, 40f, 5f, getShop_name(), "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelText(0f, 22f, 60f, 5f, etName0.getText().toString(), "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelBarCode(34f, 5f, 28f, 8f, 20, etBarcode0.getText().toString(), 3f,
                    180, 3f, 0);
            PrintUtil.getInstance().drawLabelText(0f, 3f, 23f, 7f, printPrice, "", 7f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
        }

        //生成打印数据
        byte[] jsonByte = PrintUtil.getInstance().generateLabelJson();
        jsonList.add(new String(jsonByte));
        //除B32/Z401/T8的printMultiple为11.81，其他的为8
        String jsonInfo = "{  " + "\"printerImageProcessingInfo\": " + "{    " + "\"orientation\":" + 0 + "," + "   \"margin\": [      0,      0,      0,      0    ], " + "   \"printQuantity\": " + 1 + ",  " + "  \"horizontalOffset\": 0,  " + "  \"verticalOffset\": 0,  " + "  \"width\":" + width + "," + "   \"height\":" + height + "," + "\"printMultiple\":" + 8f + "," + "  \"epc\": \"\"  }}";
        infoList.add(jsonInfo);
        PrintUtil.getInstance().commitData(jsonList, infoList);
    }

    /*****************************接口交互start*****************************/

    /**
     * 更新UI（商品详情）
     *
     * @param data
     */
    private void setUI(GoodsInfoData data) {
        if (data == null) {
            return;
        }
        /*商品信息*/
        //供货商
        supplierUnique = data.getSupplierUnique();
        if (TextUtils.isEmpty(data.getSupplierName())) {
            tvSupplier.setText("");
        } else {
            tvSupplier.setText(data.getSupplierName());
        }
        //分类
//        cateUnique1 = data.getKindUnique();
//        if (TextUtils.isEmpty(data.getGroupsName())) {
//            if (TextUtils.isEmpty(data.getKindName())) {
//                tvCate.setText("");
//            } else {
//                tvCate.setText(data.getKindName());
//            }
//        } else {
//            if (TextUtils.isEmpty(data.getKindName())) {
//                tvCate.setText(data.getGroupsName());
//            } else {
//                tvCate.setText(data.getGroupsName() + "-" + data.getKindName());
//            }
//        }
        cateUnique = data.getGroupsUnique();
        cateUnique1 = data.getKindUnique();
        cateUnique2 = data.getThreeUnique();
        String cates = "";
        if (!TextUtils.isEmpty(data.getGroupsName())) {
            cates = data.getGroupsName();
        }
        if (!TextUtils.isEmpty(data.getKindName())) {
            cates = cates + "-" + data.getKindName();
        }
        if (!TextUtils.isEmpty(data.getThreeName())) {
            cates = cates + "-" + data.getThreeName();
        }
        tvCate.setText(cates);
        //保质期
        goodsLife = data.getGoodsLife();
        if (goodsLife > 0) {
            etGoodsLife.setText(String.valueOf(goodsLife));
        } else {
            etGoodsLife.setText("");
        }
        //品牌
        brand = data.getGoodsBrand();
        etBrand.setText(data.getGoodsBrand());
        if (data.getListDetail() == null) {
            return;
        }
        /*基础包装*/
        if (data.getListDetail().size() > 0) {
            GoodsInfoData.ListDetailBean data0 = data.getListDetail().get(0);
            goodsBarcode0 = data0.getGoodsBarcode();
            //库存预警
            warningStatus = data0.getStock_warning_status();
            if (warningStatus == 0) {
                ivWarning.setSelected(false);
                linWarning.setVisibility(View.GONE);
            } else {
                ivWarning.setSelected(true);
                linWarning.setVisibility(View.VISIBLE);
                warningLow = data0.getOut_stock_waring_count();
                warningTall = data0.getUnsalable_count();
                if (warningLow > 0) {
                    etLow.setText(DFUtils.getNum4(warningLow));
                } else {
                    etLow.setText("");
                }
                if (warningTall > 0) {
                    etTall.setText(DFUtils.getNum4(warningTall));
                } else {
                    etTall.setText("");
                }
            }
            goodsId0 = data0.getGoodsId();
            //图片
            img0 = data0.getGoodsPicturepath();
            Glide.with(this)
                    .load(StringUtils.handledImgUrl(img0))
                    .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                    .into(ivImg0);
            //条码
            etBarcode0.setText(data0.getGoodsBarcode());
            //名称
            etName0.setText(data0.getGoodsName());
            //计价类型
            chengType = data.getGoodsChengType();
            if (chengType == 0) {
                tvChengType0.setBackgroundResource(R.drawable.shape_e5efff_22);
                tvChengType0.setTextColor(getResources().getColor(R.color.blue));
                tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                tvChengType1.setBackgroundResource(R.drawable.shape_f7_22);
                tvChengType1.setTextColor(getResources().getColor(R.color.color_666));
                tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            } else {
                chengType = 1;
                tvChengType1.setBackgroundResource(R.drawable.shape_e5efff_22);
                tvChengType1.setTextColor(getResources().getColor(R.color.blue));
                tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                tvChengType0.setBackgroundResource(R.drawable.shape_f7_22);
                tvChengType0.setTextColor(getResources().getColor(R.color.color_666));
                tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            }
            //单位
            unit0 = data0.getGoodsUnit();
            tvUnit0.setText(unit0);
            //库存
            goodsStock = data.getGoodsCount();
            etStock0.setText(DFUtils.getNum4(goodsStock));
//            if (goodsStock == 0) {
//                etStock0.setText("");
//            } else {
//                etStock0.setText(DFUtils.getNum4(goodsStock));
//            }
            //入库单价
            if (data0.getGoodsInPrice() == 0) {
                etInPrice0.setText("");
            } else {
                etInPrice0.setText(DFUtils.getNum4(data0.getGoodsInPrice()));
            }
            //最近入库价
            tvStockPrice0.setText(DFUtils.getNum2(data0.getGoodStockPrice()));
            //零售单价
            if (data0.getGoodsSalePrice() == 0) {
                etSalePrice0.setText("");
            } else {
                etSalePrice0.setText(DFUtils.getNum4(data0.getGoodsSalePrice()));
            }
            //网购单价
            if (data0.getGoodsWebSalePrice() == 0) {
                etOnlinePrice0.setText("");
            } else {
                etOnlinePrice0.setText(DFUtils.getNum4(data0.getGoodsWebSalePrice()));
            }
            //会员单价
            if (data0.getGoodsCusPrice() == 0) {
                etMemberPrice0.setText("");
            } else {
                etMemberPrice0.setText(DFUtils.getNum4(data0.getGoodsCusPrice()));
            }
            //规格
            etSpecs0.setText(data0.getGoodsStandard());
            //起订量
            if (TextUtils.isEmpty(data0.getMinSaleCount())) {
                etStartOrder0.setText("");
            } else {
                startOrder0 = Double.parseDouble(data0.getMinSaleCount());
                if (startOrder0 > 0) {
                    etStartOrder0.setText(DFUtils.getNum4(startOrder0));
                } else {
                    etStartOrder0.setText("");
                }
            }
            //收银机上架状态 1.上架 2.下架
            if (data0.getPcShelfState() == 1) {
                printerStatus0 = 1;
                ivPrinterUp0.setSelected(true);
                ivPrinterDown0.setSelected(false);
            } else {
                printerStatus0 = 2;
                ivPrinterDown0.setSelected(true);
                ivPrinterUp0.setSelected(false);
            }
            //小程序上架状态 1.上架 2.下架
            if (data0.getShelfState() == 1) {
                appletStatus0 = 1;
                ivAppletUp0.setSelected(true);
                ivAppletDown0.setSelected(false);
            } else {
                appletStatus0 = 2;
                ivAppletDown0.setSelected(true);
                ivAppletUp0.setSelected(false);
            }
            //批发
            wholesalePriceStatus0 = data0.getWholesalePriceFlg();
//            wholesaleCount0 = data0.getWholesaleCount();
//            wholesalePrice0 = data0.getWholesalePrice();
//            if (wholesalePriceStatus0 == 1) {
//                ivWholesale0.setSelected(true);
//                linWholesale0.setVisibility(View.VISIBLE);
//                etWholesaleCount0.setText(wholesaleCount0 > 0 ? DFUtils.getNum4(wholesaleCount0) : "");
//                etWholesalePrice0.setText(wholesalePrice0 > 0 ? DFUtils.getNum2(wholesalePrice0) : "");
//            } else {
//                ivWholesale0.setSelected(false);
//                linWholesale0.setVisibility(View.GONE);
//            }
            if (wholesalePriceStatus0 == 1) {
                ivWholesale0.setSelected(true);
                vWholesale0.setVisibility(View.VISIBLE);
                linWholesale0.setVisibility(View.VISIBLE);
            } else {
                ivWholesale0.setSelected(false);
                vWholesale0.setVisibility(View.GONE);
                linWholesale0.setVisibility(View.GONE);
            }
            wholesaleList0.clear();
            if (data0.getWholesaleList() == null) {
                getWholesaleData(0);
            } else {
                if (data0.getWholesaleList().isEmpty()) {
                    getWholesaleData(0);
                } else {
                    wholesaleList0.addAll(data0.getWholesaleList());
                    wholesaleAdapter0.setDataList(wholesaleList0);
                }
            }
            //货位
            goodsPosition0 = data0.getGoodsPosition();
            completePositionName0 = data0.getCompletePositionName();
            tvLocation0.setText(completePositionName0);
            //划线单价
            if (data0.getUnderlinedPrice() == 0) {
                etUnderlinePrice0.setText("");
            } else {
                etUnderlinePrice0.setText(DFUtils.getNum4(data0.getUnderlinedPrice()));
            }
        } else {
            getWholesaleData(0);
        }

        /*中间包装*/
        if (data.getListDetail().size() > 1) {
            isSpecs1 = true;
            GoodsInfoData.ListDetailBean data1 = data.getListDetail().get(1);
            goodsBarcode1 = data1.getGoodsBarcode();
            goodsId1 = data1.getGoodsId();
            img1 = data1.getGoodsPicturepath();
            Glide.with(this)
                    .load(StringUtils.handledImgUrl(img1))
                    .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                    .into(ivImg1);
            etBarcode1.setText(data1.getGoodsBarcode());
            etName1.setText(data1.getGoodsName());
            //入库单价
            if (data1.getGoodsInPrice() == 0) {
                etInPrice1.setText("");
            } else {
                etInPrice1.setText(DFUtils.getNum4(data1.getGoodsInPrice()));
            }
            if (data1.getGoodsSalePrice() == 0) {
                etSalePrice1.setText("");
            } else {
                etSalePrice1.setText(DFUtils.getNum4(data1.getGoodsSalePrice()));
            }
            if (data1.getGoodsWebSalePrice() == 0) {
                etOnlinePrice1.setText("");
            } else {
                etOnlinePrice1.setText(DFUtils.getNum4(data1.getGoodsWebSalePrice()));
            }
            unit1 = data1.getGoodsUnit();
            tvUnit1.setText(unit1);
            if (data1.getGoodsCusPrice() == 0) {
                etMemberPrice1.setText("");
            } else {
                etMemberPrice1.setText(DFUtils.getNum4(data1.getGoodsCusPrice()));
            }
            etCount1.setText(DFUtils.getNum4(data1.getContainCount()));
            etSpecs1.setText(data1.getGoodsStandard());
            if (TextUtils.isEmpty(data1.getMinSaleCount())) {
                etStartOrder1.setText("");
            } else {
                startOrder1 = Double.parseDouble(data1.getMinSaleCount());
                if (startOrder1 > 0) {
                    etStartOrder1.setText(DFUtils.getNum4(startOrder1));
                } else {
                    etStartOrder1.setText("");
                }
            }
            //收银机上架状态 1.上架 2.下架
            if (data1.getPcShelfState() == 1) {
                printerStatus1 = 1;
                ivPrinterUp1.setSelected(true);
                ivPrinterDown1.setSelected(false);
            } else {
                printerStatus1 = 2;
                ivPrinterDown1.setSelected(true);
                ivPrinterUp1.setSelected(false);
            }
            //小程序上架状态 1.上架 2.下架
            if (data1.getShelfState() == 1) {
                appletStatus1 = 1;
                ivAppletUp1.setSelected(true);
                ivAppletDown1.setSelected(false);
            } else {
                appletStatus1 = 2;
                ivAppletDown1.setSelected(true);
                ivAppletUp1.setSelected(false);
            }
            //批发
            wholesalePriceStatus1 = data1.getWholesalePriceFlg();
//            wholesaleCount1 = data1.getWholesaleCount();
//            wholesalePrice1 = data1.getWholesalePrice();
//            if (wholesalePriceStatus1 == 1) {
//                ivWholesale1.setSelected(true);
//                linWholesale1.setVisibility(View.VISIBLE);
//                etWholesaleCount1.setText(wholesaleCount1 > 0 ? DFUtils.getNum4(wholesaleCount1) : "");
//                etWholesalePrice1.setText(wholesalePrice1 > 0 ? DFUtils.getNum2(wholesalePrice1) : "");
//            } else {
//                ivWholesale1.setSelected(false);
//                linWholesale1.setVisibility(View.GONE);
//            }
            if (wholesalePriceStatus1 == 1) {
                ivWholesale1.setSelected(true);
                vWholesale1.setVisibility(View.VISIBLE);
                linWholesale1.setVisibility(View.VISIBLE);
            } else {
                ivWholesale1.setSelected(false);
                vWholesale1.setVisibility(View.GONE);
                linWholesale1.setVisibility(View.GONE);
            }
            wholesaleList1.clear();
            if (data1.getWholesaleList() == null) {
                getWholesaleData(1);
            } else {
                if (data1.getWholesaleList().isEmpty()) {
                    getWholesaleData(1);
                } else {
                    wholesaleList1.addAll(data1.getWholesaleList());
                    wholesaleAdapter1.setDataList(wholesaleList1);
                }
            }
            //货位
            goodsPosition1 = data1.getGoodsPosition();
            completePositionName1 = data1.getCompletePositionName();
            tvLocation1.setText(completePositionName1);
            //划线单价
            if (data1.getUnderlinedPrice() == 0) {
                etUnderlinePrice1.setText("");
            } else {
                etUnderlinePrice1.setText(DFUtils.getNum4(data1.getUnderlinedPrice()));
            }
            //条码不可编辑
            ivBarcode1.setVisibility(View.GONE);
            etBarcode1.setFocusable(false);
            etBarcode1.setFocusableInTouchMode(false);
            etBarcode1.setTextColor(getResources().getColor(R.color.color_999));
            ivScan1.setVisibility(View.GONE);
            //库存
            linStock1.setVisibility(View.VISIBLE);
            if (data1.getContainCount() > 0) {
                tvStock1.setText(String.valueOf((int) (goodsStock / data1.getContainCount())));
            } else {
                tvStock1.setText("0");
            }
            //入库单价
            linInPrice1.setVisibility(View.GONE);
            linStockPrice1.setVisibility(View.VISIBLE);
            tvStockPrice1.setText(DFUtils.getNum2(data1.getGoodStockPrice()));
        } else {
            isSpecs1 = false;
            //条码可编辑
            ivBarcode1.setVisibility(View.VISIBLE);
            etBarcode1.setFocusable(true);
            etBarcode1.setFocusableInTouchMode(true);
            etBarcode1.setTextColor(getResources().getColor(R.color.color_333));
            ivScan1.setVisibility(View.VISIBLE);
            //库存
            linStock1.setVisibility(View.GONE);
            //入库单价
            linInPrice1.setVisibility(View.VISIBLE);
            linStockPrice1.setVisibility(View.GONE);
            getWholesaleData(1);
        }

        /*最大包装*/
        if (data.getListDetail().size() > 2) {
            isSpecs2 = true;
            GoodsInfoData.ListDetailBean data2 = data.getListDetail().get(2);
            goodsBarcode2 = data2.getGoodsBarcode();
            goodsId2 = data2.getGoodsId();
            img2 = data2.getGoodsPicturepath();
            Glide.with(this)
                    .load(StringUtils.handledImgUrl(img2))
                    .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                    .into(ivImg2);
            etBarcode2.setText(data2.getGoodsBarcode());
            etName2.setText(data2.getGoodsName());
            //入库单价
            if (data2.getGoodsInPrice() == 0) {
                etInPrice2.setText("");
            } else {
                etInPrice2.setText(DFUtils.getNum4(data2.getGoodsInPrice()));
            }
            if (data2.getGoodsSalePrice() == 0) {
                etSalePrice2.setText("");
            } else {
                etSalePrice2.setText(DFUtils.getNum4(data2.getGoodsSalePrice()));
            }
            if (data2.getGoodsWebSalePrice() == 0) {
                etOnlinePrice2.setText("");
            } else {
                etOnlinePrice2.setText(DFUtils.getNum4(data2.getGoodsWebSalePrice()));
            }
            unit2 = data2.getGoodsUnit();
            tvUnit2.setText(unit2);
            if (data2.getGoodsCusPrice() == 0) {
                etMemberPrice2.setText("");
            } else {
                etMemberPrice2.setText(DFUtils.getNum4(data2.getGoodsCusPrice()));
            }
            if (data2.getContainCount() == 0) {
                etCount2.setText("");
            } else {
                etCount2.setText(DFUtils.getNum4(data2.getContainCount()));
            }
            etSpecs2.setText(data2.getGoodsStandard());
            if (TextUtils.isEmpty(data2.getMinSaleCount())) {
                etStartOrder2.setText("");
            } else {
                startOrder2 = Double.parseDouble(data2.getMinSaleCount());
                if (startOrder2 > 0) {
                    etStartOrder2.setText(DFUtils.getNum4(startOrder2));
                } else {
                    etStartOrder2.setText("");
                }
            }
            //收银机上架状态 1.上架 2.下架
            if (data2.getPcShelfState() == 1) {
                printerStatus2 = 1;
                ivPrinterUp2.setSelected(true);
                ivPrinterDown2.setSelected(false);
            } else {
                printerStatus2 = 2;
                ivPrinterDown2.setSelected(true);
                ivPrinterUp2.setSelected(false);
            }
            //小程序上架状态 1.上架 2.下架
            if (data2.getShelfState() == 1) {
                appletStatus2 = 1;
                ivAppletUp2.setSelected(true);
                ivAppletDown2.setSelected(false);
            } else {
                appletStatus2 = 2;
                ivAppletDown2.setSelected(true);
                ivAppletUp2.setSelected(false);
            }
            //批发
            wholesalePriceStatus2 = data2.getWholesalePriceFlg();
//            wholesaleCount2 = data2.getWholesaleCount();
//            wholesalePrice2 = data2.getWholesalePrice();
//            if (wholesalePriceStatus2 == 1) {
//                ivWholesale2.setSelected(true);
//                linWholesale2.setVisibility(View.VISIBLE);
//                etWholesaleCount2.setText(wholesaleCount2 > 0 ? DFUtils.getNum4(wholesaleCount2) : "");
//                etWholesalePrice2.setText(wholesalePrice2 > 0 ? DFUtils.getNum2(wholesalePrice2) : "");
//            } else {
//                ivWholesale2.setSelected(false);
//                linWholesale2.setVisibility(View.GONE);
//            }
            if (wholesalePriceStatus2 == 1) {
                ivWholesale2.setSelected(true);
                vWholesale2.setVisibility(View.VISIBLE);
                linWholesale2.setVisibility(View.VISIBLE);
            } else {
                ivWholesale2.setSelected(false);
                vWholesale2.setVisibility(View.GONE);
                linWholesale2.setVisibility(View.GONE);
            }
            wholesaleList2.clear();
            if (data2.getWholesaleList() == null) {
                getWholesaleData(2);
            } else {
                if (data2.getWholesaleList().isEmpty()) {
                    getWholesaleData(2);
                } else {
                    wholesaleList2.addAll(data2.getWholesaleList());
                    wholesaleAdapter2.setDataList(wholesaleList2);
                }
            }
            //货位
            goodsPosition2 = data2.getGoodsPosition();
            completePositionName2 = data2.getCompletePositionName();
            tvLocation2.setText(completePositionName2);
            //划线单价
            if (data2.getUnderlinedPrice() == 0) {
                etUnderlinePrice2.setText("");
            } else {
                etUnderlinePrice2.setText(DFUtils.getNum4(data2.getUnderlinedPrice()));
            }
            //条码不可编辑
            ivBarcode2.setVisibility(View.GONE);
            etBarcode2.setFocusable(false);
            etBarcode2.setFocusableInTouchMode(false);
            etBarcode2.setTextColor(getResources().getColor(R.color.color_999));
            ivScan2.setVisibility(View.GONE);
            linStock2.setVisibility(View.VISIBLE);
            if (data2.getContainCount() > 0) {
                tvStock2.setText(String.valueOf((int) (goodsStock / data2.getContainCount())));
            } else {
                tvStock2.setText("0");
            }
            linInPrice2.setVisibility(View.GONE);
            linStockPrice2.setVisibility(View.VISIBLE);
            tvStockPrice2.setText(DFUtils.getNum2(data2.getGoodStockPrice()));
        } else {
            isSpecs2 = false;
            //条码可编辑
            ivBarcode2.setVisibility(View.VISIBLE);
            etBarcode2.setFocusable(true);
            etBarcode2.setFocusableInTouchMode(true);
            ivScan2.setVisibility(View.VISIBLE);
            linStock2.setVisibility(View.GONE);
            linInPrice2.setVisibility(View.VISIBLE);
            linStockPrice2.setVisibility(View.GONE);
            getWholesaleData(2);
        }
    }

    /**
     * 更新UI（清空）
     *
     * @param type -1.所有 0.基础包装 1.中间包装 2.最大包装
     */
    private void setUIClear(int type) {
        if (type == -1) {
            /*商品信息*/
            supplierUnique = "";
            tvSupplier.setText("");
            cateUnique = "";
            cateUnique1 = "";
            cateUnique2 = "";
            tvCate.setText("");
            goodsDate = "";
            tvGoodsDate.setText("");
            goodsLife = 0;
            etGoodsLife.setText("");
            etBrand.setText("");
            warningStatus = 0;
            ivWarning.setSelected(false);
            linWarning.setVisibility(View.GONE);
            etLow.setText("");
            etTall.setText("");
        }

        if (type == -1 || type == 0) {
            //基础包装
            goodsId0 = 0;
            img0 = "";
            ivImg0.setImageResource(R.mipmap.ic_camera003);
            if (this.type == 0) {
                etBarcode0.setText("");
                etStock0.setText("");
            }
            etName0.setText("");
            chengType = 0;
            tvChengType0.setBackgroundResource(R.drawable.shape_e5efff_22);
            tvChengType0.setTextColor(getResources().getColor(R.color.blue));
            tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tvChengType1.setBackgroundResource(R.drawable.shape_f7_22);
            tvChengType1.setTextColor(getResources().getColor(R.color.color_666));
            tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            unit0 = "";
            tvUnit0.setText("");
            etInPrice0.setText("");
            etSalePrice0.setText("");
            etOnlinePrice0.setText("");
            etMemberPrice0.setText("");
            etStartOrder0.setText("");
            etSpecs0.setText("");
//            etWholesaleCount0.setText("");
//            etWholesalePrice0.setText("");
            goodsPosition0 = "";
            completePositionName0 = "";
            tvLocation0.setText("");
            etUnderlinePrice0.setText("");
            //批发价
            wholesalePriceStatus0 = 0;
            ivWholesale0.setSelected(false);
            vWholesale0.setVisibility(View.GONE);
            linWholesale0.setVisibility(View.GONE);
            wholesaleList0.clear();
            getWholesaleData(0);
        }
        if (type == -1 || type == 1) {
            //中间包装
            goodsId1 = 0;
            img1 = "";
            ivImg1.setImageResource(R.mipmap.ic_camera003);
            if (this.type == 0) {
                etBarcode1.setText("");
            }
            etName1.setText("");
            unit1 = "";
            tvUnit1.setText("");
            etCount1.setText("");
            etInPrice1.setText("");
            etSalePrice1.setText("");
            etOnlinePrice1.setText("");
            etMemberPrice1.setText("");
            etStartOrder1.setText("");
            etSpecs1.setText("");
//            etWholesaleCount1.setText("");
//            etWholesalePrice1.setText("");
            goodsPosition1 = "";
            completePositionName1 = "";
            tvLocation1.setText("");
            etUnderlinePrice1.setText("");
            //批发价
            wholesalePriceStatus1 = 0;
            ivWholesale1.setSelected(false);
            vWholesale1.setVisibility(View.GONE);
            linWholesale1.setVisibility(View.GONE);
            wholesaleList1.clear();
            getWholesaleData(1);
        }
        if (type == -1 || type == 2) {
            goodsId2 = 0;
            img2 = "";
            ivImg2.setImageResource(R.mipmap.ic_camera003);
            if (this.type == 0) {
                etBarcode2.setText("");
            }
            etName2.setText("");
            unit2 = "";
            tvUnit2.setText("");
            etCount2.setText("");
            etInPrice2.setText("");
            etSalePrice2.setText("");
            etOnlinePrice2.setText("");
            etMemberPrice2.setText("");
            etStartOrder2.setText("");
            etSpecs2.setText("");
//            etWholesaleCount2.setText("");
//            etWholesalePrice2.setText("");
            goodsPosition2 = "";
            completePositionName2 = "";
            tvLocation2.setText("");
            etUnderlinePrice2.setText("");
            //批发价
            wholesalePriceStatus2 = 0;
            ivWholesale2.setSelected(false);
            vWholesale2.setVisibility(View.GONE);
            linWholesale2.setVisibility(View.GONE);
            wholesaleList2.clear();
            getWholesaleData(2);
        }
    }

    /**
     * 生成条码
     *
     * @param type 0.基础 1.中间 2.最大
     */
    private void getGoodsBarcode(int type) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsBarcode(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        switch (type) {
                            case 1:
                                etBarcode1.setText(s);
                                break;
                            case 2:
                                etBarcode2.setText(s);
                                break;
                            default:
                                etBarcode0.setText(s);
                                break;
                        }
                    }
                });
    }

    /**
     * 商品详情
     */
    private void getGoodsInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSelecDetail(),
                map,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 扫描获取商品详情
     */
    private void getGoodsInfoScan(String code) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", code);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSelecScan(),
                map,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        switch (data.getTableType()) {
                            case 1:
                                //云库商品-直接赋值
                                switch (scanType) {
                                    case 0:
                                        etBarcode0.setText(code);
                                        break;
                                    case 1:
                                        etBarcode1.setText(code);
                                        break;
                                    case 2:
                                        etBarcode2.setText(code);
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case 2:
                                //本店商品-获取商品详情
                                if (data.getListDetail().size() > 0) {
                                    IAlertDialog.showDialog(TAG,
                                            getLanguageValue("barcodeBeUsed") + "【" + data.getListDetail().get(0).getGoodsName() + "】" + getLanguageValue("occupation"),
                                            getLanguageValue("editor"),
                                            (dialog, which) -> {
                                                startActivity(new Intent(TAG, GoodsEditActivity.class)
                                                        .putExtra("goodsBarcode", code)
                                                        .putExtra("type", 1)
                                                );
                                                finish();
                                            });
                                }
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.deletegoods(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(getLanguageValue("delete") + getLanguageValue("succeed"));
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(getLanguageValue("delete") + getLanguageValue("failed"));
                    }
                });
    }

    private String brand;//品牌
    private double goodsStock;//库存

    private String code0, code1, code2,//条码
            name0, name1, name2,//名称
            unit0, unit1, unit2,//单位
            specs0, specs1, specs2,//规格
            goodsPosition0, goodsPosition1, goodsPosition2,//货位id
            completePositionName0, completePositionName1, completePositionName2;//货位名称
    private double inPrice0, inPrice1, inPrice2,//采购价
            salePrice0, salePrice1, salePrice2,//售价
            onlinePrice0, onlinePrice1, onlinePrice2,//网购价
            memberPrice0, memberPrice1, memberPrice2,//会员价
            startOrder0, startOrder1, startOrder2,//起订量
    //            wholesaleCount0, wholesaleCount1, wholesaleCount2,//起批数量
//            wholesalePrice0, wholesalePrice1, wholesalePrice2,//批发单价
    underLinePrice0, underLinePrice1, underLinePrice2;//划线单价
    private int count1, count2,//最小单位数量
            goodsId0, goodsId1, goodsId2, goodsId,//商品id
            printerStatus0 = 1, printerStatus1 = 1, printerStatus2 = 1,//收银机上架状态 1.上架 2.下架
            appletStatus0 = 1, appletStatus1 = 1, appletStatus2 = 1,//小程序上架状态 1.上架 2.下架
            wholesalePriceStatus0, wholesalePriceStatus1, wholesalePriceStatus2;//是否设置批发价 0.否 1.是

    /**
     * 商品新增、编辑
     *
     * @param addType 0.保存并继续 1.保存并返回
     */
    private void postGoodsEdit(int addType) {
        List array = new ArrayList();
        if (TextUtils.isEmpty(cateUnique1) && TextUtils.isEmpty(cateUnique2)) {
            showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("classification"));
            return;
        }
        if (TextUtils.isEmpty(etGoodsLife.getText().toString().trim())) {
            goodsLife = 0;
        } else {
            goodsLife = Integer.parseInt(etGoodsLife.getText().toString().trim());
        }
        brand = etBrand.getText().toString().trim();
        //库存预警
        if (warningStatus == 1) {
            if (!TextUtils.isEmpty(etLow.getText().toString().trim())) {
                warningLow = Double.parseDouble(etLow.getText().toString().trim());
            } else {
                warningLow = 0;
            }
            if (!TextUtils.isEmpty(etTall.getText().toString().trim())) {
                warningTall = Double.parseDouble(etTall.getText().toString().trim());
            } else {
                warningTall = 0;
            }
        }
        /*基础包装*/
        code0 = etBarcode0.getText().toString().trim();
        if (TextUtils.isEmpty(code0)) {
            showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("barcode"));
            return;
        }
        name0 = etName0.getText().toString().trim();
        if (TextUtils.isEmpty(name0)) {
            showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("name"));
            return;
        }
        if (TextUtils.isEmpty(unit0)) {
            showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("unit"));
            return;
        }
        if (type == 0) {
            //编辑时有入库单价
            if (TextUtils.isEmpty(etStock0.getText().toString().trim())) {
                goodsStock = 0;
            } else {
                goodsStock = Double.parseDouble(etStock0.getText().toString().trim());
            }
            if (TextUtils.isEmpty(etInPrice0.getText().toString().trim())) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
                return;
            }
            inPrice0 = Double.parseDouble(etInPrice0.getText().toString().trim());
            if (inPrice0 == 0) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
                return;
            }
        } else {
            if (TextUtils.isEmpty(etInPrice0.getText().toString().trim())) {
                inPrice0 = 0;
            } else {
                inPrice0 = Double.parseDouble(etInPrice0.getText().toString().trim());
            }
        }

        if (TextUtils.isEmpty(etSalePrice0.getText().toString().trim())) {
            showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("retailPrice"));
            return;
        }
        salePrice0 = Double.parseDouble(etSalePrice0.getText().toString().trim());
        if (salePrice0 == 0) {
            showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("retailPrice"));
            return;
        }
        if (TextUtils.isEmpty(etMemberPrice0.getText().toString().trim())) {
            memberPrice0 = 0;
        } else {
            memberPrice0 = Double.parseDouble(etMemberPrice0.getText().toString().trim());
        }
        if (TextUtils.isEmpty(etOnlinePrice0.getText().toString().trim())) {
            onlinePrice0 = 0;
        } else {
            onlinePrice0 = Double.parseDouble(etOnlinePrice0.getText().toString().trim());
        }
        if (TextUtils.isEmpty(etStartOrder0.getText().toString().trim())) {
            startOrder0 = 0;
        } else {
            startOrder0 = Double.parseDouble(etStartOrder0.getText().toString().trim());
        }
        specs0 = etSpecs0.getText().toString().trim();

        //批发
//        wholesaleCount0 = TextUtils.isEmpty(etWholesaleCount0.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesaleCount0.getText().toString().trim());
//        wholesalePrice0 = TextUtils.isEmpty(etWholesalePrice0.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesalePrice0.getText().toString().trim());
//        if (wholesalePriceStatus0 == 1 && wholesaleCount0 == 0) {
//            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity"));
//            return;
//        }
//        if (wholesalePriceStatus0 == 1 && wholesalePrice0 == 0) {
//            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice"));
//            return;
//        }

        List wholesaleArray0 = new ArrayList();
        if (wholesalePriceStatus0 == 1) {
            String toast0 = "";
            for (int i = 0; i < wholesaleList0.size(); i++) {
                if (wholesaleList0.get(i).getWholesaleCount() == 0) {
                    toast0 = getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity");
                    break;
                }
                if (wholesaleList0.get(i).getWholesalePrice() == 0) {
                    toast0 = getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice");
                    break;
                }
            }
            if (!TextUtils.isEmpty(toast0)) {
                showMessage(toast0);
                return;
            }
            if (isHaveWholesaleList(wholesaleList0, GoodsWholesaleData::getWholesaleCount)) {
                showMessage(getLanguageValue("startBatchCannotBeRepeated"));
                return;
            }
            for (int i = 0; i < wholesaleList0.size(); i++) {
                Map object = new HashMap();
                object.put("wholesaleCount", wholesaleList0.get(i).getWholesaleCount());
                object.put("wholesalePrice", wholesaleList0.get(i).getWholesalePrice());
                wholesaleArray0.add(object);
            }
        }
        //划线单价
        underLinePrice0 = TextUtils.isEmpty(etUnderlinePrice0.getText().toString().trim()) ? 0 : Double.parseDouble(etUnderlinePrice0.getText().toString().trim());
        Map object0 = new HashMap();
        if (type == 1 && goodsId0 != 0) {
            object0.put("goodsId", goodsId0);
        }
        object0.put("goodsPicturePath", img0);
        object0.put("goodsBarcode", code0);
        object0.put("goodsName", name0);
        object0.put("goodsInPrice", inPrice0);
        object0.put("goodsSalePrice", salePrice0);
        object0.put("goodsWebSalePrice", onlinePrice0);
        object0.put("goodsUnit", unit0);
        object0.put("goodsCusPrice", memberPrice0);
        object0.put("goodsContain", 1);
        object0.put("goodsStandard", specs0);
        object0.put("minSaleCount", startOrder0);
        object0.put("shelfState", appletStatus0);//线上上架状态：1、已上架；2、已下架
        object0.put("pcShelfState", printerStatus0);//pc收银上架状态：1、已上架；2、已下架
        object0.put("wholesalePriceFlg", wholesalePriceStatus0);//是否设置批发价
        if (wholesalePriceStatus0 == 1) {
//            object0.put("wholesaleCount", wholesaleCount0);//起批数量
//            object0.put("wholesalePrice", wholesalePrice0);//批发单价
            object0.put("wholesaleList", wholesaleArray0);
        }
        object0.put("goodsPosition", goodsPosition0);//商品货位
        object0.put("underlinedPrice", underLinePrice0);//划线单价
        array.add(object0);

        /*中间包装*/
        code1 = etBarcode1.getText().toString().trim();
        name1 = etName1.getText().toString().trim();
        if (!TextUtils.isEmpty(etInPrice1.getText().toString().trim())) {
            inPrice1 = Double.parseDouble(etInPrice1.getText().toString().trim());
        } else {
            inPrice1 = 0;
        }
        if (!TextUtils.isEmpty(etSalePrice1.getText().toString().trim())) {
            salePrice1 = Double.parseDouble(etSalePrice1.getText().toString().trim());
        } else {
            salePrice1 = 0;
        }
        if (!TextUtils.isEmpty(etOnlinePrice1.getText().toString().trim())) {
            onlinePrice1 = Double.parseDouble(etOnlinePrice1.getText().toString().trim());
        } else {
            onlinePrice1 = 0;
        }
        if (!TextUtils.isEmpty(etMemberPrice1.getText().toString().trim())) {
            memberPrice1 = Double.parseDouble(etMemberPrice1.getText().toString().trim());
        } else {
            memberPrice1 = 0;
        }
        if (!TextUtils.isEmpty(etCount1.getText().toString().trim())) {
            count1 = Integer.parseInt(etCount1.getText().toString().trim());
        } else {
            count1 = 0;
        }
        specs1 = etSpecs1.getText().toString().trim();
        if (TextUtils.isEmpty(etStartOrder1.getText().toString().trim())) {
            startOrder1 = 0;
        } else {
            startOrder1 = Double.parseDouble(etStartOrder1.getText().toString().trim());
        }
//        wholesaleCount1 = TextUtils.isEmpty(etWholesaleCount1.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesaleCount1.getText().toString().trim());
//        wholesalePrice1 = TextUtils.isEmpty(etWholesalePrice1.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesalePrice1.getText().toString().trim());
        underLinePrice1 = TextUtils.isEmpty(etUnderlinePrice1.getText().toString().trim()) ? 0 : Double.parseDouble(etUnderlinePrice1.getText().toString().trim());
        List wholesaleArray1 = new ArrayList();
        if (wholesalePriceStatus1 == 1) {
            String toast1 = "";
            for (int i = 0; i < wholesaleList1.size(); i++) {
                if (wholesaleList1.get(i).getWholesaleCount() == 0) {
                    toast1 = getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity");
                    break;
                }
                if (wholesaleList1.get(i).getWholesalePrice() == 0) {
                    toast1 = getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice");
                    break;
                }
            }
            if (!TextUtils.isEmpty(toast1)) {
                showMessage(toast1);
                return;
            }
            if (isHaveWholesaleList(wholesaleList1, GoodsWholesaleData::getWholesaleCount)) {
                showMessage(getLanguageValue("startBatchCannotBeRepeated"));
                return;
            }
            for (int i = 0; i < wholesaleList1.size(); i++) {
                Map object = new HashMap();
                object.put("wholesaleCount", wholesaleList1.get(i).getWholesaleCount());
                object.put("wholesalePrice", wholesaleList1.get(i).getWholesalePrice());
                wholesaleArray1.add(object);
            }
        }

        //必选项有一个则判断
        if (!TextUtils.isEmpty(code1) || !TextUtils.isEmpty(name1) || !TextUtils.isEmpty(unit1)
                || count1 > 0 || inPrice1 > 0 || salePrice1 > 0 || wholesalePriceStatus1 == 1) {
            if (TextUtils.isEmpty(code1)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("intermediatePackaging") + getLanguageValue("commodity") + getLanguageValue("barcode"));
                return;
            }
            if (TextUtils.isEmpty(name1)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("intermediatePackaging") + getLanguageValue("commodity") + getLanguageValue("name"));
                return;
            }
            if (TextUtils.isEmpty(unit1)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("intermediatePackaging") + getLanguageValue("commodity") + getLanguageValue("unit"));
                return;
            }
            if (count1 <= 1) {
                showMessage(getLanguageValue("minThan1"));
                return;
            }
            if (!isSpecs1) {
                if (inPrice1 == 0) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("intermediatePackaging") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
                    return;
                }
            }
            if (salePrice1 == 0) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("intermediatePackaging") + getLanguageValue("retailPrice"));
                return;
            }
//            if (wholesaleCount1 == 0 && wholesalePriceStatus1 == 1) {
//                showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("intermediatePackaging") + getLanguageValue("startingQuantity"));
//                return;
//            }
//            if (wholesalePrice1 == 0 && wholesalePriceStatus1 == 1) {
//                showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("intermediatePackaging") + getLanguageValue("wholesaleUnitPrice"));
//                return;
//            }

            Map object1 = new HashMap();
            if (type == 1 && goodsId1 != 0) {
                object1.put("goodsId", goodsId1);
            }
            object1.put("goodsPicturePath", img1);
            object1.put("goodsBarcode", code1);
            object1.put("goodsName", name1);
            object1.put("goodsInPrice", inPrice1);
            object1.put("goodsSalePrice", salePrice1);
            object1.put("goodsWebSalePrice", onlinePrice1);
            object1.put("goodsUnit", unit1);
            object1.put("goodsCusPrice", memberPrice1);
            object1.put("goodsContain", count1);
            object1.put("goodsStandard", specs1);
            object1.put("minSaleCount", startOrder1);
            object1.put("shelfState", appletStatus1);//线上上架状态：1、已上架；2、已下架
            object1.put("pcShelfState", printerStatus1);//pc收银上架状态：1、已上架；2、已下架
            object1.put("wholesalePriceFlg", wholesalePriceStatus1);//是否设置批发价
            if (wholesalePriceStatus1 == 1) {
//                object1.put("wholesaleCount", wholesaleCount1);//起批数量
//                object1.put("wholesalePrice", wholesalePrice1);//批发单价
                object1.put("wholesaleList", wholesaleArray1);
            }
            object1.put("goodsPosition", goodsPosition1);//商品货位
            object0.put("underlinedPrice", underLinePrice1);//划线单价
            array.add(object1);
        }
        /*最大包装*/
        code2 = etBarcode2.getText().toString().trim();
        name2 = etName2.getText().toString().trim();
        if (!TextUtils.isEmpty(etInPrice2.getText().toString().trim())) {
            inPrice2 = Double.parseDouble(etInPrice2.getText().toString().trim());
        } else {
            inPrice2 = 0;
        }
        if (!TextUtils.isEmpty(etSalePrice2.getText().toString().trim())) {
            salePrice2 = Double.parseDouble(etSalePrice2.getText().toString().trim());
        } else {
            salePrice2 = 0;
        }
        if (!TextUtils.isEmpty(etOnlinePrice2.getText().toString().trim())) {
            onlinePrice2 = Double.parseDouble(etOnlinePrice2.getText().toString().trim());
        } else {
            onlinePrice2 = 0;
        }
        if (!TextUtils.isEmpty(etMemberPrice2.getText().toString().trim())) {
            memberPrice2 = Double.parseDouble(etMemberPrice2.getText().toString().trim());
        } else {
            memberPrice2 = 0;
        }
        if (!TextUtils.isEmpty(etCount2.getText().toString().trim())) {
            count2 = Integer.parseInt(etCount2.getText().toString().trim());
        } else {
            count2 = 0;
        }
        specs2 = etSpecs2.getText().toString().trim();
        if (TextUtils.isEmpty(etStartOrder2.getText().toString().trim())) {
            startOrder2 = 0;
        } else {
            startOrder2 = Double.parseDouble(etStartOrder2.getText().toString().trim());
        }
//        wholesaleCount2 = TextUtils.isEmpty(etWholesaleCount2.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesaleCount2.getText().toString().trim());
//        wholesalePrice2 = TextUtils.isEmpty(etWholesalePrice2.getText().toString().trim()) ? 0 : Double.parseDouble(etWholesalePrice2.getText().toString().trim());
        List wholesaleArray2 = new ArrayList();
        if (wholesalePriceStatus2 == 1) {
            String toast2 = "";
            for (int i = 0; i < wholesaleList2.size(); i++) {
                if (wholesaleList2.get(i).getWholesaleCount() == 0) {
                    toast2 = getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity");
                    break;
                }
                if (wholesaleList2.get(i).getWholesalePrice() == 0) {
                    toast2 = getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice");
                    break;
                }
            }
            if (!TextUtils.isEmpty(toast2)) {
                showMessage(toast2);
                return;
            }
            if (isHaveWholesaleList(wholesaleList2, GoodsWholesaleData::getWholesaleCount)) {
                showMessage(getLanguageValue("startBatchCannotBeRepeated"));
                return;
            }
            for (int i = 0; i < wholesaleList2.size(); i++) {
                if (wholesaleList2.get(i).getWholesaleCount() > 0 && wholesaleList2.get(i).getWholesalePrice() > 0) {
                    Map object = new HashMap();
                    object.put("wholesaleCount", wholesaleList2.get(i).getWholesaleCount());
                    object.put("wholesalePrice", wholesaleList2.get(i).getWholesalePrice());
                    wholesaleArray2.add(object);
                }
            }
        }

        underLinePrice2 = TextUtils.isEmpty(etUnderlinePrice2.getText().toString().trim()) ? 0 : Double.parseDouble(etUnderlinePrice2.getText().toString().trim());
        //判断几个必选项是否全都不为空
        if (!TextUtils.isEmpty(code2) || !TextUtils.isEmpty(name2) || !TextUtils.isEmpty(unit2)
                || count2 > 0 || inPrice2 > 0 || salePrice2 > 0 || wholesalePriceStatus2 == 1) {
            if (TextUtils.isEmpty(code2)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("commodity") + getLanguageValue("barcode"));
                return;
            }
            if (TextUtils.isEmpty(name2)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("commodity") + getLanguageValue("name"));
                return;
            }
            if (TextUtils.isEmpty(unit2)) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("commodity") + getLanguageValue("unit"));
                return;
            }
            if (count2 <= count1) {
                showMessage(getLanguageValue("minUnitThanMiddle"));
                return;
            }
            if (!isSpecs2) {
                if (inPrice2 == 0) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
                    return;
                }
            }
            if (salePrice2 == 0) {
                showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("retailPrice"));
                return;
            }
//            if (wholesaleCount2 == 0 && wholesalePriceStatus2 == 1) {
//                showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("startingQuantity"));
//                return;
//            }
//            if (wholesalePrice2 == 0 && wholesalePriceStatus2 == 1) {
//                showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("max") + getLanguageValue("package") + getLanguageValue("wholesaleUnitPrice"));
//                return;
//            }
            Map object2 = new HashMap();
            if (type == 1 && goodsId2 != 0) {
                object2.put("goodsId", goodsId2);
            }
            object2.put("goodsPicturePath", img2);
            object2.put("goodsBarcode", code2);
            object2.put("goodsName", name2);
            if (!isSpecs2) {
                object2.put("goodsInPrice", inPrice2);
            }
            object2.put("goodsSalePrice", salePrice2);
            object2.put("goodsWebSalePrice", onlinePrice2);
            object2.put("goodsUnit", unit2);
            object2.put("goodsCusPrice", memberPrice2);
            object2.put("goodsContain", count2);
            object2.put("goodsStandard", specs2);
            object2.put("minSaleCount", startOrder2);
            object2.put("shelfState", appletStatus2);//线上上架状态：1、已上架；2、已下架
            object2.put("pcShelfState", printerStatus2);//pc收银上架状态：1、已上架；2、已下架
            object2.put("wholesalePriceFlg", wholesalePriceStatus2);//是否设置批发价
            if (wholesalePriceStatus2 == 1) {
//                object2.put("wholesaleCount", wholesaleCount2);//起批数量
//                object2.put("wholesalePrice", wholesalePrice2);//批发单价
                object2.put("wholesaleList", wholesaleArray2);
            }
            object2.put("goodsPosition", goodsPosition2);//商品货位
            object0.put("underlinedPrice", underLinePrice2);//划线单价
            array.add(object2);
        }
        Log.e(tag, "array = " + array);
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        if (type == 1) {
            //编辑
            url = ZURL.getGoodsEdit();
        } else {
            //新增
            url = ZURL.getGoodsAdd();
            map.put("goodsCount", goodsStock);//库存
            map.put("goodsProd", goodsDate);//生产日期
        }
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);//供货商编号
        if (TextUtils.isEmpty(cateUnique2)) {
            map.put("goodsKindUnique", cateUnique1);//二级分类编号
        } else {
            map.put("goodsKindUnique", cateUnique2);//二级分类编号
        }
        map.put("goodsChengType", chengType);//计价类型 0.计件 1.称重
        map.put("goodsBrand", brand);//品牌
        map.put("foreignKey", code0);//包装外键（最小规格的商品条码）
        map.put("goodsLife", goodsLife);//保质期
        //库存预警
        if (warningStatus == 1) {
            map.put("stockWarningStatus", warningStatus);
            map.put("outStockWaringCount", warningLow);
            map.put("unsalableCount", warningTall);
        }
        map.put("goodsMessage", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        if (addType == 1) {
                            finish();
                        } else {
                            setUIClear(-1);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /*****************************接口交互end*****************************/

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.BLUETOOTH:
                    //蓝牙返回mac地址
                    printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
                    setPrint();
                    break;
                case Constants.SCAN:
                    //扫码
                    getGoodsInfoScan(data.getStringExtra("result"));
                    break;
                case Constants.CHOOSE_UNIT:
                    //选择单位
                    String id = data.getStringExtra("id"),
                            name = data.getStringExtra("name");
                    switch (unitType) {
                        case 1:
                            unit1 = name;
                            tvUnit1.setText(name);
                            break;
                        case 2:
                            unit2 = name;
                            tvUnit2.setText(name);
                            break;
                        default:
                            unit0 = name;
                            tvUnit0.setText(name);
                            break;
                    }
                    break;
                case Constants.CHOOSE_LOCATION:
                    //商品货位
                    String ids = data.getStringExtra("ids"),
                            names = data.getStringExtra("names");
                    switch (locationType) {
                        case 1:
                            goodsPosition1 = ids;
                            completePositionName1 = names;
                            tvLocation1.setText(completePositionName1);
                            break;
                        case 2:
                            goodsPosition2 = ids;
                            completePositionName2 = names;
                            tvLocation2.setText(completePositionName2);
                            break;
                        default:
                            goodsPosition0 = ids;
                            completePositionName0 = names;
                            tvLocation0.setText(completePositionName0);
                            break;
                    }

                    break;
            }
        }
    }
}
