package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * 经营统计-热销商品TOP5（实体类）
 */
public class TopFiveBean {
    /**
     * saleCountPre : -76.8
     * saleCount : 133.02
     * totalPre : 32.76
     * goodsName : 蔬菜
     * goodsBarcode : 2200001
     */

    private double saleCountPre;
    private double saleCount;
    private double totalPre;
    private String goodsName;
    private String goodsBarcode;

    public double getSaleCountPre() {
        return saleCountPre;
    }

    public void setSaleCountPre(double saleCountPre) {
        this.saleCountPre = saleCountPre;
    }

    public double getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(double saleCount) {
        this.saleCount = saleCount;
    }

    public double getTotalPre() {
        return totalPre;
    }

    public void setTotalPre(double totalPre) {
        this.totalPre = totalPre;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }
}