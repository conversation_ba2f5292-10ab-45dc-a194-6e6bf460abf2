package cn.bl.mobile.buyhoostore_sea.ui.farm.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseFragment;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmMsgAdapter;

/**
 * Describe:农批-消息
 * Created by jingang on 2023/6/12
 */
@SuppressLint("NonConstantResourceId")
public class FarmMsgFragment extends BaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private FarmMsgAdapter mAdapter;

    /**
     * 初始化fragment
     *
     * @return
     */
    public static FarmMsgFragment newInstance() {
        FarmMsgFragment fragment = new FarmMsgFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fm_forms_msg;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new FarmMsgAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishLoadMore();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishRefresh();
            }
        });
        mAdapter.setOnItemClickListener((view, position) -> {

        });
    }
}
