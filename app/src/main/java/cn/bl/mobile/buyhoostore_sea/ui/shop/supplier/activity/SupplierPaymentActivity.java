package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.activity.GouXInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.dialog.PayVoucherAddDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierGouXAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierUnpaidGouXData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-供货商端丽-详情-还款
 * Created by jingang on 2023/9/4
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierPaymentActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvCount)
    TextView tvCount;
    @BindView(R.id.tvMoney)
    TextView tvMoney;
    @BindView(R.id.tvYuanValue)
    TextView tvYuanValue;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.ivAll)
    ImageView ivAll;
    @BindView(R.id.tvAll)
    TextView tvAll;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String supplierUnique;
    private int count;
    private double total;
    private List array;
    private boolean isAll;

    private SupplierGouXAdapter mAdapter;
    private List<GouXOrderListData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier_payment;
    }

    @Override
    public void initViews() {
        supplierUnique = getIntent().getStringExtra("unique");
        tvTitle.setText(getLanguageValue("repaymentAndSettlementByOrder"));
        tvAll.setText(getLanguageValue("whole") + getLanguageValue("choose"));
        tvConfirm.setText(getLanguageValue("confirm") + getLanguageValue("repayment"));
        setAdapter();
    }

    @Override
    public void initData() {
        getOrderList();
    }

    @OnClick({R.id.ivBack, R.id.ivAll, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivAll:
                //全选
                isAll = !isAll;
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isCheck() != isAll) {
                        dataList.get(i).setCheck(isAll);
                        mAdapter.notifyItemChanged(i, dataList.get(i));
                    }
                }
                isAll();
                break;
            case R.id.tvConfirm:
                //确认还款
                if (count < 1) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("connodity"));
                    return;
                }
                PayVoucherAddDialog.showDialog(this, this::postOrderRepayment);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new SupplierGouXAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setType(1);
        mAdapter.setListener(new SupplierGouXAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(TAG, GouXInfoActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getId()))
                );
            }

            @Override
            public void onCheckClick(View view, int position) {
                //选择
                dataList.get(position).setCheck(!dataList.get(position).isCheck());
                mAdapter.notifyItemChanged(position, dataList.get(position));
                isAll();
            }

            @Override
            public void onKefuClick(View view, int position) {
                //打电话
                mobile = dataList.get(position).getSupplierPhone();
                if (PermissionUtils.checkPermissionsGroup(TAG, 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(TAG, Constants.PERMISSION, 4);
                }
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getOrderList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 是否全选
     */
    private void isAll() {
        for (int i = 0; i < dataList.size(); i++) {
            boolean isSelect = dataList.get(i).isCheck();
            if (!isSelect) {
                isAll = false;
                break;//根据标记，跳出嵌套循环
            } else {
                isAll = true;
            }
        }
        if (isAll) {
            ivAll.setImageResource(R.mipmap.ic_chosen001);
        } else {
            ivAll.setImageResource(R.mipmap.ic_chose001);
        }

        count = 0;
        total = 0;
        array = new ArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            if (dataList.get(i).isCheck()) {
                count++;
                total = total + dataList.get(i).getTotalPrice();
                try {
                    array.add(dataList.get(i).getId());
                } catch (Exception ignored) {
                }
            }
        }
        if (count > 0) {
            tvTotal.setVisibility(View.VISIBLE);
            tvTotal.setText(getLanguageValue("selected") + count
                    + getLanguageValue("ones") + getLanguageValue("order") + ","
                    + getLanguageValue("totalArrears") + "RM" + DFUtils.getNum2(total));
        } else {
            tvTotal.setVisibility(View.GONE);
        }
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this,
                getLanguageValue("confirmCall") + ":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    /**
     * 未还款购销单列表
     */
    private void getOrderList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoUnpaidGouXList(),
                map,
                SupplierUnpaidGouXData.class,
                new RequestListener<SupplierUnpaidGouXData>() {
                    @Override
                    public void success(SupplierUnpaidGouXData data) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        tvCount.setText(getLanguageValue("aTotalOf") + " " + data.getOutstandingCount()
                                + " " + getLanguageValue("ones") + getLanguageValue("batches")
                                + " " + getLanguageValue("totalArrears"));
                        tvMoney.setText(DFUtils.getNum2(data.getPurchaseAmounts()));
                        dataList.clear();
                        dataList.addAll(data.getBillList());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            isAll();
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 还款
     *
     * @param money
     * @param remarks
     * @param arrayImg
     */
    private void postOrderRepayment(double money, String remarks, List arrayImg) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("supplierUnique", supplierUnique);
        map.put("billIdList", array);
        map.put("paymentMoney", money);
        map.put("remark", remarks);
        map.put("voucherPicturepath", arrayImg);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoRepayment(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(getLanguageValue("repayment") + getLanguageValue("succeed"));
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    callPhone(mobile);
                }
                break;
        }
    }
}
