package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:供货商详情-所供商品（适配器）
 * Created by jingang on 2023/9/4
 */
public class SupplierGoodsAdapter extends BaseAdapter<GoodsData> {
    private int type;//0.未建档 1.已建档

    public void setType(int type) {
        this.type = type;
    }

    public SupplierGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivPrinter, ivApplet, ivMore;
        ivImg = holder.getView(R.id.ivItemImg);
        ivPrinter = holder.getView(R.id.ivItemPrinter);
        ivApplet = holder.getView(R.id.ivItemApplet);
        ivMore = holder.getView(R.id.ivItemMore);
        TextView tvName, tvBarcode, tvPrice_in, tvPriceType, tvPrice_sale, tvCount, tvKucun, tvConfirm,tvInPriceValue;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice_in = holder.getView(R.id.tvItemPrice_in);
        tvPriceType = holder.getView(R.id.tvItemPriceType);
        tvPrice_sale = holder.getView(R.id.tvItemPrice_sale);
        tvCount = holder.getView(R.id.tvItemCount);
        tvKucun = holder.getView(R.id.tvItemKucun);
        tvConfirm = holder.getView(R.id.tvItemConfirm);
        LinearLayout linInfo = holder.getView(R.id.linItemInfo);
        tvInPriceValue = holder.getView(R.id.tvInPriceValue);
        tvInPriceValue.setText(getLanguageValue("purchasePrice")+":");

        String img;
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsPicturePath())) {
            img = StringUtils.handledImgUrl(mDataList.get(position).getGoodsImageUrl());
        } else {
            img = StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath());
        }
        Glide.with(mContext)
                .load(img)
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        if (type == 1) {
            //已建档
            tvBarcode.setVisibility(View.GONE);
            linInfo.setVisibility(View.VISIBLE);
            tvPriceType.setText(getLanguageValue("sellingPrice")+":");
            ivMore.setVisibility(View.VISIBLE);
            tvConfirm.setVisibility(View.GONE);
            //收银机上架状态
            if (mDataList.get(position).getPcShelfState() == 1) {
                ivPrinter.setImageResource(R.mipmap.ic_printer001);
            } else {
                ivPrinter.setImageResource(R.mipmap.ic_printer002);
            }
            //小程序上架状态
            if (mDataList.get(position).getShelfState() == 1) {
                ivApplet.setImageResource(R.mipmap.ic_applet001);
            } else {
                ivApplet.setImageResource(R.mipmap.ic_applet002);
            }
            tvCount.setText(getLanguageValue("month")+getLanguageValue("salesVolume")+":" + DFUtils.getNum(mDataList.get(position).getSaleCount()));
            tvKucun.setText(getLanguageValue("inventory")+":" + DFUtils.getNum2(mDataList.get(position).getGoodsCount()));
            tvPrice_in.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
            tvPrice_sale.setText(DFUtils.getNum2(mDataList.get(position).getGoodsSalePrice()));
        } else {
            tvBarcode.setVisibility(View.VISIBLE);
            linInfo.setVisibility(View.GONE);
            tvPriceType.setText(getLanguageValue("suppestPrice")+":");
            ivMore.setVisibility(View.GONE);
            tvConfirm.setVisibility(View.VISIBLE);
            tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
            tvPrice_sale.setText(DFUtils.getNum2(mDataList.get(position).getGoodsSalePriceUndoc()));
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(holder.itemView, position));
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
            tvConfirm.setOnClickListener(v -> listener.onConfirmClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onMoreClick(View view, int position);

        void onConfirmClick(View view, int position);
    }
}
