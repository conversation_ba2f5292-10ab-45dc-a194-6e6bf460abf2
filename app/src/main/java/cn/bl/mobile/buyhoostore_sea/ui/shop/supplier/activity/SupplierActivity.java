package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment.SupplierApplyFragment;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment.SupplierFragment;

/**
 * Describe:店铺-供货商管理
 * Created by jingang on 2023/9/2
 */
@SuppressLint("NonConstantResourceId")
public class SupplierActivity extends BaseActivity {
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvCate)
    TextView tvCate;
    @BindView(R.id.tabLayout)
    TabLayout tabLayout;
    @BindView(R.id.viewPager)
    ViewPager viewPager;

    public static String keyWords;

    private String[] titles = new String[]{getLanguageValue("supplier"), getLanguageValue("supplier"+getLanguageValue("apply"))};
    private SimpleFragmentPagerAdapter sfpAdapter;
    private List<Fragment> fragmentList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier;
    }

    @Override
    public void initViews() {
        etSearch.setHint(getLanguageValue("supplier")+getLanguageValue("name")+"/"+getLanguageValue("phoneNum"));
        tvCate.setText(getLanguageValue("classification")+getLanguageValue("administration"));
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setFragment();
    }

    @Override
    public void initData() {
    }

    @OnClick({R.id.ivBack, R.id.ivClear, R.id.tvCate})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除搜索
                etSearch.setText("");
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                break;
            case R.id.tvCate:
                //分类管理
                goToActivity(SupplierCateActivity.class);
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        keyWords = "";
        fragmentList.clear();
        fragmentList.add(SupplierFragment.newInstance());
        fragmentList.add(SupplierApplyFragment.newInstance());
        sfpAdapter = new SimpleFragmentPagerAdapter(getSupportFragmentManager(), fragmentList, titles);
        viewPager.setAdapter(sfpAdapter);
        viewPager.setCurrentItem(0);
        viewPager.setOffscreenPageLimit(titles.length);
        tabLayout.setupWithViewPager(viewPager);
    }
}
