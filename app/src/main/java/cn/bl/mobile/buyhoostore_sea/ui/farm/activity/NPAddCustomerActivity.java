package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.king.keyboard.KingKeyboard;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.Validation;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.utils.KeyBoardUtils;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
public class NPAddCustomerActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.nameET)
    EditText nameET;
    @BindView(R.id.phoneET)
    EditText phoneET;
    @BindView(R.id.keyboardParent)
    LinearLayout keyboardParent;
    KingKeyboard kingKeyboard;
    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_add_customer;
    }

    @Override
    public void initViews() {
        tvTitle.setText("添加客户");
        kingKeyboard = new KingKeyboard(NPAddCustomerActivity.this, keyboardParent);
        kingKeyboard.register(phoneET,KingKeyboard.KeyboardType.NUMBER);
    }
    private  void submit(){

        if (TextUtils.isEmpty(nameET.getText())) {
            ToastUtil.showToast(this,"请输入姓名");
            return;
        }
        if (TextUtils.isEmpty(phoneET.getText())) {
            ToastUtil.showToast(this,"请输入手机号");
            return;
        }
        if (!Validation.isPhone(phoneET.getText().toString()) || !Validation.isPhone1(phoneET.getText().toString())) {
            ToastUtil.showToast(this,"手机号格式错误");
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("cusName", nameET.getText());
        map.put("cusPhone", phoneET.getText());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAddCusNP(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        ToastUtil.showToast(NPAddCustomerActivity.this,"添加成功");
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFRESH_MEMBER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        kingKeyboard.onResume();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        kingKeyboard.onDestroy();
        KeyBoardUtils.closeKeyboard(phoneET);
        KeyBoardUtils.closeKeyboard(nameET);

    }

    @OnClick({R.id.saveTV,R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.saveTV:
                submit();

                break;

        }
    }

}
