package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;

/**
 * Describe:店铺-聚合码-申请开通状态
 * Created by jingang on 2023/5/9
 */
@SuppressLint("NonConstantResourceId")
public class AggregationApplyStateActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivStatus)
    ImageView ivStatus;
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvMsg)
    TextView tvMsg;
    @BindView(R.id.linMsg)
    LinearLayout linMsg;
    @BindView(R.id.tvMsgValue)
    TextView tvMsgValue;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;
    @BindView(R.id.tvLook)
    TextView tvLook;

    private int status,// -1.审核提交成功 1.审核中 3.审核失败
            applyType;//申请人类型0未知1法人申请2非法人申请
    private String reason,//审核失败原因
            phone = "400-7088-365";

    @Override
    protected int getLayoutId() {
        return R.layout.activity_aggregation_apply_state;
    }

    @Override
    public void initViews() {
        tvMsgValue.setText(getLanguageValue("bePatient"));
        status = getIntent().getIntExtra("status", -1);
        reason = getIntent().getStringExtra("reason");
        applyType = getIntent().getIntExtra("applyType", 1);
        setUI();
    }

    @OnClick({R.id.ivBack, R.id.tvKefu, R.id.tvConfirm, R.id.tvLook})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvKefu:
                //联系客服
                if (PermissionUtils.checkPermissionsGroup(this, 4)) {
                    callPhone(phone);
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 4);
                }
                break;
            case R.id.tvConfirm:
                if (status == 3) {
                    //去修改
                    if (applyType == 1) {
                        startActivityForResult(new Intent(this, AggregationApplyActivity0.class)
                                , Constants.CREDIT);
                    } else {
                        startActivityForResult(new Intent(this, AggregationApplyActivity1.class)
                                        .putExtra("guideImage", getIntent().getStringExtra("guideImage"))
                                        .putExtra("helibao", getIntent().getStringExtra("helibao"))
                                        .putExtra("ruiyinxin", getIntent().getStringExtra("ruiyinxin"))
                                , Constants.CREDIT);
                    }
                } else {
                    //好的
                    this.finish();
                }
                break;
            case R.id.tvLook:
                //查看资料信息
                if (applyType == 1) {
                    startActivityForResult(new Intent(this, AggregationApplyActivity0.class)
                            .putExtra("status", 1)
                    ,Constants.CREDIT);
                } else {
                    startActivityForResult(new Intent(this, AggregationApplyActivity1.class)
                            .putExtra("status", 1)
                            .putExtra("guideImage", getIntent().getStringExtra("guideImage"))
                            .putExtra("helibao", getIntent().getStringExtra("helibao"))
                            .putExtra("ruiyinxin", getIntent().getStringExtra("ruiyinxin"))
                    ,Constants.CREDIT);
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        switch (status) {
            case -1:
                tvTitle.setText(getLanguageValue("aduitSubmitSuccess"));
                ivStatus.setImageResource(R.mipmap.ic_credit_state001);
                tvStatus.setText(getLanguageValue("aggregationCodeSubmitSuccess"));
                tvMsg.setVisibility(View.VISIBLE);
                linMsg.setVisibility(View.GONE);
                tvMsg.setText(getLanguageValue("contactYour"));
                tvConfirm.setText(getLanguageValue("allRight"));
                tvLook.setVisibility(View.VISIBLE);
                break;
            case 1:
                tvTitle.setText(getLanguageValue("auditWait"));
                ivStatus.setImageResource(R.mipmap.ic_credit_state002);
                tvStatus.setText(getLanguageValue("aggregationCodeAuditing"));
                tvMsg.setVisibility(View.GONE);
                linMsg.setVisibility(View.VISIBLE);
                tvConfirm.setText(getLanguageValue("allRight"));
                tvLook.setVisibility(View.VISIBLE);
                break;
            case 3:
                tvTitle.setText(getLanguageValue("audit")+getLanguageValue("rejected"));
                ivStatus.setImageResource(R.mipmap.ic_credit_state003);
                tvStatus.setText(getLanguageValue("audit")+getLanguageValue("rejected"));
                tvMsg.setVisibility(View.VISIBLE);
                linMsg.setVisibility(View.GONE);
                tvMsg.setText(reason);
                tvConfirm.setText(getLanguageValue("modification"));
                tvLook.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this,
                getLanguageValue("confirmCall")+":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == Constants.PERMISSION) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                callPhone(phone);
            } else {
                showMessage(getLanguageValue("requiredCallPermission"));
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constants.CREDIT) {
            finish();
        }
    }
}
