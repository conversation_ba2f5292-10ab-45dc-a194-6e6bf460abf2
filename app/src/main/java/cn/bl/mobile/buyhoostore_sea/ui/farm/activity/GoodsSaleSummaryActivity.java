package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.GoodsSaleSummaryAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.GoodsSaleData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-报表-货品销售汇总
 * Created by jingang on 2023/5/28
 */
@SuppressLint("NonConstantResourceId")
public class GoodsSaleSummaryActivity extends BaseActivity {
    @BindView(R.id.tvDay0)
    TextView tvDay0;
    @BindView(R.id.tvDay1)
    TextView tvDay1;
    @BindView(R.id.tvDay2)
    TextView tvDay2;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvSaleSum)
    TextView tvSaleSum;
    @BindView(R.id.tvChengCount)
    TextView tvChengCount;
    @BindView(R.id.tvStandardCount)
    TextView tvStandardCount;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int day;//0.今日 1.近7日 2.近14日
    private int type;//业务类型 0.全部 1.自营 2.代卖
    private String keywords,
            startTime = DateUtils.getOldDate(0),
            endTime = DateUtils.getOldDate(0);

    private GoodsSaleSummaryAdapter mAdapter;
    private List<GoodsSaleData.DataBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_sale_summary;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            page = 1;
            getGoodsSaleStatistics();
            return true;
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getGoodsSaleStatistics();
    }

    @OnClick({R.id.ivBack, R.id.tvDay0, R.id.tvDay1, R.id.tvDay2, R.id.tvScreen})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvDay0:
                //今日
                if (day != 0) {
                    day = 0;
                    tvDay0.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay1.setBackgroundResource(0);
                    tvDay2.setBackgroundResource(0);
                    startTime = DateUtils.getOldDate(0);
                    endTime = DateUtils.getOldDate(0);
                    page = 1;
                    getGoodsSaleStatistics();
                }
                break;
            case R.id.tvDay1:
                //近7日
                if (day != 1) {
                    day = 1;
                    tvDay1.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay0.setBackgroundResource(0);
                    tvDay2.setBackgroundResource(0);
//                    startTime = DateUtils.getOldDate(-7);
                    startTime = DateUtils.getWeekStartTime();
                    endTime = DateUtils.getOldDate(0);
                    page = 1;
                    getGoodsSaleStatistics();
                }
                break;
            case R.id.tvDay2:
                //近14日
                if (day != 2) {
                    day = 2;
                    tvDay2.setBackgroundResource(R.drawable.shape_33cc67_4);
                    tvDay1.setBackgroundResource(0);
                    tvDay0.setBackgroundResource(0);
                    startTime = DateUtils.getOldDate(-14);
                    endTime = DateUtils.getOldDate(0);
                    page = 1;
                    getGoodsSaleStatistics();
                }
                break;
            case R.id.tvScreen:
                //筛选
                showDialogScreen();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsSaleSummaryAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishLoadMore();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishRefresh();
            }
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsSaleData data) {
        if (data.getObject() != null) {
            tvSaleSum.setText(DFUtils.getNum2(data.getObject().getSaleSum()));
            tvChengCount.setText(DFUtils.getNum2(data.getObject().getChengSaleCount()));
            tvStandardCount.setText(DFUtils.getNum2(data.getObject().getStandardSaleCount()));
        }

        if (page == 1) {
            smartRefreshLayout.finishRefresh();
            dataList.clear();
        } else {
            smartRefreshLayout.finishLoadMore();
        }
        dataList.addAll(data.getData());
        if (dataList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            recyclerView.setVisibility(View.GONE);
            linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * dialog（订单筛选）
     */
    @SuppressLint("SetTextI18n")
    private void showDialogScreen() {
        TextView tvStartTime, tvEndTime, tvType0, tvType1, tvType2, tvBatch;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_screen_farm_goods_sale_summary, null);
        dialog.setContentView(view);

        tvStartTime = view.findViewById(R.id.tvDialogStartTime);
        tvEndTime = view.findViewById(R.id.tvDialogEndTime);
        tvType0 = view.findViewById(R.id.tvDialogType0);
        tvType1 = view.findViewById(R.id.tvDialogType1);
        tvType2 = view.findViewById(R.id.tvType2);
        tvBatch = view.findViewById(R.id.tvDialogBatch);

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());

        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());
        //开始时间
        tvStartTime.setOnClickListener(v -> {
            //开始时间
            DateStartEndDialog.showDialog(this,
                    startTime,
                    endTime,
                    startTime,
                    (startDate, endDate) -> {
                        startTime = startDate;
                        endTime = endDate;
                        tvStartTime.setText(startDate);
                        tvEndTime.setText(endDate);
                    });
        });
        //结束时间
        tvEndTime.setOnClickListener(v -> {
            DateStartEndDialog.showDialog(this,
                    startTime,
                    endTime,
                    startTime,
                    (startDate, endDate) -> {
                        startTime = startDate;
                        endTime = endDate;
                        tvStartTime.setText(startDate);
                        tvEndTime.setText(endDate);
                    });
        });
        tvType0.setOnClickListener(v -> {
            if (type != 0) {
                type = 0;
                tvType0.setBackgroundResource(R.drawable.shape_33cc67_4);
                tvType0.setTextColor(getResources().getColor(R.color.white));
                tvType1.setBackgroundResource(R.drawable.shape_f2_4);
                tvType1.setTextColor(getResources().getColor(R.color.color_333));
                tvType2.setBackgroundResource(R.drawable.shape_f2_4);
                tvType2.setTextColor(getResources().getColor(R.color.color_333));
            }
        });
        tvType1.setOnClickListener(v -> {
            if (type != 1) {
                type = 1;
                tvType1.setBackgroundResource(R.drawable.shape_33cc67_4);
                tvType1.setTextColor(getResources().getColor(R.color.white));
                tvType0.setBackgroundResource(R.drawable.shape_f2_4);
                tvType0.setTextColor(getResources().getColor(R.color.color_333));
                tvType2.setBackgroundResource(R.drawable.shape_f2_4);
                tvType2.setTextColor(getResources().getColor(R.color.color_333));
            }
        });
        tvType2.setOnClickListener(v -> {
            if (type != 2) {
                type = 2;
                tvType2.setBackgroundResource(R.drawable.shape_33cc67_4);
                tvType2.setTextColor(getResources().getColor(R.color.white));
                tvType1.setBackgroundResource(R.drawable.shape_f2_4);
                tvType1.setTextColor(getResources().getColor(R.color.color_333));
                tvType0.setBackgroundResource(R.drawable.shape_f2_4);
                tvType0.setTextColor(getResources().getColor(R.color.color_333));
            }
        });
        //选择批次
        tvBatch.setOnClickListener(v -> {

        });
        //重置
        view.findViewById(R.id.tvDialogResetting).setOnClickListener(v -> {
            dialog.dismiss();
        });
        //确认
        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> {
            dialog.dismiss();
        });
    }

    /**
     * 商品销售统计（货品销售汇总）
     */
    private void getGoodsSaleStatistics() {
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsMessage", keywords);
        if (!TextUtils.isEmpty(startTime)) {
            map.put("startTime", startTime);
        }
        if (!TextUtils.isEmpty(endTime)) {
            map.put("endTime", endTime);
        }
        map.put("pageNum", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getFarm_GoodsSaleStatistics(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        GoodsSaleData data = new Gson().fromJson(s, GoodsSaleData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setUI(data);
                        } else {
                            if (page == 1) {
                                smartRefreshLayout.finishRefresh();
                                dataList.clear();
                                mAdapter.clear();
                                recyclerView.setVisibility(View.GONE);
                                linEmpty.setVisibility(View.VISIBLE);
                            } else {
                                smartRefreshLayout.finishLoadMore();
                            }
                        }
                    }
                });
    }
}
