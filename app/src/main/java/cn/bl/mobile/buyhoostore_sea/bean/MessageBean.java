package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/10/18 0018.
 */
public class MessageBean {


    /**
     * status : 1
     * msg :
     * totals : 0
     * perPageNum : 0
     * data : [{"shopMsgRead":0,"shopMsgDate":"2017-09-19","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":14},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":12},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":13},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":15},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":16},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":17},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":18},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":19},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":20},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":21},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":22},{"shopMsgRead":0,"shopMsgDate":"0001-01-01","shopMsgInfo":"士大夫","shopMsgTitle":"店铺2","shopMsgId":23}]
     * supData : null
     * shopMembership : null
     * addMembership : null
     * heji : null
     */

    private int status;
    private String msg;
    private int totals;
    private int perPageNum;
    private Object supData;
    private Object shopMembership;
    private Object addMembership;
    private Object heji;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getTotals() {
        return totals;
    }

    public void setTotals(int totals) {
        this.totals = totals;
    }

    public int getPerPageNum() {
        return perPageNum;
    }

    public void setPerPageNum(int perPageNum) {
        this.perPageNum = perPageNum;
    }

    public Object getSupData() {
        return supData;
    }

    public void setSupData(Object supData) {
        this.supData = supData;
    }

    public Object getShopMembership() {
        return shopMembership;
    }

    public void setShopMembership(Object shopMembership) {
        this.shopMembership = shopMembership;
    }

    public Object getAddMembership() {
        return addMembership;
    }

    public void setAddMembership(Object addMembership) {
        this.addMembership = addMembership;
    }

    public Object getHeji() {
        return heji;
    }

    public void setHeji(Object heji) {
        this.heji = heji;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * shopMsgRead : 0
         * shopMsgDate : 2017-09-19
         * shopMsgInfo : 士大夫
         * shopMsgTitle : 店铺2
         * shopMsgId : 14
         */

        private int shopMsgRead;
        private String shopMsgDate;
        private String shopMsgInfo;
        private String shopMsgTitle;
        private int shopMsgId;
        private int time;
        private String timeType;
        private int shopMsgType;

        public int getTime() {
            return time;
        }

        public void setTime(int time) {
            this.time = time;
        }

        public String getTimeType() {
            return timeType;
        }

        public void setTimeType(String timeType) {
            this.timeType = timeType;
        }

        public int getShopMsgType() {
            return shopMsgType;
        }

        public void setShopMsgType(int shopMsgType) {
            this.shopMsgType = shopMsgType;
        }

        public int getShopMsgRead() {
            return shopMsgRead;
        }

        public void setShopMsgRead(int shopMsgRead) {
            this.shopMsgRead = shopMsgRead;
        }

        public String getShopMsgDate() {
            return shopMsgDate;
        }

        public void setShopMsgDate(String shopMsgDate) {
            this.shopMsgDate = shopMsgDate;
        }

        public String getShopMsgInfo() {
            return shopMsgInfo;
        }

        public void setShopMsgInfo(String shopMsgInfo) {
            this.shopMsgInfo = shopMsgInfo;
        }

        public String getShopMsgTitle() {
            return shopMsgTitle;
        }

        public void setShopMsgTitle(String shopMsgTitle) {
            this.shopMsgTitle = shopMsgTitle;
        }

        public int getShopMsgId() {
            return shopMsgId;
        }

        public void setShopMsgId(int shopMsgId) {
            this.shopMsgId = shopMsgId;
        }
    }
}
