package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:快读还款（适配器）
 * Created by jingang on 2023/5/30
 */
public class RepaymentAdapter extends BaseAdapter<String> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RepaymentAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_repayment;
    }

    @Override
    public int getItemCount() {
        return 10;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvRepayment;
        tvRepayment = holder.getView(R.id.tvItemRepayment);

        if (onItemClickListener != null) {
            tvRepayment.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
