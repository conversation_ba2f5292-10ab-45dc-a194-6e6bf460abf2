package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean;

import java.io.Serializable;

/**
 * Describe:供货商详情-付款记录（实体类）
 * Created by jingang on 2023/9/21
 */
public class SupplierPaymentData implements Serializable {
    /**
     * paymentId : 8
     * createId : 3586
     * createBy : 益农社服务员
     * createTime : 2023-09-15 15:10:49
     * status : 4
     * paymentMoney : 100
     * remark : 哈哈哈
     */

    private int paymentId;
    private int createId;
    private String createBy;
    private String createTime;
    private int status;//状态1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废，6-异常
    private double paymentMoney;
    private String remark;

    public int getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(int paymentId) {
        this.paymentId = paymentId;
    }

    public int getCreateId() {
        return createId;
    }

    public void setCreateId(int createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public double getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(double paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
