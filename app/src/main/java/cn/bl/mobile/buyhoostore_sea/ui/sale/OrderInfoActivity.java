package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.BaseResponse;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.evevtbus.FirstEvent;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ShopRiderAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.OrderInfoGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.RiderAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.OrderInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.RiderData;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:销售订单-详情
 * Created by jingang on 2023/5/17
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OrderInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;

    //订单状态
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvStatusTips)
    TextView tvStatusTips;
    @BindView(R.id.ivStatus)
    ImageView ivStatus;
    @BindView(R.id.tvPick)
    TextView tvPick;

    //配送
    @BindView(R.id.linDelivery)
    LinearLayout linDelivery;
    @BindView(R.id.tvDelivery)
    TextView tvDelivery;
    @BindView(R.id.linDelivery0)
    LinearLayout linDelivery0;
    @BindView(R.id.tvDelivery_name)
    TextView tvDelivery_name;
    @BindView(R.id.tvDelivery_mobile)
    TextView tvDelivery_mobile;
    @BindView(R.id.tvSale_name)
    TextView tvSale_name;
    @BindView(R.id.tvSale_mobile)
    TextView tvSale_mobile;
    @BindView(R.id.tvSale_address)
    TextView tvSale_address;
    @BindView(R.id.linLine)
    LinearLayout linLine;

    //订单信息
    @BindView(R.id.tvCompany)
    TextView tvCompany;
    @BindView(R.id.rvGoods)
    RecyclerView rvGoods;
    @BindView(R.id.tvRemarksValue)
    TextView tvRemarksValue;
    @BindView(R.id.tvRemarks)
    TextView tvRemarks;
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvTotal)
    TextView tvTotal;//商品总计
    @BindView(R.id.tvFeeValue)
    TextView tvFeeValue;
    @BindView(R.id.tvFee)
    TextView tvFee;//运费总计
    @BindView(R.id.tvCouponsValue)
    TextView tvCouponsValue;
    @BindView(R.id.tvCoupons)
    TextView tvCoupons;//优惠金额
    @BindView(R.id.tvReceiveValue)
    TextView tvReceiveValue;
    @BindView(R.id.tvReceive)
    TextView tvReceive;//实付款
    @BindView(R.id.linOrder)
    LinearLayout linOrder;
    @BindView(R.id.tvNoValue)
    TextView tvNoValue;
    @BindView(R.id.tvNo)
    TextView tvNo;//订单编号
    @BindView(R.id.tvCopy)
    TextView tvCopy;
    @BindView(R.id.tvDateTimeValue)
    TextView tvDateTimeValue;
    @BindView(R.id.tvDateTime)
    TextView tvDateTime;//下单时间
    @BindView(R.id.linPay_time)
    LinearLayout linPay_time;
    @BindView(R.id.tvPayTimeValue)
    TextView tvPayTimeValue;
    @BindView(R.id.tvPay_time)
    TextView tvPay_time;//付款时间
    @BindView(R.id.linPay_type)
    LinearLayout linPay_type;
    @BindView(R.id.tvPayTypeValue)
    TextView tvPayTypeValue;
    @BindView(R.id.tvPay_type)
    TextView tvPay_type;//付款方式
    @BindView(R.id.linSend_time)
    LinearLayout linSend_time;
    @BindView(R.id.tvSendTimeValue)
    TextView tvSendTimeValue;
    @BindView(R.id.tvSend_time)
    TextView tvSend_time;//发货时间
    @BindView(R.id.linReceive_time)
    LinearLayout linReceive_time;
    @BindView(R.id.tvReceiveTimeValue)
    TextView tvReceiveTimeValue;
    @BindView(R.id.tvReceive_time)
    TextView tvReceive_time;//接单时间
    @BindView(R.id.linPick_time)
    LinearLayout linPick_time;
    @BindView(R.id.tvPickTimeValue)
    TextView tvPickTimeValue;
    @BindView(R.id.tvPick_time)
    TextView tvPick_time;//取货时间
    @BindView(R.id.linSign_time)
    LinearLayout linSign_time;
    @BindView(R.id.tvSignTimeValue)
    TextView tvSignTimeValue;
    @BindView(R.id.tvSign_time)
    TextView tvSign_time;//签收时间

    @BindView(R.id.tvOpen)
    TextView tvOpen;
    @BindView(R.id.ivOpen)
    ImageView ivOpen;
    @BindView(R.id.tvKefuValue)
    TextView tvKefuValue;
    @BindView(R.id.linOperation)
    LinearLayout linOperation;
    @BindView(R.id.tvOperation)
    TextView tvOperation;

    private String unique,statusName;
    private int status;//订单状态 1.无效订单 2.新订单（待发货） 3.已发货（代收货）4.已完成（已收货）5.已取消 6.待评论 7.待骑手配送 10.配送异常

    //商品
    private OrderInfoGoodsAdapter goodsAdapter;
    private List<OrderInfoData.DataBean.ListDetailBean> goodsList = new ArrayList<>();

    private String mobile = "400-7088-365",//联系买家
            driverPhone;//配送人员电话
    private int call_type;//0.联系买家 1.联系配送人员
    private boolean isOpen = true;//是否展开

    //骑手
    private List<RiderData> riderList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_order_info;
    }

    @Override
    public void initViews() {
        unique = getIntent().getStringExtra("unique");
        setAdapter();
    }

    @Override
    public void initData() {
        getOrder_info();
    }

    @OnClick({R.id.ivBack, R.id.ivKefu_delivery, R.id.ivCoupons, R.id.tvCopy, R.id.linOpen, R.id.linKefu, R.id.tvOperation})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivKefu_delivery:
                //配送员电话
                call_type = 1;
                if (PermissionUtils.checkPermissionsGroup(this, 4)) {
                    callPhone(driverPhone);
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 4);
                }
                break;
            case R.id.ivCoupons:
                //优惠
                break;
            case R.id.tvCopy:
                //复制
                ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                // 创建普通字符型ClipData
                ClipData mClipData = ClipData.newPlainText("code", tvNo.getText().toString().trim());
                // 将ClipData内容放到系统剪贴板里。
                cm.setPrimaryClip(mClipData);
                showMessage(getLanguageValue("copy") + getLanguageValue("succeed"));
                break;
            case R.id.linOpen:
                //查看更多/收起
                isOpen = !isOpen;
                if (isOpen) {
                    linOrder.setVisibility(View.VISIBLE);
                    tvOpen.setText(getLanguageValue("putItAway"));
                    ivOpen.setImageResource(R.mipmap.ic_arrow011);
                } else {
                    linOrder.setVisibility(View.GONE);
                    tvOpen.setText(getLanguageValue("seeMore"));
                    ivOpen.setImageResource(R.mipmap.ic_arrow010);
                }
                break;
            case R.id.linKefu:
                //联系客服
                call_type = 0;
                if (PermissionUtils.checkPermissionsGroup(this, 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 4);
                }
                break;
            case R.id.tvOperation:
                //操作
                switch (status) {
                    case 2:
                        //接单
                    case 10:
                        //重新接单
                        if (shippingMethod == 1) {
                            switch (deliveryType) {
                                case 0:
                                    //自配送
                                    getShopCourierList();
                                    break;
                                case 2:
                                    //一刻钟配送
                                    showDialogRider(goodTotalCount);
                                    break;
                                default:
                                    showMessage(getLanguageValue("orderNotSupport"));
                                    break;
                            }
                        }
                        break;
                    case 8:
                        //取消
                        IAlertDialog.showDialog(this,
                                getLanguageValue("confirm") + getLanguageValue("cancel") + getLanguageValue("order") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    cancelOrder();
                                });
                        break;
                    case 3:
                        //收货
                        IAlertDialog.showDialog(this,
                                getLanguageValue("confirm") + getLanguageValue("receipt") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    finishOrder();
                                });
                        break;
                    case 9:
                        //完成
                        IAlertDialog.showDialog(this,
                                getLanguageValue("confirm") + getLanguageValue("finish") + getLanguageValue("order") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    finishOrder();
                                });
                        break;
                    case 7:
                        //取消配送
                        IAlertDialog.showDialog(this,
                                getLanguageValue("confirm") + getLanguageValue("cancel") + getLanguageValue("delivery") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    postCancelReceive();
                                });
                        break;
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvRemarksValue.setText(getLanguageValue("remark") + ":");
        tvTotalValue.setText(getLanguageValue("commodity") + getLanguageValue("total"));
        tvFeeValue.setText(getLanguageValue("shippingCost") + getLanguageValue("total"));
        tvCouponsValue.setText(getLanguageValue("preferential") + getLanguageValue("amount"));
        tvReceiveValue.setText(getLanguageValue("actual") + getLanguageValue("pay"));
        tvNoValue.setText(getLanguageValue("order") + getLanguageValue("number") + ":");
        tvCopy.setText(getLanguageValue("copy"));
        tvDateTimeValue.setText(getLanguageValue("placeAnOrder") + getLanguageValue("time") + ":");
        tvPayTimeValue.setText(getLanguageValue("pay") + getLanguageValue("time") + ":");
        tvPayTypeValue.setText(getLanguageValue("pay") + getLanguageValue("way") + ":");
        tvSendTimeValue.setText(getLanguageValue("sendProduct") + getLanguageValue("time") + ":");
        tvReceiveTimeValue.setText(getLanguageValue("acceptOrder") + getLanguageValue("time") + ":");
        tvPickTimeValue.setText(getLanguageValue("pickup") + getLanguageValue("time") + ":");
        tvSignTimeValue.setText(getLanguageValue("sign") + getLanguageValue("time") + ":");
        tvOpen.setText(getLanguageValue("putItAway"));
        tvKefuValue.setText(getLanguageValue("contact") + getLanguageValue("buyer"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        goodsAdapter = new OrderInfoGoodsAdapter(this);
        rvGoods.setAdapter(goodsAdapter);
    }

    private int shippingMethod,
            deliveryType = -1;
    private double goodTotalCount;

    /**
     * 更新UI
     *
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void setUI(OrderInfoData.DataBean data) {
        if (data == null) {
            return;
        }
        //0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待确认 8-待付款9-待自提 10-配送异常 11-已核单未发货'
        status = data.getHandleStateCode();
        //配送方式1:送货上门 2:自提
        shippingMethod = data.getShipping_method();
        //送货方式，0：自配送 2:一刻钟配送
        deliveryType = data.getDelivery_type();
        goodTotalCount = data.getTotalCount();
        mobile = data.getSaleListPhone();
        driverPhone = data.getDriverPhone();
        switch (status) {
            case 2:
                //待发货
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("acceptOrder"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(getResources().getColor(R.color.white));
                tvStatus.setText(getLanguageValue("waitSellerDeliver"));
                tvStatusTips.setText(getLanguageValue("waitSellerDeliver"));
                ivStatus.setImageResource(R.mipmap.ic_order_status5);
                statusName = getLanguageValue("toBeDelivered");
                break;
            case 3:
                //待收货
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("receipt"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(getResources().getColor(R.color.blue));
                tvStatus.setText(getLanguageValue("toBeReceived"));
                tvStatusTips.setText(getLanguageValue("waitBuyerReceive"));
                ivStatus.setImageResource(R.mipmap.ic_order_status1);
                statusName = getLanguageValue("toBeReceived");
                break;
            case 4:
                //已完成
                statusName = getLanguageValue("completed");
            case 6:
                //待评价
                tvOperation.setVisibility(View.GONE);
                tvStatus.setText(getLanguageValue("order") + getLanguageValue("finish"));
                tvStatusTips.setText(getLanguageValue("signSuccessOrderComplete"));
                ivStatus.setImageResource(R.mipmap.ic_order_status3);
                statusName = getLanguageValue("beEvaluated");
                break;
            case 5:
                //已取消
                tvOperation.setVisibility(View.GONE);
                tvStatus.setText(getLanguageValue("cancelOrder"));
                tvStatusTips.setText(getLanguageValue("endTransaction"));
                ivStatus.setImageResource(R.mipmap.ic_order_status4);
                statusName = getLanguageValue("cancelled");
                break;
            case 7:
                //配送单待确认
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("cancel") + getLanguageValue("delivery"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(getResources().getColor(R.color.blue));
                tvStatus.setText(getLanguageValue("beDelivered"));
                tvStatusTips.setText(getLanguageValue("waitDistributorPickup"));
                ivStatus.setImageResource(R.mipmap.ic_order_status2);
                statusName = getLanguageValue("waitConfirmDelivery");
                break;
            case 8:
                //待付款
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("cancel") + getLanguageValue("order"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(getResources().getColor(R.color.blue));
                tvStatus.setText(getLanguageValue("toBePaid"));
                tvStatusTips.setText(getLanguageValue("waitPay"));
                ivStatus.setImageResource(R.mipmap.ic_order_status0);
                statusName = getLanguageValue("toBePaid");
                break;
            case 9:
                //待自提
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("pickup"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(getResources().getColor(R.color.white));
                tvStatus.setText(getLanguageValue("toBePickedUp"));
                tvStatusTips.setText(getLanguageValue("waitBuyerPickup"));
                ivStatus.setImageResource(R.mipmap.ic_order_status6);
                statusName = getLanguageValue("toBePickedUp");
                break;
            case 10:
                //异常单
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("reacceptOrder"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(getResources().getColor(R.color.white));
                tvStatus.setText(getLanguageValue("beDelivered"));
                tvStatusTips.setText(getLanguageValue("waitDistributorPickup"));
                ivStatus.setImageResource(R.mipmap.ic_order_status2);
                statusName = getLanguageValue("deliveryException");
                break;
            default:
                tvOperation.setVisibility(View.GONE);
                statusName = "";
                break;
        }

        //标题
//        if (status == 7 || status == 10) {
//            tvTitle.setText(data.getDelivery_status());
//        } else {
//            if (null != data.getHandleState()) {
//                tvTitle.setText(data.getHandleState());
//            }
//        }
        tvTitle.setText(statusName);
        //配送人员
        if (status == 7) {
            linDelivery.setVisibility(View.VISIBLE);
            linLine.setVisibility(View.VISIBLE);
            tvDelivery.setVisibility(View.VISIBLE);
            linDelivery0.setVisibility(View.GONE);
        } else {
            if (TextUtils.isEmpty(data.getDriverName())) {
                linDelivery.setVisibility(View.GONE);
                linLine.setVisibility(View.GONE);
            } else {
                linDelivery.setVisibility(View.VISIBLE);
                linLine.setVisibility(View.VISIBLE);
                tvDelivery.setVisibility(View.GONE);
                linDelivery0.setVisibility(View.VISIBLE);
                tvDelivery_name.setText(data.getDriverName());
                tvDelivery_mobile.setText(data.getDriverPhone());
            }
        }

        //配送信息
        tvSale_name.setText(data.getSaleListName());
        tvSale_mobile.setText(data.getSaleListPhone());
        tvSale_address.setText(data.getSaleListAddress());

        //商品
        tvCompany.setText(getShop_name());
        goodsList.clear();
        goodsList.addAll(data.getListDetail());
        goodsAdapter.setDataList(goodsList);

        //订单信息
        if (TextUtils.isEmpty(data.getSaleListRemarks())) {
            tvRemarks.setText(getLanguageValue("none"));
        } else {
            tvRemarks.setText(data.getSaleListRemarks());
        }
        tvTotal.setText("RM" + DFUtils.getNum2(data.getSaleListTotal()));
        tvFee.setText("RM" + DFUtils.getNum2(data.getPeisong_money()));
        tvCoupons.setText("-RM" + DFUtils.getNum2(data.getCoupon_amount()));
        tvReceive.setText("RM" + DFUtils.getNum2(data.getActuallyReceived()));

        tvNo.setText(unique);
        tvDateTime.setText(data.getDateTime());
        if (TextUtils.isEmpty(data.getPayTime())) {
            linPay_time.setVisibility(View.GONE);
        } else {
            linPay_time.setVisibility(View.VISIBLE);
            tvPay_time.setText(data.getPayTime());
        }
        if (TextUtils.isEmpty(data.getSaleListPayment())) {
            linPay_type.setVisibility(View.GONE);
        } else {
            linPay_type.setVisibility(View.VISIBLE);
            tvPay_type.setText(data.getSaleListPayment());
        }
        if (TextUtils.isEmpty(data.getSendDateTime())) {
            linSend_time.setVisibility(View.GONE);
        } else {
            linSend_time.setVisibility(View.VISIBLE);
            tvSend_time.setText(data.getSendDateTime());
        }
//        if (TextUtils.isEmpty("接单时间")) {
//            linReceive_time.setVisibility(View.GONE);
//        } else {
//            linReceive_time.setVisibility(View.VISIBLE);
//            tvReceive_time.setText("不知道");
//        }
//        if (TextUtils.isEmpty("取货时间")) {
//            linPick_time.setVisibility(View.GONE);
//        } else {
//            linPick_time.setVisibility(View.VISIBLE);
//            tvPick_time.setText("不知道");
//        }
        if (TextUtils.isEmpty(data.getReceiptDateTime())) {
            linSign_time.setVisibility(View.GONE);
        } else {
            linSign_time.setVisibility(View.VISIBLE);
            tvSign_time.setText(data.getReceiptDateTime());
        }
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this, getLanguageValue("confirmCall") + ":" + phoneNum, getLanguageValue("confirm"), (dialog, which) -> {
            startActivity(new Intent(Intent.ACTION_CALL).setData(Uri.parse("tel:" + phoneNum)));
        });
    }

    /**
     * dialog（选择骑手）
     */
    private void showDialogRider(double count) {
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_rider, null);
        dialog.setContentView(view);

        TextView tvDialogTitle = view.findViewById(R.id.tvDialogTitle);
        RecyclerView rvRider = view.findViewById(R.id.rvDialogRider);
        TextView tvDialogAdd = view.findViewById(R.id.tvDialogAdd);
        tvDialogTitle.setText(getLanguageValue("choose") + getLanguageValue("distributor"));
        tvDialogAdd.setText(getLanguageValue("addTo") + getLanguageValue("distributor"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        view.findViewById(R.id.vDialog).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.linDialogAdd).setOnClickListener(v -> {
            startActivityForResult(new Intent(this, ShopRiderAddActivity.class), Constants.RIDER);
            dialog.dismiss();
        });

        rvRider.setLayoutManager(new LinearLayoutManager(this));
        RiderAdapter adapter = new RiderAdapter(this);
        rvRider.setAdapter(adapter);
        adapter.setDataList(riderList);
        adapter.setOnItemClickListener((view1, position) -> {
            riderList.get(position).setCheck(true);
            if (deliveryType == 2) {
                createSelfDistribution("2", String.valueOf(count), "", "", "");
            } else {
                createSelfDistribution(
                        "0",
                        "0",
                        String.valueOf(riderList.get(position).getId()),
                        riderList.get(position).getCourier_name(),
                        riderList.get(position).getCourier_phone()
                );
            }
            dialog.dismiss();
        });
    }

    /**
     * 销售订单详情
     */
    private void getOrder_info() {
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getOrderDetailUrlTWO(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderInfoData data = new Gson().fromJson(s, OrderInfoData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setUI(data.getData());
                        }
                    }
                });
    }

    /**
     * 创建自配送
     */
    private void createSelfDistribution(
            String deliveryType,
            String goodsWeight,
            String shopCourierId,
            String courierName,
            String courierPhone) {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", unique);
        map.put("delivery_type", deliveryType);
        map.put("goods_weight", goodsWeight);
        map.put("shop_courier_id", shopCourierId);
        map.put("courier_name", courierName);
        map.put("courier_phone", courierPhone);
        map.put("sale_list_cashier", getStaff_id());
        map.put("return_price", 0.00);
        map.put("goodsList", "");
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getCreateOrder(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "创建自配送 = " + s);
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        try {
                            JSONObject confrim_object = new JSONObject(s);
                            int confrim_status = confrim_object.getInt("status");
                            if (confrim_status == 1) {
                                //请求接口，更新订单数量
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                                EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
                                EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_ORSER_LIST));
                                finish();
                            } else {
                                boolean msgFlag = confrim_object.isNull("msg");
                                String err_msg = "";
                                if (!msgFlag) {
                                    err_msg = confrim_object.getString("msg");
                                }
                                if ("" != err_msg) {
                                    showMessage(err_msg);
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 店铺查询骑手列表
     */
    public void getShopCourierList() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getShopCourierList(),
                params,
                RiderData.class,
                new RequestListListener<RiderData>() {
                    @Override
                    public void onResult(List<RiderData> riderData) {
                        hideDialog();
                        riderList.clear();
                        riderList.addAll(riderData);
                        showDialogRider(0);
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        riderList.clear();
                    }
                });
    }

    /**
     * 订单完成
     */
    private void finishOrder() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", unique);
        map.put("sale_list_cashier", getStaff_id());
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getConfirmReceipt(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "订单完成 = " + s);
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        try {
                            JSONObject finishObject = new JSONObject(s);
                            boolean finishIsNull = finishObject.isNull("status");
                            if (!finishIsNull) {
                                int finishStatus = finishObject.getInt("status");
                                if (finishStatus == 1) {
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                                    EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
                                    EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_ORSER_LIST));
                                    finish();
                                    showMessage(getLanguageValue("completed"));
                                } else {
                                    boolean msgFlag = finishObject.isNull("msg");
                                    String err_msg = "";
                                    if (!msgFlag) {
                                        err_msg = finishObject.getString("msg");
                                    }
                                    if (!err_msg.isEmpty()) {
                                        showMessage(err_msg);
                                    }
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 订单取消
     */
    private void cancelOrder() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getCancelSaleList(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "订单取消 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        try {
                            JSONObject cancelObject = new JSONObject(s);
                            boolean canclIsNull = cancelObject.isNull("status");
                            if (!canclIsNull) {
                                int cancelStatus = cancelObject.getInt("status");
                                if (cancelStatus == 1) {
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                                    EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
                                    EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_ORSER_LIST));
                                    showMessage(getLanguageValue("cancelOrder"));
                                    finish();
                                } else {
                                    boolean msgFlag = cancelObject.isNull("msg");
                                    String err_msg = "";
                                    if (!msgFlag) {
                                        err_msg = cancelObject.getString("msg");
                                    }
                                    if (!err_msg.isEmpty()) {
                                        showMessage(err_msg);
                                    }
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 取消配送
     */
    public void postCancelReceive() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("orderNum", unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getCancelDelivery(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseResponse bean = new Gson().fromJson(s, BaseResponse.class);
                        if (bean.getStatus() == 1) {
                            //取消配送
                            showMessage(getLanguageValue("order") + getLanguageValue("cancel") + getLanguageValue("delivery") + getLanguageValue("succeed"));
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                            finish();
                        } else {
                            showMessage(bean.getMsg());
                        }
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == Constants.PERMISSION) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (call_type == 0) {
                    callPhone(mobile);
                } else {
                    callPhone(driverPhone);
                }
            } else {
                showMessage(getLanguageValue("requiredCallPermission"));
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.RIDER:
                    //添加骑手
                    getShopCourierList();
                    break;
            }
        }
    }
}
