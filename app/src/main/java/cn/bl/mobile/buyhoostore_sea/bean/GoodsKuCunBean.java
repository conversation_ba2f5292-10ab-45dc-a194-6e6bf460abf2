package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * 店铺-库存-库存预警（实体类）
 */
public class GoodsKuCunBean {
    /**
     * goods_name : 还得
     * goods_barcode : 2111782516193016
     * unsalable_count : 0
     * goods_contain : 1.0
     * foreign_key : 2111782516193016
     * goods_in_price : 0.0
     * goodsPicturePath : upload/no_goodsB.jpg
     * goods_count : -16.0
     * out_stock_waring_count : 0
     * goods_sale_price : 5.0
     */

    private String goods_name;
    private String goods_barcode;
    private int unsalable_count;
    private double goods_contain;
    private long foreign_key;
    private double goods_in_price;
    private String goodsPicturePath;
    private double goods_count;
    private int out_stock_waring_count;
    private double goods_sale_price;

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getGoods_barcode() {
        return goods_barcode;
    }

    public void setGoods_barcode(String goods_barcode) {
        this.goods_barcode = goods_barcode;
    }

    public int getUnsalable_count() {
        return unsalable_count;
    }

    public void setUnsalable_count(int unsalable_count) {
        this.unsalable_count = unsalable_count;
    }

    public double getGoods_contain() {
        return goods_contain;
    }

    public void setGoods_contain(double goods_contain) {
        this.goods_contain = goods_contain;
    }

    public long getForeign_key() {
        return foreign_key;
    }

    public void setForeign_key(long foreign_key) {
        this.foreign_key = foreign_key;
    }

    public double getGoods_in_price() {
        return goods_in_price;
    }

    public void setGoods_in_price(double goods_in_price) {
        this.goods_in_price = goods_in_price;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public double getGoods_count() {
        return goods_count;
    }

    public void setGoods_count(double goods_count) {
        this.goods_count = goods_count;
    }

    public int getOut_stock_waring_count() {
        return out_stock_waring_count;
    }

    public void setOut_stock_waring_count(int out_stock_waring_count) {
        this.out_stock_waring_count = out_stock_waring_count;
    }

    public double getGoods_sale_price() {
        return goods_sale_price;
    }

    public void setGoods_sale_price(double goods_sale_price) {
        this.goods_sale_price = goods_sale_price;
    }
}