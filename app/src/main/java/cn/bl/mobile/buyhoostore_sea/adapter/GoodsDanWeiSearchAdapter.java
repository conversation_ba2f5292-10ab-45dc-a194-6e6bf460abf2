package cn.bl.mobile.buyhoostore_sea.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseApplication;

import org.json.JSONException;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsDanWeiBean;

/**
 * Created by Administrator on 2017/7/24 0024.
 */
public class GoodsDanWeiSearchAdapter extends BaseAdapter {
    private ViewHolder holder;
    private List<GoodsDanWeiBean.DataBean> list;
    private Context context;
    private GoodsDanWeiBean.DataBean categoryBean;
    private OnClickListenerEditOrDelete onClickListenerEditOrDelete;

    public GoodsDanWeiSearchAdapter(Context context, List<GoodsDanWeiBean.DataBean> list) {
        this.list = list;
        this.context = context;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        Log.e("TAG", position + "position");
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(
                    R.layout.item_danwei_tab, null);

            holder.text_danwei = (TextView) convertView.findViewById(R.id.text_danwei);
            holder.tvDelete = (TextView) convertView.findViewById(R.id.delete);
            holder.tvEdit = (TextView) convertView.findViewById(R.id.tvEdit);
            holder.rlTop = (RelativeLayout) convertView.findViewById(R.id.rlTop);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        holder.tvEdit.setText(getLanguageValue("editor"));
        holder.tvDelete.setText(getLanguageValue("delete"));
        categoryBean = list.get(position);
        if (position % 2 == 0) {

            holder.rlTop.setBackgroundResource(R.color.bg_color);
        } else {
            holder.rlTop.setBackgroundResource(R.color.white);
        }
        holder.tvDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onClickListenerEditOrDelete != null) {
                    onClickListenerEditOrDelete.OnClickListenerDelete(position);
                }
            }
        });
        holder.tvEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onClickListenerEditOrDelete != null) {
                    onClickListenerEditOrDelete.OnClickListenerEdit(position);
                }
            }
        });
        holder.text_danwei.setText(categoryBean.getGoods_unit());

        return convertView;
    }

    private class ViewHolder {
        TextView text_danwei, tvEdit, tvDelete;
        RelativeLayout rlTop;

    }

    public interface OnClickListenerEditOrDelete {
        void OnClickListenerEdit(int position);

        void OnClickListenerDelete(int position);
    }

    public void setOnClickListenerEditOrDelete(OnClickListenerEditOrDelete onClickListenerEditOrDelete1) {
        this.onClickListenerEditOrDelete = onClickListenerEditOrDelete1;
    }

    /**
     * 获取多语言值
     *
     * @param key
     * @return
     */
    public String getLanguageValue(String key) {
        String value;
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        if (BaseApplication.getLanguageValueJson() == null) {
            value = "";
        } else {
            try {
                value = BaseApplication.getLanguageValueJson().getString(key);
            } catch (JSONException e) {
                e.printStackTrace();
                value = "";
            }
        }
        return value;
    }

}
