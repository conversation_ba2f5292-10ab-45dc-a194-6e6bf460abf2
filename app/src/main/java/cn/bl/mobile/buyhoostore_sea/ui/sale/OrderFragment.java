package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.BaseResponse;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.OrderListData;
import cn.bl.mobile.buyhoostore_sea.adapter.OrderAdapter;

import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ShopRiderAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.RiderAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.TitleItemDecoration;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.RiderData;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:销售-订单
 * Created by jingang on 2022/12/10
 */
@SuppressLint("NonConstantResourceId")
public class OrderFragment extends LazyBaseFragment {
    private String tag = "OrderFragment";
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int state = -1;//订单状态 -1.全部
    private String staffId;
    private String deliveryType;//配送方式
    private int count;//商品数量
    private String orderId;//订单id

    private OrderAdapter mAdapter;
    private List<OrderListData> dataList = new ArrayList<>();

    //骑手
    private List<RiderData> riderList = new ArrayList<>();

    private boolean isVisible;//当前fragment是否显示

    //判断列表是否需要刷新
    private boolean isKeywords,//搜索关键字是否改变
            isState;//订单状态是否改变

    /**
     * 初始化fragment
     *
     * @return
     */
    public static OrderFragment newInstance(int state, String staffId) {
        OrderFragment fragment = new OrderFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("state", state);
        bundle.putString("staffId", staffId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public void initData() {

    }

    @Override
    protected void onRetryBtnClick() {

    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        assert getArguments() != null;
        state = getArguments().getInt("state", -1);
        staffId = getArguments().getString("staffId");
        getOrderList(SaleFragment.keywords, state, page);
    }


    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.e(tag, "state = " + state + " isVisible = " + isVisibleToUser + " isKeywords = " + isKeywords + " isState = " + isState);
        this.isVisible = isVisibleToUser;
        if (!isVisible) {
            return;
        }
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
//        if (isKeywords || isState) {
//            page = 1;
//            mPresenter.getOrderList(shopId, SaleFragment.keywords, state, page);
//        }
    }

//    @Override
//    protected void onFragmentVisibleChange(boolean isVisible) {
//        super.onFragmentVisibleChange(isVisible);
//        Log.e(tag, "state = " + state + " isVisible = " + isVisible + " isKeywords = " + isKeywords + " isState = " + isState);
//        this.isVisible = isVisible;
//        if (mPresenter == null || !isVisible) {
//            return;
//        }
//        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
////        if (isKeywords || isState) {
////            page = 1;
////            mPresenter.getOrderList(shopId, SaleFragment.keywords, state, page);
////        }
//    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "keywords":
                //搜索关键字
                if (isVisible) {
                    page = 1;
                    getOrderList(SaleFragment.keywords, state, page);
                } else {
                    isKeywords = true;
                }
                break;
            case "state":
                //订单状态改变
                if (isVisible) {
                    page = 1;
                    getOrderList(SaleFragment.keywords, state, page);
                } else {
                    isState = true;
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new OrderAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);

        smartRefreshLayout.setEnableAutoLoadMore(true);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getOrderList(SaleFragment.keywords, state, page);
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getOrderList(SaleFragment.keywords, state, page);
        });

        mAdapter.setListener(new OrderAdapter.MyListener() {
            @Override
            public void onInfoClick(View view, int position) {
                //详情
                startActivity(new Intent(getActivity(), OrderInfoActivity.class)
                        .putExtra("unique", String.valueOf(dataList.get(position).getSaleListUnique()))
                );
//                //是否是退款单
//                if (TextUtils.isEmpty(dataList.get(position).getRetListUnique())) {
////                    SaleOrderDetailActivity.toSaleOrderDetailActivity(getActivity(), String.valueOf(dataList.get(position).getSaleListUnique()), String.valueOf(state));
//                    startActivity(new Intent(getActivity(), OrderInfoActivity.class)
//                            .putExtra("unique", String.valueOf(dataList.get(position).getSaleListUnique()))
//                    );
//                } else {
//                    startActivity(new Intent(getActivity(), RefundOrderInfoActivity.class)
//                            .putExtra("orderId", dataList.get(position).getRetListUnique())
//                    );
//                }
            }

            @Override
            public void onOperationClick(View view, int position) {
                //操作
                orderId = String.valueOf(dataList.get(position).getSaleListUnique());
                deliveryType = String.valueOf(dataList.get(position).getDelivery_type());
                count = 0;
                for (int i = 0; i < dataList.get(position).getListDetail().size(); i++) {
                    count = (int) (count + dataList.get(position).getListDetail().get(i).getSaleListDetailCount());
                }
                //操作 2.接单 3.收货 8.取消 9.完成 10.重新接单
                int state = dataList.get(position).getHandleStateCode();
                switch (state) {
                    case 2:
                    case 10:
                        switch (deliveryType) {
                            case "0":
                            case "2":
                                if (riderList.size() > 0) {
                                    showDialogRider();
                                } else {
                                    getShopCourierList();
                                }
                                break;
                            default:
                                showMessage(getLanguageValue("orderNotSupport"));
                                break;
                        }
                        break;
                    case 3:
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm") + getLanguageValue("receipt") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    postReceive(orderId, staffId, state);
                                });
                        break;
                    case 9:
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm") + getLanguageValue("finish") + getLanguageValue("order") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    postReceive(orderId, staffId, state);
                                });
                        break;
                    case 8:
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm") + getLanguageValue("cancel") + getLanguageValue("order") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    postCancel(orderId);
                                });
                        break;
                    case 7:
                        //取消配送
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm") + getLanguageValue("cancel") + getLanguageValue("delivery") + "?",
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
                                    postCancelReceive(orderId);
                                });
                        break;
                }

//                //取消配送
//                if (TextUtils.isEmpty(dataList.get(position).getSaleListState())) {
//                    return;
//                }
//                if (dataList.get(position).getSaleListState().equals("配送单待确定")) {
//                    //取消配送
//                    postCancelReceive(orderId);
//                }
            }

            @Override
            public void onRefundInfoClick(View view, int position) {
                //退款单
                if (TextUtils.isEmpty(dataList.get(position).getRetListUnique())) {
                    startActivity(new Intent(getActivity(), OrderInfoActivity.class)
                            .putExtra("unique", String.valueOf(dataList.get(position).getSaleListUnique()))
                    );
                } else {
                    startActivity(new Intent(getActivity(), RefundOrderInfoActivity.class)
                            .putExtra("orderId", dataList.get(position).getRetListUnique())
                    );
                }

            }
        });
    }

    /**
     * dialog（选择骑手）
     */
    private void showDialogRider() {
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_rider, null);
        dialog.setContentView(view);
        TextView tvTitle = view.findViewById(R.id.tvDialogTitle);
        TextView tvAdd = view.findViewById(R.id.tvDialogAdd);
        RecyclerView rvRider = view.findViewById(R.id.rvDialogRider);
        tvTitle.setText(getLanguageValue("choose") + getLanguageValue("distributor"));
        tvAdd.setText(getLanguageValue("addTo") + getLanguageValue("distributor"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        view.findViewById(R.id.vDialog).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.linDialogAdd).setOnClickListener(v -> {
            goToActivity(ShopRiderAddActivity.class);
        });

        rvRider.setLayoutManager(new LinearLayoutManager(getActivity()));
        RiderAdapter adapter = new RiderAdapter(getActivity());
        rvRider.setAdapter(adapter);
        for (int i = 0; i < riderList.size(); i++) {
            riderList.get(i).setCheck(false);
        }
        adapter.setDataList(riderList);
        adapter.setOnItemClickListener((view1, position) -> {
            riderList.get(position).setCheck(true);
            if (deliveryType.equals("2")) {
                //创建自配送订单
                postCreateOrder(orderId, "2", String.valueOf(count), "", "", "", "");
            } else {
                //创建自配送订单
                postCreateOrder(orderId,
                        "0",
                        "0",
                        String.valueOf(riderList.get(position).getId()),
                        riderList.get(position).getCourier_name(),
                        riderList.get(position).getCourier_phone(),
                        staffId);
            }
            dialog.dismiss();
        });
    }

    /*******************************接口交互start***********************************/
    /**
     * 网单订单查询
     *
     * @param keywords
     * @param state
     * @param page
     */
    public void getOrderList(String keywords, int state, int page) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("saleListMessage", keywords);
        params.put("handleState", state);
        params.put("pageIndex", page);
        params.put("pageSize", 20);
        //下单时间
        if (!TextUtils.isEmpty(SaleFragment.startTime)) {
            params.put("orderStartDate", SaleFragment.startTime);
        }
        if (!TextUtils.isEmpty(SaleFragment.endTime)) {
            params.put("orderEndDate", SaleFragment.endTime);
        }
        //订单类型
        if (SaleFragment.saleType != -1) {
            params.put("saleType", SaleFragment.saleType);
        }
        //付款时间
        if (SaleFragment.saleListPayment != -1) {
            params.put("saleListPayment", SaleFragment.saleListPayment);
        }
        //是否展示已取消订单
        boolean isShow = !TextUtils.isEmpty(SharedUtils.get(Constants.IS_SHOW_ORDER_CANCEL, IPreference.DataType.STRING));
        if (state == -1) {
            if (!isShow) {
                params.put("noCancelOrder", 1);//为1时，查询不包含已取消订单。
            }
        }
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getsalelistTWO(),
                params,
                OrderListData.class,
                new RequestListListener<OrderListData>() {
                    @Override
                    public void onResult(List<OrderListData> orderListData) {
                        hideDialog();
                        //订单列表获取成功
                        isKeywords = false;
                        isState = false;
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(orderListData);
                        mAdapter.setDataList(dataList);
                        if (recyclerView.getItemDecorationCount() == 0) {
                            recyclerView.addItemDecoration(new TitleItemDecoration(getActivity(), dataList));
                        }
                        if (dataList.size() > 0) {
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        //订单列表获取失败
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 店铺查询骑手列表
     */
    public void getShopCourierList() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        params.put("page", 1);
        params.put("pageSize", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getShopCourierList(),
                params,
                RiderData.class,
                new RequestListListener<RiderData>() {
                    @Override
                    public void onResult(List<RiderData> riderData) {
                        hideDialog();
                        riderList.clear();
                        riderList.addAll(riderData);
                        showDialogRider();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        riderList.clear();
                    }
                });
    }

    /**
     * 创建自配送订单
     *
     * @param orderId       订单id
     * @param deliveryType  配送方式
     * @param goodsWeight   货物重量
     * @param shopCourierId 骑手id
     * @param courierName   骑手姓名
     * @param courierPhone  骑手电话
     * @param staffId
     */
    public void postCreateOrder(String orderId, String deliveryType, String goodsWeight, String shopCourierId, String courierName, String courierPhone, String staffId) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", orderId);
        map.put("delivery_type", deliveryType);
        map.put("goods_weight", goodsWeight);
        map.put("shop_courier_id", shopCourierId);
        map.put("courier_name", courierName);
        map.put("courier_phone", courierPhone);
        map.put("sale_list_cashier", staffId);
        map.put("return_price", "0.00");
        map.put("goodsList", "");
        RXHttpUtil.requestByBodyPostAsOriginalResponse(getActivity(),
                ZURL.getCreateOrder(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseResponse bean = new Gson().fromJson(s, BaseResponse.class);
                        if (bean.getStatus() == 1) {
                            //接单成功
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        } else {
                            showMessage(bean.getMsg());
                        }
                    }
                });
    }

    /**
     * 收货、完成
     *
     * @param orderId
     */
    public void postReceive(String orderId, String staffId, int state) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", orderId);
        map.put("sale_list_cashier", staffId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getConfirmReceipt(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseResponse bean = new Gson().fromJson(s, BaseResponse.class);
                        if (bean.getStatus() == 1) {
                            //收货
                            switch (state) {
                                case 3:
                                    showMessage(getLanguageValue("order") + getLanguageValue("receipt") + getLanguageValue("succeed"));
                                    break;
                                case 9:
                                    showMessage(getLanguageValue("order") + getLanguageValue("completed"));
                                    break;
                            }
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        } else {
                            showMessage(bean.getMsg());
                        }
                    }
                });
    }

    /**
     * 取消订单
     *
     * @param orderId
     */
    public void postCancel(String orderId) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("sale_list_unique", orderId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getCancelSaleList(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseResponse bean = new Gson().fromJson(s, BaseResponse.class);
                        if (bean.getStatus() == 1) {
                            //取消订单
                            showMessage(getLanguageValue("order") + getLanguageValue("cancelled"));
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        } else {
                            showMessage(bean.getMsg());
                        }
                    }
                });
    }

    /**
     * 取消配送
     *
     * @param orderId
     */
    public void postCancelReceive(String orderId) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("orderNum", orderId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getCancelDelivery(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseResponse bean = new Gson().fromJson(s, BaseResponse.class);
                        if (bean.getStatus() == 1) {
                            //取消配送
                            showMessage(getLanguageValue("order") + getLanguageValue("cancel") + getLanguageValue("delivery") + getLanguageValue("succeed"));
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        } else {
                            showMessage(bean.getMsg());
                        }
                    }
                });
    }

    /*******************************接口交互end***********************************/

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.RIDER:
                    //添加骑手
                    getShopCourierList();
                    break;
            }
        }
    }
}
