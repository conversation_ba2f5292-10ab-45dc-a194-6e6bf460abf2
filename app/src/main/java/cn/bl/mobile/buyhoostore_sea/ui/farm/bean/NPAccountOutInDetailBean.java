package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

import android.graphics.Paint;

import com.bin.david.form.annotation.SmartColumn;
import com.bin.david.form.annotation.SmartTable;

/**
 * author 刘清营
 * Created on 2023/5/28.
 * Describe:
 */
@SmartTable(name = "")
public class NPAccountOutInDetailBean {
    private String cusHeadPath;
    private String cusId;

    @SmartColumn(id =0,name="序号",fixed = true)
    private String index;
    @SmartColumn(id =1,name="姓名",align = Paint.Align.CENTER,fixed = false)
    private String cusName;
    @SmartColumn(id =2,name="手机号",align = Paint.Align.CENTER)
    private String cusPhone;
    @SmartColumn(id =3,name="手机号1",align = Paint.Align.CENTER)

    private String cusRegeditDate;
    @SmartColumn(id =4,name="手机号1",align = Paint.Align.CENTER)

    private double cusTotal;
    @SmartColumn(id =5,name="手机号5",align = Paint.Align.CENTER)

    private String cus_unique;
    private String cus_points;
    private String cus_level_val;
    private String cus_balance;
    private String cus_type;
    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getCusPhone() {
        return cusPhone;
    }

    public void setCusPhone(String cusPhone) {
        this.cusPhone = cusPhone;
    }
    public String getCus_type() {
        return cus_type;
    }

    public void setCus_type(String cus_type) {
        this.cus_type = cus_type;
    }

    public String getCus_balance() {
        return cus_balance;
    }

    public void setCus_balance(String cus_balance) {
        this.cus_balance = cus_balance;
    }

    public String getCus_unique() {
        return cus_unique;
    }

    public void setCus_unique(String cus_unique) {
        this.cus_unique = cus_unique;
    }

    public String getCus_points() {
        return cus_points;
    }

    public void setCus_points(String cus_points) {
        this.cus_points = cus_points;
    }

    public String getCus_level_val() {
        return cus_level_val;
    }

    public void setCus_level_val(String cus_level_val) {
        this.cus_level_val = cus_level_val;
    }

    public String getCusHeadPath() {
        return cusHeadPath;
    }

    public void setCusHeadPath(String cusHeadPath) {
        this.cusHeadPath = cusHeadPath;
    }

    public String getCusId() {
        return cusId;
    }

    public void setCusId(String cusId) {
        this.cusId = cusId;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusRegeditDate() {
        return cusRegeditDate;
    }

    public void setCusRegeditDate(String cusRegeditDate) {
        this.cusRegeditDate = cusRegeditDate;
    }

    public double getCusTotal() {
        return cusTotal;
    }

    public void setCusTotal(double cusTotal) {
        this.cusTotal = cusTotal;
    }
}
