package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.SaleStatisticsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.SaleStatisticsTitleAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.SaleStatisticsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-销售统计
 * Created by jingang on 2023/8/17
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SaleStatisticsActivity extends BaseActivity {
    @BindView(R.id.drawerLayout)
    DrawerLayout drawerLayout;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;

    @BindView(R.id.linScreen)
    LinearLayout linScreen;
    @BindView(R.id.tvScreen)
    TextView tvScreen;
    @BindView(R.id.tvGoodsNameValue)
    TextView tvGoodsNameValue;

    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.linTitle)
    LinearLayout linTitle;
    @BindView(R.id.rvTitle)
    RecyclerView rvTitle;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    @BindView(R.id.linSlidingRight)
    LinearLayout linSlidingRight;
    @BindView(R.id.tvScreenValue)
    TextView tvScreenValue;
    @BindView(R.id.tvCateValue)
    TextView tvCateValue;
    @BindView(R.id.tvCate)
    TextView tvCate;
    //    @BindView(R.id.tvCateChild)
//    TextView tvCateChild;
    @BindView(R.id.tvDateValue)
    TextView tvDateValue;
    @BindView(R.id.tvDay0)
    TextView tvDay0;
    @BindView(R.id.tvDay1)
    TextView tvDay1;
    @BindView(R.id.tvDay2)
    TextView tvDay2;
    @BindView(R.id.tvStartTime)
    TextView tvStartTime;
    @BindView(R.id.tvEndTime)
    TextView tvEndTime;
    @BindView(R.id.tvRankValue)
    TextView tvRankValue;
    @BindView(R.id.tvOrderType0)
    TextView tvOrderType0;
    @BindView(R.id.tvOrderType1)
    TextView tvOrderType1;
    @BindView(R.id.tvOrderType2)
    TextView tvOrderType2;
    @BindView(R.id.tvOrderType3)
    TextView tvOrderType3;
    @BindView(R.id.tvResetting)
    TextView tvResetting;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    //筛选条件（已确认）
    private String keyWords,//搜索关键字
            cateUnique,//一级分类
            cateName,
            cateChildUnique,//二级分类
            cateChildName,
            cateUnique2,//三级分类
            cateName2,
            startTime,//开始日期
            endTime,//结束日期
            orderTypeName;//排序名称
    private int day = 1,//0.具体时间 1.今天 2.昨天 3.本周
            orderType;//1：金额降序；2：金额升序；3：数量降序； 4：数量升序

    private SaleStatisticsTitleAdapter rightAdapter;
    private List<String> strList = new ArrayList<>();
    private SaleStatisticsAdapter mAdapter;
    private List<SaleStatisticsData.DataBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_sale_statistics;
    }

    @Override
    public void initViews() {
        startTime = DateUtils.getCurrentDate();
        endTime = DateUtils.getCurrentDate();
        tvStartTime.setText(startTime);
        tvEndTime.setText(endTime);
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getSaleStatisticsList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        //禁止手势滑动
        drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        setAdapter();
    }

    @Override
    public void initData() {
        getScreenMsg();
    }

    @OnClick({R.id.ivBack, R.id.ivClear, R.id.ivScan, R.id.ivScreen, R.id.ivScreenClose,
            R.id.tvCate,
            R.id.tvDay0, R.id.tvDay1, R.id.tvDay2, R.id.tvStartTime, R.id.tvEndTime,
            R.id.tvOrderType0, R.id.tvOrderType1, R.id.tvOrderType2, R.id.tvOrderType3,
            R.id.tvResetting, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除内容输入
                etSearch.setText("");
                page = 1;
                getSaleStatisticsList();
                break;
            case R.id.ivScan:
                //扫码
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.ivScreen:
                //筛选
                drawerLayout.openDrawer(linSlidingRight);
                break;
            case R.id.tvCate:
                //一级分类
                CateDialog.showDialog(this, cateUnique, cateChildUnique, cateUnique2, (cateId, cateName, cateId1, cateName1, cateId2, cateName2) -> {
                    cateUnique = cateId;
                    this.cateName = cateName;
                    cateChildUnique = cateId1;
                    this.cateChildName = cateName1;
                    cateUnique2 = cateId2;
                    this.cateName2 = cateName2;
                    String cates = "";
                    if (!TextUtils.isEmpty(cateUnique)) {
                        cates = cateName;
                    }
                    if (!TextUtils.isEmpty(cateChildUnique)) {
                        cates = cates + "-" + cateChildName;
                    }
                    if (!TextUtils.isEmpty(cateUnique2)) {
                        cates = cates + "-" + cateName2;
                    }
                    tvCate.setText(cates);
                    getScreenMsg();
                });
                break;
            case R.id.tvDay0:
                //今天
                if (day != 1) {
                    day = 1;
                    startTime = DateUtils.getOldDate(0);
                    endTime = DateUtils.getOldDate(0);
                    tvStartTime.setText(startTime);
                    tvEndTime.setText(endTime);
                    clearDay();
                    tvDay0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay0.setTextColor(getResources().getColor(R.color.blue));
                    getScreenMsg();
                }
                break;
            case R.id.tvDay1:
                //昨天
                clearDay();
                if (day != 2) {
                    day = 2;
                    startTime = DateUtils.getOldDate(-1);
                    tvDay1.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay1.setTextColor(getResources().getColor(R.color.blue));
                } else {
                    day = 1;
                    startTime = DateUtils.getOldDate(0);
                    tvDay0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay0.setTextColor(getResources().getColor(R.color.blue));
                }
                endTime = DateUtils.getOldDate(0);
                tvStartTime.setText(startTime);
                tvEndTime.setText(endTime);
                getScreenMsg();
                break;
            case R.id.tvDay2:
                //本周
                clearDay();
                if (day != 3) {
                    day = 3;
                    startTime = DateUtils.getWeekStartTime();
//                    startTime = DateUtils.getOldDate(-7);
                    tvDay2.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay2.setTextColor(getResources().getColor(R.color.blue));
                } else {
                    day = 1;
                    startTime = DateUtils.getOldDate(0);
                    tvDay0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvDay0.setTextColor(getResources().getColor(R.color.blue));
                }
                endTime = DateUtils.getOldDate(0);
                tvStartTime.setText(startTime);
                tvEndTime.setText(endTime);
                getScreenMsg();
                break;
            case R.id.tvStartTime:
                //开始时间
            case R.id.tvEndTime:
                //结束时间
                DateStartEndDialog.showDialog(this,
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            startTime = startDate;
                            endTime = endDate;
                            tvStartTime.setText(startTime);
                            tvEndTime.setText(endTime);
                            clearDay();
                            getScreenMsg();
                        });
                break;
            case R.id.tvOrderType0:
                //销售额降序
                clearOrderType();
                if (orderType != 1) {
                    orderType = 1;
                    tvOrderType0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvOrderType0.setTextColor(getResources().getColor(R.color.blue));
                    orderTypeName = tvOrderType0.getText().toString().trim();
                } else {
                    orderType = 0;
                }
                getScreenMsg();
                break;
            case R.id.tvOrderType1:
                //销售额升序
                clearOrderType();
                if (orderType != 2) {
                    orderType = 2;
                    tvOrderType1.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvOrderType1.setTextColor(getResources().getColor(R.color.blue));
                    orderTypeName = tvOrderType1.getText().toString().trim();
                } else {
                    orderType = 0;
                }
                getScreenMsg();
                break;
            case R.id.tvOrderType2:
                //销量降序
                clearOrderType();
                if (orderType != 3) {
                    orderType = 3;
                    tvOrderType2.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvOrderType2.setTextColor(getResources().getColor(R.color.blue));
                    orderTypeName = tvOrderType2.getText().toString().trim();
                } else {
                    orderType = 0;
                }
                getScreenMsg();
                break;
            case R.id.tvOrderType3:
                //销量升序
                clearOrderType();
                if (orderType != 4) {
                    orderType = 4;
                    tvOrderType3.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                    tvOrderType3.setTextColor(getResources().getColor(R.color.blue));
                    orderTypeName = tvOrderType3.getText().toString().trim();
                } else {
                    orderType = 0;
                }
                getScreenMsg();
                break;
            case R.id.ivScreenClose:
                //重置筛选条件
            case R.id.tvResetting:
                //重置
                cateUnique = "";
                cateName = "";
                cateChildUnique = "";
                cateChildName = "";
                cateUnique2 = "";
                cateName2 = "";
                tvCate.setText("");
                day = 1;
                clearDay();
                startTime = DateUtils.getCurrentDate();
                endTime = DateUtils.getCurrentDate();
//                startTime = "";
//                endTime = "";
                tvStartTime.setText(startTime);
                tvEndTime.setText(endTime);
                orderType = 0;
                clearOrderType();
                drawerLayout.closeDrawer(linSlidingRight);
                getScreenMsg();
                break;
            case R.id.tvConfirm:
                //确认
                drawerLayout.closeDrawer(linSlidingRight);
                break;
        }
    }

    @Override
    public void setText() {
        etSearch.setHint(getLanguageValue("search") + getLanguageValue("commodity") + getLanguageValue("name") + "/" + getLanguageValue("barcode"));
        tvGoodsNameValue.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        tvScreenValue.setText(getLanguageValue("data") + getLanguageValue("filter"));
        tvCateValue.setText(getLanguageValue("classification") + getLanguageValue("choose"));
        tvCate.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("classification"));
//        tvCateChild.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("level2") + getLanguageValue("classification"));
        tvDateValue.setText(getLanguageValue("time") + getLanguageValue("choose"));
        tvDay0.setText(getLanguageValue("today"));
        tvDay1.setText(getLanguageValue("yesterday"));
        tvDay2.setText(getLanguageValue("thisWeek"));
        tvStartTime.setHint(getLanguageValue("start") + getLanguageValue("time"));
        tvEndTime.setHint(getLanguageValue("end") + getLanguageValue("time"));
        tvRankValue.setText(getLanguageValue("sale") + getLanguageValue("ranking"));
        tvOrderType0.setText(getLanguageValue("sale") + getLanguageValue("amount") + getLanguageValue("descend"));
        tvOrderType1.setText(getLanguageValue("sale") + getLanguageValue("amount") + getLanguageValue("ascend"));
        tvOrderType2.setText(getLanguageValue("salesVolume") + getLanguageValue("descend"));
        tvOrderType3.setText(getLanguageValue("salesVolume") + getLanguageValue("ascend"));
        tvResetting.setText(getLanguageValue("reset"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //title
        rvTitle.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        rightAdapter = new SaleStatisticsTitleAdapter(this);
        rvTitle.setAdapter(rightAdapter);
        rightAdapter.setDataList(strList);

        //
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new SaleStatisticsAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SaleStatisticsAdapter.MyListener() {
            @Override
            public void onStopScroll() {
                rvTitle.stopScroll();
            }

            @Override
            public void onScroll(int position, int offset) {
                LinearLayoutManager layoutManager = (LinearLayoutManager) rvTitle.getLayoutManager();
                if (layoutManager != null) {
                    //通过当前显示item的位置和偏移量的位置来置顶recycleview 也就是同步其它item的移动距离
                    layoutManager.scrollToPositionWithOffset(position, offset);
                }
            }
        });
        mAdapter.initRecyclerView(rvTitle);

        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getSaleStatisticsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getSaleStatisticsList();
            }
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(SaleStatisticsData data) {
        if (data == null) {
            if (dataList.size() > 0) {
                linTitle.setVisibility(View.VISIBLE);
                recyclerView.setVisibility(View.VISIBLE);
                linEmpty.setVisibility(View.GONE);
            } else {
                linTitle.setVisibility(View.GONE);
                recyclerView.setVisibility(View.GONE);
                linEmpty.setVisibility(View.VISIBLE);
            }
            return;
        }
        if (data.getData() == null) {
            if (dataList.size() > 0) {
                linTitle.setVisibility(View.VISIBLE);
                recyclerView.setVisibility(View.VISIBLE);
                linEmpty.setVisibility(View.GONE);
            } else {
                linTitle.setVisibility(View.GONE);
                recyclerView.setVisibility(View.GONE);
                linEmpty.setVisibility(View.VISIBLE);
            }
            return;
        }
        if (data.getObject() != null) {
            strList.clear();
            strList.add(getLanguageValue("salesVolume") + "(" + DFUtils.getNum2(data.getObject().getSaleCount()) + ")");
            strList.add(getLanguageValue("sale") + getLanguageValue("amount") + "(" + DFUtils.getNum2(data.getObject().getSaleSum()) + ")");
            strList.add(getLanguageValue("sale") + getLanguageValue("proportion"));
            strList.add(getLanguageValue("profits") + getLanguageValue("amount"));
            strList.add(getLanguageValue("commodity") + getLanguageValue("barcode"));
            rightAdapter.setDataList(strList);
        }
        if (page == 1) {
            dataList.clear();
        }
        dataList.addAll(data.getData());
        if (dataList.size() > 0) {
            linTitle.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.VISIBLE);
            linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            linTitle.setVisibility(View.GONE);
            recyclerView.setVisibility(View.GONE);
            linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 清除时间选择
     */
    private void clearDay() {
        tvDay0.setBackgroundResource(R.drawable.shape_f2_4);
        tvDay0.setTextColor(getResources().getColor(R.color.color_666));
        tvDay1.setBackgroundResource(R.drawable.shape_f2_4);
        tvDay1.setTextColor(getResources().getColor(R.color.color_666));
        tvDay2.setBackgroundResource(R.drawable.shape_f2_4);
        tvDay2.setTextColor(getResources().getColor(R.color.color_666));
    }

    /**
     * 清除排序选择
     */
    private void clearOrderType() {
        tvOrderType0.setBackgroundResource(R.drawable.shape_f2_4);
        tvOrderType0.setTextColor(getResources().getColor(R.color.color_666));
        tvOrderType1.setBackgroundResource(R.drawable.shape_f2_4);
        tvOrderType1.setTextColor(getResources().getColor(R.color.color_666));
        tvOrderType2.setBackgroundResource(R.drawable.shape_f2_4);
        tvOrderType2.setTextColor(getResources().getColor(R.color.color_666));
        tvOrderType3.setBackgroundResource(R.drawable.shape_f2_4);
        tvOrderType3.setTextColor(getResources().getColor(R.color.color_666));
    }

    /**
     * 获取筛选条件
     */
    private void getScreenMsg() {
        page = 1;
        getSaleStatisticsList();
        String screen = "";
        //一级分类
        if (!TextUtils.isEmpty(cateName)) {
            if (TextUtils.isEmpty(screen)) {
                screen = cateName;
            } else {
                screen = screen + "," + cateName;
            }
        }
        //二级分类
        if (!TextUtils.isEmpty(cateChildName)) {
            if (TextUtils.isEmpty(screen)) {
                screen = cateChildName;
            } else {
                screen = screen + "," + cateChildName;
            }
        }
        //三级分类
        if (!TextUtils.isEmpty(cateName2)) {
            if (TextUtils.isEmpty(screen)) {
                screen = cateName2;
            } else {
                screen = screen + "," + cateName2;
            }
        }
        //时间
        if (!TextUtils.isEmpty(startTime)) {
            if (TextUtils.isEmpty(screen)) {
                screen = startTime + getLanguageValue("to") + endTime;
            } else {
                screen = screen + "," + startTime + getLanguageValue("to") + endTime;
            }
        }
        //排序
        if (orderType != 0) {
            if (TextUtils.isEmpty(screen)) {
                screen = orderTypeName;
            } else {
                screen = screen + "," + orderTypeName;
            }
        }
        if (TextUtils.isEmpty(screen)) {
            linScreen.setVisibility(View.GONE);
        } else {
            linScreen.setVisibility(View.VISIBLE);
            tvScreen.setText(getLanguageValue("filter") + ":" + screen);
        }
    }

    /**
     * 商品销售统计
     */
    private void getSaleStatisticsList() {
        showDialog();
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsMessage", keyWords);//搜索关键字
        map.put("startTime", startTime);//不可为空
        map.put("endTime", endTime);//不可为空
        map.put("parUnique", cateUnique);
        map.put("goodsKindUnique", cateChildUnique);
        map.put("threeUnique", cateUnique2);
        if (orderType != 0) {
            map.put("orderType", orderType);
        }
        map.put("pageNum", page);
        map.put("pageSize", 20);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getsales(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        SaleStatisticsData data = new Gson().fromJson(s, SaleStatisticsData.class);
                        if (data.getStatus() == 1) {
                            setUI(data);
                        } else {
                            showMessage(data.getMsg());
                            if (page == 1) {
                                dataList.clear();
                                mAdapter.clear();
                            }
                            if (dataList.size() > 0) {
                                linTitle.setVisibility(View.VISIBLE);
                                recyclerView.setVisibility(View.VISIBLE);
                                linEmpty.setVisibility(View.GONE);
                            } else {
                                linTitle.setVisibility(View.GONE);
                                recyclerView.setVisibility(View.GONE);
                                linEmpty.setVisibility(View.VISIBLE);
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            linTitle.setVisibility(View.VISIBLE);
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            linTitle.setVisibility(View.GONE);
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    etSearch.setText(data.getStringExtra("result"));
                    page = 1;
                    getSaleStatisticsList();
                    break;
            }
        }
    }
}
