package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Describe:店铺-工具（实体类）
 * Created by jingang on 2023/2/6
 */
public class ShopToolData {
    /**
     * shopUnique : 8302016134121
     * modularType : 1
     * list : [{"newTitle":1,"titleImg":"image/shopTitle/qrcode.png","titleName":"上传二维码","titleSort":0,"addTime":"2018-12-21","id":"108680","modularNum":15},{"newTitle":1,"titleImg":"image/shopTitle/jjjl.png","titleName":"交接班记录","titleSort":1,"addTime":"2018-12-21","id":"108678","modularNum":13},{"newTitle":1,"titleImg":"image/shopTitle/skcpd.png","titleName":"库存盘点","titleSort":2,"addTime":"2018-12-21","id":"108668","modularNum":3},{"newTitle":1,"titleImg":"image/shopTitle/yjfk.png","titleName":"意见反馈","titleSort":3,"addTime":"2018-12-21","id":"108679","modularNum":14},{"newTitle":1,"titleImg":"image/shopTitle/sxstj.png","titleName":"销售统计","titleSort":4,"addTime":"2018-12-21","id":"108671","modularNum":6},{"newTitle":1,"titleImg":"image/shopTitle/cwd.png","titleName":"财务对账","titleSort":5,"addTime":"2018-12-21","id":"108677","modularNum":12},{"newTitle":1,"titleImg":"image/shopTitle/sxxzx.png","titleName":"消息中心","titleSort":6,"addTime":"2018-12-21","id":"108675","modularNum":10},{"newTitle":1,"titleImg":"image/shopTitle/sydsy.png","titleName":"移动收银","titleSort":7,"addTime":"2018-12-21","id":"108666","modularNum":1},{"newTitle":1,"titleImg":"image/shopTitle/skc.png","titleName":"库存","titleSort":8,"addTime":"2018-12-21","id":"108670","modularNum":5},{"newTitle":1,"titleImg":"image/shopTitle/sjysj.png","titleName":"经营数据","titleSort":9,"addTime":"2018-12-21","id":"108667","modularNum":2},{"newTitle":1,"titleImg":"image/shopTitle/yjfk.png","titleName":"会员管理","titleSort":10,"addTime":"2018-12-21","id":"108669","modularNum":4},{"newTitle":1,"titleImg":"image/shopTitle/sspcx.png","titleName":"商品促销","titleSort":12,"addTime":"2018-12-21","id":"108674","modularNum":9},{"newTitle":1,"titleImg":"image/shopTitle/.png","titleName":"出入库","titleSort":13,"addTime":"2018-12-21","id":"108672","modularNum":7}]
     */

    private long shopUnique;
    private int modularType;
    private String modularName;
    private List<ListBean> list;

    public long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public int getModularType() {
        return modularType;
    }

    public void setModularType(int modularType) {
        this.modularType = modularType;
    }

    public String getModularName() {
        return modularName;
    }

    public void setModularName(String modularName) {
        this.modularName = modularName;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean {
        /**
         * newTitle : 1
         * titleImg : image/shopTitle/qrcode.png
         * titleName : 上传二维码
         * titleSort : 0
         * addTime : 2018-12-21
         * id : 108680
         * modularNum : 15
         */

        private int newTitle;
        private String titleImg;
        private String titleName;
        private int titleSort;
        private String addTime;
        private String id;
        private int modularNum;

        public int getNewTitle() {
            return newTitle;
        }

        public void setNewTitle(int newTitle) {
            this.newTitle = newTitle;
        }

        public String getTitleImg() {
            return titleImg;
        }

        public void setTitleImg(String titleImg) {
            this.titleImg = titleImg;
        }

        public String getTitleName() {
            return titleName;
        }

        public void setTitleName(String titleName) {
            this.titleName = titleName;
        }

        public int getTitleSort() {
            return titleSort;
        }

        public void setTitleSort(int titleSort) {
            this.titleSort = titleSort;
        }

        public String getAddTime() {
            return addTime;
        }

        public void setAddTime(String addTime) {
            this.addTime = addTime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getModularNum() {
            return modularNum;
        }

        public void setModularNum(int modularNum) {
            this.modularNum = modularNum;
        }
    }
}
