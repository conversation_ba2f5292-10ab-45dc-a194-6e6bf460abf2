package cn.bl.mobile.buyhoostore_sea.ui.sale.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.BaseDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.StringAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.StringData;

/**
 * Describe:dialog（销售订单-筛选）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SaleScreenDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvShowValue)
    TextView tvShowValue;
    @BindView(R.id.ivDialogShow)
    ImageView ivShow;
    @BindView(R.id.tvTimeValue)
    TextView tvTimeValue;
    @BindView(R.id.rvDialogTime)
    RecyclerView rvTime;
    @BindView(R.id.tvDialogStartTime)
    TextView tvStartTime;
    @BindView(R.id.tvDialogEndTime)
    TextView tvEndTime;
    @BindView(R.id.tvTypeValue)
    TextView tvTypeValue;
    @BindView(R.id.rvDialogType)
    RecyclerView rvSaleType;
    @BindView(R.id.tvPayTypeValue)
    TextView tvPayTypeValue;
    @BindView(R.id.rvDialogPayType)
    RecyclerView rvPayType;
    @BindView(R.id.tvDialogResetting)
    TextView tvDialogResetting;
    @BindView(R.id.tvDialogConfirm)
    TextView tvDialogConfirm;

    public static int time = -1, saleType = -1, saleListPayment = -1;
    public static String startTime, endTime;
    private boolean isShowOrderCancel;//是否展示已取消订单
    private List<StringData> timeList = new ArrayList<>(),
            saleTypeList = new ArrayList<>(),
            payTypeList = new ArrayList<>();
    private StringAdapter timeAdapter, saleTypeAdapter, payTypeAdapter;

    public static void showDialog(Context context, int time, String startTime, String endTime, int saleType, int saleListPayment, MyListener listener) {
        SaleScreenDialog.listener = listener;
        SaleScreenDialog.time = time;
        SaleScreenDialog.saleType = saleType;
        SaleScreenDialog.startTime = startTime;
        SaleScreenDialog.endTime = endTime;
        SaleScreenDialog.saleListPayment = saleListPayment;
        SaleScreenDialog dialog = new SaleScreenDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public SaleScreenDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_screen_sale);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("order") + getLanguageValue("filter"));
        tvShowValue.setText(getLanguageValue("show") + getLanguageValue("cancelled") + getLanguageValue("order"));
        tvTimeValue.setText(getLanguageValue("placeAnOrder") + getLanguageValue("time"));
        tvStartTime.setHint(getLanguageValue("start") + getLanguageValue("time"));
        tvEndTime.setHint(getLanguageValue("end") + getLanguageValue("time"));
        tvTypeValue.setText(getLanguageValue("order") + getLanguageValue("type"));
        tvPayTypeValue.setText(getLanguageValue("collection")+getLanguageValue("way"));
        tvDialogResetting.setText(getLanguageValue("reset"));
        tvDialogConfirm.setText(getLanguageValue("confirm"));

        setDate();
        initData();
        setAdapter();
    }

    @OnClick({R.id.ivDialogClose, R.id.ivDialogShow, R.id.tvDialogStartTime, R.id.tvDialogEndTime, R.id.tvDialogResetting, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogShow:
                //是否展示已取消订单
                isShowOrderCancel = !isShowOrderCancel;
                ivShow.setSelected(isShowOrderCancel);
                break;
            case R.id.tvDialogStartTime:
                //开始时间
            case R.id.tvDialogEndTime:
                //结束时间
                DateStartEndDialog.showDialog(getContext(),
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            time = -1;
                            startTime = startDate;
                            endTime = endDate;
                            for (int i = 0; i < timeList.size(); i++) {
                                timeList.get(i).setCheck(false);
                            }
                            timeAdapter.setDataList(timeList);
                            setDate();
                        });
                break;
            case R.id.tvDialogResetting:
                //重置
                time = -1;
                saleType = -1;
                saleListPayment = -1;
                startTime = "";
                endTime = "";
                for (int i = 0; i < timeList.size(); i++) {
                    timeList.get(i).setCheck(false);
                }
                timeAdapter.setDataList(timeList);
                for (int i = 0; i < saleTypeList.size(); i++) {
                    saleTypeList.get(i).setCheck(false);
                }
                saleTypeAdapter.setDataList(saleTypeList);
                for (int i = 0; i < payTypeList.size(); i++) {
                    payTypeList.get(i).setCheck(false);
                }
                payTypeAdapter.setDataList(payTypeList);
                SharedUtils.put(Constants.IS_SHOW_ORDER_CANCEL, "");
                setDate();
                if (listener != null) {
                    listener.onClick(view, isShowOrderCancel, time, startTime, endTime, saleType, saleListPayment);
                    dismiss();
                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                SharedUtils.put(Constants.IS_SHOW_ORDER_CANCEL, isShowOrderCancel ? Constants.IS_SHOW_ORDER_CANCEL : "");
//                if (TextUtils.isEmpty(startTime) && TextUtils.isEmpty(endTime) && saleType == -1 && saleListPayment == -1) {
//                    showMessage("请选择筛选条件");
//                    return;
//                }
                if (listener != null) {
                    listener.onClick(view, isShowOrderCancel, time, startTime, endTime, saleType, saleListPayment);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //下单时间
        rvTime.setLayoutManager(new GridLayoutManager(getContext(), 3));
        timeAdapter = new StringAdapter(getContext());
        rvTime.setAdapter(timeAdapter);
        timeAdapter.setDataList(timeList);
        timeAdapter.setOnItemClickListener((view1, position) -> {
            if (time != timeList.get(position).getId()) {
                for (int i = 0; i < timeList.size(); i++) {
                    timeList.get(i).setCheck(false);
                }
                timeList.get(position).setCheck(true);
                time = timeList.get(position).getId();
                timeAdapter.setDataList(timeList);
                setDate();
            }
        });
        //订单类型
        rvSaleType.setLayoutManager(new GridLayoutManager(getContext(), 3));
        saleTypeAdapter = new StringAdapter(getContext());
        rvSaleType.setAdapter(saleTypeAdapter);
        saleTypeAdapter.setDataList(saleTypeList);
        saleTypeAdapter.setOnItemClickListener((view12, position) -> {
            if (saleType != saleTypeList.get(position).getId()) {
                for (int i = 0; i < saleTypeList.size(); i++) {
                    saleTypeList.get(i).setCheck(false);
                }
                saleTypeList.get(position).setCheck(true);
                saleType = saleTypeList.get(position).getId();
                saleTypeAdapter.setDataList(saleTypeList);
            }
        });
        //付款方式
        rvPayType.setLayoutManager(new GridLayoutManager(getContext(), 3));
        payTypeAdapter = new StringAdapter(getContext());
        rvPayType.setAdapter(payTypeAdapter);
        payTypeAdapter.setDataList(payTypeList);
        payTypeAdapter.setOnItemClickListener((view13, position) -> {
            if (saleListPayment != payTypeList.get(position).getId()) {
                for (int i = 0; i < payTypeList.size(); i++) {
                    payTypeList.get(i).setCheck(false);
                }
                payTypeList.get(position).setCheck(true);
                saleListPayment = payTypeList.get(position).getId();
                payTypeAdapter.setDataList(payTypeList);
            }
        });
    }

    private void setDate() {
        switch (time) {
            case -1:
                break;
            case 0:
                startTime = DateUtils.getOldDate(0);
                endTime = DateUtils.getOldDate(0);
                break;
            case 1:
                startTime = DateUtils.getOldDate(-1);
                endTime = DateUtils.getOldDate(-1);
                break;
            case 2:
                startTime = DateUtils.getOldDate(-2);
                endTime = DateUtils.getOldDate(-2);
                break;
            default:
                startTime = "";
                endTime = "";
                break;
        }
        if (time == -1) {
            tvStartTime.setText(startTime);
            tvEndTime.setText(endTime);
        } else {
            tvStartTime.setText("");
            tvEndTime.setText("");
        }
        //是否展示已取消订单
        isShowOrderCancel = !TextUtils.isEmpty(SharedUtils.get(Constants.IS_SHOW_ORDER_CANCEL, IPreference.DataType.STRING));
        ivShow.setSelected(isShowOrderCancel);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        //下单时间
        timeList.clear();
        timeList.add(new StringData(0, getLanguageValue("today"), false));
        timeList.add(new StringData(1, getLanguageValue("yesterday"), false));
        timeList.add(new StringData(2, getLanguageValue("dayBeforeYesterday"), false));
        for (int i = 0; i < timeList.size(); i++) {
            if (time == timeList.get(i).getId()) {
                timeList.get(i).setCheck(true);
            }
        }
        //订单类型
        saleTypeList.clear();
        saleTypeList.add(new StringData(1, getLanguageValue("localCashier"), false));
        saleTypeList.add(new StringData(2, getLanguageValue("shop") + getLanguageValue("pickupYourself"), false));
        saleTypeList.add(new StringData(3, getLanguageValue("homeDelivery"), false));
        for (int i = 0; i < saleTypeList.size(); i++) {
            if (saleType == saleTypeList.get(i).getId()) {
                saleTypeList.get(i).setCheck(true);
            }
        }
        //收款类型
        payTypeList.clear();
        payTypeList.add(new StringData(3, getLanguageValue("wechat"), false));
        payTypeList.add(new StringData(2, getLanguageValue("alipay"), false));
        payTypeList.add(new StringData(14, getLanguageValue("aggregateCode"), false));
        payTypeList.add(new StringData(13, getLanguageValue("goldenCirclePlatform"), false));
        payTypeList.add(new StringData(8, getLanguageValue("blend") + getLanguageValue("payment"), false));
        payTypeList.add(new StringData(1, getLanguageValue("cash") + getLanguageValue("collection"), false));
        payTypeList.add(new StringData(4, getLanguageValue("bankCard"), false));
        payTypeList.add(new StringData(5, getLanguageValue("storedValueCard"), false));
        payTypeList.add(new StringData(10, getLanguageValue("integral") + getLanguageValue("deduction"), false));
        for (int i = 0; i < payTypeList.size(); i++) {
            if (saleListPayment == payTypeList.get(i).getId()) {
                payTypeList.get(i).setCheck(true);
            }
        }
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param view
         * @param isShowOrderCancel 显示已取消订单
         * @param time              下单时间
         * @param startTime
         * @param endTime
         * @param saleType          订单类型
         * @param saleListPayment   收款方式
         */
        void onClick(View view, boolean isShowOrderCancel, int time, String startTime, String endTime, int saleType, int saleListPayment);
    }
}
