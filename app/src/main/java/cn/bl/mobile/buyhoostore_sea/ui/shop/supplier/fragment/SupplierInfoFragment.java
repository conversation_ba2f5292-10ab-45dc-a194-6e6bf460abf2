package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierGoodsActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierInfosData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商详情-业务信息
 * Created by jingang on 2023/9/4
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierInfoFragment extends LazyBaseFragment {
    @BindView(R.id.tvGoodsValue)
    TextView tvGoodsValue;
    @BindView(R.id.tvGoods)
    TextView tvGoods;
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.tvPendingValue)
    TextView tvPendingValue;
    @BindView(R.id.tvPending)
    TextView tvPending;
    @BindView(R.id.tvPaymentValue)
    TextView tvPaymentValue;
    @BindView(R.id.tvPayment)
    TextView tvPayment;
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;
    @BindView(R.id.tvOrderCount)
    TextView tvOrderCount;
    @BindView(R.id.tvSupplierValue)
    TextView tvSupplierValue;
    @BindView(R.id.tvSupplier)
    TextView tvSupplier;

    private String supplierUnique, supplierName;

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SupplierInfoFragment newInstance(String unique, String name) {
        SupplierInfoFragment fragment = new SupplierInfoFragment();
        Bundle bundle = new Bundle();
        bundle.putString("unique", unique);
        bundle.putString("name", name);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_supplier_info;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {

    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        assert getArguments() != null;
        supplierUnique = getArguments().getString("unique");
        supplierName = getArguments().getString("name");
        getInfo();
    }

    @OnClick({R.id.linGoods, R.id.linTotal, R.id.linPending, R.id.linPayment, R.id.linOrderCount, R.id.linSupplier})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linGoods:
                //所供商品
                startActivity(new Intent(getActivity(), SupplierGoodsActivity.class)
                        .putExtra("unique", supplierUnique)
                        .putExtra("name", supplierName)
                );
                break;
            case R.id.linTotal:
                //采购总额
                break;
            case R.id.linPending:
                //待结金额
                break;
            case R.id.linPayment:
                //已结金额0
                break;
            case R.id.linOrderCount:
                //订单数量
                break;
            case R.id.linSupplier:
                //替换供货商
                break;
        }
    }
    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_LIST:
                //刷新详情
                getInfo();
                break;
        }
    }

    @Override
    public void setText() {
        tvGoodsValue.setText(getLanguageValue("goodsSupplied"));
        tvTotalValue.setText(getLanguageValue("procurement")+getLanguageValue("amount"));
        tvPaymentValue.setText(getLanguageValue("settled")+getLanguageValue("amount"));
        tvPendingValue.setText(getLanguageValue("beSettled")+getLanguageValue("amount"));
        tvCountValue.setText(getLanguageValue("order")+getLanguageValue("quantity"));
        tvSupplierValue.setText(getLanguageValue("replaceTheSupplier"));
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(SupplierInfosData data) {
        if (data == null) {
            return;
        }
        tvGoods.setText(data.getGoodsTypeCount() + getLanguageValue("numberTypes"));
        tvTotal.setText(DFUtils.getNum2(data.getPurchaseAmount()));
        tvPending.setText(DFUtils.getNum2(data.getOutstandingAmount()));
        tvPayment.setText(DFUtils.getNum2(data.getSettledAmount()));
        tvOrderCount.setText(String.valueOf(data.getBillCount()));
    }


    /**
     * 业务信息
     */
    private void getInfo() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierInfos(),
                map,
                SupplierInfosData.class,
                new RequestListener<SupplierInfosData>() {
                    @Override
                    public void success(SupplierInfosData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

}
