package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:店铺-聚合码-选择申请人身份
 * Created by jingang on 2023/5/9
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyTypeActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvTips0)
    TextView tvTips0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.tvTips1)
    TextView tvTips1;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_aggregation_apply_type;
    }

    @Override
    public void initViews() {

    }

    @OnClick({R.id.ivBack, R.id.linType0, R.id.linType1})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linType0:
                //法人申请
                startActivityForResult(new Intent(this, AggregationApplyActivity0.class), Constants.CREDIT);
                break;
            case R.id.linType1:
                //非法人申请（他人代理）
                startActivityForResult(new Intent(this, AggregationApplyActivity1.class)
                                .putExtra("guideImage", getIntent().getStringExtra("guideImage"))
                                .putExtra("helibao", getIntent().getStringExtra("helibao"))
                                .putExtra("ruiyinxin", getIntent().getStringExtra("ruiyinxin"))
                        , Constants.CREDIT);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("pleaseSelect")+getLanguageValue("identityApplicant"));
        tvType0.setText(getLanguageValue("legalPerson")+getLanguageValue("apply"));
        tvTips0.setText(getLanguageValue("registerMyself"));
        tvType1.setText(getLanguageValue("unincorporatedApplication"));
        tvTips1.setText(getLanguageValue("registerOther"));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constants.CREDIT) {
            setResult(Constants.CREDIT, new Intent());
            finish();
        }
    }
}
