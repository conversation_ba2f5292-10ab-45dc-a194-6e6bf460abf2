package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:
 * Created by jingang on 2023/3/25
 */
public class GoodsSearchHistoryAdapter extends BaseAdapter<String> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public GoodsSearchHistoryAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_search_history;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> {
                onItemClickListener.onItemClick(holder.itemView, position);
            });
        }
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position));
    }
}
