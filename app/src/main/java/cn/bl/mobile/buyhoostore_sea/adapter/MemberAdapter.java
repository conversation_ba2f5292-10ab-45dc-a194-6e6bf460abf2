package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;

/**
 * Created by Administrator on 2017/7/24 0024.
 */
public class MemberAdapter extends BaseAdapter<MemberBean.DataBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    private int type;//1.宁宇会员

    public void setType(int type) {
        this.type = type;
    }

    public MemberAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member_tab;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }

        ImageView ivHead, ivLevel;
        TextView tvName, tvUnique, tvPoints, tvBalance;
        ivHead = holder.getView(R.id.ivItemHead);
        tvName = holder.getView(R.id.tvItemName);
        tvUnique = holder.getView(R.id.tvItemUnique);
        tvPoints = holder.getView(R.id.tvItemPoints);
        tvBalance = holder.getView(R.id.tvItemBalance);
        ivLevel = holder.getView(R.id.ivItemLevel);

        if (type == 1) {
            //宁宇会员
            tvName.setText(mDataList.get(position).getCusName());
            tvUnique.setText("NO." + mDataList.get(position).getCusUnique());
            tvPoints.setText(getLanguageValue("integral")+":" + mDataList.get(position).getCus_points());
            tvBalance.setText(getLanguageValue("balance")+":" + (mDataList.get(position).getCusBalance() + mDataList.get(position).getCus_rebate()));
            ivLevel.setVisibility(View.GONE);
        } else {
            ivLevel.setVisibility(View.VISIBLE);
            Glide.with(mContext)
                    .load(StringUtils.handledImgUrl(mDataList.get(position).getCusHeadPath()))
                    .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                    .into(ivHead);
            tvName.setText(mDataList.get(position).getCusName());
            tvUnique.setText("NO." + mDataList.get(position).getCus_unique());
            tvPoints.setText(getLanguageValue("integral")+":" + mDataList.get(position).getCus_points());
            String cus_type = mDataList.get(position).getCus_type();
            if (TextUtils.isEmpty(cus_type)) {
                tvBalance.setText("");
            } else {
                if (!cus_type.equals(getLanguageValue("cus"))) {
                    tvBalance.setText(getLanguageValue("balance")+":" + mDataList.get(position).getCus_balance());
                } else {
                    tvBalance.setText("");
                }
            }
            String level = mDataList.get(position).getCus_level_val();
            if (!TextUtils.isEmpty(level)) {
                switch (level) {
                    case "1":
                        ivLevel.setImageResource(R.mipmap.ic_vip0);
                        break;
                    case "2":
                        ivLevel.setImageResource(R.mipmap.ic_vip1);
                        break;
                    case "3":
                        ivLevel.setImageResource(R.mipmap.ic_vip2);
                        break;
                    case "4":
                        ivLevel.setImageResource(R.mipmap.ic_vip3);
                        break;
                }
            }
        }
    }
}

