package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.StringCallback;
import com.lzy.okgo.model.Response;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.LazyBaseFragment;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.RefundOrderBean;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.evevtbus.FirstEvent;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.RefundRefuseReasonDialog;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.RefundOrderAdapter;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import kotlin.jvm.JvmStatic;

/**
 * Describe:销售-退款单
 * Created by jingang on 2023/3/24
 */
@SuppressLint("NonConstantResourceId")
public class RefundOrderListFragment extends LazyBaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private RefundOrderAdapter mAdapter;
    private ArrayList<RefundOrderBean> dataList = new ArrayList<>();

    private String staffId = null;
    private SharedPreferences sp = null;
    public int orderStatus = -1;

    @JvmStatic
    public static RefundOrderListFragment getNewInstance(int orderStatus) {
        RefundOrderListFragment fragment = new RefundOrderListFragment();
        Bundle args = new Bundle();
        args.putInt("orderStatus", orderStatus);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        orderStatus = getArguments().getInt("orderStatus");
        sp = getActivity().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        staffId = sp.getString("staffId", "");
        setAdapter();
    }

    @Override
    public void onResume() {
        super.onResume();
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Subscribe
    public void onEventMainThread(FirstEvent event) {
        String msg = event.getMsg();
        if (Constants.CONSTANT_REFRESH_ORSER_REFUND_LIST.equals(msg)) {
            page = 1;
            gainOrderList();
        }
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        page = 1;
        gainOrderList();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new RefundOrderAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener((type, saleOrderBean) -> {
            if (type == 0) {
                startActivity(new Intent(getActivity(), RefundOrderInfoActivity.class)
                        .putExtra("orderId", saleOrderBean.getRetListUnique())
                );
            } else if (type == 1) {
                RefundRefuseReasonDialog.showDialog(getActivity(), msg -> {
                    handleRefund(false, saleOrderBean, msg);
                });
            } else if (type == 2) {
                IAlertDialog.showDialog(getActivity(),
                        getLanguageValue("confirm") + getLanguageValue("refund") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            handleRefund(true, saleOrderBean, "");
                        });
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            gainOrderList();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            gainOrderList();
        });
    }

    /**
     * 退款
     */
    private void handleRefund(boolean isAgree, RefundOrderBean bean, String reason) {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        int status = 4;
        if (isAgree) {
            status = 3;
        }
        OkGo.<String>post(ZURL.getHandelRefundUrL()).tag(this).params("retListHandlestate", status).params("retListUnique", bean.getRetListUnique()).params("retListRemarks", reason).params("staffId", staffId).params("macId", "安卓Klip").execute(new StringCallback() {
            @Override
            public void onSuccess(Response<String> response) {
                JSONObject jsonObject = JSON.parseObject(response.body());
                try {
                    if (jsonObject.getInteger("status") == 1) {
                        hideDialog();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        page = 1;
                        gainOrderList();
                    } else {
                        ToastUtil.showToast(getActivity(), jsonObject.getString("msg"));
                    }
                } catch (Exception e) {
                    Log.e("1111", e.toString());
                }
            }

            @Override
            public void onError(Response<String> response) {
                super.onError(response);
                hideDialog();
            }
        });
    }

    /**
     * 订单列表
     */
    private void gainOrderList() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        OkGo.<String>post(ZURL.getRefundOrderList()).tag(this).params("shopUnique", getShop_id()).params("retListHandlestate", orderStatus).params("page", page).params("pageSize", Constants.limit).execute(new StringCallback() {
            @Override
            public void onSuccess(Response<String> response) {
                hideDialog();
                RefundOrderBean data = new Gson().fromJson(response.body(), RefundOrderBean.class);
                if (data.getStatus() == 1) {
                    EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
                    if (page == 1) {
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                    } else {
                        smartRefreshLayout.finishLoadMore();
                    }
                    dataList.addAll(data.getData());
                    if (dataList.size() > 0) {
                        recyclerView.setVisibility(View.VISIBLE);
                        linEmpty.setVisibility(View.GONE);
                        mAdapter.setDataList(dataList);
                    } else {
                        recyclerView.setVisibility(View.GONE);
                        linEmpty.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onError(Response<String> response) {
                super.onError(response);
                showMessage(getLanguageValue("networkError"));
                hideDialog();
                if (page == 1) {
                    smartRefreshLayout.finishRefresh();
                    dataList.clear();
                    mAdapter.clear();
                    recyclerView.setVisibility(View.GONE);
                    linEmpty.setVisibility(View.VISIBLE);
                } else {
                    smartRefreshLayout.finishLoadMore();
                }
            }
        });
    }

}