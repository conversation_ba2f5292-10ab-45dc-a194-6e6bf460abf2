package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.BaseDialog;

/**
 * Describe:dialog（创建补货计划）
 * Created by jingang on 2023/6/20
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RestockPlanAddDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.etDialogContent)
    EditText etContent;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    public static void showDialog(Context context, MyListener listener) {
        RestockPlanAddDialog.listener = listener;
        RestockPlanAddDialog dialog = new RestockPlanAddDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RestockPlanAddDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_restock_plan_add);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("create") + getLanguageValue("replenishment") + getLanguageValue("plan"));
        etContent.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("replenishment") + getLanguageValue("plan") + getLanguageValue("name"));
        tvConfirm.setText(getLanguageValue("confirm") + getLanguageValue("create"));
    }

    @OnClick({R.id.ivDialogClose, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认创建
                if (listener != null) {
                    listener.onClick(etContent.getText().toString().trim());
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    @Override
    public void dismiss() {
        View view = getCurrentFocus();
        if (view instanceof TextView) {
            InputMethodManager mInputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.RESULT_UNCHANGED_SHOWN);
        }
        super.dismiss();
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(String content);
    }
}
