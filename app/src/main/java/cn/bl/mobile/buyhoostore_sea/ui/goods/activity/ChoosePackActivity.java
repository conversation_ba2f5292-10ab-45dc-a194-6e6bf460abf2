package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.adapter.GoodsDanWeiSearchAdapter;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsDanWeiBean;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CircularBeadDialog_center;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsUnitAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsUnitData;
import cn.bl.mobile.buyhoostore_sea.view.SlideListView;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * @Author: Great Han
 * @Description: 接盘添加注释：商品单位页
 * @Date: 17:07 2019/6/13
 * @email:<EMAIL>
 */
@SuppressLint("NonConstantResourceId")
public class ChoosePackActivity extends BaseActivity{
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;

    private String keyWords;

    private GoodsUnitAdapter mAdapter;
    private List<GoodsUnitData> dataList = new ArrayList<>();

    GoodsDanWeiBean goodsDanWeiBean;
//    List<GoodsDanWeiBean.DataBean> dataBeanList = new ArrayList<>();
//    GoodsDanWeiSearchAdapter goodsDanWeiSearchAdapter;
    CircularBeadDialog_center adddanwei_dialog;
    String type;
    String goods_unit_id;
    float x_down = 0; //mListView选中Item按下时的x坐标
    float x_up = 0;   //mListView选中Item松开时的x坐标
    float xMove = 0; //mListView选中Item手指移动坐标
    private int mACTION_UP_distanX;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_choose_pack;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("choose") + getLanguageValue("unit"));
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText(getLanguageValue("addTo") + getLanguageValue("unit"));

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
//            getgoodslist();
            getUnitList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });

        setAdapter();
    }

    @Override
    public void initData() {
//        getgoodslist();
        getUnitList();
    }

    @OnClick({R.id.ivBack, R.id.tvRight, R.id.ivClear})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                //新增
                type = "add";
                adddialog(type);
                break;
            case R.id.ivClear:
                //清除搜索
                etSearch.setText("");
//                getgoodslist();
                getUnitList();
                break;
        }
    }

    @Override
    public void setText() {
        tvTips.setText("*" + getLanguageValue("leftDelete"));
        etSearch.setHint(getLanguageValue("unit") + getLanguageValue("search"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter(){
        mAdapter = new GoodsUnitAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new GoodsUnitAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                setResult(RESULT_OK,new Intent()
                        .putExtra("result",dataList.get(position).getGoods_unit())
                        .putExtra("id",dataList.get(position).getGoods_unit_id())
                        .putExtra("name",dataList.get(position).getGoods_unit())
                );
                finish();
            }

            @Override
            public void onEditClick(View view, int position) {
                goods_unit_id = dataList.get(position).getGoods_unit_id();
                type = "update";
                adddialog(type);
            }

            @Override
            public void onDelClick(View view, int position) {
                goods_unit_id = dataList.get(position).getGoods_unit_id();
                delete();
            }
        });
    }

//    private void inintView() {
//        list_danwei.setOnItemClickListener((parent, view, position, id) -> {
//            if (mACTION_UP_distanX >= 0) {
//                String danwei = goodsDanWeiBean.getData().get(position).getGoods_unit();
//                Intent resultIntent = new Intent();
//                resultIntent.putExtra("result", danwei);
//                resultIntent.putExtra("id", goodsDanWeiBean.getData().get(position).getGoods_unit_id());
//                resultIntent.putExtra("name", goodsDanWeiBean.getData().get(position).getGoods_unit());
//                ChoosePackActivity.this.setResult(RESULT_OK, resultIntent);
//                ChoosePackActivity.this.finish();
//            }
//        });
//    }

//    @Override
//    public boolean onTouch(View v, MotionEvent event) {
//        boolean result = false;
//        switch (event.getAction()) {
//            case MotionEvent.ACTION_DOWN:
//                x_down = (int) event.getRawX();
//                Log.e(tag, "ACTION_UP_x_down = " + x_down);
//                break;
//            case MotionEvent.ACTION_MOVE:
//                xMove = event.getRawX();
//                //活动的距离
//                int distanceX = (int) (xMove - x_down);
//                break;
//            case MotionEvent.ACTION_UP:
//                x_up = (int) event.getRawX();
//                mACTION_UP_distanX = (int) (x_up - x_down);
//                break;
//            default:
//                break;
//        }
//        return result;
//    }


    private void getUnitList(){
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("goods_unit", keyWords);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsUnitList(),
                map,
                GoodsUnitData.class,
                new RequestListListener<GoodsUnitData>() {
                    @Override
                    public void onResult(List<GoodsUnitData> list) {
                        hideDialog();
                        dataList.clear();
                        dataList.addAll(list);
                        mAdapter.setDataList(dataList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                    }
                });
    }

    /**
     * 单位列表
     */
//    public void getgoodslist() {
//        Map<String, Object> params = new HashMap<>();
//        params.put("shop_unique", getShop_id());
//        params.put("goods_unit", keyWords);
//        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
//                ZURL.ONLINE_URL + "shopUpdate/goodsUnit/findGoodsUnitList.do?",
//                params,
//                new RequestListener<String>() {
//                    @Override
//                    public void success(String s) {
//                        Log.e("111111", "单位列表 = " + s);
//                        if (TextUtils.isEmpty(s)) {
//                            return;
//                        }
//                        int status3 = 2;
//                        JSONObject object3;
//                        try {
//                            object3 = new JSONObject(s);
//                            status3 = object3.getInt("status");
//                        } catch (JSONException e) {
//                            e.printStackTrace();
//                        }
//                        if (status3 == 1) {
//                            Gson gson1 = new Gson();
//                            goodsDanWeiBean = gson1.fromJson(s, GoodsDanWeiBean.class);
//                            dataBeanList.clear();
//                            for (int t = 0; t < goodsDanWeiBean.getData().size(); t++) {
//                                dataBeanList.add(goodsDanWeiBean.getData().get(t));
//                            }
//                            goodsDanWeiSearchAdapter = new GoodsDanWeiSearchAdapter(getApplicationContext(), dataBeanList);
//                            list_danwei.setAdapter(goodsDanWeiSearchAdapter);
//                            goodsDanWeiSearchAdapter.setOnClickListenerEditOrDelete(new GoodsDanWeiSearchAdapter.OnClickListenerEditOrDelete() {
//                                @Override
//                                public void OnClickListenerEdit(int position) {
//                                    //左滑出的编辑操作
//                                    if (x_down != x_up) {
//                                        goods_unit_id = dataBeanList.get(position).getGoods_unit_id();
//                                        type = "update";
//                                        adddialog(type);
//                                    }
//                                }
//
//                                @Override
//                                public void OnClickListenerDelete(int position) {
//                                    //左滑出的删除操作
//                                    if (x_down != x_up) {
//                                        goods_unit_id = dataBeanList.get(position).getGoods_unit_id();
//                                        delete();
//                                    }
//                                }
//                            });
//                            goodsDanWeiSearchAdapter.notifyDataSetChanged();
//                        }
//                    }
//                });
//    }


    EditText edit_danwei;
    Button btn_dwcancle, btn_dwqueren;
    TextView text_title;

    private void adddialog(final String type) {
        try {
            View view = getLayoutInflater().inflate(R.layout.add_danwei_dialog, null);
            adddanwei_dialog = new CircularBeadDialog_center(this, view, R.style.Dialog);
            edit_danwei = adddanwei_dialog.findViewById(R.id.edit_danwei);
            btn_dwqueren = adddanwei_dialog.findViewById(R.id.btn_dwqueren);
            btn_dwcancle = adddanwei_dialog.findViewById(R.id.btn_dwcancle);
            text_title = adddanwei_dialog.findViewById(R.id.text_title);
            if (type.equals("add")) {
                text_title.setText(getLanguageValue("add") + getLanguageValue("unit"));
                edit_danwei.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("add") + getLanguageValue("unit"));
            } else {
                text_title.setText(getLanguageValue("editor") + getLanguageValue("unit"));
                edit_danwei.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("editor") + getLanguageValue("unit"));
            }
            btn_dwqueren.setOnClickListener(v -> {
                String goods_unit = edit_danwei.getText().toString();
                if (goods_unit.equals("")) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("unit"));
                    return;
                }
                if (type.equals("add")) {
                    adddanwei(goods_unit);
                } else if (type.equals("update")) {
                    update(goods_unit);
                }
                adddanwei_dialog.dismiss();
            });
            btn_dwcancle.setOnClickListener(v -> adddanwei_dialog.dismiss());
            adddanwei_dialog.setCanceledOnTouchOutside(false);
            adddanwei_dialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 新增单位
     */
    public void adddanwei(String unit) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        params.put("goods_unit", unit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.ONLINE_URL + "/shopUpdate/goodsUnit/addGoodsUnit.do?",
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "新增单位 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int status2 = 2;
                        JSONObject object2;
                        try {
                            object2 = new JSONObject(s);
                            status2 = object2.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status2 == 1) {
                            showMessage(getLanguageValue("addTo") + getLanguageValue("succeed"));
//                            getgoodslist();
                            getUnitList();
                        }
                    }
                });
    }

    /**
     * 编辑单位
     */
    public void update(String unit) {
        Map<String, Object> params = new HashMap<>();
        params.put("goods_unit_id", goods_unit_id);
        params.put("goods_unit", unit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.ONLINE_URL + "/shopUpdate/goodsUnit/editGoodsUnit.do?",
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "编辑单位 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int status4 = 2;
                        JSONObject object4;
                        try {
                            object4 = new JSONObject(s);
                            status4 = object4.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status4 == 1) {
                            showMessage(getLanguageValue("editor") + getLanguageValue("succeed"));
//                            getgoodslist();
                            getUnitList();
                        }
                    }
                });
    }

    /**
     * 删除单位
     */
    public void delete() {
        Map<String, Object> params = new HashMap<>();
        params.put("goods_unit_id", goods_unit_id);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.ONLINE_URL + "shopUpdate/goodsUnit/deleteGoodsUnit.do?",
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "删除单位 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int status5 = 2;
                        JSONObject object5;
                        try {
                            object5 = new JSONObject(s);
                            status5 = object5.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status5 == 1) {
                            showMessage(getLanguageValue("delete") + getLanguageValue("succeed"));
//                            getgoodslist();
                            getUnitList();
                        }
                    }
                });
    }
}
