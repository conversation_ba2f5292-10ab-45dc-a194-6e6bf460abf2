package cn.bl.mobile.buyhoostore_sea.printer;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;

/**
 * Describe:打印机型号选择
 * Created by jingang on 2024/4/16
 */
@SuppressLint("NonConstantResourceId")
public class PrinterTypeActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivType0)
    ImageView ivType0;
    @BindView(R.id.ivType1)
    ImageView ivType1;

    private SharedPreferencesUtil sharedPreferencesUtil;
    private int printerType;//打印机型号 0.佳博 CP-M322 1.精臣 NIIMBOT B3S

    @Override
    protected int getLayoutId() {
        return R.layout.activity_printer_type;
    }

    @Override
    public void initViews() {
        tvTitle.setText("请选择打印机型号");
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
        if (printerType == 0) {
            ivType0.setSelected(true);
            ivType1.setSelected(false);
        } else {
            ivType0.setSelected(false);
            ivType1.setSelected(true);
        }
    }

    @OnClick({R.id.ivBack, R.id.linType0, R.id.linType1})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linType0:
                //佳博
                if (printerType != 0) {
                    printerType = 0;
                    ivType0.setSelected(true);
                    ivType1.setSelected(false);
                    sharedPreferencesUtil.putInt(0, Constants.PRINTER_TYPE);
                    setResult(Constants.CHOOSE, new Intent()
                            .putExtra(Constants.PRINTER_TYPE, printerType)
                    );
                    finish();
                }
                break;
            case R.id.linType1:
                //精臣
                if (printerType != 1) {
                    printerType = 1;
                    ivType0.setSelected(false);
                    ivType1.setSelected(true);
                    sharedPreferencesUtil.putInt(1, Constants.PRINTER_TYPE);
                    setResult(Constants.CHOOSE, new Intent()
                            .putExtra(Constants.PRINTER_TYPE, printerType)
                    );
                    finish();
                }
                break;
        }
    }
}
