package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（隐私政策-二次确认）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class PrivacyAgainDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogTips)
    TextView tvTips;
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    public static void showDialog(Context context, MyListener listener) {
        PrivacyAgainDialog.listener = listener;
        PrivacyAgainDialog dialog = new PrivacyAgainDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PrivacyAgainDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_privacy_again);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("agreeContinueUseBuyhoo"));
        tvTips.setText(getLanguageValue("noAgreeCannotUseBuyhoo"));
        tvCancel.setText(getLanguageValue("stillDisagree"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    @OnClick({R.id.tvDialogConfirm, R.id.tvDialogCancel})
    public void onViewClicked(View view) {
        if (listener != null) {
            switch (view.getId()) {
                case R.id.tvDialogConfirm:
                    //同意
                    listener.onClick(1);
                    break;
                case R.id.tvDialogCancel:
                    //不同意
                    listener.onClick(0);
                    break;
            }
            dismiss();
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }


    private static MyListener listener;

    public interface MyListener {
        void onClick(int type);
    }
}
