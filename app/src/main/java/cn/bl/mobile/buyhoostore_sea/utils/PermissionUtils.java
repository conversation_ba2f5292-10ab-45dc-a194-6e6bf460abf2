package cn.bl.mobile.buyhoostore_sea.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.PermissionDialog;

/**
 * 检查权限/权限数组
 * request权限
 */
public class PermissionUtils {

    //权限：手机信息
    public static final String[] PERMISSION_READ_PHONE = {
            Manifest.permission.READ_PHONE_STATE
    };

    //权限：打电话
    public static final String[] PERMISSION_CALL = {
            Manifest.permission.CALL_PHONE
    };

    //权限：读写
    public static final String[] PERMISSION_READ_AND_WRITE = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    //权限：拍照、选择图片(13一下)
    public static final String[] PERMISSION_CAMERA = {
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    //权限：拍照、选择图片(13及以上)
    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    public static final String[] PERMISSION_CAMERA_13 = {
            Manifest.permission.CAMERA,
            Manifest.permission.READ_MEDIA_IMAGES
    };

    //权限：定位
    public static final String[] PERMISSION_LOCATION = {
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
    };

    //权限：蓝牙
    public static final String[] PERMISSION_BLUETOOTH = {
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_ADVERTISE,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
    };

    //权限：录音
    public static final String[] PERMISSION_AUDIO = {
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.WAKE_LOCK,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };


    /**
     * 检查多个权限
     * <p>
     * 检查权限
     *
     * @param context Context
     * @return true 已经拥有所有check的权限 false存在一个或多个未获得的权限
     */
    public static boolean checkPermissionsGroup(Context context, int type) {
        String[] PERMISSION;
        switch (type) {
            case 1:
                PERMISSION = PERMISSION_LOCATION;
                break;
            case 2:
                PERMISSION = PERMISSION_CAMERA;
                break;
            case 3:
                PERMISSION = PERMISSION_READ_AND_WRITE;
                break;
            case 4:
                PERMISSION = PERMISSION_CALL;
                break;
            case 5:
                PERMISSION = PERMISSION_READ_PHONE;
                break;
            case 6:
                PERMISSION = PERMISSION_BLUETOOTH;
                break;
            case 7:
                PERMISSION = PERMISSION_AUDIO;
                break;
            default:
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    PERMISSION = PERMISSION_CAMERA_13;
                } else {
                    PERMISSION = PERMISSION_CAMERA;
                }
                break;
        }
        for (String permission : PERMISSION) {
            if (!checkPermission(context, permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查单个权限
     *
     * @param context    Context
     * @param permission 权限
     * @return boolean
     */
    public static boolean checkPermission(Context context, String permission) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true;
        }
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 申请权限
     *
     * @param activity    Activity
     * @param requestCode 请求码
     */
    public static void requestPermissions(Activity activity, int requestCode, int type) {
        // 先检查是否已经授权
        if (!checkPermissionsGroup(activity, type)) {
//            ActivityCompat.requestPermissions(activity, permissions, requestCode);
            showDialogPermission(activity, null, requestCode, type);
        }
    }

    /**
     * 申请权限
     *
     * @param activity    Activity
     * @param requestCode 请求码
     */
    public static void requestPermissions(Activity activity, int requestCode, int type, MyListener myListener) {
        listener = myListener;
        // 先检查是否已经授权
        if (!checkPermissionsGroup(activity, type)) {
//            ActivityCompat.requestPermissions(activity, permissions, requestCode);
            showDialogPermission(activity, null, requestCode, type);
        }
    }

    /**
     * 申请权限
     *
     * @param fragment    Fragment
     * @param requestCode 请求码
     */
    public static void requestPermissions(Fragment fragment, int requestCode, int type) {
        // 先检查是否已经授权
        if (!checkPermissionsGroup(fragment.getActivity(), type)) {
//            fragment.requestPermissions(permissions, requestCode);
            showDialogPermission(null, fragment, requestCode, type);
        }
    }

    /**
     * 申请权限
     *
     * @param fragment    Fragment
     * @param requestCode 请求码
     */
    public static void requestPermissions(Fragment fragment, int requestCode, int type, MyListener myListener) {
        listener = myListener;
        // 先检查是否已经授权
        if (!checkPermissionsGroup(fragment.getActivity(), type)) {
//            fragment.requestPermissions(permissions, requestCode);
            showDialogPermission(null, fragment, requestCode, type);
        }
    }

    /**
     * 向用户告知权限申请的目的
     *
     * @param activity
     * @param fragment
     * @param requestCode
     */
    private static void showDialogPermission(Activity activity, Fragment fragment, int requestCode, int permissionType) {
        String content;
        String[] PERMISSION;
        switch (permissionType) {
            case 1:
                content = "位置权限：我们希望获得您的位置信息以便为您提供更准确的服务。如果您同意，我们将使用您的位置信息来定位您所在的位置，并向您推荐附近的地点和服务。";
                PERMISSION = PERMISSION_LOCATION;
                break;
            case 2:
                content = "相机权限：为了让您能够拍摄照片和视频，我们请求相机权限。只有当您使用相机功能时，我们才会请求此权限，并且我们会严格遵守相关的隐私政策。";
                PERMISSION = PERMISSION_CAMERA;
                break;
            case 3:
                content = "读写权限：我们需要读写存储权限以便于保存您的文件和数据。我们承诺，我们不会查看或分享您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。";
                PERMISSION = PERMISSION_READ_AND_WRITE;
                break;
            case 4:
                content = "打电话权限：为了让您能够使用我们的应用程序拨打电话，我们需要获得电话权限。我们承诺，我们不会监听您的通话或记录您的通话内容，并且我们将采取必要的措施来保护您的隐私。";
                PERMISSION = PERMISSION_CALL;
                break;
            case 5:
                content = "电话信息权限：我们需要访问您的电话信息以便于提供更好的服务。例如，我们可以向您推荐与您联系的人或向您发送有关您已接电话的提醒。我们承诺，我们不会泄露您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。";
                PERMISSION = PERMISSION_READ_PHONE;
                break;
            case 6:
                content = "蓝牙权限：我们需要访问您的蓝牙以便于提供更好的服务。例如，我们可以蓝牙连接打印机打印小票。";
                PERMISSION = PERMISSION_BLUETOOTH;
                break;
            case 7:
                content = "1.录音权限：开启语音输入以供最终用户录入音频。"
                        + "\n2.读写权限：读写权限：我们需要读写存储权限以便于保存您的文件和数据。我们承诺，我们不会查看或分享您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。";
                PERMISSION = PERMISSION_AUDIO;
                break;
            default:
                content = "1.相机权限：为了让您能够拍摄照片和视频，我们请求相机权限。只有当您使用相机功能时，我们才会请求此权限，并且我们会严格遵守相关的隐私政策。" +
                        "\n2.读写权限：我们需要读写存储权限以便于保存您的文件和数据。我们承诺，我们不会查看或分享您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。";
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    PERMISSION = PERMISSION_CAMERA_13;
                } else {
                    PERMISSION = PERMISSION_CAMERA;
                }
                break;
        }
        if (activity != null) {
            PermissionDialog.showDialog(activity,
                    content,
                    type -> {
                        if (listener != null) {
                            listener.onPermissionCallback(type);
                        }
                        if (type == 0) {
                            ActivityCompat.requestPermissions(activity, PERMISSION, requestCode);
                        }
                    });
        }
        if (fragment != null) {
            PermissionDialog.showDialog(fragment.getActivity(),
                    content,
                    type -> {
                        if (listener != null) {
                            listener.onPermissionCallback(type);
                        }
                        if (type == 0) {
                            fragment.requestPermissions(PERMISSION, requestCode);
                        }
                    });
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onPermissionCallback(int type);
    }


}
