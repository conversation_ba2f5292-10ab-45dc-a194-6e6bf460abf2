package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCropEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CameraDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 店铺-店铺设置-店铺信息
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class ShopInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvImg)
    TextView tvImg;
    @BindView(R.id.tvImgTips)
    TextView tvImgTips;
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvMobileValue)
    TextView tvMobileValue;
    @BindView(R.id.tvMobile)
    TextView tvMobile;
    @BindView(R.id.tvAddressValue)
    TextView tvAddressValue;
    @BindView(R.id.tvAddress)
    TextView tvAddress;
    @BindView(R.id.tvShopIdValue)
    TextView tvShopIdValue;
    @BindView(R.id.tvCopy)
    TextView tvCopy;
    @BindView(R.id.tvShopId)
    TextView tvShopId;
    @BindView(R.id.tvShopIdTips)
    TextView tvShopIdTips;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_info_shop;
    }

    @Override
    public void initViews() {
    }

    @Override
    public void initData() {
        getShopInfo();
    }

    @OnClick({R.id.ivBack, R.id.ivHead, R.id.linName, R.id.linAddress, R.id.tvCopy})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivHead:
                //选择图片
                if (PermissionUtils.checkPermissionsGroup(this, 0)) {
                    showDialogCamera();
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
                }
                break;
            case R.id.linName:
                //店铺名称
                startActivityForResult(new Intent(this, ShopNameActivity.class)
                                .putExtra("name", tvName.getText().toString().trim())
                        , Constants.NAME);
                break;
            case R.id.linAddress:
                //店铺位置
                startActivityForResult(new Intent(this, ShopAddressActivity.class)
                                .putExtra("address", tvAddress.getText().toString().trim())
                        , Constants.ADDRESS);
                break;
            case R.id.tvCopy:
                //复制店铺编号
                ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                // 创建普通字符型ClipData
                ClipData mClipData = ClipData.newPlainText("code", tvShopId.getText().toString().trim());
                // 将ClipData内容放到系统剪贴板里。
                cm.setPrimaryClip(mClipData);
                showMessage(getLanguageValue("copy")+getLanguageValue("succeed"));
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("shop")+getLanguageValue("information"));
        tvImg.setText(getLanguageValue("shop")+getLanguageValue("photo"));
        tvImgTips.setText(getLanguageValue("onlineSync"));
        tvNameValue.setText(getLanguageValue("shop")+getLanguageValue("name"));
        tvMobileValue.setText(getLanguageValue("servicePhone"));
        tvAddressValue.setText(getLanguageValue("shop")+getLanguageValue("location"));
        tvShopIdValue.setText(getLanguageValue("shop")+"ID");
        tvCopy.setText(getLanguageValue("copy"));
        tvShopIdTips.setText(getLanguageValue("bindSupplierRider"));
    }

    /**
     * 选择图片方式弹窗
     */
    private void showDialogCamera() {
        CameraDialog.showDialog(this, (view, type) -> {
            //type: 0.拍照 1.从相册选择
            if (type == 1) {
                pickPhoto();
            } else {
                takePhoto();
            }
        });
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .setCropEngine(new ImageFileCropEngine(this))
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCropEngine(new ImageFileCropEngine(this))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("shopUnique", getShop_id())
                .addFormDataPart("shopPicture", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                .build();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUpdateInfoUrlTWO())
                .post(requestBody)
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        BaseData data = new Gson().fromJson(response.body().string(), BaseData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            getShopInfo();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (IOException e) {
                        e.printStackTrace();
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(ShopInfoResponseModel.DataBean data) {
        if (data == null) {
            return;
        }
        Glide.with(this)
                .load(StringUtils.handledImgUrl(data.getShopImagePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_default_head))
                .into(ivHead);
        tvName.setText(data.getShopName());
        tvMobile.setText(data.getShopPhone());
        tvAddress.setText(data.getShopAddress());
        tvShopId.setText(String.valueOf(data.getShopUnique()));
    }

    //获取店铺信息
    public void getShopInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getShopInfoUrlTWO(),
                params,
                ShopInfoResponseModel.DataBean.class,
                new RequestListener<ShopInfoResponseModel.DataBean>() {
                    @Override
                    public void success(ShopInfoResponseModel.DataBean data) {
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.NAME:
                    //店铺名称
                    tvName.setText(data.getStringExtra("name"));
                    break;
                case Constants.ADDRESS:
                    //店铺地址
                    tvAddress.setText(data.getStringExtra("address"));
                    break;
            }
        }
    }
}
