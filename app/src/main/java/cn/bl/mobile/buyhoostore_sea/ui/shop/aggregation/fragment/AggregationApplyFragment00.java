package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseFragment;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:申请开通聚合码（营业执照）
 * Created by jingang on 2023/2/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyFragment00 extends BaseFragment {
    @BindView(R.id.linBg0)
    LinearLayout linBg0;
    @BindView(R.id.ivBg0)
    ImageView ivBg0;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.ivCamera0)
    ImageView ivCamera0;
    @BindView(R.id.tvImg0Value)
    TextView tvImg0Value;
    @BindView(R.id.linBg1)
    LinearLayout linBg1;
    @BindView(R.id.ivBg1)
    ImageView ivBg1;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.ivCamera1)
    ImageView ivCamera1;
    @BindView(R.id.tvImg1Value)
    TextView tvImg1Value;
    @BindView(R.id.tvBusinessValue)
    TextView tvBusinessValue;
    @BindView(R.id.etBusiness)
    EditText etBusiness;
    @BindView(R.id.tvBusiness_codeValue)
    TextView tvBusiness_codeValue;
    @BindView(R.id.etBusiness_code)
    EditText etBusiness_code;
    @BindView(R.id.tvBusiness_addressValue)
    TextView tvBusiness_addressValue;
    @BindView(R.id.etBusiness_address)
    EditText etBusiness_address;
    @BindView(R.id.tvPrevious)
    TextView tvPrevious;
    @BindView(R.id.tvNext)
    TextView tvNext;

    private int type,//类型 0.营业执照 1.手持营业执照
            status;//聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
    private String img0, img1,
            business,//商户名称
            business_code,//营业执照号
            business_address;//地址信息
    private boolean isFirst;

    public AggregationApplyFragment00(int status, String img0, String img1, String business, String business_code, String business_address,boolean isFirst) {
        this.status = status;
        this.img0 = img0;
        this.img1 = img1;
        this.business = business;
        this.business_code = business_code;
        this.business_address = business_address;
        this.isFirst = isFirst;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_aggregation_apply00;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        if(isFirst){
            tvPrevious.setVisibility(View.GONE);
        }else{
            tvPrevious.setVisibility(View.VISIBLE);
        }
        etBusiness.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                business = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business", business));
                setTextBg();
            }
        });
        etBusiness_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                business_code = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business_code", business_code));
                setTextBg();
            }
        });
        etBusiness_address.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                business_address = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business_address", business_address));
                setTextBg();
            }
        });
        //聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
        if (status == 1) {
            linBg0.setEnabled(false);
            ivCamera0.setVisibility(View.GONE);
            linBg1.setEnabled(false);
            ivCamera1.setVisibility(View.GONE);
            etBusiness.setFocusable(false);
            etBusiness_code.setFocusable(false);
            etBusiness_address.setFocusable(false);
        } else {
            linBg0.setEnabled(true);
            ivCamera0.setVisibility(View.VISIBLE);
            linBg1.setEnabled(true);
            ivCamera1.setVisibility(View.VISIBLE);
            etBusiness.setFocusable(true);
            etBusiness_code.setFocusable(true);
            etBusiness_address.setFocusable(true);
        }
        setUI();
    }

    @Override
    public void initData() {
    }

    @OnClick({R.id.linBg0, R.id.linBg1,R.id.tvPrevious, R.id.tvNext})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linBg0:
                //营业执照
                type = 0;
                getImg();
                break;
            case R.id.linBg1:
                //手持营业执照
                type = 1;
                getImg();
                break;
            case R.id.tvPrevious:
                //上一步
                if (listener != null) {
                    listener.onPreviousClick();
                }
                break;
            case R.id.tvNext:
                //下一步
                if (TextUtils.isEmpty(img0)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("businessLicense"));
                    return;
                }
                if (TextUtils.isEmpty(img1)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("handHeld")+getLanguageValue("businessLicense"));
                    return;
                }
                if (TextUtils.isEmpty(business)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("merchant")+getLanguageValue("name"));
                    return;
                }
                if (TextUtils.isEmpty(business_code)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("businessLicense")+getLanguageValue("number"));
                    return;
                }
                if (TextUtils.isEmpty(business_address)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("address")+getLanguageValue("information"));
                    return;
                }
                if (listener != null) {
                    listener.onNextClick();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvImg0Value.setText(getLanguageValue("shootUpload")+getLanguageValue("businessLicense"));
        tvImg1Value.setText(getLanguageValue("shootUpload")+getLanguageValue("handHeld")+getLanguageValue("businessLicense"));
        tvBusinessValue.setText(getLanguageValue("merchant")+getLanguageValue("name"));
        etBusiness.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("merchant")+getLanguageValue("name"));
        tvBusiness_codeValue.setText(getLanguageValue("businessLicense")+getLanguageValue("number"));
        etBusiness.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("businessLicense")+getLanguageValue("number"));
        tvBusiness_addressValue.setText(getLanguageValue("address")+getLanguageValue("information"));
        etBusiness_address.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("address")+getLanguageValue("information"));
        tvPrevious.setText(getLanguageValue("previousStep"));
        tvNext.setText(getLanguageValue("nextStep"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(AggregationApplyFragment00.this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvCamera.setText(getLanguageValue("photograph"));
        tvAlbum.setText(getLanguageValue("phoneSelect"));
        tvCancel.setText(getLanguageValue("cancel"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(getActivity())
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        if (type == 0) {
//                            img0 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business0", img0));
//                        } else {
//                            img1 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business1", img1));
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(getActivity())
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        if (type == 0) {
//                            img0 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business0", img0));
//                        } else {
//                            img1 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business1", img1));
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                getActivity().runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                getActivity().runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            if (type == 0) {
                                img0 = data.getData().getUrl();
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business0", img0));
                            } else {
                                img1 = data.getData().getUrl();
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("business1", img1));
                            }
                            setUI();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //营业执照
        if (TextUtils.isEmpty(img0)) {
            ivBg0.setImageResource(R.mipmap.ic_business_img001);
            ivImg0.setVisibility(View.GONE);
        } else {
            ivBg0.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg0.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(img0)
                    .into(ivImg0);
        }
        //手持营业执照
        if (TextUtils.isEmpty(img1)) {
            ivBg1.setImageResource(R.mipmap.ic_business_img002);
            ivImg1.setVisibility(View.GONE);
        } else {
            ivBg1.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg1.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(img1)
                    .into(ivImg1);
        }
        etBusiness.setText(business);
        etBusiness_code.setText(business_code);
        etBusiness_address.setText(business_address);
        setTextBg();
    }

    /**
     * 判断按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(img0)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(img1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(business)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(business_code)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(business_address)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        tvNext.setBackgroundResource(R.drawable.shape_blue_22);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onPreviousClick();

        void onNextClick();
    }

}
