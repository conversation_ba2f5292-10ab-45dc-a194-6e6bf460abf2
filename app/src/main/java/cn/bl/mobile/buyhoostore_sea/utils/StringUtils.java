package cn.bl.mobile.buyhoostore_sea.utils;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;

import java.text.NumberFormat;

import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

public class StringUtils {

    public static int checkText(String str) {
        int flag = -1;
        if (str.startsWith(".")) {
            flag = 0;
        } else if (!str.startsWith("0.") && str.startsWith("0") && str.length() >= 2) {
            flag = 1;
        } else if (str.contains(".")) {
            flag = 2;
        }
        return flag;
    }

    public static int doubleToInt(String money) {
        String finalmoney = String.format("%.2f", Double.valueOf(money));//微信请求金额单位为分
        finalmoney = finalmoney.replace(".", "");
        int total_fee = Integer.parseInt(finalmoney);
        return total_fee;
    }

    public static String doubleZero(double sum) {
        String result = "";
        String str = sum + "";
        String tempStr = str.substring(str.lastIndexOf(".") + 1);
        if ("0".equals(tempStr)) {
            result = str.substring(0, str.lastIndexOf("."));
        } else {
            result = str;
        }
        return result;
    }

    public static String imagePath(String path) {
        path = path.replace("\\", "/");
        String tempUrl;
        if (path.startsWith("http")) {
            tempUrl = path;
        } else {
            tempUrl = com.yxl.commonlibrary.utils.StringUtils.handledImgUrl(path);
        }
        return tempUrl;
    }

    /**
     *设置字体颜色,参数如getResources().getColor(R.color.colorBlue)
     */
    public static SpannableStringBuilder ForeGroundColorSpan(String content, int start, int end, int colorId) {
        if (end > content.length()) end = content.length();
        SpannableStringBuilder ssb = new SpannableStringBuilder(content);
        ssb.setSpan(new ForegroundColorSpan(colorId), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ssb;
    }

    /**
     * 将double转为数值，并最多保留num位小数。例如当num为2时，1.268为1.27，1.2仍为1.2；1仍为1，而非1.00;100.00则返回100。
     *
     * @param d
     * @param num 小数位数
     * @return
     */
    public static String double2String(double d, int num) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(num);//保留两位小数
        nf.setGroupingUsed(false);//去掉数值中的千位分隔符

        String temp = nf.format(d);
        if (temp.contains(".")) {
            String s1 = temp.split("\\.")[0];
            String s2 = temp.split("\\.")[1];
            for (int i = s2.length(); i > 0; --i) {
                if (!s2.substring(i - 1, i).equals("0")) {
                    return s1 + "." + s2.substring(0, i);
                }
            }
            return s1;
        }
        return temp;
    }

}
