package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:店铺-销售统计（实体类）
 * Created by jingang on 2023/8/17
 */
public class SaleStatisticsData implements Serializable {
    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"percentage":67.53,"count":2,"sum":5.2,"goodsChengType":"标品","goodsPicturePath":"/spider/30b4a950-c72a-425c-85f9-cb6abdfc775c.jpg","goodsName":"猫王抽纸","goodsType":"自营"},{"percentage":32.47,"count":1,"sum":2.5,"goodsChengType":"标品","goodsPicturePath":"/image/1581225631870/220000640.jpg","goodsName":"6号10#柴油","goodsType":"自营"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : {"saleCount":3,"standardSaleCount":3,"saleSum":7.7,"chengSaleCount":0}
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private ObjectBean object;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ObjectBean getObject() {
        return object;
    }

    public void setObject(ObjectBean object) {
        this.object = object;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class ObjectBean {
        /**
         * saleCount : 3.0
         * standardSaleCount : 3.0
         * saleSum : 7.7
         * chengSaleCount : 0.0
         */

        private double saleCount;//销量
        private double standardSaleCount;//标准品销售数量
        private double saleSum;//销售额
        private double chengSaleCount;//称重商品销量

        public double getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(double saleCount) {
            this.saleCount = saleCount;
        }

        public double getStandardSaleCount() {
            return standardSaleCount;
        }

        public void setStandardSaleCount(double standardSaleCount) {
            this.standardSaleCount = standardSaleCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getChengSaleCount() {
            return chengSaleCount;
        }

        public void setChengSaleCount(double chengSaleCount) {
            this.chengSaleCount = chengSaleCount;
        }
    }

    public static class DataBean {
        /**
         * percentage : 67.53
         * count : 2.0
         * sum : 5.2
         * goodsChengType : 标品
         * goodsPicturePath : /spider/30b4a950-c72a-425c-85f9-cb6abdfc775c.jpg
         * goodsName : 猫王抽纸
         * goodsType : 自营
         */

        private double percentage;//占比
        private double count;//销量
        private double sum;//销售额
        private String goodsChengType;
        private String goodsPicturePath;
        private String goodsName;//商品名称
        private String goodsType;
        private double profit;//利润总额
        private String goodsBarcode;//商品编号
        private String goodsUnit;//单位

        public double getPercentage() {
            return percentage;
        }

        public void setPercentage(double percentage) {
            this.percentage = percentage;
        }

        public double getCount() {
            return count;
        }

        public void setCount(double count) {
            this.count = count;
        }

        public double getSum() {
            return sum;
        }

        public void setSum(double sum) {
            this.sum = sum;
        }

        public String getGoodsChengType() {
            return goodsChengType;
        }

        public void setGoodsChengType(String goodsChengType) {
            this.goodsChengType = goodsChengType;
        }

        public String getGoodsPicturePath() {
            return goodsPicturePath;
        }

        public void setGoodsPicturePath(String goodsPicturePath) {
            this.goodsPicturePath = goodsPicturePath;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsType() {
            return goodsType;
        }

        public void setGoodsType(String goodsType) {
            this.goodsType = goodsType;
        }

        public double getProfit() {
            return profit;
        }

        public void setProfit(double profit) {
            this.profit = profit;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }
    }
}
