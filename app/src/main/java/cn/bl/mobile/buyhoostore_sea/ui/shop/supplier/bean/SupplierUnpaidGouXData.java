package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean;

import java.io.Serializable;
import java.util.List;

import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;

/**
 * Describe:供货商详情-未还款购销单列表（实体类）
 * Created by jingang on 2023/9/21
 */
public class SupplierUnpaidGouXData implements Serializable {
    /**
     * outstandingCount : 1
     * billList : [{"supplierUnique":"d60054eac842485aa3fa31a4dcfc57f3","supplierName":"影响力","contactMobile":"131*********","status":2,"categoryCount":1,"purchaseAmount":12,"outstandingAmount":12,"goodsList":[{"goodsBarcode":"6923083028089","goodsName":"猫王抽纸","goodsImageUrl":"http://dummyimage.com/400x400","goodsCount":3}]}]
     * purchaseAmounts : 12
     */

    private int outstandingCount;//数量
    private double purchaseAmounts;//总欠款
    private List<GouXOrderListData> billList;

    public int getOutstandingCount() {
        return outstandingCount;
    }

    public void setOutstandingCount(int outstandingCount) {
        this.outstandingCount = outstandingCount;
    }

    public double getPurchaseAmounts() {
        return purchaseAmounts;
    }

    public void setPurchaseAmounts(double purchaseAmounts) {
        this.purchaseAmounts = purchaseAmounts;
    }

    public List<GouXOrderListData> getBillList() {
        return billList;
    }

    public void setBillList(List<GouXOrderListData> billList) {
        this.billList = billList;
    }
}
