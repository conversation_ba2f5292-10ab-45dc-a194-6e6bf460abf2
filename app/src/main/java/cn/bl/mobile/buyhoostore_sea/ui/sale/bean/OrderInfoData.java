package cn.bl.mobile.buyhoostore_sea.ui.sale.bean;

import java.util.List;

/**
 * Describe:
 * Created by jingang on 2023/5/20
 */
public class OrderInfoData {
    /**
     * status : 1
     * msg : 查询成功！
     * data : {"saleListId":5121,"saleListUnique":1684566330118,"retListUnique":null,"dateTime":"2023-05-20 15:05:30","shopUnique":1536215939565,"saleListTotal":0.03,"saleListPur":20,"saleListDelfee":0,"totalCount":1,"saleType":"2","saleListName":"王恩龙","saleListPhone":"13054908225","saleListAddress":"山东省临沂市兰山区临沂应用科学城滴滴滴","saleListState":"已付款","saleListStateCode":3,"handleState":"已发货","handleStateCode":3,"saleListPayment":"混合支付","payMethods":[{"saleListPayDetailId":5213,"payMethodStatus":3,"payMethod":"微信","payMoney":0.02},{"saleListPayDetailId":5212,"payMethodStatus":11,"payMethod":"百货豆","payMoney":0.01}],"saleListPaymentCode":8,"saleListRemarks":"","receiptDateTime":"","sendDateTime":null,"saleListNumber":1,"saleListCashier":3586,"actuallyReceived":0.02,"machineNum":1,"shipping_method":1,"delivery_type":0,"peisong_money":0,"coupon_amount":0,"card_deduction":0,"beans_money":0.01,"delivery_status":"配送单已确定","listDetail":[{"saleListDetailId":8287,"goodsBarcode":"6909493102299","goodsName":"冰冻西瓜","goodsPicturepath":"","saleListDetailCount":1,"saleListDetailPrice":0.03,"subTotal":0.03,"goods_unit":null,"goods_purprice":20}]}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object count;
    private Object total;
    private Object rows;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getCount() {
        return count;
    }

    public void setCount(Object count) {
        this.count = count;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * saleListId : 5121
         * saleListUnique : 1684566330118
         * retListUnique : null
         * dateTime : 2023-05-20 15:05:30
         * shopUnique : 1536215939565
         * saleListTotal : 0.03
         * saleListPur : 20.0
         * saleListDelfee : 0.0
         * totalCount : 1.0
         * saleType : 2
         * saleListName : 王恩龙
         * saleListPhone : 13054908225
         * saleListAddress : 山东省临沂市兰山区临沂应用科学城滴滴滴
         * saleListState : 已付款
         * saleListStateCode : 3
         * handleState : 已发货
         * handleStateCode : 3
         * saleListPayment : 混合支付
         * payMethods : [{"saleListPayDetailId":5213,"payMethodStatus":3,"payMethod":"微信","payMoney":0.02},{"saleListPayDetailId":5212,"payMethodStatus":11,"payMethod":"百货豆","payMoney":0.01}]
         * saleListPaymentCode : 8
         * saleListRemarks :
         * receiptDateTime :
         * sendDateTime : null
         * saleListNumber : 1
         * saleListCashier : 3586
         * actuallyReceived : 0.02
         * machineNum : 1
         * shipping_method : 1
         * delivery_type : 0
         * peisong_money : 0.0
         * coupon_amount : 0.0
         * card_deduction : 0.0
         * beans_money : 0.01
         * delivery_status : 配送单已确定
         * listDetail : [{"saleListDetailId":8287,"goodsBarcode":"6909493102299","goodsName":"冰冻西瓜","goodsPicturepath":"","saleListDetailCount":1,"saleListDetailPrice":0.03,"subTotal":0.03,"goods_unit":null,"goods_purprice":20}]
         */

        private int saleListId;//订单id
        private long saleListUnique;//订单编号
        private String retListUnique;////退款订单号，没有时为空字符
        private String dateTime;//下单时间
        private long shopUnique;//店铺编号
        private double saleListTotal;//订单应收总金额
        private double saleListPur;//订单总进价
        private double saleListDelfee;//外送费
        private double totalCount;//订单商品总数量
        private String saleType;//订单类型
        private String saleListName;//顾客姓名
        private String saleListPhone;//顾客电话
        private String saleListAddress;//送货地址
        private String saleListState;//付款状态
        private int saleListStateCode;//付款状态码
        private String handleState;//订单处理状态
        private int handleStateCode;//订单处理状态码 0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待\r\n\r\n确认 8-待付款9-待自提 10-配送异常 11-已核单未发货',
        private String saleListPayment;//付款方式
        private int saleListPaymentCode;//付款方式编码
        private String saleListRemarks;//订单备注
        private String receiptDateTime;//订单完成时间
        private String sendDateTime;//订单发货时间
        private int saleListNumber;//订单每日序号
        private int saleListCashier;//订单收银员编号
        private double actuallyReceived;
        private int machineNum;//收款机器编号
        private int shipping_method;//配送方式1:送货上门 2:自提
        private int delivery_type;//送货方式，0：自配送 2:一刻钟配送
        private double peisong_money;
        private double coupon_amount;
        private double card_deduction;
        private double beans_money;
        private String delivery_status;//配送状态
        private String cusProtrait;//顾客头像
        private String driverName;//配送人员名称
        private String driverPhone;//配送人员电话
        private String payTime;//配送时间
        private String staffName;//收银员
        private List<PayMethodsBean> payMethods;
        private List<ListDetailBean> listDetail;

        public int getSaleListId() {
            return saleListId;
        }

        public void setSaleListId(int saleListId) {
            this.saleListId = saleListId;
        }

        public long getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(long saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }

        public String getDateTime() {
            return dateTime;
        }

        public void setDateTime(String dateTime) {
            this.dateTime = dateTime;
        }

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public double getSaleListTotal() {
            return saleListTotal;
        }

        public void setSaleListTotal(double saleListTotal) {
            this.saleListTotal = saleListTotal;
        }

        public double getSaleListPur() {
            return saleListPur;
        }

        public void setSaleListPur(double saleListPur) {
            this.saleListPur = saleListPur;
        }

        public double getSaleListDelfee() {
            return saleListDelfee;
        }

        public void setSaleListDelfee(double saleListDelfee) {
            this.saleListDelfee = saleListDelfee;
        }

        public double getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(double totalCount) {
            this.totalCount = totalCount;
        }

        public String getSaleType() {
            return saleType;
        }

        public void setSaleType(String saleType) {
            this.saleType = saleType;
        }

        public String getSaleListName() {
            return saleListName;
        }

        public void setSaleListName(String saleListName) {
            this.saleListName = saleListName;
        }

        public String getSaleListPhone() {
            return saleListPhone;
        }

        public void setSaleListPhone(String saleListPhone) {
            this.saleListPhone = saleListPhone;
        }

        public String getSaleListAddress() {
            return saleListAddress;
        }

        public void setSaleListAddress(String saleListAddress) {
            this.saleListAddress = saleListAddress;
        }

        public String getSaleListState() {
            return saleListState;
        }

        public void setSaleListState(String saleListState) {
            this.saleListState = saleListState;
        }

        public int getSaleListStateCode() {
            return saleListStateCode;
        }

        public void setSaleListStateCode(int saleListStateCode) {
            this.saleListStateCode = saleListStateCode;
        }

        public String getHandleState() {
            return handleState;
        }

        public void setHandleState(String handleState) {
            this.handleState = handleState;
        }

        public int getHandleStateCode() {
            return handleStateCode;
        }

        public void setHandleStateCode(int handleStateCode) {
            this.handleStateCode = handleStateCode;
        }

        public String getSaleListPayment() {
            return saleListPayment;
        }

        public void setSaleListPayment(String saleListPayment) {
            this.saleListPayment = saleListPayment;
        }

        public int getSaleListPaymentCode() {
            return saleListPaymentCode;
        }

        public void setSaleListPaymentCode(int saleListPaymentCode) {
            this.saleListPaymentCode = saleListPaymentCode;
        }

        public String getSaleListRemarks() {
            return saleListRemarks;
        }

        public void setSaleListRemarks(String saleListRemarks) {
            this.saleListRemarks = saleListRemarks;
        }

        public String getReceiptDateTime() {
            return receiptDateTime;
        }

        public void setReceiptDateTime(String receiptDateTime) {
            this.receiptDateTime = receiptDateTime;
        }

        public String getSendDateTime() {
            return sendDateTime;
        }

        public void setSendDateTime(String sendDateTime) {
            this.sendDateTime = sendDateTime;
        }

        public int getSaleListNumber() {
            return saleListNumber;
        }

        public void setSaleListNumber(int saleListNumber) {
            this.saleListNumber = saleListNumber;
        }

        public int getSaleListCashier() {
            return saleListCashier;
        }

        public void setSaleListCashier(int saleListCashier) {
            this.saleListCashier = saleListCashier;
        }

        public double getActuallyReceived() {
            return actuallyReceived;
        }

        public void setActuallyReceived(double actuallyReceived) {
            this.actuallyReceived = actuallyReceived;
        }

        public int getMachineNum() {
            return machineNum;
        }

        public void setMachineNum(int machineNum) {
            this.machineNum = machineNum;
        }

        public int getShipping_method() {
            return shipping_method;
        }

        public void setShipping_method(int shipping_method) {
            this.shipping_method = shipping_method;
        }

        public int getDelivery_type() {
            return delivery_type;
        }

        public void setDelivery_type(int delivery_type) {
            this.delivery_type = delivery_type;
        }

        public double getPeisong_money() {
            return peisong_money;
        }

        public void setPeisong_money(double peisong_money) {
            this.peisong_money = peisong_money;
        }

        public double getCoupon_amount() {
            return coupon_amount;
        }

        public void setCoupon_amount(double coupon_amount) {
            this.coupon_amount = coupon_amount;
        }

        public double getCard_deduction() {
            return card_deduction;
        }

        public void setCard_deduction(double card_deduction) {
            this.card_deduction = card_deduction;
        }

        public double getBeans_money() {
            return beans_money;
        }

        public void setBeans_money(double beans_money) {
            this.beans_money = beans_money;
        }

        public String getDelivery_status() {
            return delivery_status;
        }

        public void setDelivery_status(String delivery_status) {
            this.delivery_status = delivery_status;
        }

        public String getCusProtrait() {
            return cusProtrait;
        }

        public void setCusProtrait(String cusProtrait) {
            this.cusProtrait = cusProtrait;
        }

        public String getDriverName() {
            return driverName;
        }

        public void setDriverName(String driverName) {
            this.driverName = driverName;
        }

        public String getDriverPhone() {
            return driverPhone;
        }

        public void setDriverPhone(String driverPhone) {
            this.driverPhone = driverPhone;
        }

        public String getPayTime() {
            return payTime;
        }

        public void setPayTime(String payTime) {
            this.payTime = payTime;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public List<PayMethodsBean> getPayMethods() {
            return payMethods;
        }

        public void setPayMethods(List<PayMethodsBean> payMethods) {
            this.payMethods = payMethods;
        }

        public List<ListDetailBean> getListDetail() {
            return listDetail;
        }

        public void setListDetail(List<ListDetailBean> listDetail) {
            this.listDetail = listDetail;
        }

        public static class PayMethodsBean {
            /**
             * saleListPayDetailId : 5213
             * payMethodStatus : 3
             * payMethod : 微信
             * payMoney : 0.02
             */

            private int saleListPayDetailId;
            private int payMethodStatus;
            private String payMethod;
            private double payMoney;

            public int getSaleListPayDetailId() {
                return saleListPayDetailId;
            }

            public void setSaleListPayDetailId(int saleListPayDetailId) {
                this.saleListPayDetailId = saleListPayDetailId;
            }

            public int getPayMethodStatus() {
                return payMethodStatus;
            }

            public void setPayMethodStatus(int payMethodStatus) {
                this.payMethodStatus = payMethodStatus;
            }

            public String getPayMethod() {
                return payMethod;
            }

            public void setPayMethod(String payMethod) {
                this.payMethod = payMethod;
            }

            public double getPayMoney() {
                return payMoney;
            }

            public void setPayMoney(double payMoney) {
                this.payMoney = payMoney;
            }
        }

        public static class ListDetailBean {
            /**
             * saleListDetailId : 8287
             * goodsBarcode : 6909493102299
             * goodsName : 冰冻西瓜
             * goodsPicturepath :
             * saleListDetailCount : 1.0
             * saleListDetailPrice : 0.03
             * subTotal : 0.03
             * goods_unit : null
             * goods_purprice : 20.0
             */

            private int saleListDetailId;
            private String goodsBarcode;
            private String goodsName;
            private String goodsPicturepath;
            private double saleListDetailCount;//重量
            private double saleListDetailPrice;//单价
            private double subTotal;//小计
            private String goods_unit;
            private double goods_purprice;
            private String goodsChengType;//散装
            private String goodsType;//自营

            public int getSaleListDetailId() {
                return saleListDetailId;
            }

            public void setSaleListDetailId(int saleListDetailId) {
                this.saleListDetailId = saleListDetailId;
            }

            public String getGoodsBarcode() {
                return goodsBarcode;
            }

            public void setGoodsBarcode(String goodsBarcode) {
                this.goodsBarcode = goodsBarcode;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getGoodsPicturepath() {
                return goodsPicturepath;
            }

            public void setGoodsPicturepath(String goodsPicturepath) {
                this.goodsPicturepath = goodsPicturepath;
            }

            public double getSaleListDetailCount() {
                return saleListDetailCount;
            }

            public void setSaleListDetailCount(double saleListDetailCount) {
                this.saleListDetailCount = saleListDetailCount;
            }

            public double getSaleListDetailPrice() {
                return saleListDetailPrice;
            }

            public void setSaleListDetailPrice(double saleListDetailPrice) {
                this.saleListDetailPrice = saleListDetailPrice;
            }

            public double getSubTotal() {
                return subTotal;
            }

            public void setSubTotal(double subTotal) {
                this.subTotal = subTotal;
            }

            public String getGoods_unit() {
                return goods_unit;
            }

            public void setGoods_unit(String goods_unit) {
                this.goods_unit = goods_unit;
            }

            public double getGoods_purprice() {
                return goods_purprice;
            }

            public void setGoods_purprice(double goods_purprice) {
                this.goods_purprice = goods_purprice;
            }

            public String getGoodsChengType() {
                return goodsChengType;
            }

            public void setGoodsChengType(String goodsChengType) {
                this.goodsChengType = goodsChengType;
            }

            public String getGoodsType() {
                return goodsType;
            }

            public void setGoodsType(String goodsType) {
                this.goodsType = goodsType;
            }
        }
    }
}
