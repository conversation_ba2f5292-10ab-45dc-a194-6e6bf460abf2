package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import cn.bl.mobile.buyhoostore_sea.bean.OrderListData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.NPBaiTiaoRecordAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.StringAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.StringData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.NPCustomerDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-支出记录
 * Created by jingang on 2023/5/25
 */
@SuppressLint("NonConstantResourceId")
public class NPZhiChuRecordActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvCustom)
    TextView tvCustom;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.tvType)
    TextView tvType;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keywords;

    private MemberBean.DataBean mMemberBean;

    private NPBaiTiaoRecordAdapter mAdapter;
    private List<OrderListData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_bai_tiao_record;
    }

    @Override
    public void initViews() {
        tvTitle.setText("支出记录");
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            getOrderList();
            return true;
        });
        setAdapter();
    }

    @Override
    public void initData() {
        //下单时间
        timeList.clear();
        timeList.add(new StringData(0, "今天", false));
        timeList.add(new StringData(1, "昨天", false));
        timeList.add(new StringData(2, "前天", false));
        getOrderList();
    }

    @OnClick({R.id.ivBack, R.id.linCustom, R.id.linTime, R.id.linType})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linCustom:
                //选择客户
                NPCustomerDialog dialog = new NPCustomerDialog(this);
                dialog.setSift(true);
                if (mMemberBean != null) {
                    dialog.setCusUnique(mMemberBean.getCus_unique());
                }
                dialog.setonDialogClickListener(memberBean -> {
                    tvCustom.setText(memberBean.getCusName());
                    keywords = memberBean.getCus_unique();
                    mMemberBean = memberBean;
                    page = 1;
                    getOrderList();
                });
                new XPopup.Builder(this)
                        .popupAnimation(PopupAnimation.ScrollAlphaFromBottom)
                        .isDestroyOnDismiss(true)
                        .asCustom(dialog)
                        .show();
                break;
            case R.id.linTime:
                //选择时间
                showDialogTime();
                break;
            case R.id.linType:
                //选择类型
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new NPBaiTiaoRecordAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getOrderList();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getOrderList();
            smartRefreshLayout.finishLoadMore();
        });
        mAdapter.setOnItemClickListener((view, position) -> {
            goToActivity(NPBaiTiaoDetailActivity.class);
//            startActivityForResult(new Intent(this, FarmOrderInfoActivity.class)
//                            .putExtra("unique", String.valueOf(dataList.get(position).getSaleListUnique()))
//                    , Constants.ORDER);
        });
    }

    public static int time = -1;
    public static String startTime, endTime;
    private List<StringData> timeList = new ArrayList<>();
    private StringAdapter timeAdapter;

    /**
     * dialog（时间选择）
     */
    private void showDialogTime() {
        TextView tvStartTime, tvEndTime;
        RecyclerView rvTime;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_time_farm, null);
        dialog.setContentView(view);

        rvTime = view.findViewById(R.id.rvDialogTime);
        tvStartTime = view.findViewById(R.id.tvDialogStartTime);
        tvEndTime = view.findViewById(R.id.tvDialogEndTime);

        for (int i = 0; i < timeList.size(); i++) {
            if (timeList.get(i).isCheck()) {
                switch (timeList.get(i).getId()) {
                    case 0:
                        //今天 0点-现在
                        startTime = DateUtils.getOldDate(0);
                        endTime = DateUtils.getOldDate(0);
                        break;
                    case 1:
                        //昨天 0点-24点
                        startTime = DateUtils.getOldDate(-1);
                        endTime = DateUtils.getOldDate(-1);
                        break;
                    case 2:
                        //前天 0点-24点
                        startTime = DateUtils.getOldDate(-2);
                        endTime = DateUtils.getOldDate(-2);
                        break;
                    default:
                        break;
                }
                tvStartTime.setText(startTime);
                tvEndTime.setText(endTime);
            }
        }

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());
        tvStartTime.setOnClickListener(v -> {
            //开始时间
            DateStartEndDialog.showDialog(this,
                    startTime,
                    endTime,
                    startTime,
                    (startDate, endDate) -> {
                        tvTime.setText(startDate);
                        startTime = startDate;
                        endTime = endDate;
                        tvStartTime.setText(startDate);
                        tvEndTime.setText(endDate);
                        for (int i = 0; i < timeList.size(); i++) {
                            timeList.get(i).setCheck(false);
                        }
                        timeAdapter.setDataList(timeList);
                        time = -1;
                        page = 1;
                        getOrderList();
                        dialog.dismiss();
                    });
        });
        tvEndTime.setOnClickListener(v -> {
            //结束时间
            DateStartEndDialog.showDialog(this,
                    startTime,
                    endTime,
                    startTime,
                    (startDate, endDate) -> {
                        tvTime.setText(startDate);
                        startTime = startDate;
                        endTime = endDate;
                        tvStartTime.setText(startDate);
                        tvEndTime.setText(endDate);
                        for (int i = 0; i < timeList.size(); i++) {
                            timeList.get(i).setCheck(false);
                        }
                        timeAdapter.setDataList(timeList);
                        time = -1;
                        page = 1;
                        getOrderList();
                        dialog.dismiss();
                    });
        });

        //下单时间
        rvTime.setLayoutManager(new GridLayoutManager(this, 3));
        timeAdapter = new StringAdapter(this);
        rvTime.setAdapter(timeAdapter);
        timeAdapter.setDataList(timeList);
        timeAdapter.setOnItemClickListener((view1, position) -> {
            if (time != timeList.get(position).getId()) {
                for (int i = 0; i < timeList.size(); i++) {
                    timeList.get(i).setCheck(false);
                }
                timeList.get(position).setCheck(true);
                time = timeList.get(position).getId();
                timeAdapter.setDataList(timeList);
                switch (time) {
                    case 0:
                        //今天 0点-现在
                        startTime = DateUtils.getOldDate(0);
                        endTime = DateUtils.getOldDate(0);
                        break;
                    case 1:
                        //昨天 0点-24点
                        startTime = DateUtils.getOldDate(-1);
                        endTime = DateUtils.getOldDate(-1);
                        break;
                    case 2:
                        //前天 0点-24点
                        startTime = DateUtils.getOldDate(-2);
                        endTime = DateUtils.getOldDate(-2);
                        break;
                    default:
                        break;
                }
                tvTime.setText(startTime);
                tvStartTime.setText(startTime);
                tvEndTime.setText(endTime);
                page = 1;
                getOrderList();
                dialog.dismiss();
            }
        });


    }

    /**
     * 网单订单查询
     */
    public void getOrderList() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("saleListMessage", keywords);
        params.put("handleState", "-1");
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        //下单时间
        params.put("orderStartDate", startTime);
        params.put("orderEndDate", endTime);
//        //订单类型
//        if (SaleFragment.saleType != -1) {
//            params.put("saleType", SaleFragment.saleType);
//        }
//        //付款时间
//        if (SaleFragment.saleListPayment != -1) {
//            params.put("saleListPayment", SaleFragment.saleListPayment);
//        }
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getsalelistTWO(),
                params,
                OrderListData.class,
                new RequestListListener<OrderListData>() {
                    @Override
                    public void onResult(List<OrderListData> orderListData) {
                        hideDialog();
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(orderListData);
                        mAdapter.setDataList(dataList);
                        if (dataList.size() > 0) {
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        //订单列表获取失败
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.ORDER:
                    page = 1;
                    getOrderList();
                    break;
            }
        }
    }
}
