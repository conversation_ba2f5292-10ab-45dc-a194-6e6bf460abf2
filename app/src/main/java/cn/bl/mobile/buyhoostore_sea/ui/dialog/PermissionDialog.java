package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import androidx.annotation.NonNull;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（权限申请）
 * Created by jingang on 2023/6/17
 */
@SuppressLint("NonConstantResourceId")
public class PermissionDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogTips)
    TextView tvTips;
    @BindView(R.id.tvDialogContent)
    TextView tvContent;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;

    private static String content;

    public static void showDialog(Activity activity, String content, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PermissionDialog.content = content;
        PermissionDialog.listener = listener;
        PermissionDialog dialog = new PermissionDialog(activity);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PermissionDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_permission);
        ButterKnife.bind(this);
        setCancelable(false);
        tvTitle.setText(getLanguageValue("buyhooAuthorizationReminder"));
        tvTips.setText(getLanguageValue("buyhooNeedAccess"));
        tvConfirm.setText(getLanguageValue("agree"));
        tvCancel.setText(getLanguageValue("disagree"));
        tvContent.setText(content);
    }

    @OnClick({R.id.tvDialogConfirm, R.id.tvDialogCancel, R.id.tvDialogAgain})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogConfirm:
                //同意并继续
                if (listener != null) {
                    listener.onClick(0);
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //不同意
                if (listener != null) {
                    listener.onClick(1);
                    dismiss();
                }
                break;
            case R.id.tvDialogAgain:
                //不在提醒
                if (listener != null) {
                    listener.onClick(2);
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.同意并继续 1.不同意 2.不再提醒
         */
        void onClick(int type);
    }
}
