package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class SearchUniqueBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"goodsId":1,"goodsCount":0,"baseBarcode":"2030504092568","goodsPicturepath":"upload/no_goodsB.jpg","groupsName":"生鲜蔬果","groupsUnique":"1200000","kindName":"蔬菜类","kindUnique":"1200003","goodsUnit":"包","goodsBrand":"","goodsAddress":"中国","goodsRemarks":"1","goodsSold":0,"supplierUnique":"","supplierName":"","goodsPrice":0,"supGoodsBarcode":"","listDetail":[{"goodsId":3463,"goodsBarcode":"2030504092568","goodsName":"筷子","goodsStandard":"10双","goodsUnit":"包","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"2030504092568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":25955,"goodsBarcode":"6925461622568","goodsName":"标榜修护除渍牙膏120g","goodsStandard":"120g","goodsUnit":"支","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6925461622568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":33919,"goodsBarcode":"6933264802568","goodsName":"蒙清草莓罐头246g","goodsStandard":"246g","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6933264802568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":41640,"goodsBarcode":"6942616502568","goodsName":"t真丽斯薰衣草活力去屑洗发露400ml","goodsStandard":"400ml","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6942616502568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":42029,"goodsBarcode":"6943265402568","goodsName":"红月亮强力油污净喷雾500ml","goodsStandard":"500ml","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6943265402568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":89862,"goodsBarcode":"6954113202568","goodsName":"话梅肉","goodsStandard":"180克","goodsUnit":"","goodsInPrice":0,"goodsSalePrice":3.5,"foreign_key":"6954113202568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/shops/shop1.jpg","goodsCusPrice":3.5,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":99744,"goodsBarcode":"6926292542568","goodsName":"金达日美不锈钢暖水瓶","goodsStandard":"","goodsUnit":"","goodsInPrice":35,"goodsSalePrice":45,"foreign_key":"6926292542568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"/image/1507796486464/6926292542568.JPEG","goodsCusPrice":45,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":118889,"goodsBarcode":"6937902812568","goodsName":"辣条","goodsStandard":"00","goodsUnit":"包","goodsInPrice":0,"goodsSalePrice":2,"foreign_key":"6937902812568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/shops/shop1.jpg","goodsCusPrice":2,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""}],"foreignKey":"2030504092568","tableType":1}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * goodsId : 1
         * goodsCount : 0.0
         * baseBarcode : 2030504092568
         * goodsPicturepath : upload/no_goodsB.jpg
         * groupsName : 生鲜蔬果
         * groupsUnique : 1200000
         * kindName : 蔬菜类
         * kindUnique : 1200003
         * goodsUnit : 包
         * goodsBrand :
         * goodsAddress : 中国
         * goodsRemarks : 1
         * goodsSold : 0.0
         * supplierUnique :
         * supplierName :
         * goodsPrice : 0.0
         * supGoodsBarcode :
         * listDetail : [{"goodsId":3463,"goodsBarcode":"2030504092568","goodsName":"筷子","goodsStandard":"10双","goodsUnit":"包","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"2030504092568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":25955,"goodsBarcode":"6925461622568","goodsName":"标榜修护除渍牙膏120g","goodsStandard":"120g","goodsUnit":"支","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6925461622568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":33919,"goodsBarcode":"6933264802568","goodsName":"蒙清草莓罐头246g","goodsStandard":"246g","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6933264802568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":41640,"goodsBarcode":"6942616502568","goodsName":"t真丽斯薰衣草活力去屑洗发露400ml","goodsStandard":"400ml","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6942616502568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":42029,"goodsBarcode":"6943265402568","goodsName":"红月亮强力油污净喷雾500ml","goodsStandard":"500ml","goodsUnit":"瓶","goodsInPrice":0,"goodsSalePrice":1,"foreign_key":"6943265402568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"upload/no_goodsB.jpg","goodsCusPrice":1,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":89862,"goodsBarcode":"6954113202568","goodsName":"话梅肉","goodsStandard":"180克","goodsUnit":"","goodsInPrice":0,"goodsSalePrice":3.5,"foreign_key":"6954113202568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/shops/shop1.jpg","goodsCusPrice":3.5,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":99744,"goodsBarcode":"6926292542568","goodsName":"金达日美不锈钢暖水瓶","goodsStandard":"","goodsUnit":"","goodsInPrice":35,"goodsSalePrice":45,"foreign_key":"6926292542568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"/image/1507796486464/6926292542568.JPEG","goodsCusPrice":45,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""},{"goodsId":118889,"goodsBarcode":"6937902812568","goodsName":"辣条","goodsStandard":"00","goodsUnit":"包","goodsInPrice":0,"goodsSalePrice":2,"foreign_key":"6937902812568","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/shops/shop1.jpg","goodsCusPrice":2,"shelfState":1,"countPresent":0,"pgoodsName":"0","pgoodsCount":0,"pgoodsUnit":""}]
         * foreignKey : 2030504092568
         * tableType : 1
         */

        private int goodsId;
        private double goodsCount;
        private String baseBarcode;
        private String goodsPicturepath;
        private String groupsName;
        private String groupsUnique;
        private String kindName;
        private String kindUnique;
        private String goodsUnit;
        private String goodsBrand;
        private String goodsAddress;
        private String goodsRemarks;
        private double goodsSold;
        private String supplierUnique;
        private String supplierName;
        private double goodsPrice;
        private String supGoodsBarcode;
        private String foreignKey;
        private int tableType;
        private List<ListDetailBean> listDetail;

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getBaseBarcode() {
            return baseBarcode;
        }

        public void setBaseBarcode(String baseBarcode) {
            this.baseBarcode = baseBarcode;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getGroupsName() {
            return groupsName;
        }

        public void setGroupsName(String groupsName) {
            this.groupsName = groupsName;
        }

        public String getGroupsUnique() {
            return groupsUnique;
        }

        public void setGroupsUnique(String groupsUnique) {
            this.groupsUnique = groupsUnique;
        }

        public String getKindName() {
            return kindName;
        }

        public void setKindName(String kindName) {
            this.kindName = kindName;
        }

        public String getKindUnique() {
            return kindUnique;
        }

        public void setKindUnique(String kindUnique) {
            this.kindUnique = kindUnique;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public String getGoodsBrand() {
            return goodsBrand;
        }

        public void setGoodsBrand(String goodsBrand) {
            this.goodsBrand = goodsBrand;
        }

        public String getGoodsAddress() {
            return goodsAddress;
        }

        public void setGoodsAddress(String goodsAddress) {
            this.goodsAddress = goodsAddress;
        }

        public String getGoodsRemarks() {
            return goodsRemarks;
        }

        public void setGoodsRemarks(String goodsRemarks) {
            this.goodsRemarks = goodsRemarks;
        }

        public double getGoodsSold() {
            return goodsSold;
        }

        public void setGoodsSold(double goodsSold) {
            this.goodsSold = goodsSold;
        }

        public String getSupplierUnique() {
            return supplierUnique;
        }

        public void setSupplierUnique(String supplierUnique) {
            this.supplierUnique = supplierUnique;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public double getGoodsPrice() {
            return goodsPrice;
        }

        public void setGoodsPrice(double goodsPrice) {
            this.goodsPrice = goodsPrice;
        }

        public String getSupGoodsBarcode() {
            return supGoodsBarcode;
        }

        public void setSupGoodsBarcode(String supGoodsBarcode) {
            this.supGoodsBarcode = supGoodsBarcode;
        }

        public String getForeignKey() {
            return foreignKey;
        }

        public void setForeignKey(String foreignKey) {
            this.foreignKey = foreignKey;
        }

        public int getTableType() {
            return tableType;
        }

        public void setTableType(int tableType) {
            this.tableType = tableType;
        }

        public List<ListDetailBean> getListDetail() {
            return listDetail;
        }

        public void setListDetail(List<ListDetailBean> listDetail) {
            this.listDetail = listDetail;
        }

        public static class ListDetailBean {
            /**
             * goodsId : 3463
             * goodsBarcode : 2030504092568
             * goodsName : 筷子
             * goodsStandard : 10双
             * goodsUnit : 包
             * goodsInPrice : 0.0
             * goodsSalePrice : 1.0
             * foreign_key : 2030504092568
             * containCount : 1
             * goodsPromotion : 1.0
             * goodsDiscount : 1.0
             * goodsPicturepath : upload/no_goodsB.jpg
             * goodsCusPrice : 1.0
             * shelfState : 1
             * countPresent : 0.0
             * pgoodsName : 0
             * pgoodsCount : 0.0
             * pgoodsUnit :
             */

            private int goodsId;
            private String goodsBarcode;
            private String goodsName;
            private String goodsStandard;
            private String goodsUnit;
            private double goodsInPrice;
            private double goodsSalePrice;
            private String foreign_key;
            private int containCount;
            private double goodsPromotion;
            private double goodsDiscount;
            private String goodsPicturepath;
            private double goodsCusPrice;
            private int shelfState;
            private double countPresent;
            private String pgoodsName;
            private double pgoodsCount;
            private String pgoodsUnit;

            public int getGoodsId() {
                return goodsId;
            }

            public void setGoodsId(int goodsId) {
                this.goodsId = goodsId;
            }

            public String getGoodsBarcode() {
                return goodsBarcode;
            }

            public void setGoodsBarcode(String goodsBarcode) {
                this.goodsBarcode = goodsBarcode;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getGoodsStandard() {
                return goodsStandard;
            }

            public void setGoodsStandard(String goodsStandard) {
                this.goodsStandard = goodsStandard;
            }

            public String getGoodsUnit() {
                return goodsUnit;
            }

            public void setGoodsUnit(String goodsUnit) {
                this.goodsUnit = goodsUnit;
            }

            public double getGoodsInPrice() {
                return goodsInPrice;
            }

            public void setGoodsInPrice(double goodsInPrice) {
                this.goodsInPrice = goodsInPrice;
            }

            public double getGoodsSalePrice() {
                return goodsSalePrice;
            }

            public void setGoodsSalePrice(double goodsSalePrice) {
                this.goodsSalePrice = goodsSalePrice;
            }

            public String getForeign_key() {
                return foreign_key;
            }

            public void setForeign_key(String foreign_key) {
                this.foreign_key = foreign_key;
            }

            public int getContainCount() {
                return containCount;
            }

            public void setContainCount(int containCount) {
                this.containCount = containCount;
            }

            public double getGoodsPromotion() {
                return goodsPromotion;
            }

            public void setGoodsPromotion(double goodsPromotion) {
                this.goodsPromotion = goodsPromotion;
            }

            public double getGoodsDiscount() {
                return goodsDiscount;
            }

            public void setGoodsDiscount(double goodsDiscount) {
                this.goodsDiscount = goodsDiscount;
            }

            public String getGoodsPicturepath() {
                return goodsPicturepath;
            }

            public void setGoodsPicturepath(String goodsPicturepath) {
                this.goodsPicturepath = goodsPicturepath;
            }

            public double getGoodsCusPrice() {
                return goodsCusPrice;
            }

            public void setGoodsCusPrice(double goodsCusPrice) {
                this.goodsCusPrice = goodsCusPrice;
            }

            public int getShelfState() {
                return shelfState;
            }

            public void setShelfState(int shelfState) {
                this.shelfState = shelfState;
            }

            public double getCountPresent() {
                return countPresent;
            }

            public void setCountPresent(double countPresent) {
                this.countPresent = countPresent;
            }

            public String getPgoodsName() {
                return pgoodsName;
            }

            public void setPgoodsName(String pgoodsName) {
                this.pgoodsName = pgoodsName;
            }

            public double getPgoodsCount() {
                return pgoodsCount;
            }

            public void setPgoodsCount(double pgoodsCount) {
                this.pgoodsCount = pgoodsCount;
            }

            public String getPgoodsUnit() {
                return pgoodsUnit;
            }

            public void setPgoodsUnit(String pgoodsUnit) {
                this.pgoodsUnit = pgoodsUnit;
            }
        }
    }
}