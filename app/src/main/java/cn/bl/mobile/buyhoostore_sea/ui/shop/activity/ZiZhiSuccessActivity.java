package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;

@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ZiZhiSuccessActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.tvTips1)
    TextView tvTips1;
    @BindView(R.id.tvTips2)
    TextView tvTips2;
    @BindView(R.id.tvMobile)
    TextView tvMobile;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_zi_zhi_success;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("shop")+getLanguageValue("qualifications")+getLanguageValue("authentication"));
    }

    @OnClick({R.id.ivBack, R.id.tvMobile})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvMobile:
                //打电话
                if (PermissionUtils.checkPermissionsGroup(TAG, 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(TAG, Constants.PERMISSION, 4);
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvMobile.setText(mobile);
        tvTips.setText(getLanguageValue("shop") + getLanguageValue("credential") + getLanguageValue("authentication"));
        tvStatus.setText(getLanguageValue("submit") + getLanguageValue("succeed"));
        tvTips.setText(getLanguageValue("tips") + ":");
        tvTips1.setText(getLanguageValue("openAuditNeedWeek"));
        tvTips2.setText(getLanguageValue("callCustomerServiceKotline"));
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this,
                getLanguageValue("confirmCall") + ":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == Constants.PERMISSION) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                callPhone(mobile);
            } else {
                showMessage(getLanguageValue("requiredCallPermission"));
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

}
