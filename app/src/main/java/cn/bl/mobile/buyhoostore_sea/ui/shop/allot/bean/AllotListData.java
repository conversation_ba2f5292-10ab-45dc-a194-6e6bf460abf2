package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:调拨单列表（实体类）
 * Created by jingang on 2023/5/24
 */
public class AllotListData implements Serializable {

    private List<AllotData> rows;

    public List<AllotData> getRows() {
        return rows;
    }

    public void setRows(List<AllotData> rows) {
        this.rows = rows;
    }
}
