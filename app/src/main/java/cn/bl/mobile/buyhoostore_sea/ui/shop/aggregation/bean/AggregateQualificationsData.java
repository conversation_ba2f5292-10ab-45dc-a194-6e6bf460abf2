package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.bean;

/**
 * Describe:聚合码查询资质信息（实体类）
 * Created by jingang on 2023/5/11
 */
public class AggregateQualificationsData {
    /**
     * status : 1
     * msg : 查询成功
     * data : {"license":"","licenseHand":"","licenseMerchantName":"","licenseCode":"","legalName":"","licenseAddress":"","legalIdCardPortrait":"","legalIdCardEmblem":"","legalIdCardHand":"","legalIdCardCode":"","bankCardFront":"","bankCardBack":"","bankCode":"","openBank":"","shopDoorhead":"","shopInside":"","shopCashier":"","cardholderIdCardPortrait":"","cardholderIdCardEmblem":"","cardholderName":"","cardholderIdCardCode":"","aggregateApplyType":0,"helibaoAuth":"","ruiyinxinAuth":""}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object count;
    private Object total;
    private Object rows;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getCount() {
        return count;
    }

    public void setCount(Object count) {
        this.count = count;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * license :
         * licenseHand :
         * licenseMerchantName :
         * licenseCode :
         * legalName :
         * licenseAddress :
         * legalIdCardPortrait :
         * legalIdCardEmblem :
         * legalIdCardHand :
         * legalIdCardCode :
         * bankCardFront :
         * bankCardBack :
         * bankCode :
         * openBank :
         * shopDoorhead :
         * shopInside :
         * shopCashier :
         * cardholderIdCardPortrait :
         * cardholderIdCardEmblem :
         * cardholderName :
         * cardholderIdCardCode :
         * aggregateApplyType : 0
         * helibaoAuth :
         * ruiyinxinAuth :
         */

        private String license;//营业执照
        private String licenseHand;//手持营业执照
        private String licenseMerchantName;//商户名称
        private String licenseCode;//营业执照号
        private String legalName;//法人姓名
        private String licenseAddress;//公司地址
        private String legalIdCardPortrait;//法人身份证人像面
        private String legalIdCardEmblem;//法人身份证国徽面
        private String legalIdCardHand;//法人手持身份证照片
        private String legalIdCardCode;//法人身份证号
        private String bankCardFront;//银行卡正面
        private String bankCardBack;//银行卡反面
        private String bankCode;//银行卡号
        private String openBank;//开户行
        private String shopDoorhead;//门头照
        private String shopInside;//店内照
        private String shopCashier;//收银台照
        private String cardholderIdCardPortrait;//持卡人身份证人像面
        private String cardholderIdCardEmblem;//持卡人身份证国徽面
        private String cardholderName;//持卡人姓名
        private String cardholderIdCardCode;//持卡人身份证号
        private int aggregateApplyType;//申请人类型：1.法人申请 2.非法人申请
        private String helibaoAuth;//合利宝授权书
        private String ruiyinxinAuth;//瑞银信授权书

        public String getLicense() {
            return license;
        }

        public void setLicense(String license) {
            this.license = license;
        }

        public String getLicenseHand() {
            return licenseHand;
        }

        public void setLicenseHand(String licenseHand) {
            this.licenseHand = licenseHand;
        }

        public String getLicenseMerchantName() {
            return licenseMerchantName;
        }

        public void setLicenseMerchantName(String licenseMerchantName) {
            this.licenseMerchantName = licenseMerchantName;
        }

        public String getLicenseCode() {
            return licenseCode;
        }

        public void setLicenseCode(String licenseCode) {
            this.licenseCode = licenseCode;
        }

        public String getLegalName() {
            return legalName;
        }

        public void setLegalName(String legalName) {
            this.legalName = legalName;
        }

        public String getLicenseAddress() {
            return licenseAddress;
        }

        public void setLicenseAddress(String licenseAddress) {
            this.licenseAddress = licenseAddress;
        }

        public String getLegalIdCardPortrait() {
            return legalIdCardPortrait;
        }

        public void setLegalIdCardPortrait(String legalIdCardPortrait) {
            this.legalIdCardPortrait = legalIdCardPortrait;
        }

        public String getLegalIdCardEmblem() {
            return legalIdCardEmblem;
        }

        public void setLegalIdCardEmblem(String legalIdCardEmblem) {
            this.legalIdCardEmblem = legalIdCardEmblem;
        }

        public String getLegalIdCardHand() {
            return legalIdCardHand;
        }

        public void setLegalIdCardHand(String legalIdCardHand) {
            this.legalIdCardHand = legalIdCardHand;
        }

        public String getLegalIdCardCode() {
            return legalIdCardCode;
        }

        public void setLegalIdCardCode(String legalIdCardCode) {
            this.legalIdCardCode = legalIdCardCode;
        }

        public String getBankCardFront() {
            return bankCardFront;
        }

        public void setBankCardFront(String bankCardFront) {
            this.bankCardFront = bankCardFront;
        }

        public String getBankCardBack() {
            return bankCardBack;
        }

        public void setBankCardBack(String bankCardBack) {
            this.bankCardBack = bankCardBack;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getOpenBank() {
            return openBank;
        }

        public void setOpenBank(String openBank) {
            this.openBank = openBank;
        }

        public String getShopDoorhead() {
            return shopDoorhead;
        }

        public void setShopDoorhead(String shopDoorhead) {
            this.shopDoorhead = shopDoorhead;
        }

        public String getShopInside() {
            return shopInside;
        }

        public void setShopInside(String shopInside) {
            this.shopInside = shopInside;
        }

        public String getShopCashier() {
            return shopCashier;
        }

        public void setShopCashier(String shopCashier) {
            this.shopCashier = shopCashier;
        }

        public String getCardholderIdCardPortrait() {
            return cardholderIdCardPortrait;
        }

        public void setCardholderIdCardPortrait(String cardholderIdCardPortrait) {
            this.cardholderIdCardPortrait = cardholderIdCardPortrait;
        }

        public String getCardholderIdCardEmblem() {
            return cardholderIdCardEmblem;
        }

        public void setCardholderIdCardEmblem(String cardholderIdCardEmblem) {
            this.cardholderIdCardEmblem = cardholderIdCardEmblem;
        }

        public String getCardholderName() {
            return cardholderName;
        }

        public void setCardholderName(String cardholderName) {
            this.cardholderName = cardholderName;
        }

        public String getCardholderIdCardCode() {
            return cardholderIdCardCode;
        }

        public void setCardholderIdCardCode(String cardholderIdCardCode) {
            this.cardholderIdCardCode = cardholderIdCardCode;
        }

        public int getAggregateApplyType() {
            return aggregateApplyType;
        }

        public void setAggregateApplyType(int aggregateApplyType) {
            this.aggregateApplyType = aggregateApplyType;
        }

        public String getHelibaoAuth() {
            return helibaoAuth;
        }

        public void setHelibaoAuth(String helibaoAuth) {
            this.helibaoAuth = helibaoAuth;
        }

        public String getRuiyinxinAuth() {
            return ruiyinxinAuth;
        }

        public void setRuiyinxinAuth(String ruiyinxinAuth) {
            this.ruiyinxinAuth = ruiyinxinAuth;
        }
    }
}
