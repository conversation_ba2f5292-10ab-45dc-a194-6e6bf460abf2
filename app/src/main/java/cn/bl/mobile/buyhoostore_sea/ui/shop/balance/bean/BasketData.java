package cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:余额（差价）-筐退款详情（实体类）
 * Created by jingang on 2023/6/7
 */
public class BasketData implements Serializable {
    /**
     * bucket_code : 1
     * shop_unique : null
     * shop_name : test
     * create_time : 2023-06-06 16:57:52.0
     * applicant : 陈向滨
     * return_money_total : 0.0
     * refund_status : 1
     * return_time : 2023-06-06 16:57:52.0
     * retrun_people : null
     * bucketlist : [{"bucket_name":"鸡蛋筐","bucket_deposit":"20.00","remarks":"ddd","bucket_amount":1}]
     */

    private String bucket_code;
    private String shop_unique;
    private String shop_name;
    private String create_time;
    private String applicant;//申请人
    private double return_money_total;//实际退款总金额
    private String refund_status;//退款状态
    private String return_time;
    private String retrun_people;//退款人
    private String staffer_phone;
    private List<BucketlistBean> bucketlist;

    public String getBucket_code() {
        return bucket_code;
    }

    public void setBucket_code(String bucket_code) {
        this.bucket_code = bucket_code;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getShop_name() {
        return shop_name;
    }

    public void setShop_name(String shop_name) {
        this.shop_name = shop_name;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public double getReturn_money_total() {
        return return_money_total;
    }

    public void setReturn_money_total(double return_money_total) {
        this.return_money_total = return_money_total;
    }

    public String getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(String refund_status) {
        this.refund_status = refund_status;
    }

    public String getReturn_time() {
        return return_time;
    }

    public void setReturn_time(String return_time) {
        this.return_time = return_time;
    }

    public String getRetrun_people() {
        return retrun_people;
    }

    public void setRetrun_people(String retrun_people) {
        this.retrun_people = retrun_people;
    }

    public String getStaffer_phone() {
        return staffer_phone;
    }

    public void setStaffer_phone(String staffer_phone) {
        this.staffer_phone = staffer_phone;
    }

    public List<BucketlistBean> getBucketlist() {
        return bucketlist;
    }

    public void setBucketlist(List<BucketlistBean> bucketlist) {
        this.bucketlist = bucketlist;
    }

    public static class BucketlistBean {
        /**
         * bucket_name : 鸡蛋筐
         * bucket_deposit : 20.00
         * remarks : ddd
         * bucket_amount : 1
         */

        private String bucket_name;
        private String bucket_deposit;
        private String remarks;
        private int bucket_amount;

        public String getBucket_name() {
            return bucket_name;
        }

        public void setBucket_name(String bucket_name) {
            this.bucket_name = bucket_name;
        }

        public String getBucket_deposit() {
            return bucket_deposit;
        }

        public void setBucket_deposit(String bucket_deposit) {
            this.bucket_deposit = bucket_deposit;
        }

        public String getRemarks() {
            return remarks;
        }

        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }

        public int getBucket_amount() {
            return bucket_amount;
        }

        public void setBucket_amount(int bucket_amount) {
            this.bucket_amount = bucket_amount;
        }
    }
}
