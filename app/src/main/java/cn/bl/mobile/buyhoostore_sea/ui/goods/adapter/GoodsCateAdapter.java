package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;

/**
 * Describe:商品分类-一级-分类管理（适配器）
 * Created by jingang on 2023/3/20
 */
public class GoodsCateAdapter extends BaseAdapter<CateData> {

    public GoodsCateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        ImageView ivIcon = holder.getView(R.id.ivItemIcon);
        TextView tvName = holder.getView(R.id.tvItemName);
        RecyclerView rvCate = holder.getView(R.id.rvItemCate);
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getKindIcon()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGroupName());
        if (mDataList.get(position).getKindDetail().size() > 0) {
            rvCate.setLayoutManager(new LinearLayoutManager(mContext));
            GoodsCate1Adapter adapter = new GoodsCate1Adapter(mContext);
            rvCate.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getKindDetail());
            adapter.setListener(new GoodsCate1Adapter.MyListener() {
                @Override
                public void onItemClick(View view, int position1) {
                    if (listener != null) {
                        listener.onChildItemClick(view, position, position1);
                    }
                }

                @Override
                public void onOpenClick(View view, int position1) {
                    if (listener != null) {
                        listener.onChildOpenClick(view, position, position1);
                    }
                }

                @Override
                public void onMoreClick(View view, int position1) {
                    if (listener != null) {
                        listener.onChildMoreClick(view, position, position1);
                    }
                }

                @Override
                public void onChildItemClick(View view, int position1, int position2) {
                    if (listener != null) {
                        listener.onChildChildItemClick(view, position, position1, position2);
                    }
                }

//                @Override
//                public void onChildEditClick(View view, int position1, int position2) {
//                    if (listener != null) {
//                        listener.onChildChildEditClick(view, position, position1, position2);
//                    }
//                }
//
//                @Override
//                public void onChildDelClick(View view, int position1, int position2) {
//                    if (listener != null) {
//                        listener.onChildChildDelClick(view, position, position1, position2);
//                    }
//                }

                @Override
                public void onChildMoreClick(View view, int position1, int position2) {
                    if (listener != null) {
                        listener.onChildChildMoreClick(view, position, position1, position2);
                    }
                }
            });
            if (mDataList.get(position).isCheck()) {
                ivIcon.setImageResource(R.mipmap.ic_arrow011);
                rvCate.setVisibility(View.VISIBLE);
            } else {
                ivIcon.setImageResource(R.mipmap.ic_arrow010);
                rvCate.setVisibility(View.GONE);
            }
        } else {
            rvCate.setVisibility(View.GONE);
            ivIcon.setVisibility(View.GONE);
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivIcon, ivMore;
        TextView tvName, tvDefault, tvEnable;
        ivImg = holder.getView(R.id.ivItemImg);
        ivIcon = holder.getView(R.id.ivItemIcon);
        ivMore = holder.getView(R.id.ivItemMore);
        tvName = holder.getView(R.id.tvItemName);
        tvDefault = holder.getView(R.id.tvItemDefault);
        tvEnable = holder.getView(R.id.tvItemEnable);
        RecyclerView rvCate = holder.getView(R.id.rvItemCate);
        tvEnable.setText(getLanguageValue("deactivated"));

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getKindIcon()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGroupName());
        tvEnable.setVisibility(mDataList.get(position).getValid_type() == 2 ? View.VISIBLE : View.GONE);
//        //是否可编辑，1、不可编辑；2、可编辑
//        if (mDataList.get(position).getEditType() == 2) {
//            ivMore.setVisibility(View.VISIBLE);
//            tvDefault.setVisibility(View.GONE);
//        } else {
//            ivMore.setVisibility(View.GONE);
//            tvDefault.setVisibility(View.VISIBLE);
//        }
        if (mDataList.get(position).getKindDetail() == null) {
            rvCate.setVisibility(View.GONE);
            ivIcon.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getKindDetail().isEmpty()) {
                rvCate.setVisibility(View.GONE);
                ivIcon.setVisibility(View.GONE);
            } else {
                rvCate.setVisibility(View.VISIBLE);
                ivIcon.setVisibility(View.VISIBLE);
                GoodsCate1Adapter adapter = new GoodsCate1Adapter(mContext);
                rvCate.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getKindDetail());
                adapter.setListener(new GoodsCate1Adapter.MyListener() {
                    @Override
                    public void onItemClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildItemClick(view, position, position1);
                        }
                    }

                    @Override
                    public void onOpenClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildOpenClick(view, position, position1);
                        }
                    }

                    @Override
                    public void onMoreClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildMoreClick(view, position, position1);
                        }
                    }

                    @Override
                    public void onChildItemClick(View view, int position1, int position2) {
                        if (listener != null) {
                            listener.onChildChildItemClick(view, position, position1, position2);
                        }
                    }

                    //                    @Override
//                    public void onChildEditClick(View view, int position1, int position2) {
//                        if (listener != null) {
//                            listener.onChildChildEditClick(view, position, position1, position2);
//                        }
//                    }
//
//                    @Override
//                    public void onChildDelClick(View view, int position1, int position2) {
//                        if (listener != null) {
//                            listener.onChildChildDelClick(view, position, position1, position2);
//                        }
//                    }
                    @Override
                    public void onChildMoreClick(View view, int position1, int position2) {
                        if (listener != null) {
                            listener.onChildChildMoreClick(view, position, position1, position2);
                        }
                    }
                });
            }
        }
        if (mDataList.get(position).isCheck()) {
            ivIcon.setImageResource(R.mipmap.ic_arrow011);
            rvCate.setVisibility(View.VISIBLE);
        } else {
            ivIcon.setImageResource(R.mipmap.ic_arrow010);
            rvCate.setVisibility(View.GONE);
        }

        if (listener != null) {
            ivIcon.setOnClickListener(v -> listener.onOpenClick(v, position));
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {

        void onOpenClick(View view, int position);

        void onMoreClick(View view, int position);

        void onChildItemClick(View view, int position, int position1);

        void onChildOpenClick(View view, int position, int position1);

        void onChildMoreClick(View view, int position, int position1);

        void onChildChildItemClick(View view, int position, int position1, int position2);

//        void onChildChildEditClick(View view, int position, int position1, int position2);
//
//        void onChildChildDelClick(View view, int position, int position1, int position2);

        void onChildChildMoreClick(View view, int position, int position1, int position2);
    }
}