package cn.bl.mobile.buyhoostore_sea.zhttp;

import cn.bl.mobile.buyhoostore_sea.BuildConfig;

public class ZURL {

    public static String CONTANT_WECHAT_APPID = "wx38618456447b4af2";

    //    public final static String CONSTANT_ONLINE_URL = "http://buyhoo.cc/";//地址shopUpdate
    public final static String CONSTANT_ONLINE_URL = "https://global-shop.all-scm.com/";//地址shopUpdate
    public final static String CONSTANT_ONLINE_MALL_URL = "http://yun.buyhoo.cc/";//地址shopUpdate
    //    public final static String TEST_THREE_URL = "http://test170.buyhoo.cc/";//测试地址shopUpdate
    public final static String TEST_THREE_URL = "https://test-global.buyhoo.cc/";//测试地址shopUpdate
    public final static String DEV_THREE_URL = "https://dev-global.buyhoo.cc/";//开发地址shopUpdate

    //    public final static String TEST_CONSTANT_ONLINE_MALL_URL = "http://test170.buyhoo.cc/";//测试地址shopUpdate
    public final static String TEST_CONSTANT_ONLINE_MALL_URL = "https://test-global.buyhoo.cc/";//测试地址shopUpdate

    //动态切换域名环境
    public static String ONLINE_URL = BuildConfig.DEBUG ? TEST_THREE_URL : CONSTANT_ONLINE_URL;
    public static String COMMON_Mall_URL = BuildConfig.DEBUG ? TEST_CONSTANT_ONLINE_MALL_URL : CONSTANT_ONLINE_MALL_URL;

    //供货商对账
    public static String getSupplier() {
        return ONLINE_URL + "shopmanager/app/shop/checkAccountEasy.do?";
    }

    //检查更新
    public static String getUpdateUrl() {
        return ONLINE_URL + "shopmanager/app/shop/updateAppCheck.do";
    }

    //手动输入数量
    public static String getnum() {
        return ONLINE_URL + "shopUpdate/purList/directlyModifyCartGoodsNew.do?";
    }

    public static String getAddress() {
        return ONLINE_URL + "goBuy/user/area.do";
    }


    //------------------------------------第二版接口信息------------------------------
    //用户登录页面后刷新用的的 channleID
    public static String getLoginTwo() {
        return ONLINE_URL + "shopUpdate/shopsStaff/staffLoginByAccountPwd.do";
    }

    public static String getRegister() {
        return ONLINE_URL + "shop/html/shop/register";
    }

    //发送验证码
    public static String getSendCodeUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/sendMessage.do?";
    }

    //验证手机号和密码
    public static String getCheckPwdUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/passMsg.do?";
    }

    //设置新密码
    public static String getSetNewPwdUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/updateShopsPwd.do?";
    }

    //销售订单数量查询
    public static String getshopsSaleListCount() {
        return ONLINE_URL + "shopUpdate/saleList/shopsSaleListCount.do?";
    }

    //网单订单查询
    public static String getsalelistTWO() {
        return ONLINE_URL + "shopUpdate/saleList/querySaleList.do?";
    }

    // 查询退款订单列表
    public static String getRefundOrderList() {
        return ONLINE_URL + "shopUpdate/saleList/queryRetLists.do?";
    }

    // 查询退款订单详情
    public static String getRefundOrderDetail() {
        return ONLINE_URL + "shopUpdate/saleList/queryReturnDetail.do?";
    }

    // 查询店铺配送设置
    public static String getDistributionsetting() {
        return ONLINE_URL + "shopUpdate/cash/queryShopDelivery.do?";
    }

    // 更新店铺配送设置
    public static String getUpdateDistributionSetting() {
        return ONLINE_URL + "shopUpdate/cash/updateShopDelivery.do?";
    }

    // 查询店铺骑手
    public static String getDistributionpersonList() {
        return ONLINE_URL + "shopUpdate/cash/quertShopCourierList.do?";
    }

    // 添加新的骑手信息或修改现有骑手信息
    public static String getUpdateDistributionPerson() {
        return ONLINE_URL + "shopUpdate/cash/addShopCourier.do?";
    }

    // 查询常用话术
    public static String getRefundCommonMsgList() {
        return ONLINE_URL + "shopUpdate/saleList/queryReturnListMsg.do?";
    }

    // 退款处理
    public static String getHandelRefundUrL() {
        return ONLINE_URL + "shopUpdate/saleList/modifyReturnMsg.do?";
    }

    // 修改退款拒绝话术
    public static String getHandelRefundMsgUrL() {
        return ONLINE_URL + "shopUpdate/saleList/modifyReturnListMsg.do?";
    }

    // 修改退款拒绝话术
    public static String getAddRefundMsgUrL() {
        return ONLINE_URL + "shopUpdate/saleList/addNewReturnListMsg.do?";
    }

    //销售订单详情
    public static String getOrderDetailUrlTWO() {
        return ONLINE_URL + "shopUpdate/saleList/querySaleListDetail.do?";
    }

    //取消订单
    public static String getCancelSaleList() {
        return ONLINE_URL + "goBuy/my/cancelSaleList.do";
    }

    //店铺信息查询
    public static String getShopInfoUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/shopNewMessage.do?";
    }

    //修改店铺名称
    public static String getUpdateInfoUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/updateShopsMessage.do?";
    }

    //店员信息查询
    public static String getPersonInfoUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/staffNewMessage.do?";
    }

    //个人信息更新
    public static String getTIME_gerenTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/editShopsInfo.do?";
    }

    //功能模块查询
    public static String getSetSwitchUrlTWO() {
        return ONLINE_URL + "shopUpdate/shopFunction/queryShopFunction.do?";
    }

    //功能模块修改
    public static String setshoppower() {
        return ONLINE_URL + "shopUpdate/shopFunction/modifyShopFunction.do?";
    }

    //获取店铺列表
    public static String getshoplistTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/queryShopsByManager.do";
    }

    //获取员工列表
    public static String getpersonlistTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/shopsStaffsSearchByShopUnique.do?";
    }

    //交接班记录
    public static String getShifts() {
        return ONLINE_URL + "shopUpdate/shopsStaff/queryHandoverRecord.do";
    }

    //权限更新
    public static String updatepersonlistTWO() {
        return ONLINE_URL + "shopUpdate/shopsStaff/modifyStaffPower.do?";
    }

    //会员列表及查询
    public static String getRemember() {
        return ONLINE_URL + "shopUpdate/cuscheckout/getCustList.do?";
    }

    //会员列表及查询
    public static String getRememberDetail() {
        return ONLINE_URL + "shopUpdate/cuscheckout/findCusById.do?";
    }

    //会员详情页下半部
    public static String getQueryCusDetailDown() {
        return ONLINE_URL + "shopUpdate/cuscheckout/queryCusDetailDown.do";
    }

    //修改会员密码
    public static String getEditCusPassword() {
        return ONLINE_URL + "shopUpdate/cuscheckout/editCusPassword.do";
    }

    //修改会员状态
    public static String getEditCusStatus() {
        return ONLINE_URL + "shopUpdate/cuscheckout/editCusStatus.do";
    }

    //修改会员等级
    public static String getEditCusLevel() {
        return ONLINE_URL + "shopUpdate/cuscheckout/editCusLevel.do";
    }

    //储值卡充值取现
    public static String getRecharge() {
        return ONLINE_URL + "harricane/cuscheckout/recharge.do";
    }

    //修改会员
    public static String getRememberUpdate() {
        return ONLINE_URL + "shopUpdate/cuscheckout/editCus.do?";
    }

    //添加会员
    public static String getaddRember() {
        return ONLINE_URL + "shopUpdate/cuscheckout/addCus.do?";
    }

    //消息列表
    public static String getShopMessagetwo() {
        return ONLINE_URL + "shopUpdate/cuscheckout/getMsgList.do?";
    }

    //商品小分类
    public static String getsmalltype() {
        return ONLINE_URL + "shopUpdate/goodsKinds/appQueryGoodsKinds.do?";
    }

    //根据大类小类查商品
    public static String getSelectGoods() {
        return ONLINE_URL + "shopUpdate/goods/queryGoodsMessage.do?";
    }

    //商品详情
    public static String getSelecDetail() {
        return ONLINE_URL + "shopUpdate/goods/goodsDetail.do";
    }

    //扫描获取商品详情
    public static String getSelecScan() {
        return ONLINE_URL + "shopUpdate/goods/searchBaseGoods.do?";
    }

    //修改库存
    public static String setUpdateNum2() {
        return ONLINE_URL + "shopUpdate/stock/newStockRecord.do?";
    }

    //出入库历史记录
    public static String gethistory() {
        return ONLINE_URL + "shopUpdate/goods/stockRecord.do?";
    }

    //出入库原因
    public static String getReason_churu() {
        return ONLINE_URL + "shopUpdate/stock/reason.do";
    }

    //查询批次列表
    public static String getGoodBatchList() {
        return ONLINE_URL + "shopUpdate/goodBatch/queryGoodBatchList.do";
    }

    //销售统计
    public static String getsales() {
        return ONLINE_URL + "shopUpdate/goods/goodsSaleStatistics.do?";
    }

    //通过扫码，查询商品信息
    public static String getgoodsname() {
        return ONLINE_URL + "shopUpdate/goods/queryBaseGoodsMessageByCode.do?";
    }

    //扫码查询商品
    public static String getselectgoods() {
//        return ONLINE_URL + "shopUpdate/inventory/queryGoodsByBarcode.do?";
        return ONLINE_URL + "shopUpdate/inventory/queryGoodsByBarcodeCurrent.do";
    }

    //热销排行
    public static String getrexiaodetail() {
        return ONLINE_URL + "shopUpdate/purList/sellList.do?";
    }

    //新版：主界面信息统计
    public static String getShopMessagenew() {
        return ONLINE_URL + "shopUpdate/statistics/queryStatisticsMessageInMain.do?";
    }

    //经营统计界面-经营总览
    public static String getzonglan() {
        return ONLINE_URL + "shopUpdate/statistics/businessOverview.do?";
    }

    //经营统计界面-经营总览
    public static String getfenleizhanbi() {
        return ONLINE_URL + "shopUpdate/statistics/querySaleSumByKind.do?";
    }

    //top5
    public static String gettopfive() {
        return ONLINE_URL + "shopUpdate/statistics/queryPreSaleFiveGoods.do?";
    }

    /**
     * 经营统计-销售额走势
     */
    public static String getShopSellzoushi() {
        return ONLINE_URL + "shopUpdate/statistics/querySaleSumTrend.do?";
    }

    /**
     * 修改密码
     */
    public static String setnewpassword() {
        return ONLINE_URL + "shopUpdate/shopsStaff/editStafPwd.do?";
    }

    /**
     * 会员等级列表
     */
    public static String getgradelist() {
        return ONLINE_URL + "harricane/cuscheckout/getCustLevelList.do?";
    }

    /**
     * 删除商品
     */
    public static String deletegoods() {
        return ONLINE_URL + "shopUpdate/goods/deleteShopsGoods.do?";
    }

    /**
     * 库存
     */
    public static String getkucun() {
        return ONLINE_URL + "shopUpdate/goodsCount/queryGoodsCount.do?";
    }

    public static String getyujing() {
        return ONLINE_URL + "shopUpdate/goodsCount/queryGoodsCountWarning.do?";
    }

    /**
     * 查询店铺资质
     */
    public static String getzizhi() {
        return ONLINE_URL + "shopUpdate/shopsStaff/queryShopQualification.do?";
    }

    /**
     * 查询排序分类大类（查询所有的大分类信息，不包含隐藏的分类（添加零利润和负利润分类））
     */
    public static String getdapaixu() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryGoodsBigKindsByShop.do?";
    }

    /**
     * 查询更多分类（查询所有的大分类信息，隐藏的）
     */
    public static String getmoreclass() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryMoreGoodsBigKinds.do";
    }

    /**
     * 保存分类排序
     */
    public static String getbaocun() {
        return ONLINE_URL + "shopUpdate/goodsKinds/saveGoodsBigKindsByShop.do?";
    }

    /**
     * 查询主页的模块信息_V2
     */
    public static String getmodule_v2() {
        return ONLINE_URL + "shopUpdate/shopTitle/queryMainPageTitle/v2.do?";
    }

    /**
     * 查询支持的银行列表
     */
    public static String selectbanklist() {
        return ONLINE_URL + "shopUpdate/diamonds/getBankListMsg.do?";
    }

    /**
     * 添加或修改银行卡
     */
    public static String addbank() {
        return ONLINE_URL + "shopUpdate/diamonds/addNewShopCard.do?";
    }

    /**
     * 获取银行卡详情
     */
    public static String getbankdetail() {
        return ONLINE_URL + "shopUpdate/diamonds/getCardDetail.do?";
    }

    /**
     * 自定义商品分类查询（大分类带小分类，查询所有的有效分类）
     */
    public static String getGoodsCate() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryGoodsKindsByShop.do?";
    }

    //自定义商品分类的新增、修改、删除（假的，是启用/禁用）
    public static String addcustomize() {
        return ONLINE_URL + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    }

    //商品分类图标查询
    public static String getGoodsCateImg() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryGoodsKindIconByIconType.do";
    }

    //商品分类删除前，删除数量的查询
    public static String getcustomizecount() {
        return ONLINE_URL + "shopUpdate/goodsKinds/getGoodsCountByKindUnique.do?";
    }

    //分类使用说明
    public static String getinstructions() {
        return ONLINE_URL + "shopUpdate/goodsKinds/getRemarksForCustomKind.do?";
    }

    //商品分类：删除
    public static String getGoodsCateDel() {
        return ONLINE_URL + "shopUpdate/goodsKinds/deleteKind.do";
    }

    //关于我们
    public final static String CONSTANT_QUERY_PRIVACY_POLICY = CONSTANT_ONLINE_MALL_URL + "purchase-app/html/privacyAgreement.html";

    //查询供货商列表
    public static String getQuerySupplierList() {
        return ONLINE_URL + "harricane/html/supplier/querySupplierList.do";
    }


    //创建订单编号
    public static String getcreateSaleListUnique() {
        return ONLINE_URL + "shopUpdate/appPay/createSaleListUnique.do";
    }

    //自助收银-易通支付接口u7
    public static String getYiTongPay() {
        return ONLINE_URL + "harricane/payOnline/yiTongPay.do";
    }

    //登录传推送注册ID
    public static String getUpdateRegistrationId() {
        return ONLINE_URL + "shopUpdate/shopsStaff/updateRegistrationId.do";
    }

    //确认自提和确认收货接口,完成
    public static String getConfirmReceipt() {
        return ONLINE_URL + "goBuy/my/confirmReceipt.do";
    }

    public static String getCancelDelivery() {
        return "http://delivery.buyhoo.cc/outside/cancelDelivery";
    }

    //获取商家自配送骑手列表
    public static String getShopCourierList() {
//        return ONLINE_URL + "shop/peisong/order/shopCourierList";
        return ONLINE_URL + "shopUpdate/cash/quertShopCourierList.do";
    }

    //配送订单创建，根据订单不同的配送方式，创建不同的配送单
    //配送方式为自配送时，需先选择自配送骑手
    public static String getCreateOrder() {
//        return ONLINE_URL + "shop/peisong/createOrder";
        return ONLINE_URL + "shopUpdate/cash/createOrder.do";
    }

    //店铺资质
    public static String getSubmitShopQualification() {
        return ONLINE_URL + "shopUpdate/shopsStaff/submitShopQualification.do";
    }

    //用户协议
    public final static String CONSTANT_shopDoc = "http://buyhoo.cc/html/shop_doc.html?";

    //公用上传文件接口
    public static String getUploadFile() {
        return ONLINE_URL + "shopUpdate/loanMoney/uploadFile.do";
    }

    //商品-单位新增
    public static String getGoodsUnitAdd() {
        return ONLINE_URL + "shopUpdate/goodsUnit/addGoodsUnit.do";
    }

    //商品-单位删除
    public static String getGoodsUnitDel() {
        return ONLINE_URL + "shopUpdate/goodsUnit/deleteGoodsUnit.do";
    }

    //商品-单位编辑
    public static String getGoodsUnitEdit() {
        return ONLINE_URL + "shopUpdate/goodsUnit/editGoodsUnit.do";
    }

    //商品-单位列表
    public static String getGoodsUnitList() {
        return ONLINE_URL + "shopUpdate/goodsUnit/findGoodsUnitList.do";
    }

    //商品变更记录
    public static String getGoodsChangeRecord() {
        return ONLINE_URL + "shopUpdate/recordGoodsOper/queryRecordGoodsOper.do";
    }

    //商品变更记录-详情
    public static String getGoodsChangeRecordInfo() {
        return ONLINE_URL + "shopUpdate/recordGoodsOper/queryRecordGoodsOperDetail.do";
    }

    /*****************************商品end******************************/

    /*****************************店铺******************************/

    //百货豆首页（查询豆的数量、支出情况）
    public static String getQueryBeanList() {
        return ONLINE_URL + "shopUpdate/bean/queryBeanList.do";
    }

    //提现按钮（返回可提现金额、提现说明）
    public static String getQueryBeanMoney() {
        return ONLINE_URL + "shopUpdate/bean/queryBeanMoney.do";
    }

    //百货豆提现
    public static String getAddBeanMoney() {
        return ONLINE_URL + "shopUpdate/bean/addBeanMoney.do";
    }

    //查询已绑定的银行卡信息列表
    public static String getGetShopCardList() {
        return ONLINE_URL + "shopUpdate/diamonds/getShopCardList.do";
    }

    //添加或更新银行卡信息
    public static String getAddNewShopCard() {
        return ONLINE_URL + "shopUpdate/diamonds/addNewShopCard.do";
    }

    //删除银行卡信息
    public static String getRemoveShopCard() {
        return ONLINE_URL + "shopUpdate/diamonds/removeShopCard.do";
    }

    //查询支持的银行列表
    public static String getGetBankListMsg() {
        return ONLINE_URL + "shopUpdate/diamonds/getBankListMsg.do";
    }

    //商品补货调价
    public static String getQueryPurchaseOrderGoods() {
        return ONLINE_URL + "shopUpdate/goods/queryPurchaseOrderGoods.do";
    }

    //补货调价-修改商品价格
    public static String getUpdateReplenishmentGoods() {
        return ONLINE_URL + "shopUpdate/goods/updateReplenishmentGoods.do";
    }

    //订单差价列表及店铺店铺差价信息
    public static String getShopGapList() {
        return COMMON_Mall_URL + "purchase-app/shopping/queryShopGapList.do";
    }

    //筐押金退款详情
    public static String getBasketRecoveryInfo() {
        return COMMON_Mall_URL + "purchase-app/shopping/queryBucketRecoveryDetail.do";
    }

    //查询聚合码审核状态
    public static String getAggregatePayStatus() {
        return ONLINE_URL + "shopUpdate/shopQualificationInfo/aggregationCode/getAuditStatus.do";
    }

    //聚合码查询资质信息
    public static String getAggregateQualifications() {
        return ONLINE_URL + "shopUpdate/shopQualificationInfo/aggregationCode/getQualifications.do";
    }

    //保存聚合码资质信息
    public static String getAggregateSaveQualifications() {
        return ONLINE_URL + "shopUpdate/shopQualificationInfo/aggregationCode/saveAggregationCode.do";
    }

    //调拨单列表
    public static String getAllot_list() {
        return ONLINE_URL + "shopUpdate/goods/allocate/queryList.do";
    }

    //新增调拨
    public static String getAllot_add() {
        return ONLINE_URL + "shopUpdate/goods/allocate/addAllocate.do";
    }

    //调拨-确认收货
    public static String getAllot_finish() {
        return ONLINE_URL + "shopUpdate/goods/allocate/finishAllocate.do";
    }

    //调拨-撤销
    public static String getAllot_quash() {
        return ONLINE_URL + "shopUpdate/goods/allocate/quashAllocate.do";
    }

    //农批产品信息查询
    public static String getFarmGoodsList() {
        return ONLINE_URL + "shopUpdate/goods/queryFarmGoodsList.do";
    }

    //农批产品订单保存
    public static String getFarmOrderSave() {
        return ONLINE_URL + "shopUpdate/saleList/saveFarmOrder.do";
    }

    public static String getAddCusNP() {
        return ONLINE_URL + "shopUpdate/cuscheckout/addCusNP.do";
    }

    //添加(编辑)农批商品
    public static String getFarmAddGoods() {
        return ONLINE_URL + "shopUpdate/goods/addNewGoodsNP.do";
    }

    /*****库存盘点******/
    //盘点-新增任务
    public static String getTaskAdd() {
        return ONLINE_URL + "shopUpdate/inventoryTask/addTask.do";
    }

    //盘点任务列表
    public static String getTaskList() {
        return ONLINE_URL + "shopUpdate/inventoryTask/taskList.do";
    }

    //盘点-删除任务
    public static String getTaskDel() {
        return ONLINE_URL + "shopUpdate/inventoryTask/deleteTask.do";
    }

    //盘点-任务详情
    public static String getTaskInfo() {
        return ONLINE_URL + "shopUpdate/inventoryTask/taskDetail.do";
    }

    //盘点-修改盘点单名称
    public static String getTaskUpdateName() {
        return ONLINE_URL + "shopUpdate/inventoryTask/updateTaskName.do";
    }

    //盘点-保存盘点单详情(添加商品)
    public static String getTaskInfoAdd() {
        return ONLINE_URL + "shopUpdate/inventoryTask/addTaskDetail.do";
    }

    //盘点-修改盘点详情（修改商品）
    public static String getTaskInfoUpdate() {
        return ONLINE_URL + "shopUpdate/inventoryTask/updateTaskDetail.do";
    }

    //盘点-删除盘点详情（删除商品）
    public static String getTaskInfoDel() {
        return ONLINE_URL + "shopUpdate/inventoryTask/deleteTaskDetail.do";
    }

    //盘点-货位列表
    public static String getTaskGoodsLocationList() {
        return ONLINE_URL + "shopUpdate/inventoryTask/goodsLocationList.do";
    }

    //盘点-新增货位
    public static String getTaskGoodsLocationAdd() {
        return ONLINE_URL + "shopUpdate/inventoryTask/addGoodsLocation.do";
    }

    //盘点-修改货位
    public static String getTaskGoodsLocationUpdate() {
        return ONLINE_URL + "shopUpdate/inventoryTask/updateGoodsLocation.do";
    }

    //盘点-删除货位
    public static String getTaskGoodsLocationDel() {
        return ONLINE_URL + "shopUpdate/inventoryTask/deleteGoodsLocation.do";
    }

    //盘点-盘点单预览
    public static String getTaskPreview() {
        return ONLINE_URL + "shopUpdate/inventoryTask/taskPreview.do";
    }

    //盘点-提交
    public static String getTaskSubmit() {
        return ONLINE_URL + "shopUpdate/inventoryTask/submitTask.do";
    }

    //盘点-单个商品明细
    public static String getTaskGoodsInfo() {
        return ONLINE_URL + "shopUpdate/inventoryTask/taskGoodsDetail.do";
    }

    //盘点-新增筐
    public static String getTaskBasketAdd() {
        return ONLINE_URL + "shopUpdate/inventoryTask/addBucketWeight.do";
    }

    //盘点-修改筐
    public static String getTaskBasketUpdate() {
        return ONLINE_URL + "shopUpdate/inventoryTask/updateBucketWeight.do";
    }

    //盘点-删除筐
    public static String getTaskBasketDel() {
        return ONLINE_URL + "shopUpdate/inventoryTask/deleteBucketWeight.do";
    }

    //盘点-筐列表
    public static String getTaskBasketList() {
        return ONLINE_URL + "shopUpdate/inventoryTask/bucketWeightList.do";
    }

    //盘点-单个商品盘点记录
    public static String getTaskGoodsRecord() {
        return ONLINE_URL + "shopUpdate/inventoryTask/inventoryGoodsRecord.do";
    }

    /*****************************登录注册******************************/

    //验证注册手机号和密码
    public static String getVerifyUser() {
        return ONLINE_URL + "shop/html/shop/verifyUser";
    }

    //校验店铺名称
    public static String getVerifyShop() {
        return ONLINE_URL + "shop/html/shop/verifyShop";
    }

    /*****************************商品管理******************************/

    //新增商品-v2.0
    public static String getGoodsAdd() {
        return ONLINE_URL + "shopUpdate/goods/v2/addGoods.do";
    }

    //修改商品 v2.0
    public static String getGoodsEdit() {
        return ONLINE_URL + "shopUpdate/goods/v2/updateGoods.do";
    }

    //生成条码
    public static String getGoodsBarcode() {
        return ONLINE_URL + "shopUpdate/goods/queryGoodsBarcodeSameForeignkey.do";
    }

    //商品转移分类
    public static String getGoodsKindTransfer() {
        return ONLINE_URL + "shopUpdate/goods/goodsKindTransfer.do";
    }

    //商品上架、下架
    public static String getGoodsShelfState() {
        return ONLINE_URL + "shopUpdate/goods/goodsShelfState.do";
    }

    /*****************************农批******************************/
    //农批首页统计数据
    public static String getFarm_index() {
        return ONLINE_URL + "shopUpdate/saleList/indexStatisticsNP.do";
    }

    //农批-订单作废
    public static String getFarm_salelist_cancel() {
        return ONLINE_URL + "shopUpdate/saleList/saleListInvalidNP.do";
    }

    //农批-商品销售统计（货品销售汇总）
    public static String getFarm_GoodsSaleStatistics() {
        return ONLINE_URL + "shopUpdate/goods/goodsSaleStatistics.do";
    }

    /*****************************店铺-宁宇收银start******************************/

    //查询快捷金额配置
    public static String getShopQuickPayConfigList() {
        return ONLINE_URL + "shopUpdate/shopQuickPay/configList.do";
    }

    //快捷金额配置新增
    public static String getShopQuickPayConfigAdd() {
        return ONLINE_URL + "shopUpdate/shopQuickPay/addConfig.do";
    }

    //快捷金额配置编辑
    public static String getShopQuickPayConfigEdit() {
        return ONLINE_URL + "shopUpdate/shopQuickPay/updateConfig.do";
    }

    //快捷金额配置删除
    public static String getShopQuickPayConfigDel() {
        return ONLINE_URL + "shopUpdate/shopQuickPay/deleteConfig.do";
    }

    //宁宇会员列表
    public static String getNingYuCusList() {
        return ONLINE_URL + "shopmanager/customer/queryShopCusList.do";
    }

    //收银-宁宇收银端订单（宁宇会员支付）-余额支付
    public static String getNingYuMemberPay() {
        return ONLINE_URL + "harricane/payOnline/cashierPay_ny.do";
    }

    //宁宇收银端（线下）-现金收款
    public static String getNingYuPay() {
        return ONLINE_URL + "harricane/payOnline/cashierPay.do";
    }

    //扫码支付结果查询
    public static String getNingYuPayStatus() {
        return ONLINE_URL + "harricane/payOnline/queryOrderYT.do";
    }

    /*****************************店铺-宁宇收银end******************************/

    /****************************店铺-今日营业额start****************************/
    //统计信息
    public static String getStatisticsByShop() {
        return ONLINE_URL + "shopUpdate/statistics/queryStatisticsByShop.do";
    }

    //查询各支付方式下的订单列表
    public static String getSaleListByPaymethod() {
        return ONLINE_URL + "shopUpdate/statistics/querySaleListByPaymethod.do";
    }

    //查询订单或退款订单的支付详情
    public static String getSaleListPayMethodBySaleListUnique() {
        return ONLINE_URL + "shopUpdate/statistics/querySaleListPayMethodBySaleListUnique.do";
    }

    /****************************店铺-今日营业额end****************************/

    /*****************************补货单start******************************/

    //查询店铺绑定的供货商信息
    public static String getRestockSupplierList() {
        return ONLINE_URL + "shopUpdate/restockPlan/getGoodsSupplierMsg.do";
    }

    public static String getSupplierListWeb() {
        return ONLINE_URL + "shop/html/goods/getGoodsSupplierMsg.do";
    }

    //添加补货计划
    public static String getRestockPlanADD() {
        return ONLINE_URL + "shopUpdate/restockPlan/addRestockPlan.do";
    }

    //查询补货计划列表
    public static String getRestockPlanList() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryRestockPlanList.do";
    }

    //修改补货计划状态信息
    public static String getRestockPlanStatus() {
        return ONLINE_URL + "shopUpdate/restockPlan/updatePlanStatus.do";
    }

    //删除补货计划
    public static String getRestockPlanDel() {
        return ONLINE_URL + "shopUpdate/restockPlan/deleteRestockPlan.do";
    }

    //再次补货
    public static String getRestockPlanAgain() {
        return ONLINE_URL + "shopUpdate/restockPlan/restockAgain.do";
    }

    //查询补货计划商品列表
    public static String getRestockPlanGoodsList() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryGoodsListByPlanId.do";
    }

    //补货单-添加商品
    public static String getRestockPlanGoodsAdd() {
        return ONLINE_URL + "shopUpdate/restockPlan/addRestockPlanGoods.do";
    }

    //补货单-商品编辑
    public static String getRestockPlanGoodsEdit() {
        return ONLINE_URL + "shopUpdate/restockPlan/modifyRestockPlanGoods.do";
    }

    //查询商品销量信息
    public static String getRestockPlanGoodsSale() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryGoodsDetail.do";
    }

    //更换商品对应的供货商信息
    public static String getRestockSupplierEdit() {
        return ONLINE_URL + "shopUpdate/restockPlan/updateSupplier.do";
    }

    //补货单-预览
    public static String getRestockPlanPreview() {
        return ONLINE_URL + "shopUpdate/restockPlan/getPresentListByPlanId.do";
    }

    //添加供应商备注
    public static String getRestockSupplierRemarks() {
        return ONLINE_URL + "shopUpdate/restockPlan/updateRestockPlanSupplier.do";
    }

    //查询补货计划商品详情
    public static String getRestockPlanPreviewInfo() {
        return ONLINE_URL + "shopUpdate/restockPlan/getGoodsListBySupplierId.do";
    }
    /*****************************补货单end******************************/

    /*****************************购销单start******************************/

    //购销单列表
    public static String getGouXOrderList() {
        return ONLINE_URL + "shopUpdate/supBill/querySupBillList.do";
    }

    //购销单详情
    public static String getGouXOrderInfo() {
        return ONLINE_URL + "shopUpdate/supBill/querySupBillGoodsList.do";
    }

    //入库单个商品（核对）
    public static String getGouXGoodsCheck() {
        return ONLINE_URL + "shopUpdate/supBill/checkGoods.do";
    }

    //入库全部商品(核对全部、确认商品入库)
    public static String getGouXGoodsCheckAll() {
        return ONLINE_URL + "shopUpdate/supBill/storageAllGoods.do";
    }

    //撤销核对入库
    public static String getGouXGoodsCheckCancel() {
        return ONLINE_URL + "shopUpdate/supBill/cancelCheckGoods.do";
    }

    //添加付款凭证
    public static String getGouXPaymentAdd() {
        return ONLINE_URL + "shopUpdate/supBill/addPaymentOrder.do";
    }

    //查看付款凭证
    public static String getGouXPayment() {
        return ONLINE_URL + "shopUpdate/supBill/queryPayment.do";
    }

    /*****************************购销单end******************************/

    /*****************************供货商管理start******************************/

    //供货商分类新增
    public static String getSupplierCateAdd() {
        return ONLINE_URL + "shopUpdate/shopSupplier/addSupKind.do";
    }

    //供货商分类修改或删除
    public static String getSupplierCateEdit() {
        return ONLINE_URL + "shopUpdate/shopSupplier/modifySupKind.do";
    }

    //供货商分类排序
    public static String getSupplierCateSort() {
        return ONLINE_URL + "shopUpdate/shopSupplier/updateSupKindSort.do";
    }

    //供货商分类列表
    public static String getSupplierCateList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupKindList.do";
    }

    //供货商新增
    public static String getSupplierAdd() {
        return ONLINE_URL + "shopUpdate/shopSupplier/addsupInfo.do";
    }

    //供货商修改
    public static String getSupplierEdit() {
        return ONLINE_URL + "shopUpdate/shopSupplier/updateSupInfo.do";
    }

    //供货商删除
    public static String getSupplierDel() {
        return ONLINE_URL + "shopUpdate/shopSupplier/deleteSupInfo.do";
    }

    //供货商列表
    public static String getSupplierList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupList.do";
    }

    //供货商详情
    public static String getSupplierInfo() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupInfo.do";
    }

    //供货商申请列表
    public static String getSupplierApplyList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupExamineList.do";
    }

    //绑定供货商（确认通过）
    public static String getSupplierBind() {
        return ONLINE_URL + "shopUpdate/shopSupplier/bindSupplier.do";
    }

    //供货商详情-业务信息
    public static String getSupplierInfos() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupBusinessInfo.do";
    }

    //供货商详情-业务信息-所供商品
    public static String getSupplierInfoGoods() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupRecordGoodList.do";
    }

    //根据已建档商品ID和店铺编码删除已建档商品信息
    public static String getSupplierInfoGoodsDel() {
        return ONLINE_URL + "shopUpdate/shopSupplier/deleteShopSupGoodsEntity.do";
    }

    //供货商详情-业务信息-所动商品-建档并更新商品信息
    public static String getSupplierInfoGoodsSupRecord() {
        return ONLINE_URL + "shopUpdate/shopSupplier/updateSupRecordGood.do";
    }

    //供货商详情-购销订单
    public static String getSupplierInfoGouXList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupBillInfo.do";
    }

    //供货商详情-付款记录
    public static String getSupplierInfoPayment() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupPaymentInfo.do";
    }

    //供货商详情-付款记录-付款详情
    public static String getSupplierInfoPaymentInfo() {
        return ONLINE_URL + "shopUpdate/shopSupplier/queryQepaymentInfo.do";
    }

    //供货商详情-未还款购销单列表
    public static String getSupplierInfoUnpaidGouXList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/queryUnpaidBillList.do";
    }

    //供货商详情-还款
    public static String getSupplierInfoRepayment() {
        return ONLINE_URL + "shopUpdate/shopSupplier/repaymentBills.do";
    }
    /*****************************供货商管理end******************************/


    /*****************************店铺-货位管理start******************************/
    //货位列表
    public static String getLocationList() {
        return ONLINE_URL + "shopUpdate/shops/goods/position/list.do";
    }

    //货位新增
    public static String getLocationAdd() {
        return ONLINE_URL + "shopUpdate/shops/goods/position/add.do";
    }

    //货位编辑
    public static String getLocationEdit() {
        return ONLINE_URL + "shopUpdate/shops/goods/position/update.do";
    }

    //货位删除
    public static String getLocationDel() {
        return ONLINE_URL + "shopUpdate/shops/goods/position/delete.do";
    }

    //货位删除前校验
    public static String getLocationCheck() {
        return ONLINE_URL + "shopUpdate/shops/goods/position/check.do";
    }

    /*****************************店铺-货位管理end******************************/

    //根据key和语言查询多语言值
    public static String getLanguageValueByKey() {
        return ONLINE_URL + "shopUpdate/i18n/queryI18nByKey.do";
    }

    //根据语言查询多语言值
    public static String getLanguageValue() {
        return ONLINE_URL + "shopUpdate/i18n/queryI18n.do";
    }
}