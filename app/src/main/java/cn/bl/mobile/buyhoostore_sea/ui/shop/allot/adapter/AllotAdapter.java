package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;

/**
 * Describe:调拨单（适配器）
 * Created by jingang on 2023/5/19
 */
public class AllotAdapter extends BaseAdapter<AllotData> {

    public AllotAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_allot;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvStatus, tvBg, tvOperation;
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvBg = holder.getView(R.id.tvItemBg);
        tvOperation = holder.getView(R.id.tvItemOperation);
        //2.待发货（待调出） 3.待收货（待调入） 4.已完成
        switch (mDataList.get(position).getAllocationStatus()) {
            case 2:
                tvStatus.setText(getLanguageValue("beTransferOut"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("revoke") + getLanguageValue("transfer"));
                tvOperation.setBackgroundResource(R.drawable.shape_ce_kuang_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 3:
                tvStatus.setText(getLanguageValue("beTransferEnter"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_f7931e));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow016);
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("receipt"));
                tvOperation.setBackgroundResource(R.drawable.shape_jb_448df6_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvNo, tvOutTips, tvInTips, tvStatus, tvBg, tvInName, tvOutName, tvCount, tvName, tvTime, tvOperation;
        tvNo = holder.getView(R.id.tvItemNo);
        tvOutTips = holder.getView(R.id.tvItemOutTips);
        tvInTips = holder.getView(R.id.tvItemInTips);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvBg = holder.getView(R.id.tvItemBg);
        tvInName = holder.getView(R.id.tvItemInName);
        tvOutName = holder.getView(R.id.tvItemOutName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvName = holder.getView(R.id.tvItemName);
        tvTime = holder.getView(R.id.tvItemTime);
        tvOperation = holder.getView(R.id.tvItemOperation);
        RelativeLayout relGoods = holder.getView(R.id.relItemGoods);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);
        tvBg.setText(getLanguageValue("transfer") + getLanguageValue("to"));

        //本店判断
        String outId = String.valueOf(mDataList.get(position).getStorehouseOutId()),
                inId = String.valueOf(mDataList.get(position).getStorehouseInId());
        if (TextUtils.isEmpty(getShop_id()) || TextUtils.isEmpty(outId)) {
            tvOutTips.setText(getLanguageValue("transferOutStore"));
        } else {
            if (getShop_id().equals(outId)) {
                tvOutTips.setText(getLanguageValue("transferOutStore") + "(" + getLanguageValue("ourStore") + ")");
            } else {
                tvOutTips.setText(getLanguageValue("transferOutStore"));
            }
        }
        if (TextUtils.isEmpty(getShop_id()) || TextUtils.isEmpty(outId)) {
            tvInTips.setText(getLanguageValue("transferIntoStore"));
        } else {
            if (getShop_id().equals(inId)) {
                tvInTips.setText(getLanguageValue("transferIntoStore") + "(" + getLanguageValue("ourStore") + ")");
            } else {
                tvInTips.setText(getLanguageValue("transferIntoStore"));
            }
        }
        tvNo.setText(getLanguageValue("transfer") + getLanguageValue("orderNumber") + ":" + mDataList.get(position).getPurchaseListUnique());
        tvInName.setText(mDataList.get(position).getStorehouseInName());
        tvOutName.setText(mDataList.get(position).getStorehouseOutName());
        tvName.setText(getLanguageValue("apply") + getLanguageValue("people") + ":" + mDataList.get(position).getUserName());
        tvTime.setText(getLanguageValue("transfer") + getLanguageValue("time") + ":" + DateUtils.getDateToString(mDataList.get(position).getPurchaseListDate(), DateUtils.PATTERN_SECOND));
        //2.待发货（待调出） 3.待收货（待调入） 4.已完成
        switch (mDataList.get(position).getAllocationStatus()) {
            case 2:
                tvStatus.setText(getLanguageValue("beTransferOut"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("revoke") + getLanguageValue("transfer"));
                tvOperation.setBackgroundResource(R.drawable.shape_ce_kuang_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 3:
                tvStatus.setText(getLanguageValue("beTransferEnter"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_f7931e));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow016);
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("receipt"));
                tvOperation.setBackgroundResource(R.drawable.shape_jb_448df6_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
        }

        //商品列表
        if (mDataList.get(position).getDetailInfoList() != null) {
            if (mDataList.get(position).getDetailInfoList().size() > 0) {
                rvGoods.setVisibility(View.VISIBLE);
                rvGoods.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
                AllotGoodsAdapter adapter = new AllotGoodsAdapter(mContext);
                rvGoods.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getDetailInfoList());
                adapter.setOnItemClickListener((view, position1) -> {
                    if (listener != null) {
                        listener.onGoodsClick(view, position);
                    }
                });
                tvCount.setVisibility(View.VISIBLE);
                tvCount.setText(mDataList.get(position).getDetailInfoList().size() + getLanguageValue("numberTypes"));
            } else {
                rvGoods.setVisibility(View.GONE);
                tvCount.setVisibility(View.GONE);
            }
        } else {
            rvGoods.setVisibility(View.GONE);
            tvCount.setVisibility(View.GONE);
        }


        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            relGoods.setOnClickListener(v -> listener.onGoodsClick(v, position));
            tvOperation.setOnClickListener(v -> listener.onOperationClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onGoodsClick(View view, int position);

        void onOperationClick(View view, int position);
    }

    /**
     * 获取店铺编号
     *
     * @return
     */
    private String getShop_id() {
        return mContext.getSharedPreferences("shop", Context.MODE_PRIVATE).getString("shopId", "");
    }
}
