package cn.bl.mobile.buyhoostore_sea.ui;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.http.SslError;
import android.os.AsyncTask;
import android.util.Log;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;

import java.net.URLDecoder;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * web页
 */
@SuppressLint("NonConstantResourceId")
public class WebActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.webView)
    WebView webView;

    private String content_url, mHtml;

    public static final String CONSTANT_WEB_TITLE = "webTitle";
    public static final String CONSTANT_WEB_URL = "webUrl";
    public static final String CONSTANT_WEB_HTML = "webHtml";

    @Override
    protected int getLayoutId() {
        return R.layout.activity_web;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getIntent().getStringExtra(CONSTANT_WEB_TITLE));
        content_url = getIntent().getStringExtra(CONSTANT_WEB_URL);
        mHtml = getIntent().getStringExtra(CONSTANT_WEB_HTML);
        Log.i("gg", "web的地址--->" + content_url);
        new WebViewTask().execute();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    private class MyWebViewClient extends WebViewClient {

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            handler.proceed();
            super.onReceivedSslError(view, handler, error);
            Log.d("证书错误", "onReceivedSslError: "); //如果是证书问题，会打印出此条log到console
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.e("MessageView", url); // tel:13330983586
            String overUrl = URLDecoder.decode(url);// url.URLDecode;
            Log.e("urlcode", overUrl);
            if ((url != null && !url.equals("")) && overUrl.contains("tag=backapp")) {
                finish();
                return true;
            } else {
                return super.shouldOverrideUrlLoading(view, url);
            }
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);

        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode,
                                    String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
        }
    }

    private class WebViewTask extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        protected Boolean doInBackground(Void... param) {
            return false;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            WebSettings webSettings = webView.getSettings();
            webSettings.setDefaultTextEncodingName("UTF-8");
            webSettings.setJavaScriptEnabled(true);
            webSettings.setBuiltInZoomControls(true);
            webSettings.setDisplayZoomControls(false);//隐藏webview缩放比例
            webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
            webSettings.setUseWideViewPort(true);//适配手机
            webSettings.setLoadWithOverviewMode(true);//适配手机
            webView.setWebViewClient(new MyWebViewClient());
            if (content_url != null && !content_url.isEmpty()) {
                webView.loadUrl(content_url);
            } else {
                webView.loadData(mHtml, "text/html; charset=UTF-8", null);
            }
        }
    }

    public static void toWebActivity(Activity activity, String wTitle, String wUrl) {
        Intent intent = new Intent(activity, WebActivity.class);
        intent.putExtra(CONSTANT_WEB_TITLE, wTitle);
        intent.putExtra(CONSTANT_WEB_URL, wUrl);
        activity.startActivity(intent);
    }

    public static void toWebActivity2(Activity activity, String wTitle, String html) {
        Intent intent = new Intent(activity, WebActivity.class);
        intent.putExtra(CONSTANT_WEB_TITLE, wTitle);
        intent.putExtra(CONSTANT_WEB_HTML, html);
        activity.startActivity(intent);
    }

}
