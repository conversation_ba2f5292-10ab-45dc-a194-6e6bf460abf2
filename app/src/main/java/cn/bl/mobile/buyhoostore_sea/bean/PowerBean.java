package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/9/27 0027.
 */
public class PowerBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"powerPrice":2,"powerAdd":1,"powerCount":2,"powerSupplier":2,"powerPur":2,"powerKind":2,"staffName":"张三","powerChange":2,"powerInPrice":2,"powerName":1,"staffId":4,"powerDelete":1}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * powerPrice : 2
         * powerAdd : 1
         * powerCount : 2
         * powerSupplier : 2
         * powerPur : 2
         * powerKind : 2
         * staffName : 张三
         * powerChange : 2
         * powerInPrice : 2
         * powerName : 1
         * staffId : 4
         * powerDelete : 1
         */

        private String powerPrice;
        private String powerAdd;
        private String powerCount;
        private String powerSupplier;
        private String powerPur;
        private String powerKind;
        private String staffName;
        private String powerChange;
        private String powerInPrice;
        private String powerName;
        private String staffId;
        private String powerDelete;

        public String getPowerPrice() {
            return powerPrice;
        }

        public void setPowerPrice(String powerPrice) {
            this.powerPrice = powerPrice;
        }

        public String getPowerAdd() {
            return powerAdd;
        }

        public void setPowerAdd(String powerAdd) {
            this.powerAdd = powerAdd;
        }

        public String getPowerCount() {
            return powerCount;
        }

        public void setPowerCount(String powerCount) {
            this.powerCount = powerCount;
        }

        public String getPowerSupplier() {
            return powerSupplier;
        }

        public void setPowerSupplier(String powerSupplier) {
            this.powerSupplier = powerSupplier;
        }

        public String getPowerPur() {
            return powerPur;
        }

        public void setPowerPur(String powerPur) {
            this.powerPur = powerPur;
        }

        public String getPowerKind() {
            return powerKind;
        }

        public void setPowerKind(String powerKind) {
            this.powerKind = powerKind;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getPowerChange() {
            return powerChange;
        }

        public void setPowerChange(String powerChange) {
            this.powerChange = powerChange;
        }

        public String getPowerInPrice() {
            return powerInPrice;
        }

        public void setPowerInPrice(String powerInPrice) {
            this.powerInPrice = powerInPrice;
        }

        public String getPowerName() {
            return powerName;
        }

        public void setPowerName(String powerName) {
            this.powerName = powerName;
        }

        public String getStaffId() {
            return staffId;
        }

        public void setStaffId(String staffId) {
            this.staffId = staffId;
        }

        public String getPowerDelete() {
            return powerDelete;
        }

        public void setPowerDelete(String powerDelete) {
            this.powerDelete = powerDelete;
        }
    }
}
