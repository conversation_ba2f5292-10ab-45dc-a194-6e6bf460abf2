package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

import java.io.Serializable;

/**
 * Describe:商品单位（实体类）
 * Created by jingang on 2023/8/11
 */
public class GoodsUnitData implements Serializable {
    /**
     * shop_unique : 1689730585472
     * goods_unit : 个
     * count : 0
     * goods_unit_id : 11752
     */

    private String shop_unique;
    private String goods_unit;
    private String goods_unit_id;

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getGoods_unit() {
        return goods_unit;
    }

    public void setGoods_unit(String goods_unit) {
        this.goods_unit = goods_unit;
    }

    public String getGoods_unit_id() {
        return goods_unit_id;
    }

    public void setGoods_unit_id(String goods_unit_id) {
        this.goods_unit_id = goods_unit_id;
    }
}
