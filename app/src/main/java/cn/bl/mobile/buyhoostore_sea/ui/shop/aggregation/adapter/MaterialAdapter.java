package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.adapter;

import android.content.Context;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:聚合码-物料列表（适配器）
 * Created by jingang on 2023/5/12
 */
public class MaterialAdapter extends BaseAdapter<String> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public MaterialAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_material;
    }

    @Override
    public int getItemCount() {
        return 3;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if(onItemClickListener != null){
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v,position));
        }
    }
}
