package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsChangeRecordData;

/**
 * Describe:商品变更记录（适配器）
 * Created by jingang on 2023/8/23
 */
public class GoodsChangeRecordAdapter extends BaseAdapter<GoodsChangeRecordData> {

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    private String goodsName;

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public GoodsChangeRecordAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_change_record;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvUser, tvName, tvContent;
        tvUser = holder.getView(R.id.tvItemUser);
        tvName = holder.getView(R.id.tvItemName);
        tvContent = holder.getView(R.id.tvItemContent);

        if (TextUtils.isEmpty(mDataList.get(position).getUserName())) {
            if (TextUtils.isEmpty(mDataList.get(position).getCreateTime())) {
                tvUser.setText("");
            } else {
                tvUser.setText(mDataList.get(position).getCreateTime());
            }
        } else {
            if (TextUtils.isEmpty(mDataList.get(position).getCreateTime())) {
                tvUser.setText(mDataList.get(position).getUserName());
            } else {
                tvUser.setText(mDataList.get(position).getUserName() + " " + mDataList.get(position).getCreateTime());
            }
        }
        tvName.setText(getLanguageValue("change") + getLanguageValue("commodity") + ":" + goodsName + "(" + mDataList.get(position).getGoodsBarcode() + ")");
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsOperClass())) {
            tvContent.setText(getLanguageValue("change") + getLanguageValue("content") + ":" + getLanguageValue("none"));
        } else {
            tvContent.setText(getLanguageValue("change") + getLanguageValue("content") + ":" + mDataList.get(position).getGoodsOperClass());
        }
    }
}
