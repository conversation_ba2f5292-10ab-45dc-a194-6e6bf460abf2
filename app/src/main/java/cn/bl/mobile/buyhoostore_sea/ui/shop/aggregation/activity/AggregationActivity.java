package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.net.http.SslError;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.FileUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.statusbar.StatusBarUtil;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.utils.ZxingUtils;

/**
 * Describe:店铺-聚合码
 * Created by jingang on 2023/5/9
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationActivity extends BaseActivity {
    @BindView(R.id.fl0)
    FrameLayout fl0;
    @BindView(R.id.webView)
    WebView webView;
    @BindView(R.id.fl1)
    FrameLayout fl1;
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.tvLoad)
    TextView tvLoad;
    @BindView(R.id.ivQrcode)
    ImageView ivQrcode;
    @BindView(R.id.tvEditValue)
    TextView tvEditValue;
    @BindView(R.id.tvEdit)
    TextView tvEdit;
    @BindView(R.id.tvApply)
    TextView tvApply;

    private int status,//聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
            applyType;//申请人类型0未知1法人申请2非法人申请
    private String indexImage,//首页
            payImage,//聚合码
            guideImage,//授权书示例
            helibao,//合利宝授权书
            ruiyinxin;//瑞银信授权书
    private Bitmap bitmap;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_aggregation;
    }

    @Override
    public void initViews() {
        //设置状态栏
        StatusBarUtil.setLightStatusBar(this, false, true);
        getWindow().setNavigationBarColor(Color.parseColor("#ffffff"));
        status = getIntent().getIntExtra("status", 0);
        indexImage = getIntent().getStringExtra("indexImage");
        payImage = getIntent().getStringExtra("payImage");
        guideImage = getIntent().getStringExtra("guideImage");
        helibao = getIntent().getStringExtra("helibao");
        ruiyinxin = getIntent().getStringExtra("ruiyinxin");
        applyType = getIntent().getIntExtra("applyType", 0);
        setUI();
    }

    @OnClick({R.id.ivBack0,
            R.id.ivBack, R.id.tvLoad, R.id.tvEdit,
            R.id.tvApply})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack0:
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvLoad:
                //下载聚合码
                download();
                break;
            case R.id.tvEdit:
                //编辑资料信息
                if (applyType == 1) {
                    startActivityForResult(new Intent(this, AggregationApplyActivity0.class)
                                    .putExtra("status", status)
                            , Constants.CREDIT);
                } else {
                    startActivityForResult(new Intent(this, AggregationApplyActivity1.class)
                                    .putExtra("status", status)
                                    .putExtra("guideImage", getIntent().getStringExtra("guideImage"))
                                    .putExtra("helibao", getIntent().getStringExtra("helibao"))
                                    .putExtra("ruiyinxin", getIntent().getStringExtra("ruiyinxin"))
                            , Constants.CREDIT);
                }
                break;
            case R.id.tvApply:
                if (status == 0) {
                    //立即申请聚合码
                    startActivityForResult(new Intent(this, AggregationApplyTypeActivity.class)
                                    .putExtra("status", status)
                                    .putExtra("guideImage", guideImage)
                                    .putExtra("helibao", helibao)
                                    .putExtra("ruiyinxin", ruiyinxin)
                            , Constants.CREDIT);
                } else {
                    //申请线下收款码物料
                    goToActivity(MaterialActivity.class);
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("aggregateCode"));
        tvTips.setText(getLanguageValue("updateInfo"));
        tvLoad.setText(getLanguageValue("download")+getLanguageValue("aggregateCode"));
        tvEditValue.setText(getLanguageValue("beenDispalyed"));
        tvEdit.setText(getLanguageValue("editProfile"));
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (status == 0) {
            fl0.setVisibility(View.VISIBLE);
            fl1.setVisibility(View.GONE);
            tvApply.setText(getLanguageValue("applyOpening"));
            new WebViewTask().execute();
        } else {
            fl0.setVisibility(View.GONE);
            fl1.setVisibility(View.VISIBLE);
            tvApply.setText(getLanguageValue("applyOffline"));
            checkPermissions();
        }

    }

    private class MyWebViewClient extends WebViewClient {

        @SuppressLint("WebViewClientOnReceivedSslError")
        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            handler.proceed();
            super.onReceivedSslError(view, handler, error);
            Log.d("证书错误", "onReceivedSslError: "); //如果是证书问题，会打印出此条log到console
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            String overUrl = URLDecoder.decode(url);// url.URLDecode;
            Log.e("urlcode", overUrl);
            if ((url != null && !url.equals("")) && overUrl.contains("tag=backapp")) {
                finish();
                return true;

            } else {
                return super.shouldOverrideUrlLoading(view, url);
            }
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);

        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode,
                                    String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
        }
    }

    @SuppressLint("StaticFieldLeak")
    private class WebViewTask extends AsyncTask<Void, Void, Boolean> {
        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        protected Boolean doInBackground(Void... param) {
            return false;
        }

        @SuppressLint("SetJavaScriptEnabled")
        @Override
        protected void onPostExecute(Boolean result) {
            WebSettings webSettings = webView.getSettings();
            webSettings.setDefaultTextEncodingName("UTF-8");
            webSettings.setJavaScriptEnabled(true);
            webSettings.setBuiltInZoomControls(true);
            webSettings.setDisplayZoomControls(false);//隐藏webview缩放比例
            webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
            webSettings.setUseWideViewPort(true);//适配手机
            webSettings.setLoadWithOverviewMode(true);//适配手机
            webView.setWebViewClient(new MyWebViewClient());
            if (indexImage != null && !indexImage.isEmpty()) {
                webView.loadUrl(indexImage);
            }
        }
    }

    /**
     * 检查读写权限
     */
    private void checkPermissions() {
        if (PermissionUtils.checkPermissionsGroup(this, 5)) {
            getQrcode();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 5);
        }
    }

    /**
     * 生成二维码
     */
    private void getQrcode() {
        bitmap = ZxingUtils.createQRImage(payImage,
                DensityUtils.dip2px(this, 180),
                DensityUtils.dip2px(this, 180),
                null);
        ivQrcode.setImageBitmap(bitmap);
    }

    /**
     * 动态申请权限（下载文件）
     */
    private void download() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!Environment.isExternalStorageManager()) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivity(intent);
            }else{
                saveImageToGallery();
            }
        } else {
            if (PermissionUtils.checkPermissionsGroup(this, 3)) {
                saveImageToGallery();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION1, 3);
            }
        }
    }

    /**
     * bitmap保存到本地
     */
    public void saveImageToGallery() {
        if (bitmap == null) {
            showMessage(getLanguageValue("downloadFail"));
            return;
        }
        showDialog();

        String filePath = FileUtils.getPath(this);
//        String filePath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Download/";
        File appDir = new File(filePath);
        if (!appDir.exists()) {
            appDir.mkdirs();
        }

        String fileName = getLanguageValue("aggregateCode")+".jpg";
        File file = new File(appDir, fileName);
        try {
            FileOutputStream fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.flush();
            fos.close();
            Log.e(tag, "下载成功 = " + file.getAbsolutePath());
            showMessage(getLanguageValue("download")+getLanguageValue("succeed"));
            hideDialog();
            // 最后通知图库更新
            scanFile(file.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(tag, "下载失败 = " + e.getMessage());
            showMessage(getLanguageValue("downloadFail"));
            hideDialog();
        }
    }

    private MediaScannerConnection connection;

    /**
     * 扫描文件（更新相册）
     *
     * @param strRecordFile
     */
    private void scanFile(String strRecordFile) {
        connection = new MediaScannerConnection(this, new MediaScannerConnection.MediaScannerConnectionClient() {
            @Override
            public void onMediaScannerConnected() {
                connection.scanFile(strRecordFile, null);
            }

            @Override
            public void onScanCompleted(String path, Uri uri) {
                connection.disconnect();
            }
        });
        connection.connect();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    getQrcode();
                }
                break;
            case Constants.PERMISSION1:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    saveImageToGallery();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && resultCode == Constants.CREDIT) {
            this.finish();
        }
    }
}
