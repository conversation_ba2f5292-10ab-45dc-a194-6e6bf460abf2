package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class ReXiaoBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"picture_name":"upload/no_goodsB.jpg","goods_barcode":"","goods_name":"牛奶","saleCount":9999,"exit_status":-1,"proportion_no":"15%"},{"picture_name":"upload/no_goodsB.jpg","goods_barcode":"","goods_name":"牛奶","saleCount":9999,"exit_status":1,"proportion_no":"15%"}]
     */

    private int status;
    private String msg;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * picture_name : upload/no_goodsB.jpg
         * goods_barcode :
         * goods_name : 牛奶
         * saleCount : 9999
         * exit_status : -1
         * proportion_no : 15%
         */

        private String picture_name;
        private String goods_barcode;
        private String goods_name;
        private String saleCount;
        private int exit_status;
        private String proportion_no;

        public String getPicture_name() {
            return picture_name;
        }

        public void setPicture_name(String picture_name) {
            this.picture_name = picture_name;
        }

        public String getGoods_barcode() {
            return goods_barcode;
        }

        public void setGoods_barcode(String goods_barcode) {
            this.goods_barcode = goods_barcode;
        }

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public String getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(String saleCount) {
            this.saleCount = saleCount;
        }

        public int getExit_status() {
            return exit_status;
        }

        public void setExit_status(int exit_status) {
            this.exit_status = exit_status;
        }

        public String getProportion_no() {
            return proportion_no;
        }

        public void setProportion_no(String proportion_no) {
            this.proportion_no = proportion_no;
        }
    }
}