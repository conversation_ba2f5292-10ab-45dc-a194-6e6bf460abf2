package cn.bl.mobile.buyhoostore_sea.ui;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
 * Created by jingang on 2016/11/2.
 * 店铺 TabLayout
 */

public class SimpleFragmentPagerAdapter extends FragmentPagerAdapter {
    private List<Fragment> fragmentList;
    private String[] titles;

    public SimpleFragmentPagerAdapter(FragmentManager fm, List<Fragment> fragmentList, String[] titles) {
        super(fm);
        this.fragmentList = fragmentList;
        this.titles = titles;
    }

    @Override
    public Fragment getItem(int position) {
        return fragmentList.get(position);
    }

    @Override
    public int getCount() {
        return fragmentList.size();
    }


    @Override
    public CharSequence getPageTitle(int position) {
        return titles[position];
    }

    @Override
    public long getItemId(int position) {
        super.getItemId(position);
        if (fragmentList != null) {
            if ((position < fragmentList.size())) {
                return fragmentList.get(position).hashCode(); //important
            }
        }
        return super.getItemId(position);
    }
}
