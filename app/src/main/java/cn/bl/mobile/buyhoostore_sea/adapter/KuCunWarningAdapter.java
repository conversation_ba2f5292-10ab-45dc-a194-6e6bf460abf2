package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsKuCunBean;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:库存预警（）适配器
 * Created by jingang on 2022/12/17
 */
public class KuCunWarningAdapter extends BaseAdapter<GoodsKuCunBean> {
    public KuCunWarningAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_kucun_search;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView kc_name;
        TextView kc_xiaxian;
        TextView kc_shangxian;
        TextView kc_num;
        ImageView goods_img;
        TextView goods_barcode;

        kc_name = holder.getView(R.id.kc_name);
        kc_xiaxian = holder.getView(R.id.kc_xiaxian);
        kc_shangxian = holder.getView(R.id.kc_shangxian);
        kc_num = holder.getView(R.id.kc_num);
        goods_img = holder.getView(R.id.goods_img);
        goods_barcode = holder.getView(R.id.goods_barcode);

        kc_name.setText(mDataList.get(position).getGoods_name());
        String shop_xia = String.valueOf(mDataList.get(position).getOut_stock_waring_count());
        String shop_shang = String.valueOf(mDataList.get(position).getUnsalable_count());
        if (shop_xia.equals("-1")) {
            shop_xia = "-";
        }
        if (shop_shang.equals("-1")) {
            shop_shang = "-";
        }
        kc_xiaxian.setText(shop_xia);
        kc_shangxian.setText(shop_shang);
        kc_num.setText(mDataList.get(position).getGoods_count() + "");
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                .into(goods_img);
        goods_barcode.setText("(" + mDataList.get(position).getGoods_barcode() + ")");
    }
}
