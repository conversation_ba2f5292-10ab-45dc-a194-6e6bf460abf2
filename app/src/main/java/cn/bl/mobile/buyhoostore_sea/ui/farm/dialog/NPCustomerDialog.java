package cn.bl.mobile.buyhoostore_sea.ui.farm.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.google.gson.Gson;
import com.king.keyboard.KingKeyboard;
import com.lxj.xpopup.impl.FullScreenPopupView;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.NPAddCustomerActivity;
import cn.bl.mobile.buyhoostore_sea.utils.KeyBoardUtils;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
@SuppressLint("NonConstantResourceId")
public class NPCustomerDialog extends FullScreenPopupView {

    @BindView(R.id.tv_cancel)
    TextView cancel;
    @BindView(R.id.rv_customer)
    RecyclerView customerRecyclerView;
    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout listRefresh;

    @BindView(R.id.tvAdd)
    TextView addTV;
    @BindView(R.id.et_search)
    EditText searchET;
    String shopId;
    String staffId;
    @BindView(R.id.keyboardParent)
    LinearLayout keyboardParent;
    private MemberBean memberBean;

    private CustomerAdapter mCustomerAdapter;
    private int mPage = 1;
    private List<MemberBean.DataBean> dataList = new ArrayList<>();

    private OnDialogClickListener onDialogClickListener;
    /**
     * 会员编号
     */
    private String cusUnique;

    /**
     * 设置筛选模式
     * @param sift true为筛选模式 订单中心筛选选择客户使用
     */
    public void setSift(boolean sift) {
        isSift = sift;
    }

    /**
     * 选择的id
     * @param cusUnique
     */
    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    /**
     * 是否筛选用
     */
    private boolean isSift = false;

    public NPCustomerDialog(@NonNull Context context) {
        super(context);
    }
    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_np_customer;
    }
    @Override
    protected void onCreate() {
        super.onCreate();
        //初始化
        ButterKnife.bind(this);
        KingKeyboard kingKeyboard = new KingKeyboard((Activity) getContext(), keyboardParent);
        kingKeyboard.register(searchET,KingKeyboard.KeyboardType.NUMBER);
        addTV.setVisibility(isSift?GONE:VISIBLE);
        mCustomerAdapter = new CustomerAdapter(getContext(),cusUnique);
        customerRecyclerView.setAdapter(mCustomerAdapter);
        mCustomerAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (onDialogClickListener != null) {
                    close();
                    onDialogClickListener.onConfirm(dataList.get(position));
                }
            }
        });
        shopId = getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE)
                .getString("shopId", "0");
        staffId = getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE)
                .getString("staffId", "0");
        listRefresh.setOnLoadMoreListener(refreshLayout -> {
            mPage += 1;
            loadData();
        });
        listRefresh.setOnRefreshListener(refreshLayout -> {
            mPage = 1;
            loadData();
        });
        loadData();
        searchET.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView, int actionId, KeyEvent keyEvent) {
                if (actionId == EditorInfo.IME_ACTION_DONE || actionId == EditorInfo.IME_ACTION_SEARCH) {
                    mPage = 1;
                    loadData();
                }
                return true;
            }
        });
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }

    }
    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.REFRESH_MEMBER_LIST:
                mPage = 1;
                loadData();
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

    }
    private void close(){
        dismiss();
        KeyBoardUtils.closeKeyboard(searchET);
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }


    public void setonDialogClickListener(OnDialogClickListener dialogClickListener) {
        this.onDialogClickListener = dialogClickListener;
    }
    @OnClick({R.id.tv_cancel,R.id.tvAdd,R.id.rv_container})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_cancel:
            case R.id.rv_container:
                close();
                break;
            case R.id.tvAdd:
                getContext().startActivity(new Intent(getContext(), NPAddCustomerActivity.class));
                break;
            case R.id.ll_container:
                break;
        }
    }

    private void loadData() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique",shopId);
        params.put("searchKey", searchET.getText());
        params.put("pages", mPage);
        params.put("perpage", 10);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getRemember(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        listRefresh.finishRefresh();
                        listRefresh.finishLoadMore();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object;
                        int status = 2;
                        try {
                            object = new JSONObject(s);
                            status = object.getInt("status");

                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status == 1) {
                            Gson gson1 = new Gson();
                            memberBean = gson1.fromJson(s, MemberBean.class);
                            if (mPage == 1) {
                                dataList.clear();
                                MemberBean.DataBean tempBean = new MemberBean.DataBean();
                                if (!isSift) {
                                    tempBean.setCusName("匿名客户");
                                }else {
                                    tempBean.setCusName(String.format("全部客户: %s",memberBean.getShopMembership()));
                                }
                                dataList.add(tempBean);
                            }
                            dataList.addAll(memberBean.getData());
                            mCustomerAdapter.setNewInstance(dataList);
                            mCustomerAdapter.notifyDataSetChanged();
                        } else {
                            ToastUtil.showToast(getContext(),memberBean.getMsg());
                        }
                    }
                });
    }
    public static class CustomerAdapter extends BaseQuickAdapter<MemberBean.DataBean, BaseViewHolder> {
        private final Context mContext;
        public String mCusUnique;

        public CustomerAdapter(Context context,String cusUnique) {
            super(R.layout.item_np_customer);
            mContext = context;
            mCusUnique = cusUnique;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder baseViewHolder, MemberBean.DataBean bean) {
            baseViewHolder.setText(R.id.tv_name, bean.getCusName());
            baseViewHolder.setText(R.id.tv_phone, bean.getCusPhone());
            baseViewHolder.getView(R.id.tv_phone).setVisibility(TextUtils.isEmpty(bean.getCusPhone())? GONE: VISIBLE);
            ImageView ivCheck = baseViewHolder.getView(R.id.ivItemCheck);

            if ((mCusUnique == null && bean.getCus_unique() == null) || (null!=mCusUnique && mCusUnique.equals(bean.getCus_unique()))) {
                ivCheck.setVisibility(VISIBLE);
               ivCheck.setSelected(true);
           }else {
                ivCheck.setVisibility(GONE);
            }
        }

    }
    public interface OnDialogClickListener {

        void onConfirm(MemberBean.DataBean memberBean);

    }



}
