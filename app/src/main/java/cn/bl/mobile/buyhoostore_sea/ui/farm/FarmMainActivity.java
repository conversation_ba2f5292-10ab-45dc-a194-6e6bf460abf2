package cn.bl.mobile.buyhoostore_sea.ui.farm;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.fragment.FarmFormsFragment;
import cn.bl.mobile.buyhoostore_sea.ui.farm.fragment.FarmHomeFragment;
import cn.bl.mobile.buyhoostore_sea.ui.farm.fragment.FarmMeFragment;
import cn.bl.mobile.buyhoostore_sea.ui.farm.fragment.FarmMsgFragment;

/**
 * Describe:农批-主函数
 * Created by jingang on 2023/5/28
 */
@SuppressLint("NonConstantResourceId")
public class FarmMainActivity extends BaseActivity {
    @BindView(R.id.iv0)
    ImageView iv0;
    @BindView(R.id.tv0)
    TextView tv0;
    @BindView(R.id.iv1)
    ImageView iv1;
    @BindView(R.id.tv1)
    TextView tv1;
    @BindView(R.id.iv2)
    ImageView iv2;
    @BindView(R.id.tv2)
    TextView tv2;
    @BindView(R.id.iv3)
    ImageView iv3;
    @BindView(R.id.tv3)
    TextView tv3;

    private Fragment[] fragments;

    private int index = 0;//点击的页卡索引
    private int currentTabIndex = 0;//当前的页卡索引

    @Override
    protected int getLayoutId() {
        return R.layout.activity_main_farm;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        setFragment();
    }

    @OnClick({R.id.lin0, R.id.lin1, R.id.lin2, R.id.lin3})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.lin0:
                setStatusBar(false);
                index = 0;
                fragmentControl();
                break;
            case R.id.lin1:
                setStatusBar(true);
                index = 1;
                fragmentControl();
                break;
            case R.id.lin2:
                setStatusBar(false);
                index = 2;
                fragmentControl();
                break;
            case R.id.lin3:
                setStatusBar(false);
                index = 3;
                fragmentControl();
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        fragments = new Fragment[]{FarmHomeFragment.newInstance(),
                FarmFormsFragment.newInstance(),
                FarmMsgFragment.newInstance(),
                FarmMeFragment.newInstance()};
        setBottomColor();
        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                iv0.setImageResource(R.mipmap.ic_tab_home001);
                tv0.setTextColor(getResources().getColor(R.color.green));
                break;
            case 1:
                iv1.setImageResource(R.mipmap.ic_tab_forms001);
                tv1.setTextColor(getResources().getColor(R.color.green));
                break;
            case 2:
                iv2.setImageResource(R.mipmap.ic_tab_msg001);
                tv2.setTextColor(getResources().getColor(R.color.green));
                break;
            case 3:
                iv3.setImageResource(R.mipmap.ic_tab_me001);
                tv3.setTextColor(getResources().getColor(R.color.green));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                iv0.setImageResource(R.mipmap.ic_tab_home002);
                tv0.setTextColor(getResources().getColor(R.color.color_666));
                break;
            case 1:
                iv1.setImageResource(R.mipmap.ic_tab_forms002);
                tv1.setTextColor(getResources().getColor(R.color.color_666));
                break;
            case 2:
                iv2.setImageResource(R.mipmap.ic_tab_msg002);
                tv2.setTextColor(getResources().getColor(R.color.color_666));
                break;
            case 3:
                iv3.setImageResource(R.mipmap.ic_tab_me002);
                tv3.setTextColor(getResources().getColor(R.color.color_666));
                break;
            default:
                break;
        }
    }

}
