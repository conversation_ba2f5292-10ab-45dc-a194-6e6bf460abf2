package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.CombinationInfoDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.StatisticsTodayOrderInfoData;

/**
 * Describe:dialog（店铺-今日营业额-组合付详情）
 * Created by jingang on 2024/5/22
 */
@SuppressLint("NonConstantResourceId")
public class CombinationInfoDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.rvDialog)
    RecyclerView recyclerView;

    private CombinationInfoDialogAdapter mAdapter;
    private static List<StatisticsTodayOrderInfoData> dataList;

    public static void showDialog(Context context, List<StatisticsTodayOrderInfoData> list) {
        CombinationInfoDialog.dataList = list;
        CombinationInfoDialog dialog = new CombinationInfoDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.setCancelable(true);
        dialog.show();
    }

    public CombinationInfoDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_combination_info);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("combinatePaymentDetails"));
        setAdapter();
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @OnClick({R.id.ivDialogClose})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new CombinationInfoDialogAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        if (dataList != null) {
            mAdapter.setDataList(dataList);
        }
    }
}
