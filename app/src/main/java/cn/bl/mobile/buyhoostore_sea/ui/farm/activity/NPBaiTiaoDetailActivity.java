package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.app.AlertDialog;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.lxj.xpopup.XPopup;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
public class NPBaiTiaoDetailActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.tv_select_customer)
    TextView selectCustomerTV;
    @BindView(R.id.tv_qian_kuan_date)
    TextView qianKuanDateTV;
    @BindView(R.id.tv_qian_kuan_money)
    TextView qianKuanTV;
    @BindView(R.id.iv_photo)
    ImageView photoIV;
    @BindView(R.id.tv_remark)
    TextView remarkET;
    private MemberBean.DataBean mMemberBean;

    private AlertDialog.Builder photo_builder;
    private File mFile;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_bai_tiao_detail;
    }

    @Override
    public void initViews() {
        tvTitle.setText("白条详情");
    }

    private void submit() {



        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("cusPhone", remarkET.getText());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAddCusNP(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        ToastUtil.showToast(NPBaiTiaoDetailActivity.this, "添加成功");
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFRESH_MEMBER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @OnClick({R.id.zuofeiTV, R.id.ivBack, R.id.iv_photo, R.id.tvRight,R.id.editTV,R.id.backMoneyTV})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                goToActivity(NPBaiTiaoRecordActivity.class);
                break;
            case R.id.zuofeiTV:
                new XPopup.Builder(this).isDestroyOnDismiss(true).asConfirm("是否作废该条记录？", "作废后所记录数据不会计入统计", "取消", "确认作废", () -> {
                }, null, false).show();
                break;
            case R.id.editTV:
                Intent intent = new Intent(NPBaiTiaoDetailActivity.this,NPLuBaiTiaoActivity.class);
//                intent.putExtra("data", result);

                startActivity(intent);
                break;
            case R.id.backMoneyTV:

                break;
            case R.id.tv_select_customer: {
                break;
            }

            case R.id.iv_photo:

                break;

        }
    }




}
