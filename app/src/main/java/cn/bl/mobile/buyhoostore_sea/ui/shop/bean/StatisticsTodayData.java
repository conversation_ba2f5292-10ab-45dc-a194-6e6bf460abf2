package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:今日营业额（实体类）
 * Created by jingang on 2024/5/22
 */
public class StatisticsTodayData implements Serializable {
    /**
     * queryStatisticsStatis : {"saleListTotal":6.02,"saleListTotalMoney":6,"saleListRefundMoney":0.02,"saleListCount":2}
     * unlineStatis : {"queryStatisticsStatis":{"saleListTotal":6.02,"saleListTotalMoney":6,"saleListRefundMoney":0.02,"saleListCount":2},"list":[{"payMethod":3,"payMethodName":"微信","payMoney":6,"orderCount":1},{"payMethod":13,"payMethodName":"金圈支付","payMoney":0.02,"orderCount":1}]}
     * onlineStatis : {"queryStatisticsStatis":{"saleListTotal":0,"saleListTotalMoney":0,"saleListRefundMoney":0,"saleListCount":0},"list":[]}
     */

    private QueryStatisticsStatisBean queryStatisticsStatis;
    private UnlineStatisBean unlineStatis;
    private UnlineStatisBean onlineStatis;

    public QueryStatisticsStatisBean getQueryStatisticsStatis() {
        return queryStatisticsStatis;
    }

    public void setQueryStatisticsStatis(QueryStatisticsStatisBean queryStatisticsStatis) {
        this.queryStatisticsStatis = queryStatisticsStatis;
    }

    public UnlineStatisBean getUnlineStatis() {
        return unlineStatis;
    }

    public void setUnlineStatis(UnlineStatisBean unlineStatis) {
        this.unlineStatis = unlineStatis;
    }

    public UnlineStatisBean getOnlineStatis() {
        return onlineStatis;
    }

    public void setOnlineStatis(UnlineStatisBean onlineStatis) {
        this.onlineStatis = onlineStatis;
    }

    public static class QueryStatisticsStatisBean {
        /**
         * saleListTotal : 6.02
         * saleListTotalMoney : 6.0
         * saleListRefundMoney : 0.02
         * saleListCount : 2
         */

        private double saleListTotal;//总营业额
        private double saleListTotalMoney;//总收入
        private double saleListRefundMoney;//总退款
        private int saleListCount;//总订单数

        public double getSaleListTotal() {
            return saleListTotal;
        }

        public void setSaleListTotal(double saleListTotal) {
            this.saleListTotal = saleListTotal;
        }

        public double getSaleListTotalMoney() {
            return saleListTotalMoney;
        }

        public void setSaleListTotalMoney(double saleListTotalMoney) {
            this.saleListTotalMoney = saleListTotalMoney;
        }

        public double getSaleListRefundMoney() {
            return saleListRefundMoney;
        }

        public void setSaleListRefundMoney(double saleListRefundMoney) {
            this.saleListRefundMoney = saleListRefundMoney;
        }

        public int getSaleListCount() {
            return saleListCount;
        }

        public void setSaleListCount(int saleListCount) {
            this.saleListCount = saleListCount;
        }
    }

    public static class UnlineStatisBean {
        /**
         * queryStatisticsStatis : {"saleListTotal":6.02,"saleListTotalMoney":6,"saleListRefundMoney":0.02,"saleListCount":2}
         * list : [{"payMethod":3,"payMethodName":"微信","payMoney":6,"orderCount":1},{"payMethod":13,"payMethodName":"金圈支付","payMoney":0.02,"orderCount":1}]
         */

        private QueryStatisticsStatisBeanX queryStatisticsStatis;
        private List<ListBean> list;

        public QueryStatisticsStatisBeanX getQueryStatisticsStatis() {
            return queryStatisticsStatis;
        }

        public void setQueryStatisticsStatis(QueryStatisticsStatisBeanX queryStatisticsStatis) {
            this.queryStatisticsStatis = queryStatisticsStatis;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        public static class QueryStatisticsStatisBeanX {
            /**
             * saleListTotal : 6.02
             * saleListTotalMoney : 6.0
             * saleListRefundMoney : 0.02
             * saleListCount : 2
             */

            private double saleListTotal;
            private double saleListTotalMoney;
            private double saleListRefundMoney;
            private int saleListCount;

            public double getSaleListTotal() {
                return saleListTotal;
            }

            public void setSaleListTotal(double saleListTotal) {
                this.saleListTotal = saleListTotal;
            }

            public double getSaleListTotalMoney() {
                return saleListTotalMoney;
            }

            public void setSaleListTotalMoney(double saleListTotalMoney) {
                this.saleListTotalMoney = saleListTotalMoney;
            }

            public double getSaleListRefundMoney() {
                return saleListRefundMoney;
            }

            public void setSaleListRefundMoney(double saleListRefundMoney) {
                this.saleListRefundMoney = saleListRefundMoney;
            }

            public int getSaleListCount() {
                return saleListCount;
            }

            public void setSaleListCount(int saleListCount) {
                this.saleListCount = saleListCount;
            }
        }

        public static class ListBean {
            /**
             * payMethod : 3
             * payMethodName : 微信
             * payMoney : 6.0
             * orderCount : 1
             */

            private int payMethod;//收款方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 6.美团 7.饿了吗 8.混合支付 10.积分支付 11.百货豆支付 13.金圈支付 14.聚合码支付
            private String payMethodName;//收款方式名称
            private double payMoney;//收款金额
            private int orderCount;//数量
            private String payMethodIcon;//图标地址

            public int getPayMethod() {
                return payMethod;
            }

            public void setPayMethod(int payMethod) {
                this.payMethod = payMethod;
            }

            public String getPayMethodName() {
                return payMethodName;
            }

            public void setPayMethodName(String payMethodName) {
                this.payMethodName = payMethodName;
            }

            public double getPayMoney() {
                return payMoney;
            }

            public void setPayMoney(double payMoney) {
                this.payMoney = payMoney;
            }

            public int getOrderCount() {
                return orderCount;
            }

            public void setOrderCount(int orderCount) {
                this.orderCount = orderCount;
            }

            public String getPayMethodIcon() {
                return payMethodIcon;
            }

            public void setPayMethodIcon(String payMethodIcon) {
                this.payMethodIcon = payMethodIcon;
            }
        }
    }

}
