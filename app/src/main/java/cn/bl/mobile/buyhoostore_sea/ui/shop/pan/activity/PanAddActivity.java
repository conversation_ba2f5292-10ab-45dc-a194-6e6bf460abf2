package cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsQuickAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.location.LocationSelectActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.adapter.PanInfoGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.bean.PanGoodsData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.bean.PanInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.dialog.PanAddDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.dialog.PanGoodsDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:盘点-详情（编辑、添加）-（普通手机）
 * Created by jingang on 2023/6/25
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PanAddActivity extends BaseActivity {
    private PanAddActivity TAG = PanAddActivity.this;
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvEdit)
    TextView tvEdit;
    @BindView(R.id.tvDel)
    TextView tvDel;
    @BindView(R.id.tvUser)
    TextView tvUser;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.tvNo)
    TextView tvNo;
    @BindView(R.id.tvPanInfoValue)
    TextView tvPanInfoValue;
    @BindView(R.id.tvAdd)
    TextView tvAdd;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;
    @BindView(R.id.tvUserValue)
    TextView tvUserValue;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvPreview)
    TextView tvPreview;

    private String keyWords, id, name,
            taskId;//盘点任务id
    private int status;//1待提交2已盘点

    private PanInfoGoodsAdapter mAdapter;
    private List<PanGoodsData> dataList = new ArrayList<>();

    private SharedPreferences sp = null;
    private String powerAdd;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_pan_add;
    }

    @Override
    public void initViews() {
        id = getIntent().getStringExtra("id");
        status = getIntent().getIntExtra("status", 0);
        if (status == 2) {
            tvEdit.setVisibility(View.GONE);
            tvDel.setVisibility(View.GONE);
            tvAdd.setVisibility(View.GONE);
        } else {
            if (getStaffPosition().equals("3")) {
                tvEdit.setVisibility(View.VISIBLE);
                tvDel.setVisibility(View.VISIBLE);
            } else {
                tvEdit.setVisibility(View.GONE);
                tvDel.setVisibility(View.GONE);
            }
            tvAdd.setVisibility(View.VISIBLE);
        }
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        powerAdd = sp.getString("power_add", "0");

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getTaskInfo();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getTaskInfo();
    }

    @OnClick({R.id.ivBack, R.id.tvRight,
            R.id.ivClear, R.id.ivScan,
            R.id.tvEdit, R.id.tvDel,
            R.id.tvAdd,
            R.id.tvPreview})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                page = 1;
                getTaskInfo();
                break;
            case R.id.ivScan:
                //扫码
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SEARCH);
                break;
            case R.id.tvEdit:
                //修改
                PanAddDialog.showDialog(this, name, this::postTaskUpdateName);
                break;
            case R.id.tvDel:
                //删除
                IAlertDialog.showDialog(this,
                        getLanguageValue("confirmDeleteInventory") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postTaskDel(id);
                        });
                break;
            case R.id.tvAdd:
                //扫码
                String model = android.os.Build.MODEL;
                if (model.equals("95W Series") || model.equals("NLS-MT66")) {
                    startActivityForResult(new Intent(this, PanScanActivity.class)
                            , Constants.SCAN);
                } else {
                    startActivityForResult(new Intent(this, ScanActivity.class)
                            , Constants.SCAN);
                }
                break;
            case R.id.tvPreview:
                //预览
                startActivityForResult(new Intent(TAG, PanPreviewActivity.class)
                                .putExtra("id", id)
                                .putExtra("status", status),
                        Constants.PAN);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("stocktaking"));
        etSearch.setHint(getLanguageValue("search") + getLanguageValue("commodity") + getLanguageValue("name"));
        tvEdit.setText(getLanguageValue("modification"));
        tvDel.setText(getLanguageValue("delete"));
        tvPanInfoValue.setText(getLanguageValue("stocktaking") + getLanguageValue("detail"));
        tvAdd.setText(getLanguageValue("addTo") + getLanguageValue("stocktaking") + getLanguageValue("commodity"));
        tvNameValue.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        tvCountValue.setText(getLanguageValue("stocktaking") + getLanguageValue("quantity"));
        tvUserValue.setText(getLanguageValue("operation") + getLanguageValue("people"));
        tvPreview.setText(getLanguageValue("stocktaking") + getLanguageValue("order") + getLanguageValue("preview"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new PanInfoGoodsAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getTaskInfo();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getTaskInfo();
            }
        });
        mAdapter.setListener(new PanInfoGoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情(编辑)
                taskId = dataList.get(position).getTaskDetailId();
                PanGoodsData data = new PanGoodsData();
                data.setGoodsBarcode(dataList.get(position).getGoodsId());
                data.setGoodsName(dataList.get(position).getGoodsName());
                data.setGoodsPicturepath(dataList.get(position).getGoodsPicturepath());
                data.setInventoryCount(dataList.get(position).getInventoryCount());
                data.setGoodsUnit(dataList.get(position).getGoodsUnit());
                data.setBucketWeight(dataList.get(position).getBucketWeight());
                data.setRemarks(dataList.get(position).getRemarks());
                data.setGoodsPosition(dataList.get(position).getGoodsPosition());
                data.setCompletePositionName(dataList.get(position).getCompletePositionName());
                showDialogPanGoods(data);
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("deleteProduct") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postTaskInfoDel(dataList.get(position).getTaskDetailId(), position);
                        });
            }
        });
    }

    /**
     * @param data
     */
    private void setUI(PanInfoData data) {
        if (data == null) {
            return;
        }
        name = data.getTaskName();
        tvName.setText(name);
        tvUser.setText(getLanguageValue("create") + getLanguageValue("people") + ":" + data.getCreateUser());
        tvTime.setText(getLanguageValue("create") + getLanguageValue("date") + ":" + data.getCreateTime());
        tvNo.setText(getLanguageValue("stocktaking") + getLanguageValue("orderNumber") + ":" + data.getTaskNo());
        //商品列表
        if (page == 1) {
            smartRefreshLayout.finishRefresh();
            dataList.clear();
        } else {
            smartRefreshLayout.finishLoadMore();
        }
        dataList.addAll(data.getTaskDetailList());
        mAdapter.setStatus(status);
        if (dataList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            recyclerView.setVisibility(View.GONE);
            linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 判断是否已添加
     *
     * @return
     */
    private boolean isAdd(String code) {
        String ids = "";
        if (dataList.size() < 1) {
            return false;
        } else {
            for (int i = 0; i < dataList.size(); i++) {
                if (dataList.get(i).getGoodsId().equals(code)) {
                    ids = String.valueOf(dataList.get(i).getGoodsId());
                }
            }
            return !TextUtils.isEmpty(ids);
        }
    }

    /**
     * 盘点任务详情
     */
    private void getTaskInfo() {
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("searchKey", keyWords);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        map.put("taskId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskInfo(),
                map,
                PanInfoData.class,
                new RequestListener<PanInfoData>() {
                    @Override
                    public void success(PanInfoData panInfoData) {
                        setUI(panInfoData);
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 修改盘点单名称
     *
     * @param name
     */
    private void postTaskUpdateName(String name) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskName", name);
        map.put("taskId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskUpdateName(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        PanAddActivity.this.name = name;
                        tvName.setText(name);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("pan"));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除盘点任务
     *
     * @param id
     */
    private void postTaskDel(String id) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("pan"));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 保存盘点信息
     *
     * @param goodsBarcode
     * @param count        //盘点总数量
     * @param weight       //筐重量，无筐传0
     * @param remarks      //备注（货位）
     */
    private void postTaskInfoAdd(String goodsBarcode, double count, double weight, String remarks,
                                 String goodsPosition, String completePositionName) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskId", id);
        map.put("goodsBarcode", goodsBarcode);
        map.put("inventoryCountTotal", count);
        map.put("bucketWeight", weight);
//        map.put("remarks", remarks);
        map.put("goodsPosition", goodsPosition);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskInfoAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("pan"));
                        page = 1;
                        getTaskInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 修改盘点信息
     *
     * @param taskDetailId 任务详情编号（商品编号）
     * @param goodsBarcode
     * @param count        //盘点总数量
     * @param weight       //筐重量，无筐传0
     * @param remarks      //备注（货位）
     */
    private void postTaskInfoUpdate(String taskDetailId, String goodsBarcode, double count, double weight, String remarks,
                                    String goodsPosition, String completePositionName) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskId", id);
        map.put("taskDetailId", taskDetailId);
        map.put("goodsBarcode", goodsBarcode);
        map.put("inventoryCountTotal", count);
        map.put("bucketWeight", weight);
//        map.put("remarks", remarks);
        map.put("goodsPosition", goodsPosition);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskInfoUpdate(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("pan"));
                        page = 1;
                        getTaskInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除盘点信息
     *
     * @param taskDetailId
     * @param positon
     */
    private void postTaskInfoDel(String taskDetailId, int positon) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskId", id);
        map.put("taskDetailId", taskDetailId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskInfoDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(positon);
                        mAdapter.remove(positon);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 扫码查询商品
     */
    private void getSelectGoods(String goodsBarcode) {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getselectgoods(),
                params,
                GoodsData.class,
                new RequestListener<GoodsData>() {
                    @Override
                    public void success(GoodsData goodsData) {
                        hideDialog();
                        taskId = "";
                        PanGoodsData data = new PanGoodsData();
                        data.setGoodsBarcode(goodsData.getGoodsBarcode());
                        data.setGoodsName(goodsData.getGoodsName());
                        data.setGoodsPicturepath(goodsData.getGoodsPicturePath());
                        data.setInventoryCount(1);
                        data.setGoodsUnit(goodsData.getUnit());
                        data.setBucketWeight(0);
                        data.setRemarks("");
                        data.setGoodsPosition(goodsData.getGoodsPosition());
                        data.setCompletePositionName(goodsData.getCompletePositionName());
                        showDialogPanGoods(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
//                        showDialogGoodsNo(goodsBarcode);
                    }
                });
    }

    /**
     * @param data
     */
    private void showDialogPanGoods(PanGoodsData data) {
        PanGoodsDialog.showDialog(TAG,
                data,
                new PanGoodsDialog.MyListener() {
//                    @Override
//                    public void onClick(String goodsBarcode, double count, double weight, String remarks) {
//                        if (TextUtils.isEmpty(taskId)) {
//                            postTaskInfoAdd(goodsBarcode, count, weight, remarks);
//                        } else {
//                            postTaskInfoUpdate(taskId, goodsBarcode, count, weight, remarks);
//                        }
//                    }

                    @Override
                    public void onClick(String goodsBarcode, double count, double weight, String remarks, String goodsPosition, String completePositionName) {
                        if (TextUtils.isEmpty(taskId)) {
                            postTaskInfoAdd(goodsBarcode, count, weight, remarks, goodsPosition, completePositionName);
                        } else {
                            postTaskInfoUpdate(taskId, goodsBarcode, count, weight, remarks, goodsPosition, completePositionName);
                        }
                    }

                    @Override
                    public void onLocationClick() {
                        //选择货位
//                        startActivityForResult(new Intent(TAG, PanLocationActivity.class),
//                                Constants.CHOOSE_LOCATION);

                        startActivityForResult(new Intent(TAG, LocationSelectActivity.class)
                                        .putExtra("ids", data.getGoodsPosition())
                                        .putExtra("names", data.getCompletePositionName())
                                , Constants.CHOOSE_LOCATION
                        );
                    }

                    @Override
                    public void onBasketClick() {
                        //选择筐
                        startActivityForResult(new Intent(TAG, PanBasketActivity.class),
                                Constants.CHOOSE_BASKET);
                    }
                });
    }


    /**
     * dialog（未查询到商品信息）
     *
     * @param code
     */
    private void showDialogGoodsNo(String code) {
        View view = LayoutInflater.from(this).inflate(R.layout.dialog_goods_no, null);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setView(view);

        TextView tvCancel = view.findViewById(R.id.tvDialogCancel);
        TextView tvConfirm = view.findViewById(R.id.tvDialogConfirm);

        final AlertDialog dialog = builder.create();
        Window window = dialog.getWindow();
        window.setGravity(Gravity.CENTER);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        window.setBackgroundDrawableResource(android.R.color.transparent);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();

        tvCancel.setOnClickListener(v -> dialog.dismiss());
        tvConfirm.setOnClickListener(v -> {
            //添加商品
            if ("1".equals(powerAdd)) {
                //速录
                startActivity(new Intent(this, GoodsQuickAddActivity.class)
                        .putExtra("goodsBarcode", code)
                );
            } else {
                showMessage(getLanguageValue("noPermission"));
            }
            dialog.dismiss();
            finish();
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    //扫码
                    getSelectGoods(data.getStringExtra("result"));
                    break;
                case Constants.CHOOSE_LOCATION:
                    //选择货位
//                    PanGoodsDialog.setLocation(data.getStringExtra("name"));
                    String ids = data.getStringExtra("ids"),
                            names = data.getStringExtra("names");
                    PanGoodsDialog.setLocation(ids, names);
                    break;
                case Constants.CHOOSE_BASKET:
                    //选择筐
                    PanGoodsDialog.setBasket(data.getDoubleExtra("weight", 0));
                    break;
                case Constants.PAN:
                    status = 2;
                    tvEdit.setVisibility(View.GONE);
                    tvDel.setVisibility(View.GONE);
                    tvAdd.setVisibility(View.GONE);
                    page = 1;
                    getTaskInfo();
                    break;
                case Constants.SEARCH:
                    //扫码搜索
                    keyWords = data.getStringExtra("result");
                    etSearch.setText(keyWords);
                    page = 1;
                    getTaskInfo();
                    break;
            }
        }
    }
}
