package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

import java.io.Serializable;

/**
 * Describe:供货商（实体类）
 * Created by jingang on 2023/5/26
 */
public class SupplierData implements Serializable {
    /**
     * supplier_id : 1
     * supplier_name : 供货商名称
     * supplier_address : 供货商地址
     * supplier_phone : 电话
     * supplier_unique : 1223
     */

    private int supplier_id;
    private String supplier_name;
    private String supplier_address;
    private String supplier_phone;
    private String supplier_unique;
    private boolean check;

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public int getSupplier_id() {
        return supplier_id;
    }

    public void setSupplier_id(int supplier_id) {
        this.supplier_id = supplier_id;
    }

    public String getSupplier_name() {
        return supplier_name;
    }

    public void setSupplier_name(String supplier_name) {
        this.supplier_name = supplier_name;
    }

    public String getSupplier_address() {
        return supplier_address;
    }

    public void setSupplier_address(String supplier_address) {
        this.supplier_address = supplier_address;
    }

    public String getSupplier_phone() {
        return supplier_phone;
    }

    public void setSupplier_phone(String supplier_phone) {
        this.supplier_phone = supplier_phone;
    }

    public String getSupplier_unique() {
        return supplier_unique;
    }

    public void setSupplier_unique(String supplier_unique) {
        this.supplier_unique = supplier_unique;
    }
}
