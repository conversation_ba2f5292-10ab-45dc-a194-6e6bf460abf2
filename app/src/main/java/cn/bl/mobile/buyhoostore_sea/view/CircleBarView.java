package cn.bl.mobile.buyhoostore_sea.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;

import cn.bl.mobile.buyhoostore_sea.R;

/**
 * 自定义半圆进度条
 */
@SuppressLint("DrawAllocation")
public class CircleBarView extends View {

    private float progress = 100f; // 初始进度
    private int strokeWidth; // 圆弧宽度
    private Paint arcPaint; // 画圆弧的画笔

    public CircleBarView(Context context) {
        this(context, null);
    }

    public CircleBarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        strokeWidth = dpToPx(50); // 默认圆弧宽度10dp
        arcPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        arcPaint.setStyle(Paint.Style.STROKE);
        arcPaint.setStrokeWidth(strokeWidth);
    }

    // dp转px
    private int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, getResources().getDisplayMetrics());
    }

    // 设置进度
    public void setProgress(float progress) {
        this.progress = progress;
        invalidate(); // 请求重新绘制
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int centerX = getWidth() / 2;
        int centerY = getHeight() / 2;
        int radius = Math.min(centerX, centerY) - strokeWidth / 2; // 半径计算，确保圆弧不会超出边界

        RectF oval = new RectF(centerX - radius, centerY - radius, centerX + radius, centerY + radius);
        arcPaint.setColor(getResources().getColor(R.color.orange));

        // 绘制背景圆弧（可选）
        arcPaint.setStrokeWidth(strokeWidth);
        arcPaint.setStyle(Paint.Style.STROKE);
        canvas.drawArc(oval, -180, 180, false, arcPaint); // -180表示从9点钟方向开始绘制

        // 绘制进度圆弧
        arcPaint.setColor(getResources().getColor(R.color.blue));
        canvas.drawArc(oval, -180, progress * 180 / 100, false, arcPaint); // 根据进度绘制
    }
}