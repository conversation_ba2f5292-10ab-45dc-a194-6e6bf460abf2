package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.ToastUtils;

import org.json.JSONException;

import cn.bl.mobile.buyhoostore_sea.Constants;

/**
 * Describe:dialog基类
 * Created by jingang on 2023/5/31
 */
public class BaseDialog extends Dialog implements LifecycleObserver, LifecycleOwner {
    protected LifecycleRegistry lifecycleRegistry;
    private LoadingDialog dialog;
    public String tag = "Klip弹窗";
    private long currentTime;
    public int page = 1;

    public BaseDialog(@NonNull Context context, int style) {
        super(context, style);
        lifecycleRegistry = new LifecycleRegistry(this);
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    /**
     * 接口请求等待框
     */
    public void showDialog() {
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(getContext())
                    .setMessage(getLanguageValue("LoadingKey"))
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
    }

    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    /**
     * toast提示
     *
     * @param message
     */
    public void showMessage(String message) {
        ToastUtils.getInstance(getContext()).showMessage(message);
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 500) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 跳转到指定的activity
     *
     * @param activity
     * @param clazz    目标activity
     */
    public void goToActivity(Activity activity, Class clazz) {
        Intent intent = new Intent(activity, clazz);
        activity.startActivity(intent);
    }

    public String getManager_unique() {
        return getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("managerUnique", "");
    }

    public String getShop_id() {
        return getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("shopId", "");
    }

    public String getShopName() {
        return getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("shopName", "");
    }

    public String getStaff_id() {
        return getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("staffId", "");
    }

    public String getStaffPosition() {
        return getContext().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("staffPosition", "");
    }

    /**
     * 获取多语言值
     *
     * @param key
     * @return
     */
    public String getLanguageValue(String key) {
        String value;
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        if (BaseApplication.getLanguageValueJson() == null) {
            value = "";
        } else {
            try {
                value = BaseApplication.getLanguageValueJson().getString(key);
            } catch (JSONException e) {
                e.printStackTrace();
                value = "";
            }
        }
        return value;
    }
}
