package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;

/**
 * Describe:店铺-今日营业额-根据支付方式查订单-详情（实体类）
 * Created by jingang on 2024/5/22
 */
public class StatisticsTodayOrderInfoData implements Serializable {
    /**
     * payMethodName : 现金
     * payMethod : 1
     * payMoney : 6.18
     */

    private String payMethodName;
    private int payMethod;
    private double payMoney;
    private String payMethodIcon;

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public int getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(int payMethod) {
        this.payMethod = payMethod;
    }

    public double getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(double payMoney) {
        this.payMoney = payMoney;
    }

    public String getPayMethodIcon() {
        return payMethodIcon;
    }

    public void setPayMethodIcon(String payMethodIcon) {
        this.payMethodIcon = payMethodIcon;
    }
}
