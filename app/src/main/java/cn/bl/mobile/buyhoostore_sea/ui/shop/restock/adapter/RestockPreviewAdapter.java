package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPreviewData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:补货单-预览（适配器）
 * Created by jingang on 2023/9/6
 */
public class RestockPreviewAdapter extends BaseAdapter<RestockPreviewData> {

    public RestockPreviewAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_preview;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvCount, tvTotal, tvRemarksValue, tvTotalValue;
        tvName = holder.getView(R.id.tvItemName);
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        ImageView ivKefu = holder.getView(R.id.ivItemKefu);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);
        tvCount = holder.getView(R.id.tvItemCount);
        EditText etRemarks = holder.getView(R.id.etItemRemarks);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvRemarksValue = holder.getView(R.id.tvItemRemarksValue);
        tvTotalValue = holder.getView(R.id.tvItemTotalValue);
        tvRemarksValue.setText(getLanguageValue("remark") + ":");
        etRemarks.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("remark"));
        tvTotalValue.setText(getLanguageValue("anticipated") + getLanguageValue("price"));

        tvName.setText(mDataList.get(position).getSupplierName());
        //1.购销 2.自采（本地）
        if (mDataList.get(position).getPurchaseType() == 2) {
            tvName.setText(mDataList.get(position).getSupplierName() + "("+getLanguageValue("local")+")");
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(mDataList.get(position).getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
        tvCount.setText(getLanguageValue("aTotalOf")+ mDataList.get(position).getGoodsCounts() + getLanguageValue("numberTypes"));
        if (TextUtils.isEmpty(mDataList.get(position).getRemark())) {
            etRemarks.setText("");
        } else {
            etRemarks.setText(mDataList.get(position).getRemark());
        }
        tvTotal.setText("RM" + DFUtils.getNum2(mDataList.get(position).getGoodsTotal()));
        if (mDataList.get(position).getGoodsList().size() > 0) {
            rvGoods.setVisibility(View.VISIBLE);
            rvGoods.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
            RestockPreviewGoodsAdapter adapter = new RestockPreviewGoodsAdapter(mContext);
            rvGoods.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getGoodsList());
            adapter.setOnItemClickListener((view, position1) -> {
                if (listener != null) {
                    listener.onItemClick(view, position);
                }
            });
        } else {
            rvGoods.setVisibility(View.GONE);
        }

        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                mDataList.get(position).setRemark(str);
                if (listener != null) {
                    listener.onRemarks(str, position);
                }
            }
        };
        etRemarks.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcher);
            } else {
                mV.removeTextChangedListener(watcher);
            }
        });
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivKefu.setOnClickListener(v -> listener.onKefuClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onKefuClick(View view, int position);

        void onRemarks(String remarks, int position);
    }

}
