package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.adapter.GouXListGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:供货商详情-购销单列表（适配器）
 * Created by jingang on 2023/9/21
 */
public class SupplierGouXAdapter extends BaseAdapter<GouXOrderListData> {
    private int type;//0.查看 1.选择

    public void setType(int type) {
        this.type = type;
    }

    public SupplierGouXAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goux_supplier;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivCheck = holder.getView(R.id.ivItemCheck);
        if (type == 1) {
            ivCheck.setVisibility(View.VISIBLE);
            if (mDataList.get(position).isCheck()) {
                ivCheck.setImageResource(R.mipmap.ic_chosen001);
            } else {
                ivCheck.setImageResource(R.mipmap.ic_chose001);
            }
        } else {
            ivCheck.setVisibility(View.GONE);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvStatus, tvCount, tvTotal,tvTotalValue;
        tvName = holder.getView(R.id.tvItemName);
        ImageView ivCheck, ivKefu;
        ivCheck = holder.getView(R.id.ivItemCheck);
        ivKefu = holder.getView(R.id.ivItemKefu);
        tvStatus = holder.getView(R.id.tvItemStatus);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvTotalValue = holder.getView(R.id.tvTotalValue);
        tvTotalValue.setText(getLanguageValue("totalPrice"));

        if (type == 1) {
            ivCheck.setVisibility(View.VISIBLE);
            if (mDataList.get(position).isCheck()) {
                ivCheck.setImageResource(R.mipmap.ic_chosen001);
            } else {
                ivCheck.setImageResource(R.mipmap.ic_chose001);
            }
        } else {
            ivCheck.setVisibility(View.GONE);
        }

        tvName.setText(mDataList.get(position).getSupplierName());
        tvCount.setText(getLanguageValue("aTotalOf") + DFUtils.getNum4(mDataList.get(position).getGoodsCategory()) + getLanguageValue("numberTypes"));
        tvTotal.setText("RM" + DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getLanguageValue("beWarehoused"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 2:
                tvStatus.setText(getLanguageValue("toBePaid"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 3:
                tvStatus.setText(getLanguageValue("toBeConfirmed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 5:
                tvStatus.setText(getLanguageValue("voided"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            default:
                tvStatus.setText("");
                break;
        }
        if (mDataList.get(position).getGoodsList() == null) {
            rvGoods.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getGoodsList().size() > 0) {
                rvGoods.setVisibility(View.VISIBLE);
                rvGoods.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
                GouXListGoodsAdapter adapter = new GouXListGoodsAdapter(mContext);
                rvGoods.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getGoodsList());
                adapter.setOnItemClickListener((view, position1) -> {
                    if (listener != null) {
                        listener.onItemClick(view, position);
                    }
                });
            } else {
                rvGoods.setVisibility(View.GONE);
            }
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivCheck.setOnClickListener(v -> listener.onCheckClick(v, position));
            ivKefu.setOnClickListener(v -> listener.onKefuClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onCheckClick(View view, int position);

        void onKefuClick(View view, int position);
    }
}
