package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:补货单--预览-供货商-详情
 * Created by jingang on 2023/9/8
 */
public class RestockSupplierInfoData implements Serializable {
    /**
     * supplierId : 1210
     * supplierName : 东方红
     * supplierPhone : 11011011011
     * shopUnique : 1536215939565
     * restockPlanId : 19
     * supplierAddress : 山东省临沂市兰山区兰山街道涑河北街蓝湾国际A区
     * companyLeagl : 红太阳
     * goodsAllTotal : 326.25
     * goodsTypeCount : 1
     * remark :
     * goodsList : [{"shopRestockplanGoodsId":12,"goodsPicturepath":"","goodsName":"长城干红葡萄酒","goodsBarcode":"16901009906076","goodsCount":3,"goodsInPrice":108.75,"goodsUnit":"","goodsTotal":326.25}]
     */

    private int supplierId;
    private String supplierName;
    private String supplierPhone;
    private String shopUnique;
    private int restockPlanId;
    private String supplierAddress;
    private String companyLeagl;
    private double goodsAllTotal;
    private int goodsTypeCount;
    private String remark;
    private List<GoodsListBean> goodsList;

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public int getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(int restockPlanId) {
        this.restockPlanId = restockPlanId;
    }

    public String getSupplierAddress() {
        return supplierAddress;
    }

    public void setSupplierAddress(String supplierAddress) {
        this.supplierAddress = supplierAddress;
    }

    public String getCompanyLeagl() {
        return companyLeagl;
    }

    public void setCompanyLeagl(String companyLeagl) {
        this.companyLeagl = companyLeagl;
    }

    public double getGoodsAllTotal() {
        return goodsAllTotal;
    }

    public void setGoodsAllTotal(double goodsAllTotal) {
        this.goodsAllTotal = goodsAllTotal;
    }

    public int getGoodsTypeCount() {
        return goodsTypeCount;
    }

    public void setGoodsTypeCount(int goodsTypeCount) {
        this.goodsTypeCount = goodsTypeCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public static class GoodsListBean {
        /**
         * shopRestockplanGoodsId : 12
         * goodsPicturepath :
         * goodsName : 长城干红葡萄酒
         * goodsBarcode : 16901009906076
         * goodsCount : 3.0
         * goodsInPrice : 108.75
         * goodsUnit :
         * goodsTotal : 326.25
         */

        private int shopRestockplanGoodsId;
        private String goodsPicturepath;
        private String goodsName;
        private String goodsBarcode;
        private double goodsCount;
        private double goodsInPrice;
        private String goodsUnit;
        private double goodsTotal;

        public int getShopRestockplanGoodsId() {
            return shopRestockplanGoodsId;
        }

        public void setShopRestockplanGoodsId(int shopRestockplanGoodsId) {
            this.shopRestockplanGoodsId = shopRestockplanGoodsId;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public double getGoodsInPrice() {
            return goodsInPrice;
        }

        public void setGoodsInPrice(double goodsInPrice) {
            this.goodsInPrice = goodsInPrice;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public double getGoodsTotal() {
            return goodsTotal;
        }

        public void setGoodsTotal(double goodsTotal) {
            this.goodsTotal = goodsTotal;
        }
    }
}
