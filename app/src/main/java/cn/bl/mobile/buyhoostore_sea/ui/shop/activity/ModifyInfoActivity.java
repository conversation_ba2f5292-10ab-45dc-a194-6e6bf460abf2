package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 店铺-个人信息-修改名称
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ModifyInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String name, staffAccount;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_shop_name;
    }

    @Override
    public void initViews() {
        name = getIntent().getStringExtra("name");
        etName.setText(name);
        staffAccount = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString("staffAccount", "");
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //确认
                name = etName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("individual") + getLanguageValue("name"));
                    return;
                }
                postStaffInfo();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("modification") + getLanguageValue("individual") + getLanguageValue("name"));
        etName.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("individual") + getLanguageValue("name"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    /**
     * 个人信息更新
     */
    private void postStaffInfo() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("staffAccount", staffAccount);
        map.put("staffName", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getTIME_gerenTWO(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        setResult(Constants.ADD, new Intent()
                                .putExtra("name", name)
                        );
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}