package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（商品分类更多操作：一级、二级分类）
 * Created by jingang on 2025/5/22
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsCateMoreDialog extends BaseDialog {
    @BindView(R.id.linDialogType)
    LinearLayout linType;
    @BindView(R.id.tvDialogType)
    TextView tvType;
    @BindView(R.id.linDialogAdd)
    LinearLayout linAdd;
    @BindView(R.id.tvDialogAdd)
    TextView tvAdd;
    @BindView(R.id.linDialogRename)
    LinearLayout linRename;
    @BindView(R.id.tvDialogRename)
    TextView tvRename;
    @BindView(R.id.linDialogDel)
    LinearLayout linDel;
    @BindView(R.id.tvDialogDel)
    TextView tvDel;
    @BindView(R.id.tvDialogEnable)
    TextView tvEnable;
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;

    private static int level,//0.一级 1.二级 2.三级
            edit,//是否可编辑 1.不可编辑 2.可编辑
            enable;//1.启用 2.禁用

    public static void showDialog(Context context, int level, int edit, int enable, MyListener listener) {
        GoodsCateMoreDialog.listener = listener;
        GoodsCateMoreDialog.level = level;
        GoodsCateMoreDialog.edit = edit;
        GoodsCateMoreDialog.enable = enable;
        GoodsCateMoreDialog dialog = new GoodsCateMoreDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public GoodsCateMoreDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_goods_cate_more);
        ButterKnife.bind(this);
        tvType.setText(getLanguageValue("system") + getLanguageValue("default") + getLanguageValue("classification"));
        tvAdd.setText(getLanguageValue("add") + getLanguageValue("sonClass"));
        tvRename.setText(getLanguageValue("editor"));
        tvDel.setText(getLanguageValue("delete"));
        if (enable == 1) {
            tvEnable.setText(getLanguageValue("deactivate"));
        } else {
            tvEnable.setText(getLanguageValue("enable"));
        }
        tvCancel.setText(getLanguageValue("cancel"));
        if (edit == 2) {
            linType.setVisibility(View.GONE);
            if (level == 2 || enable == 2) {
                linAdd.setVisibility(View.GONE);
            } else {
                linAdd.setVisibility(View.VISIBLE);
            }
            linRename.setVisibility(View.VISIBLE);
            linDel.setVisibility(View.VISIBLE);
        } else {
            linType.setVisibility(View.VISIBLE);
            linAdd.setVisibility(View.GONE);
            linRename.setVisibility(View.GONE);
            linDel.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.linDialogAdd, R.id.linDialogRename, R.id.linDialogDel, R.id.tvDialogEnable, R.id.tvDialogCancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linDialogAdd:
                //新增
                if (listener != null) {
                    listener.onAddClick();
                    dismiss();
                }
                break;
            case R.id.linDialogRename:
                //重命名
                if (listener != null) {
                    listener.onRenameClick();
                    dismiss();
                }
                break;
            case R.id.linDialogDel:
                //删除
                if (listener != null) {
                    listener.onDelClick();
                    dismiss();
                }
                break;
            case R.id.tvDialogEnable:
                //停用
                if (listener != null) {
                    listener.onEnableClick();
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        void onAddClick();

        void onRenameClick();

        void onDelClick();

        void onEnableClick();
    }
}
