package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

/**
 * Describe:
 * Created by jingang on 2023/5/26
 */
public class FarmOrderSubmitGoodsData {
    private String goodsBarcode;
    private double goodsCount;
    private double goodsPrice;
    private String goodsName;
    private String chengType;
    private String saleType;
    private String unit;

    public FarmOrderSubmitGoodsData(String goodsBarcode, double goodsCount, double goodsPrice, String goodsName, String chengType, String saleType, String unit) {
        this.goodsBarcode = goodsBarcode;
        this.goodsCount = goodsCount;
        this.goodsPrice = goodsPrice;
        this.goodsName = goodsName;
        this.chengType = chengType;
        this.saleType = saleType;
        this.unit = unit;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(double goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getChengType() {
        return chengType;
    }

    public void setChengType(String chengType) {
        this.chengType = chengType;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
