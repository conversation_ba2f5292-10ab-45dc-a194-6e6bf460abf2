package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;

/**
 * Describe:供货商申请列表（适配器）
 * Created by jingang on 2023/10/12
 */
public class SupplierApplyAdapter extends BaseAdapter<SupplierData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public SupplierApplyAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_apply;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvConfirm, tvUser;
        tvName = holder.getView(R.id.tvItemName);
        tvConfirm = holder.getView(R.id.tvItemConfirm);
        tvUser = holder.getView(R.id.tvItemUser);
        tvConfirm.setText(getLanguageValue("confirm")+getLanguageValue("through"));

        tvName.setText(mDataList.get(position).getSupplierName());
        tvUser.setText(mDataList.get(position).getContacts() + " " + mDataList.get(position).getContactMobile());

        if (onItemClickListener != null) {
            tvConfirm.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
