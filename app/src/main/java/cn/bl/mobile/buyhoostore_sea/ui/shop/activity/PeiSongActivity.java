package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-店铺设置-配送范围
 */
@SuppressLint("NonConstantResourceId")
public class PeiSongActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etRange)
    EditText etRange;
    @BindView(R.id.ivClear)
    ImageView ivClear;

    private int range;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_modify_info;
    }

    @Override
    public void initViews() {
        tvTitle.setText("配送范围");
        etRange.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString())) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @Override
    public void initData() {
        getdianpu();
    }

    @OnClick({R.id.ivBack, R.id.ivClear, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //请输输入
                etRange.setText("");
                break;
            case R.id.tvConfirm:
                //确认
                if (TextUtils.isEmpty(etRange.getText().toString().trim())) {
                    showMessage("请输入配送范围");
                    return;
                }
                range = Integer.parseInt(etRange.getText().toString().trim());
                if (range == 0) {
                    showMessage("请输入配送范围");
                    return;
                }
                if (String.valueOf(range).length() > 10) {
                    showMessage("输入位数超限！");
                    return;
                }
                setdistance();
                break;
        }
    }

    /**
     * 设置配送距离的接口
     */
    public void setdistance() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getString(R.string.promptcontent));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("distributionScope", range);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateInfoUrlTWO(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage("修改成功");
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    public void getdianpu() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getString(R.string.promptcontent));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getShopInfoUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "店铺信息 = " + s);
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int statusInfo = 2;
                        JSONObject objectInfo;
                        JSONObject data = null;
                        try {
                            objectInfo = new JSONObject(s);
                            statusInfo = objectInfo.getInt("status");
                            data = objectInfo.getJSONObject("data");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (statusInfo == 1) {
                            try {
                                String dis_far = data.getString("distributionScope");
                                etRange.setText(dis_far);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                });
    }
}
