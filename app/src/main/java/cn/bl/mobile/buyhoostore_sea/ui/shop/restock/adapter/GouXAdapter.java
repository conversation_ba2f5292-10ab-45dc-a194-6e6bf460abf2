package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.adapter.GouXListGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:购销单（适配器）
 * Created by jingang on 2023/9/6
 */
public class GouXAdapter extends BaseAdapter<GouXOrderListData> {

    public GouXAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goux;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvStatus, tvConfirm, tvRepayment, tvVoucher,tv;
        tvStatus = holder.getView(R.id.tvItemStatus);
        View vLine = holder.getView(R.id.vItem);
        tvConfirm = holder.getView(R.id.tvItemConfirm);
        tvRepayment = holder.getView(R.id.tvItemRepayment);
        tvVoucher = holder.getView(R.id.tvItemVoucher);
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废，6-异常
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getLanguageValue("beWarehoused"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.VISIBLE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 2:
                tvStatus.setText(getLanguageValue("toBePaid"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.VISIBLE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 3:
                tvStatus.setText(getLanguageValue("toBeConfirmed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.VISIBLE);
                break;
            case 5:
                tvStatus.setText(getLanguageValue("voided"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvStatus, tvTime, tvCount, tvTotal, tvConfirm, tvRepayment, tvVoucher,tvTotalValue;
        tvName = holder.getView(R.id.tvItemName);
        ImageView ivKefu = holder.getView(R.id.ivItemKefu);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvTime = holder.getView(R.id.tvItemTime);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        View vLine = holder.getView(R.id.vItem);
        tvConfirm = holder.getView(R.id.tvItemConfirm);
        tvRepayment = holder.getView(R.id.tvItemRepayment);
        tvVoucher = holder.getView(R.id.tvItemVoucher);
        tvTotalValue = holder.getView(R.id.tvItemTotalValue);
        tvTotalValue.setText(getLanguageValue("procurement")+getLanguageValue("amount"));
        tvConfirm.setText(getLanguageValue("confirm")+getLanguageValue("commodity")+getLanguageValue("warehousing"));
        tvRepayment.setText(getLanguageValue("repayment"));
        tvVoucher.setText(getLanguageValue("voucherPayment"));

        tvName.setText(mDataList.get(position).getSupplierName());
        if (TextUtils.isEmpty(mDataList.get(position).getCreateTime())) {
            tvTime.setVisibility(View.GONE);
        } else {
            tvTime.setVisibility(View.VISIBLE);
            tvTime.setText(mDataList.get(position).getCreateTime());
        }
        tvCount.setText(getLanguageValue("aTotalOf") + DFUtils.getNum4(mDataList.get(position).getGoodsCategory()) + getLanguageValue("numberTypes"));
        tvTotal.setText("RM" + DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getLanguageValue("beWarehoused"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.VISIBLE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 2:
                tvStatus.setText(getLanguageValue("toBePaid"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.VISIBLE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 3:
                tvStatus.setText(getLanguageValue("toBeConfirmed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                vLine.setVisibility(View.VISIBLE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.VISIBLE);
                break;
            case 5:
                tvStatus.setText(getLanguageValue("voided"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                vLine.setVisibility(View.GONE);
                tvConfirm.setVisibility(View.GONE);
                tvRepayment.setVisibility(View.GONE);
                tvVoucher.setVisibility(View.GONE);
                break;
        }
        if (mDataList.get(position).getGoodsList() == null) {
            rvGoods.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getGoodsList().size() > 0) {
                rvGoods.setVisibility(View.VISIBLE);
                rvGoods.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
                GouXListGoodsAdapter adapter = new GouXListGoodsAdapter(mContext);
                rvGoods.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getGoodsList());
                adapter.setOnItemClickListener((view, position1) -> {
                    if (listener != null) {
                        listener.onItemClick(view, position);
                    }
                });
            } else {
                rvGoods.setVisibility(View.GONE);
            }
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivKefu.setOnClickListener(v -> listener.onKefuClick(v, position));
            tvConfirm.setOnClickListener(v -> listener.onConfirmClick(v, position));
            tvRepayment.setOnClickListener(v -> listener.onPaymentClick(v, position));
            tvVoucher.setOnClickListener(v -> listener.onVoucherClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onKefuClick(View view, int position);

        void onConfirmClick(View view, int position);

        void onPaymentClick(View view, int position);

        void onVoucherClick(View view, int position);
    }
}
