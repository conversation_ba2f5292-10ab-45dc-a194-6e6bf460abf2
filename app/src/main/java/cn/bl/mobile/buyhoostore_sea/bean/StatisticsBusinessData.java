package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Describe:经营统计-经营总览（实体类）
 * Created by jingang on 2024/11/23
 */
public class StatisticsBusinessData {
    /**
     * date : 2024-11-23
     * unitPrice : 26415.19
     * unitPre : 1605.69
     * saleSum : 9007580.89
     * listCount : 341
     * listPre : 115.82
     * salePre : 3581.26
     */

    private String date;
    private double unitPrice;//客单价
    private double unitPre;//客单价同比
    private double saleSum;//销售额
    private int listCount;//订单量
    private double listPre;//订单量同比
    private double salePre;//销售额同比

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getUnitPre() {
        return unitPre;
    }

    public void setUnitPre(double unitPre) {
        this.unitPre = unitPre;
    }

    public double getSaleSum() {
        return saleSum;
    }

    public void setSaleSum(double saleSum) {
        this.saleSum = saleSum;
    }

    public int getListCount() {
        return listCount;
    }

    public void setListCount(int listCount) {
        this.listCount = listCount;
    }

    public double getListPre() {
        return listPre;
    }

    public void setListPre(double listPre) {
        this.listPre = listPre;
    }

    public double getSalePre() {
        return salePre;
    }

    public void setSalePre(double salePre) {
        this.salePre = salePre;
    }
}
