package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/10/28 0028.
 */
public class GoodsAddDetailBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : {"goodsId":2365,"goodsCount":44,"baseBarcode":"6925303773915","goodsPicturepath":"image/1497230981128/6925303773915.png","groupsName":"方便速食","groupsUnique":"130000","kindName":"方便面","kindUnique":"130001","goodsUnit":"","goodsBrand":"","goodsAddress":"","goodsRemarks":"","goodsSold":17,"supplierUnique":0,"supplierName":"","goodsPrice":0,"supGoodsBarcode":"","listDetail":[{"goodsId":2365,"goodsBarcode":"6925303773915","goodsName":"统一100老坛酸菜牛肉袋面","goodsStandard":"116克","goodsUnit":"","goodsInPrice":1,"goodsSalePrice":2.5,"foreign_key":"6925303773915","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/1497230981128/6925303773915.png","goodsCusPrice":2.5,"shelfState":1}],"foreignKey":"6925303773915","tableType":0}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * goodsId : 2365
         * goodsCount : 44.0
         * baseBarcode : 6925303773915
         * goodsPicturepath : image/1497230981128/6925303773915.png
         * groupsName : 方便速食
         * groupsUnique : 130000
         * kindName : 方便面
         * kindUnique : 130001
         * goodsUnit :
         * goodsBrand :
         * goodsAddress :
         * goodsRemarks :
         * goodsSold : 17.0
         * supplierUnique : 0
         * supplierName :
         * goodsPrice : 0.0
         * supGoodsBarcode :
         * listDetail : [{"goodsId":2365,"goodsBarcode":"6925303773915","goodsName":"统一100老坛酸菜牛肉袋面","goodsStandard":"116克","goodsUnit":"","goodsInPrice":1,"goodsSalePrice":2.5,"foreign_key":"6925303773915","containCount":1,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"image/1497230981128/6925303773915.png","goodsCusPrice":2.5,"shelfState":1}]
         * foreignKey : 6925303773915
         * tableType : 0
         */

        private int goodsId;
        private double goodsCount;
        private String baseBarcode;
        private String goodsPicturepath;
        private String groupsName;
        private String groupsUnique;
        private String kindName;
        private String kindUnique;
        private String goodsUnit;
        private String goodsBrand;
        private String goodsAddress;
        private String goodsRemarks;
        private double goodsSold;
        private String supplierUnique;
        private String supplierName;
        private double goodsPrice;
        private String supGoodsBarcode;
        private String foreignKey;
        private int tableType;
        private int goodsChengType;
        private int goodsLife;//保质期
        private List<ListDetailBean> listDetail;

        public int getGoodsChengType() {
            return goodsChengType;
        }

        public void setGoodsChengType(int goodsChengType) {
            this.goodsChengType = goodsChengType;
        }

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getBaseBarcode() {
            return baseBarcode;
        }

        public void setBaseBarcode(String baseBarcode) {
            this.baseBarcode = baseBarcode;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getGroupsName() {
            return groupsName;
        }

        public void setGroupsName(String groupsName) {
            this.groupsName = groupsName;
        }

        public String getGroupsUnique() {
            return groupsUnique;
        }

        public void setGroupsUnique(String groupsUnique) {
            this.groupsUnique = groupsUnique;
        }

        public String getKindName() {
            return kindName;
        }

        public void setKindName(String kindName) {
            this.kindName = kindName;
        }

        public String getKindUnique() {
            return kindUnique;
        }

        public void setKindUnique(String kindUnique) {
            this.kindUnique = kindUnique;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public String getGoodsBrand() {
            return goodsBrand;
        }

        public void setGoodsBrand(String goodsBrand) {
            this.goodsBrand = goodsBrand;
        }

        public String getGoodsAddress() {
            return goodsAddress;
        }

        public void setGoodsAddress(String goodsAddress) {
            this.goodsAddress = goodsAddress;
        }

        public String getGoodsRemarks() {
            return goodsRemarks;
        }

        public void setGoodsRemarks(String goodsRemarks) {
            this.goodsRemarks = goodsRemarks;
        }

        public double getGoodsSold() {
            return goodsSold;
        }

        public void setGoodsSold(double goodsSold) {
            this.goodsSold = goodsSold;
        }

        public String getSupplierUnique() {
            return supplierUnique;
        }

        public void setSupplierUnique(String supplierUnique) {
            this.supplierUnique = supplierUnique;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public double getGoodsPrice() {
            return goodsPrice;
        }

        public void setGoodsPrice(double goodsPrice) {
            this.goodsPrice = goodsPrice;
        }

        public String getSupGoodsBarcode() {
            return supGoodsBarcode;
        }

        public void setSupGoodsBarcode(String supGoodsBarcode) {
            this.supGoodsBarcode = supGoodsBarcode;
        }

        public String getForeignKey() {
            return foreignKey;
        }

        public void setForeignKey(String foreignKey) {
            this.foreignKey = foreignKey;
        }

        public int getTableType() {
            return tableType;
        }

        public void setTableType(int tableType) {
            this.tableType = tableType;
        }

        public int getGoodsLife() {
            return goodsLife;
        }

        public void setGoodsLife(int goodsLife) {
            this.goodsLife = goodsLife;
        }

        public List<ListDetailBean> getListDetail() {
            return listDetail;
        }

        public void setListDetail(List<ListDetailBean> listDetail) {
            this.listDetail = listDetail;
        }

        public static class ListDetailBean {
            /**
             * goodsId : 2365
             * goodsBarcode : 6925303773915
             * goodsName : 统一100老坛酸菜牛肉袋面
             * goodsStandard : 116克
             * goodsUnit :
             * goodsInPrice : 1.0
             * goodsSalePrice : 2.5
             * foreign_key : 6925303773915
             * containCount : 1
             * goodsPromotion : 1.0
             * goodsDiscount : 1.0
             * goodsPicturepath : image/1497230981128/6925303773915.png
             * goodsCusPrice : 2.5
             * shelfState : 1
             */

            private int goodsId;
            private String goodsBarcode;
            private String goodsName;
            private String goodsStandard;
            private String goodsUnit;
            private double goodsInPrice;
            private double goodsSalePrice;
            private String foreign_key;
            private int containCount;
            private double goodsPromotion;
            private double goodsDiscount;
            private String goodsPicturepath;
            private double goodsCusPrice;
            private int shelfState;
            private String stock_warning_status;
            private String out_stock_waring_count;
            private String unsalable_count;
            private double stockPrice;//上次出入库单价

            public String getGoodsWebSalePrice() {
                return goodsWebSalePrice;
            }

            public void setGoodsWebSalePrice(String goodsWebSalePrice) {
                this.goodsWebSalePrice = goodsWebSalePrice;
            }

            private String goodsWebSalePrice;

            public String getStock_warning_status() {
                return stock_warning_status;
            }

            public void setStock_warning_status(String stock_warning_status) {
                this.stock_warning_status = stock_warning_status;
            }

            public String getOut_stock_waring_count() {
                return out_stock_waring_count;
            }

            public void setOut_stock_waring_count(String out_stock_waring_count) {
                this.out_stock_waring_count = out_stock_waring_count;
            }

            public String getUnsalable_count() {
                return unsalable_count;
            }

            public void setUnsalable_count(String unsalable_count) {
                this.unsalable_count = unsalable_count;
            }

            public double getStockPrice() {
                return stockPrice;
            }

            public void setStockPrice(double stockPrice) {
                this.stockPrice = stockPrice;
            }

            public int getGoodsId() {
                return goodsId;
            }

            public void setGoodsId(int goodsId) {
                this.goodsId = goodsId;
            }

            public String getGoodsBarcode() {
                return goodsBarcode;
            }

            public void setGoodsBarcode(String goodsBarcode) {
                this.goodsBarcode = goodsBarcode;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getGoodsStandard() {
                return goodsStandard;
            }

            public void setGoodsStandard(String goodsStandard) {
                this.goodsStandard = goodsStandard;
            }

            public String getGoodsUnit() {
                return goodsUnit;
            }

            public void setGoodsUnit(String goodsUnit) {
                this.goodsUnit = goodsUnit;
            }

            public double getGoodsInPrice() {
                return goodsInPrice;
            }

            public void setGoodsInPrice(double goodsInPrice) {
                this.goodsInPrice = goodsInPrice;
            }

            public double getGoodsSalePrice() {
                return goodsSalePrice;
            }

            public void setGoodsSalePrice(double goodsSalePrice) {
                this.goodsSalePrice = goodsSalePrice;
            }

            public String getForeign_key() {
                return foreign_key;
            }

            public void setForeign_key(String foreign_key) {
                this.foreign_key = foreign_key;
            }

            public int getContainCount() {
                return containCount;
            }

            public void setContainCount(int containCount) {
                this.containCount = containCount;
            }

            public double getGoodsPromotion() {
                return goodsPromotion;
            }

            public void setGoodsPromotion(double goodsPromotion) {
                this.goodsPromotion = goodsPromotion;
            }

            public double getGoodsDiscount() {
                return goodsDiscount;
            }

            public void setGoodsDiscount(double goodsDiscount) {
                this.goodsDiscount = goodsDiscount;
            }

            public String getGoodsPicturepath() {
                return goodsPicturepath;
            }

            public void setGoodsPicturepath(String goodsPicturepath) {
                this.goodsPicturepath = goodsPicturepath;
            }

            public double getGoodsCusPrice() {
                return goodsCusPrice;
            }

            public void setGoodsCusPrice(double goodsCusPrice) {
                this.goodsCusPrice = goodsCusPrice;
            }

            public int getShelfState() {
                return shelfState;
            }

            public void setShelfState(int shelfState) {
                this.shelfState = shelfState;
            }
        }
    }
}
