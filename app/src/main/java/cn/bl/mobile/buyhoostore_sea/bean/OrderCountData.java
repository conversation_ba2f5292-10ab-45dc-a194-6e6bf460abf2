package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;

/**
 * Describe:销售订单数量（实体类）
 * Created by jingang on 2022/12/10
 */
public class OrderCountData implements Serializable {

    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"handleState":2,"count":44},{"handleState":3,"count":7},{"handleState":4,"count":7},{"handleState":5,"count":1},{"handleState":6,"count":26},{"handleState":7,"count":4},{"handleState":8,"count":4},{"handleState":9,"count":4},{"handleState":-1,"count":97},{"handleState":-2,"count":4},{"handleState":-3,"count":24},{"handleState":-4,"count":19},{"handleState":"-5","count":47}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    /**
     * handleState : 2
     * count : 44
     */

    private int handleState;
    private int count;

    public int getHandleState() {
        return handleState;
    }

    public void setHandleState(int handleState) {
        this.handleState = handleState;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
