package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/10/16 0016.
 */
public class MemberBean {


    /**
     * status : 1
     * msg :
     * totals : 0
     * perPageNum : 0
     * data : [{"cusHeadPath":"upload/no_goodsB.jpg","cusId":15197,"cusName":"zck","cusRegeditDate":"6","cusTotal":62}]
     * supData : null
     * shopMembership : 58
     * addMembership : 0
     * heji : null
     */

    private int status;
    private String msg;
    private String totals;
    private String perPageNum;
    private Object supData;
    private String shopMembership;
    private String addMembership;
    private String weekToShopCusNum;
    private Object heji;
    private List<DataBean> data;

    public String getWeekToShopCusNum() {
        return weekToShopCusNum;
    }

    public void setWeekToShopCusNum(String weekToShopCusNum) {
        this.weekToShopCusNum = weekToShopCusNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getTotals() {
        return totals;
    }

    public void setTotals(String totals) {
        this.totals = totals;
    }

    public String getPerPageNum() {
        return perPageNum;
    }

    public void setPerPageNum(String perPageNum) {
        this.perPageNum = perPageNum;
    }

    public Object getSupData() {
        return supData;
    }

    public void setSupData(Object supData) {
        this.supData = supData;
    }

    public String getShopMembership() {
        return shopMembership;
    }

    public void setShopMembership(String shopMembership) {
        this.shopMembership = shopMembership;
    }

    public String getAddMembership() {
        return addMembership;
    }

    public void setAddMembership(String addMembership) {
        this.addMembership = addMembership;
    }

    public Object getHeji() {
        return heji;
    }

    public void setHeji(Object heji) {
        this.heji = heji;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * cusHeadPath : upload/no_goodsB.jpg
         * cusId : 15197
         * cusName : zck
         * cusRegeditDate : 6
         * cusTotal : 62.0
         */

        private String cusHeadPath;
        private String cusId;
        private String cusName;

        private String cusPhone;

        private String cusRegeditDate;
        private double cusTotal;
        private String cus_unique;
        private double cus_points;
        private String cus_level_val;
        private String cus_balance;
        private String cus_type;

        /*宁宇会员*/
        private int cus_count;
        private int powerCredit;
        private String cus_level_name;//会员等级名称
//        private String cusName;//会员名称
        private String cusUnique;//会员编号
        private double cus_rebate;//赠送金额
//        private String cusPhone;//会员电话
        private double cusBalance;//会员本金余额
        private String cus_id;//会员ID
//        private double cus_points;//会员积分
        private String cus_remark;//备注
        private double creditLimit;
        private int cus_level_id;//会员等级id
        private int cus_sex;//性别
        private String cus_password;//密码
        private String cus_regedit_date;//注册日期


        public String getCusPhone() {
            return cusPhone;
        }

        public void setCusPhone(String cusPhone) {
            this.cusPhone = cusPhone;
        }

        public String getCus_type() {
            return cus_type;
        }

        public void setCus_type(String cus_type) {
            this.cus_type = cus_type;
        }

        public String getCus_balance() {
            return cus_balance;
        }

        public void setCus_balance(String cus_balance) {
            this.cus_balance = cus_balance;
        }

        public String getCus_unique() {
            return cus_unique;
        }

        public void setCus_unique(String cus_unique) {
            this.cus_unique = cus_unique;
        }

        public double getCus_points() {
            return cus_points;
        }

        public void setCus_points(double cus_points) {
            this.cus_points = cus_points;
        }

        public String getCus_level_val() {
            return cus_level_val;
        }

        public void setCus_level_val(String cus_level_val) {
            this.cus_level_val = cus_level_val;
        }

        public String getCusHeadPath() {
            return cusHeadPath;
        }

        public void setCusHeadPath(String cusHeadPath) {
            this.cusHeadPath = cusHeadPath;
        }

        public String getCusId() {
            return cusId;
        }

        public void setCusId(String cusId) {
            this.cusId = cusId;
        }

        public String getCusName() {
            return cusName;
        }

        public void setCusName(String cusName) {
            this.cusName = cusName;
        }

        public String getCusRegeditDate() {
            return cusRegeditDate;
        }

        public void setCusRegeditDate(String cusRegeditDate) {
            this.cusRegeditDate = cusRegeditDate;
        }

        public double getCusTotal() {
            return cusTotal;
        }

        public void setCusTotal(double cusTotal) {
            this.cusTotal = cusTotal;
        }


        public int getCus_count() {
            return cus_count;
        }

        public void setCus_count(int cus_count) {
            this.cus_count = cus_count;
        }

        public int getPowerCredit() {
            return powerCredit;
        }

        public void setPowerCredit(int powerCredit) {
            this.powerCredit = powerCredit;
        }

        public String getCus_level_name() {
            return cus_level_name;
        }

        public void setCus_level_name(String cus_level_name) {
            this.cus_level_name = cus_level_name;
        }

        public String getCusUnique() {
            return cusUnique;
        }

        public void setCusUnique(String cusUnique) {
            this.cusUnique = cusUnique;
        }

        public double getCus_rebate() {
            return cus_rebate;
        }

        public void setCus_rebate(double cus_rebate) {
            this.cus_rebate = cus_rebate;
        }

        public double getCusBalance() {
            return cusBalance;
        }

        public void setCusBalance(double cusBalance) {
            this.cusBalance = cusBalance;
        }

        public String getCus_id() {
            return cus_id;
        }

        public void setCus_id(String cus_id) {
            this.cus_id = cus_id;
        }

        public String getCus_remark() {
            return cus_remark;
        }

        public void setCus_remark(String cus_remark) {
            this.cus_remark = cus_remark;
        }

        public double getCreditLimit() {
            return creditLimit;
        }

        public void setCreditLimit(double creditLimit) {
            this.creditLimit = creditLimit;
        }

        public int getCus_level_id() {
            return cus_level_id;
        }

        public void setCus_level_id(int cus_level_id) {
            this.cus_level_id = cus_level_id;
        }

        public int getCus_sex() {
            return cus_sex;
        }

        public void setCus_sex(int cus_sex) {
            this.cus_sex = cus_sex;
        }

        public String getCus_password() {
            return cus_password;
        }

        public void setCus_password(String cus_password) {
            this.cus_password = cus_password;
        }

        public String getCus_regedit_date() {
            return cus_regedit_date;
        }

        public void setCus_regedit_date(String cus_regedit_date) {
            this.cus_regedit_date = cus_regedit_date;
        }
    }
}
