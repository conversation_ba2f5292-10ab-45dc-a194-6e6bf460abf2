package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:商品列表（适配器）
 * Created by jingang on 2022/12/1
 */
public class GoodsAdapter extends BaseAdapter<GoodsData> {
    private boolean isShow;//是否展示选择按钮

    @SuppressLint("NotifyDataSetChanged")
    public void setShow(boolean show) {
        isShow = show;
        notifyDataSetChanged();
    }

    public GoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivMore, ivCheck, ivPrinter, ivApplet;
        ivMore = holder.getView(R.id.ivItemMore);
        ivCheck = holder.getView(R.id.ivItemCheck);
        ivPrinter = holder.getView(R.id.ivItemPrinter);
        ivApplet = holder.getView(R.id.ivItemApplet);

        //收银机上架状态
        if (mDataList.get(position).getPcShelfState() == 1) {
            ivPrinter.setImageResource(R.mipmap.ic_printer001);
        } else {
            ivPrinter.setImageResource(R.mipmap.ic_printer002);
        }
        //小程序上架状态
        if (mDataList.get(position).getShelfState() == 1) {
            ivApplet.setImageResource(R.mipmap.ic_applet001);
        } else {
            ivApplet.setImageResource(R.mipmap.ic_applet002);
        }

        //是否展示
        if (isShow) {
            ivCheck.setVisibility(View.VISIBLE);
            ivMore.setVisibility(View.GONE);
            //是否选中
            if (mDataList.get(position).isCheck()) {
                ivCheck.setImageResource(R.mipmap.ic_chosen001);
            } else {
                ivCheck.setImageResource(R.mipmap.ic_chose001);
            }
        } else {
            ivCheck.setVisibility(View.GONE);
            ivMore.setVisibility(View.VISIBLE);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivPrinter, ivApplet, ivMore, ivCheck;
        ivImg = holder.getView(R.id.ivItemImg);
        ivPrinter = holder.getView(R.id.ivItemPrinter);
        ivApplet = holder.getView(R.id.ivItemApplet);
        ivMore = holder.getView(R.id.ivItemMore);
        ivCheck = holder.getView(R.id.ivItemCheck);
        TextView tvName, tvPrice_in, tvPrice_sale, tvCount, tvKucun, tvPriceIn, tvPriceSale;
        tvName = holder.getView(R.id.tvItemName);
        tvPriceIn = holder.getView(R.id.tvItemPriceIn);
        tvPrice_in = holder.getView(R.id.tvItemPrice_in);
        tvPriceSale = holder.getView(R.id.tvItemPriceSale);
        tvPrice_sale = holder.getView(R.id.tvItemPrice_sale);
        tvCount = holder.getView(R.id.tvItemCount);
        tvKucun = holder.getView(R.id.tvItemKucun);

        tvPriceIn.setText(getLanguageValue("latestReceiptPrice") + ":");
        tvPriceSale.setText(getLanguageValue("sellingPrice") + ":");

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvPrice_in.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        tvPrice_sale.setText(DFUtils.getNum2(mDataList.get(position).getGoodsSalePrice()));
        tvCount.setText(getLanguageValue("month") + getLanguageValue("salesVolume") + ":" + DFUtils.getNum(mDataList.get(position).getSaleCount()));
        tvKucun.setText(getLanguageValue("inventory") + ":" + DFUtils.getNum2(mDataList.get(position).getGoodsCount()));

        //收银机上架状态
        if (mDataList.get(position).getPcShelfState() == 1) {
            ivPrinter.setImageResource(R.mipmap.ic_printer001);
        } else {
            ivPrinter.setImageResource(R.mipmap.ic_printer002);
        }
        //小程序上架状态
        if (mDataList.get(position).getShelfState() == 1) {
            ivApplet.setImageResource(R.mipmap.ic_applet001);
        } else {
            ivApplet.setImageResource(R.mipmap.ic_applet002);
        }

        //是否展示
        if (isShow) {
            ivCheck.setVisibility(View.VISIBLE);
            ivMore.setVisibility(View.GONE);
            //是否选中
            if (mDataList.get(position).isCheck()) {
                ivCheck.setImageResource(R.mipmap.ic_chosen001);
            } else {
                ivCheck.setImageResource(R.mipmap.ic_chose001);
            }
        } else {
            ivCheck.setVisibility(View.GONE);
            ivMore.setVisibility(View.VISIBLE);
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(holder.itemView, position));
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
            ivCheck.setOnClickListener(v -> listener.onCheckClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onMoreClick(View view, int position);

        void onCheckClick(View view, int position);
    }
}
