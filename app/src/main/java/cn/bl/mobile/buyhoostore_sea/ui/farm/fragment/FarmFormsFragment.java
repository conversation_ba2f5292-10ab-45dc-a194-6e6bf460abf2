package cn.bl.mobile.buyhoostore_sea.ui.farm.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseFragment;

import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.GoodsSaleSummaryActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.OperatePerformanceActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.NPAccountOutInDetailActivity;

/**
 * Describe:农批-报表
 * Created by jingang on 2023/5/28
 */
@SuppressLint("NonConstantResourceId")
public class FarmFormsFragment extends BaseFragment {
    /**
     * 初始化fragment
     *
     * @return
     */
    public static FarmFormsFragment newInstance() {
        FarmFormsFragment fragment = new FarmFormsFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fm_forms_farm;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {

    }

    @OnClick({R.id.tvGoodsSaleSummary, R.id.tvOperatePerformance, R.id.tvInAndOutMx, R.id.tvClasses})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvGoodsSaleSummary:
                //货品销售汇总
                goToActivity(GoodsSaleSummaryActivity.class);
                break;
            case R.id.tvOperatePerformance:
                //经营业绩
                goToActivity(OperatePerformanceActivity.class);
                break;
            case R.id.tvInAndOutMx:
                //账户收支明细
                goToActivity(NPAccountOutInDetailActivity.class);
                break;
            case R.id.tvClasses:
                //班次统计
                break;
        }
    }
}
