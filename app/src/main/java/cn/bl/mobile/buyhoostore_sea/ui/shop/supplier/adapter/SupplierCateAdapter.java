package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierCateData;

/**
 * Describe:供货商分类（适配器）
 * Created by jingang on 2023/9/20
 */
public class SupplierCateAdapter extends BaseAdapter<SupplierCateData> {

    public SupplierCateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_manage;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getSupplierKindName());
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        ImageView ivEdit, ivDel, ivSort;
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);
        ivSort = holder.getView(R.id.ivItemSort);
        tvName.setText(mDataList.get(position).getSupplierKindName());
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
            ivSort.setOnClickListener(v -> listener.onSortClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);

        void onSortClick(View view, int position);
    }
}
