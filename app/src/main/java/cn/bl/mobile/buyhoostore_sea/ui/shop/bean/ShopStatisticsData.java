package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;

/**
 * Describe:主界面信息统计（实体类）
 * Created by jingang on 2023/7/3
 */
public class ShopStatisticsData implements Serializable {
    /**
     * unitPrice : 0
     * saleSum : 0
     * listCount : 0
     * percent : 0
     * profit : 0
     */

    private double unitPrice;//客单价
    private double saleSum;//今日营业额
    private int listCount;//订单量
    private double percent;//昨日占比
    private double profit;//毛利润
    private double beans_use;//百货豆数量

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getSaleSum() {
        return saleSum;
    }

    public void setSaleSum(double saleSum) {
        this.saleSum = saleSum;
    }

    public int getListCount() {
        return listCount;
    }

    public void setListCount(int listCount) {
        this.listCount = listCount;
    }

    public double getPercent() {
        return percent;
    }

    public void setPercent(double percent) {
        this.percent = percent;
    }

    public double getProfit() {
        return profit;
    }

    public void setProfit(double profit) {
        this.profit = profit;
    }

    public double getBeans_use() {
        return beans_use;
    }

    public void setBeans_use(double beans_use) {
        this.beans_use = beans_use;
    }
}
