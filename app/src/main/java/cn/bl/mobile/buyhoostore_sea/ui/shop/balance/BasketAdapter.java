package cn.bl.mobile.buyhoostore_sea.ui.shop.balance;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean.BasketData;

/**
 * Describe:余额（差价）-详情-筐信息（适配器）
 * Created by jingang on 2023/4/25
 */
public class BasketAdapter extends BaseAdapter<BasketData.BucketlistBean> {

    public BasketAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_basket;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvCount, tvRemarks, tvPrice;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvRemarks = holder.getView(R.id.tvItemRemarks);
        tvPrice = holder.getView(R.id.tvItemPrice);

        tvName.setText(mDataList.get(position).getBucket_name());
        tvCount.setText(mDataList.get(position).getBucket_amount() + getLanguageValue("ones"));
        tvRemarks.setText(mDataList.get(position).getRemarks());
        tvPrice.setText("RM" + mDataList.get(position).getBucket_deposit());
    }
}
