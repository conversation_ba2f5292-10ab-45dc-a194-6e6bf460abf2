package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:店铺-今日营业额-根据支付方式查订单（实体类）
 * Created by jingang on 2024/5/22
 */
public class StatisticsTodayOrderData implements Serializable {
    /**
     * queryStatisticsStatis : {"saleListTotal":11.2,"saleListTotalMoney":11.2,"saleListRefundMoney":0,"saleListCount":2}
     * list : [{"payMethod":13,"payMethodName":null,"payMoney":6.2,"saleListDatetime":"2024-05-18 00:44:09","saleListUnique":"1715964205525886","orderType":1,"isZh":0},{"payMethod":13,"payMethodName":null,"payMoney":5,"saleListDatetime":"2024-05-18 09:14:20","saleListUnique":"1715994860916286","orderType":1,"isZh":1}]
     * orderCount : 2
     */

    private QueryStatisticsStatisBean queryStatisticsStatis;
    private int orderCount;//数量
    private List<ListBean> list;

    public QueryStatisticsStatisBean getQueryStatisticsStatis() {
        return queryStatisticsStatis;
    }

    public void setQueryStatisticsStatis(QueryStatisticsStatisBean queryStatisticsStatis) {
        this.queryStatisticsStatis = queryStatisticsStatis;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class QueryStatisticsStatisBean {
        /**
         * saleListTotal : 11.2
         * saleListTotalMoney : 11.2
         * saleListRefundMoney : 0.0
         * saleListCount : 2
         */

        private double saleListTotal;//总营业额
        private double saleListTotalMoney;//实收
        private double saleListRefundMoney;//退款
        private int saleListCount;//数量

        public double getSaleListTotal() {
            return saleListTotal;
        }

        public void setSaleListTotal(double saleListTotal) {
            this.saleListTotal = saleListTotal;
        }

        public double getSaleListTotalMoney() {
            return saleListTotalMoney;
        }

        public void setSaleListTotalMoney(double saleListTotalMoney) {
            this.saleListTotalMoney = saleListTotalMoney;
        }

        public double getSaleListRefundMoney() {
            return saleListRefundMoney;
        }

        public void setSaleListRefundMoney(double saleListRefundMoney) {
            this.saleListRefundMoney = saleListRefundMoney;
        }

        public int getSaleListCount() {
            return saleListCount;
        }

        public void setSaleListCount(int saleListCount) {
            this.saleListCount = saleListCount;
        }
    }

    public static class ListBean {
        /**
         * payMethod : 13
         * payMethodName : null
         * payMoney : 6.2
         * saleListDatetime : 2024-05-18 00:44:09
         * saleListUnique : 1715964205525886
         * orderType : 1
         * isZh : 0
         */

        private int payMethod;//收款方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 6.美团 7.饿了吗 8.混合支付 10.积分支付 11.百货豆支付 13.金圈支付 14.聚合码支付
        private String payMethodName;//收款方式名称
        private double payMoney;//收款金额
        private String saleListDatetime;
        private String saleListUnique;
        private int orderType;//1.收款 2.退款
        private int isZh;//是否为组合 0.非组合 1.组合
        private int shopType;//1-11.普通订单 12.餐饮订单

        public int getPayMethod() {
            return payMethod;
        }

        public void setPayMethod(int payMethod) {
            this.payMethod = payMethod;
        }

        public String getPayMethodName() {
            return payMethodName;
        }

        public void setPayMethodName(String payMethodName) {
            this.payMethodName = payMethodName;
        }

        public double getPayMoney() {
            return payMoney;
        }

        public void setPayMoney(double payMoney) {
            this.payMoney = payMoney;
        }

        public String getSaleListDatetime() {
            return saleListDatetime;
        }

        public void setSaleListDatetime(String saleListDatetime) {
            this.saleListDatetime = saleListDatetime;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public int getOrderType() {
            return orderType;
        }

        public void setOrderType(int orderType) {
            this.orderType = orderType;
        }

        public int getIsZh() {
            return isZh;
        }

        public void setIsZh(int isZh) {
            this.isZh = isZh;
        }

        public int getShopType() {
            return shopType;
        }

        public void setShopType(int shopType) {
            this.shopType = shopType;
        }
    }
}
