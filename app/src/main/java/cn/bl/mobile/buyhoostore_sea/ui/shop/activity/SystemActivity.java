package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.PackageUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.jpush.JPushUtil;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.utils.CleanMessageUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-系统设置
 * Created by jingang on 2024/12/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SystemActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvPwd)
    TextView tvPwd;
    @BindView(R.id.tvPermission)
    TextView tvPermission;
    @BindView(R.id.tvLanguage)
    TextView tvLanguage;
    @BindView(R.id.tvCacheValue)
    TextView tvCacheValue;
    @BindView(R.id.tvCache)
    TextView tvCache;
    @BindView(R.id.tvAbout)
    TextView tvAbout;
    @BindView(R.id.tvVersion)
    TextView tvVersion;
    @BindView(R.id.tvLogout)
    TextView tvLogout;
    @BindView(R.id.tvLoginOut)
    TextView tvLoginOut;

    String setPowerManager;
    SharedPreferences sp = null;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_system;
    }

    @Override
    public void initViews() {
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        setPowerManager = sp.getString("setPowerManager", "");
//        if (setPowerManager.equals("1")) {
//            tvPermission.setVisibility(View.VISIBLE);
//        } else {
//            tvPermission.setVisibility(View.GONE);
//        }
        tvVersion.setText(getLanguageValue("version") + PackageUtils.getPackageName(this));
        try {
            String listsize = CleanMessageUtil.getTotalCacheSize(this);
            tvCache.setText(listsize);
        } catch (Exception ignored) {

        }
    }

    @OnClick({R.id.ivBack,
            R.id.tvPwd,
            R.id.tvPermission,
            R.id.tvLanguage,
            R.id.linCache, R.id.linAbout, R.id.tvLogout,
            R.id.tvLoginOut})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvPwd:
                //修改密码
                goToActivity(UpdatePasswordActivity.class);
                break;
            case R.id.tvPermission:
                //权限设置
                goToActivity(PermissionSetActivity.class);
                break;
            case R.id.tvLanguage:
                //语言设置
                goToActivity(LanguageActivity.class);
                break;
            case R.id.linCache:
                //清除缓存
                IAlertDialog.showDialog(this,
                        getLanguageValue("confirm")+getLanguageValue("clear")+getLanguageValue("cashe")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            CleanMessageUtil.clearAllCache(this);//清理缓存
                            try {
                                String listsize = CleanMessageUtil.getTotalCacheSize(this);//获取缓存大小
                                tvCache.setText(listsize);
                                showMessage(getLanguageValue("clear")+getLanguageValue("finish"));
                            } catch (Exception e) {

                            }
                        });
                break;
            case R.id.linAbout:
                //关于我们
                goToActivity(AboutUsActivity.class);
                break;
            case R.id.tvLogout:
                //账号注销
                goToActivity(LogoutActivity.class);
                break;
            case R.id.tvLoginOut:
                //退出登录
                IAlertDialog.showDialog(this,
                        getLanguageValue("confirm")+getLanguageValue("signOut")+getLanguageValue("accountNumber")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postUpdateJPushId();
                        });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("system")+getLanguageValue("setUp"));
        tvPwd.setText(getLanguageValue("modification")+getLanguageValue("password"));
        tvPermission.setText(getLanguageValue("permissions")+getLanguageValue("setUp"));
        tvLanguage.setText(getLanguageValue("language")+getLanguageValue("setUp"));
        tvCacheValue.setText(getLanguageValue("clear")+getLanguageValue("cashe"));
        tvAbout.setText(getLanguageValue("aboutUs"));
        tvLogout.setText(getLanguageValue("accountNumber")+getLanguageValue("logout"));
        tvLoginOut.setText(getLanguageValue("signOut")+getLanguageValue("login"));
    }

    private void loginOut() {
        BaseApplication.getInstance().saveUserInfo("");
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("colose"));
        SharedPreferences mySharedPreferences = getSharedPreferences("test", Activity.MODE_PRIVATE);
        SharedPreferences.Editor editor = mySharedPreferences.edit();
        editor.putString("exit", "已退出");
        editor.commit();
        JPushUtil.setTagAndAlias("", this);
        if (sp != null) {
            SharedPreferences.Editor editor1 = sp.edit();
            editor1.clear();
            editor1.commit();
        }
        AppManager.getInstance().finishAllActivity();
        goToActivity(LoginActivity.class);
        finish();
    }

    /**
     * 更新推送注册id
     */
    private void postUpdateJPushId() {
        Map<String, Object> map = new HashMap<>();
        map.put("registration_phone_type", 2);
        map.put("registration_id", "");
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateRegistrationId(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        loginOut();
                    }

                    @Override
                    public void onError(String msg) {
                        loginOut();
                    }
                });
    }
}
