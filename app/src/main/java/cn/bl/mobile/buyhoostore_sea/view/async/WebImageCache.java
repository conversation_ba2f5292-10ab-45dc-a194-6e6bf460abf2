package cn.bl.mobile.buyhoostore_sea.view.async;


import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.SoftReference;
import java.util.Arrays;
import java.util.Comparator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import android.app.AlarmManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

public class WebImageCache {
	private final String DISK_CACHE_PATH = "/cltx/phonelink/image_web_cache";

	private ConcurrentHashMap<String, SoftReference<Bitmap>> memoryCache;
	private String diskCachePath;
	private boolean diskCacheEnabled = false;
	private ExecutorService writeThread;

	/*********************************************************************************/
	/** 1MB 鏂囦�? 鎵�崰鐨�bytes **/
	private final int MB = 1024 * 1024;
	/** 鍙紦�?�樺浘鐗囧埌SDCard鏃讹紝SDCard 鍓╀綑鐨勬渶灏忕┖闂�?**/
	private final int FREE_SD_SPACE_NEEDED_TO_CACHE = 10 /* MB */;
	/** 缂撳瓨鍥剧墖鏈�鍗犵敤鐨凷DCard绌洪�? **/
	private final int CACHE_SIZE = 10 * MB;
	/** 缂撳瓨鐨勫浘鐗囪繃鏈熸椂闂达紝杩囨湡鍚庡皢鍒犻櫎 **/
	private final long mTimeDiff = AlarmManager.INTERVAL_DAY * 10;
	/****/
	private final String WHOLESALE_CONV = "wholesale_conv";

	/*********************************************************************************/

	public WebImageCache(Context context) {
		// Set up in-memory cache store
		memoryCache = new ConcurrentHashMap<String, SoftReference<Bitmap>>();

		// Set up disk cache store
		if ((!Environment.MEDIA_MOUNTED.equals(Environment
				.getExternalStorageState()))
				|| (!Environment.getExternalStorageDirectory().canWrite())) {
			diskCacheEnabled = false;
		} else {
			diskCachePath = Environment.getExternalStorageDirectory().getPath()
					+ DISK_CACHE_PATH;
			File outFile = new File(diskCachePath);
			if (!outFile.exists()) {
				outFile.mkdirs();
				// outFile.mkdirs();
			}
			diskCacheEnabled = outFile.exists();
		}
		// Set up threadpool for image fetching tasks
		writeThread = Executors.newSingleThreadExecutor();
	}

	public Bitmap get(final String url) {
		Bitmap bitmap = null;

		// Check for image in memory
		bitmap = getBitmapFromMemory(url);

		// Check for image on disk cache
		if (bitmap == null) {
			bitmap = getBitmapFromDisk(url);

			// Write bitmap back into memory cache
			if (bitmap != null) {
				cacheBitmapToMemory(url, bitmap);
			}
		}

		return bitmap;
	}

	public void put(String url, Bitmap bitmap) {
		cacheBitmapToMemory(url, bitmap);
		cacheBitmapToDisk(url, bitmap);
	}

	public void remove(String url) {
		if (url == null) {
			return;
		}

		// Remove from memory cache
		memoryCache.remove(getCacheKey(url));

		// Remove from file cache
		File f = new File(diskCachePath, getCacheKey(url));
		if (f.exists() && f.isFile()) {
			f.delete();
		}
	}

	public void clear() {
		// Remove everything from memory cache
		memoryCache.clear();

		// Remove everything from file cache
		File cachedFileDir = new File(diskCachePath);
		if (cachedFileDir.exists() && cachedFileDir.isDirectory()) {
			File[] cachedFiles = cachedFileDir.listFiles();
			for (File f : cachedFiles) {
				if (f.exists() && f.isFile()) {
					f.delete();
				}
			}
		}
	}

	private void cacheBitmapToMemory(final String url, final Bitmap bitmap) {
		memoryCache.put(getCacheKey(url), new SoftReference<Bitmap>(bitmap));
	}

	private void cacheBitmapToDisk(final String url, final Bitmap bitmap) {
		writeThread.execute(new Runnable() {
			@Override
			public void run() {
				if (!diskCacheEnabled) {
					Log.v("debug", "diskCacheEnabled = false");
					return;
				}
				// -------------------------------------------------------------------------------
				removeCache(diskCachePath);
				// 鍒ゆ柇sdcard涓婄殑绌洪棿
				if (FREE_SD_SPACE_NEEDED_TO_CACHE > freeSpaceOnSd()) {
					return;
				}
				// -------------------------------------------------------------------------------

				BufferedOutputStream ostream = null;
				try {
					ostream = new BufferedOutputStream(new FileOutputStream(
							new File(diskCachePath, getCacheKey(url))),
							4 * 1024);
					bitmap.compress(CompressFormat.PNG, 100, ostream);
				} catch (FileNotFoundException e) {
					e.printStackTrace();
				} finally {
					try {
						if (ostream != null) {
							ostream.flush();
							ostream.close();
						}
					} catch (IOException e) {
					}
				}
			}
		});
	}

	private Bitmap getBitmapFromMemory(String url) {
		Bitmap bitmap = null;
		SoftReference<Bitmap> softRef = memoryCache.get(getCacheKey(url));
		if (softRef != null) {
			bitmap = softRef.get();
		}
		return bitmap;
	}

	private Bitmap getBitmapFromDisk(String url) {
		Bitmap bitmap = null;
		if (diskCacheEnabled) {
			String filePath = getFilePath(url);
			File file = new File(filePath);
			if (file.exists()) {
				bitmap = BitmapFactory.decodeFile(filePath);
				updateFileTime(file);
			}
		}
		return bitmap;
	}

	private String getFilePath(String url) {
		return diskCachePath + "/" + getCacheKey(url);
	}

	private String getCacheKey(String url) {
		if (url == null) {
			throw new RuntimeException("Null url passed in");
		} else {
			return url.replaceAll("[.:/,%?&=]", "+").replaceAll("[+]+", "+");
		}
	}

	// =================================================================
	/**
	 * 璁＄畻sdcard涓婄殑鍓�?綑绌洪棿
	 * 
	 * @return
	 */
	private int freeSpaceOnSd() {
		StatFs stat = new StatFs(Environment.getExternalStorageDirectory()
				.getPath());
		double sdFreeMB = ((double) stat.getAvailableBlocks() * (double) stat
				.getBlockSize()) / MB;
		return (int) sdFreeMB;
	}

	/**
	 * 淇敼鏂囦欢鐨勬渶鍚庝慨�?规椂闂�	 *
     */
	private void updateFileTime(File file) {
		if (file == null || (!file.exists())) {
			return;
		}
		long newModifiedTime = System.currentTimeMillis();
		file.setLastModified(newModifiedTime);
	}

	/**
	 * 璁＄畻�?�樺偍鐩綍涓嬬殑鏂囦欢澶у皬锛�	 * 褰撴枃浠舵�澶у皬澶т簬瑙勫畾鐨凜ACHE_SIZE鎴栬�sdcard鍓╀綑绌洪棿灏忎簬FREE_SD_SPACE_NEEDED_TO_CACHE鐨勮�?��	 * 閭ｄ箞鍒犻櫎40%鏈�繎娌℃湁琚娇鐢ㄧ殑鏂囦�?
	 * 
	 * @param dirPath
     */
	private void removeCache(String dirPath) {
		File dir = new File(dirPath);
		File[] files = dir.listFiles();
		if (files == null) {
			return;
		}
		int dirSize = 0;
		for (int i = 0; i < files.length; i++) {
			// if (files[i].getName().contains(WHOLESALE_CONV)) {
			dirSize += files[i].length();
			// }
		}
		if (dirSize > CACHE_SIZE
				|| FREE_SD_SPACE_NEEDED_TO_CACHE > freeSpaceOnSd()) {
			int removeFactor = (int) ((0.4 * files.length) + 1);
			Arrays.sort(files, new FileLastModifSort());
			for (int i = 0; i < Math.min(removeFactor, files.length); i++) {
				// if (files[i].getName().contains(WHOLESALE_CONV)) {
				files[i].delete();
				// }
			}
		}
	}

	/**
	 * 鍒犻櫎杩囨湡鏂囦�?
	 * 
	 * @param dirPath
	 * @param filename
	 */
	private void removeExpiredCache(String dirPath, String filename) {
		File file = new File(dirPath, filename);
		if (System.currentTimeMillis() - file.lastModified() > mTimeDiff) {
			// Log.i(TAG, "Clear some expiredcache files ");
			file.delete();
		}
	}

	/**
	 * 鏍规嵁鏂囦欢鐨勬渶鍚庝慨�?规椂闂磋繘琛屾帓搴�*
	 * 
	 * @ClassName: FileLastModifSort
	 * @date 2013-1-20 涓嬪�?9:13:13
	 */
	class FileLastModifSort implements Comparator<File> {
		public int compare(File arg0, File arg1) {
			if (arg0.lastModified() > arg1.lastModified()) {
				return 1;
			} else if (arg0.lastModified() == arg1.lastModified()) {
				return 0;
			} else {
				return -1;
			}
		}
	}
}
