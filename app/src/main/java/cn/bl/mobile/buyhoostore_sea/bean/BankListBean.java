package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class BankListBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"cardId":2,"shopUnique":null,"bank":2,"bankCard":"**** **** ***5 455","bankName":"中国银行","bankPhone":"123","creatTime":"2018-12-01","defaultType":2,"bankImg":"image/bankImg/BOC.png","validType":1,"bankLogo":"image/bankImg/BOClogo.png"},{"cardId":6,"shopUnique":null,"bank":4,"bankCard":"**** **** **** 5555","bankName":"建设银行","bankPhone":"********","creatTime":"2018-12-13","defaultType":2,"bankImg":"image/bankImg/CCB.png","validType":1,"bankLogo":"image/bankImg/CCBlogo.png"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * cardId : 2
         * shopUnique : null
         * bank : 2
         * bankCard : **** **** ***5 455
         * bankName : 中国银行
         * bankPhone : 123
         * creatTime : 2018-12-01
         * defaultType : 2
         * bankImg : image/bankImg/BOC.png
         * validType : 1
         * bankLogo : image/bankImg/BOClogo.png
         */

        private int cardId;
        private Object shopUnique;
        private int bank;
        private String bankCard;
        private String bankName;
        private String bankPhone;
        private String creatTime;
        private int defaultType;
        private String bankImg;
        private int validType;
        private String bankLogo;

        public int getCardId() {
            return cardId;
        }

        public void setCardId(int cardId) {
            this.cardId = cardId;
        }

        public Object getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(Object shopUnique) {
            this.shopUnique = shopUnique;
        }

        public int getBank() {
            return bank;
        }

        public void setBank(int bank) {
            this.bank = bank;
        }

        public String getBankCard() {
            return bankCard;
        }

        public void setBankCard(String bankCard) {
            this.bankCard = bankCard;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getBankPhone() {
            return bankPhone;
        }

        public void setBankPhone(String bankPhone) {
            this.bankPhone = bankPhone;
        }

        public String getCreatTime() {
            return creatTime;
        }

        public void setCreatTime(String creatTime) {
            this.creatTime = creatTime;
        }

        public int getDefaultType() {
            return defaultType;
        }

        public void setDefaultType(int defaultType) {
            this.defaultType = defaultType;
        }

        public String getBankImg() {
            return bankImg;
        }

        public void setBankImg(String bankImg) {
            this.bankImg = bankImg;
        }

        public int getValidType() {
            return validType;
        }

        public void setValidType(int validType) {
            this.validType = validType;
        }

        public String getBankLogo() {
            return bankLogo;
        }

        public void setBankLogo(String bankLogo) {
            this.bankLogo = bankLogo;
        }
    }
}
