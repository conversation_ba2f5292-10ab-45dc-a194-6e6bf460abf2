package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.jaredrummler.materialspinner.MaterialSpinner;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.adapter.RemeberTakeAdapter;
import cn.bl.mobile.buyhoostore_sea.bean.PersionPersonBean;
import cn.bl.mobile.buyhoostore_sea.bean.RemberDetailBean;
import cn.bl.mobile.buyhoostore_sea.bean.RemeberTakeBean;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.sale.OrderInfoActivity;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 会员充值消费记录
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class MemberTakeActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvUnique)
    TextView tvUnique;
    @BindView(R.id.tvPoints)
    TextView tvPoints;
    @BindView(R.id.tvBalance)
    TextView tvBalance;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.tvEndTime)
    TextView tvEndTime;
    @BindView(R.id.spinnerType)
    MaterialSpinner spinnerType;
    @BindView(R.id.spinnerPeople)
    MaterialSpinner spinnerPeople;
    @BindView(R.id.tvDate)
    TextView tvDate;
    @BindView(R.id.tvType)
    TextView tvType;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.tvPointsValue)
    TextView tvPointsValue;
    @BindView(R.id.tvUserValue)
    TextView tvUserValue;
    @BindView(R.id.listview)
    ListView listView;

    RemeberTakeAdapter remeberTakeAdapter;
    int staffId2 = -1;
    PersionPersonBean persionPersonBean;
    String cusId;
    //    String datetime;
    RemeberTakeBean remeberTakeBean;
    List<RemeberTakeBean.DataBean> member = new ArrayList<>();
    public static String cus_type;
    RemberDetailBean remberbean;

    private String startTime, endTime;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_remeber_take;
    }

    @Override
    public void initViews() {
        setStatusBar(false);

        inintView();
        inintData();
    }

    @OnClick({R.id.ivBack, R.id.tvTime, R.id.tvEndTime})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvTime:
                //选择日期
//                DateDialog.showDialog(this, datetime, date -> {
//                    datetime = date;
//                    tvTime.setText(date);
//                    getOrderList(cus_type);
//                });
//                break;
            case R.id.tvEndTime:
                DateStartEndDialog.showDialog(this, startTime, endTime, startTime, (startDate, endDate) -> {
                    startTime = startDate;
                    endTime = endDate;
                    tvTime.setText(startTime);
                    tvEndTime.setText(endTime);
                    getOrderList(cus_type);
                });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("member") + getLanguageValue("recharge") + getLanguageValue("consumption") + getLanguageValue("record"));
        tvPointsValue.setText(getLanguageValue("integral"));
        tvUserValue.setText(getLanguageValue("operation") + getLanguageValue("people"));
    }

    private void inintData() {
    }

    private void inintView() {
        Intent intent = getIntent();
        cusId = intent.getStringExtra("cusId");
        cus_type = intent.getStringExtra("type");
        if (cus_type.equals("1")) {
            tvDate.setText(getLanguageValue("recharge") + getLanguageValue("date"));
            tvType.setText(getLanguageValue("recharge") + getLanguageValue("way"));
            tvTotal.setText(getLanguageValue("recharge") + getLanguageValue("amount"));
        } else if (cus_type.equals("3")) {
            tvDate.setText(getLanguageValue("consumption") + getLanguageValue("date"));
            tvType.setText(getLanguageValue("consumption") + getLanguageValue("way"));
            tvTotal.setText(getLanguageValue("consumption") + getLanguageValue("amount"));
        }

//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//        String t = format.format(new Date());
//        datetime = t;
//        tvTime.setText(datetime);
        startTime = DateUtils.getOldDate(0);
        endTime = startTime;
        tvTime.setText(startTime);
        tvEndTime.setText(endTime);
        // 建立数据源
        final String[] mItems = getResources().getStringArray(R.array.orderstype);
        spinnerType.setItems(mItems);
        spinnerPeople.setOnItemSelectedListener((view, position, id, item) -> {
            staffId2 = persionPersonBean.getData().get(position).getStaffId();
            getOrderList(cus_type);
        });
        spinnerType.setOnItemSelectedListener((view, position, id, item) -> {
            if (mItems[position].equals(getLanguageValue("recharge") + getLanguageValue("record"))) {
                tvDate.setText(getLanguageValue("recharge") + getLanguageValue("date"));
                tvType.setText(getLanguageValue("recharge") + getLanguageValue("way"));
                tvTotal.setText(getLanguageValue("recharge") + getLanguageValue("amount"));
                cus_type = "1";
                getOrderList(cus_type);
            } else if (mItems[position].equals(getLanguageValue("consumption") + getLanguageValue("amount"))) {
                tvDate.setText(getLanguageValue("consumption") + getLanguageValue("date"));
                tvType.setText(getLanguageValue("consumption") + getLanguageValue("way"));
                tvTotal.setText(getLanguageValue("consumption") + getLanguageValue("amount"));
                cus_type = "3";
                getOrderList(cus_type);
            }
        });
        if (cus_type.equals("3")) {
            spinnerType.setSelectedIndex(0);
        } else {
            spinnerType.setSelectedIndex(1);
        }
        listView.setOnItemClickListener((parent, view, position, id) -> {
            String consumptionType = remeberTakeBean.getData().get(position).getConsumptionType();
            if (!consumptionType.equals(getLanguageValue("storedValueCard") + getLanguageValue("recharge"))) {
                String sale_list = remeberTakeBean.getData().get(position).getSaleListUnique();
                startActivity(new Intent(this, OrderInfoActivity.class)
                        .putExtra("unique", sale_list)
                );
            }
        });
        getRemberDetail();
        gettakename();
        getOrderList(cus_type);
    }


    /**
     * 会员消费记录
     *
     * @param cus_type
     */
    private void getOrderList(String cus_type) {
        Map<String, Object> params = new HashMap<>();
        params.put("cus_unique", cusId);
        params.put("shopUnique", getShop_id());
//        params.put("datetime", datetime);
        params.put("datetime", startTime);
        params.put("endtime", endTime);
        params.put("type", 1);
        params.put("staffId", staffId2);
        params.put("cus_type", cus_type);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.ONLINE_URL + "shopUpdate/cuscheckout/queryCusConRecord.do?",
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "会员消费记录 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object;
                        int status = 2;
                        try {
                            object = new JSONObject(s);
                            status = object.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status == 1) {
                            Gson gson1 = new Gson();
                            remeberTakeBean = gson1.fromJson(s, RemeberTakeBean.class);
                            if (remeberTakeBean.getData() != null) {
                                member.clear();
                                for (int i = 0; i < remeberTakeBean.getData().size(); i++) {
                                    member.add(remeberTakeBean.getData().get(i));
                                }
                                remeberTakeAdapter = new RemeberTakeAdapter(getApplicationContext(), member);
                                listView.setAdapter(remeberTakeAdapter);
                                remeberTakeAdapter.notifyDataSetChanged();
                            }
                        } else if (status != 1) {
                            member.clear();
                            remeberTakeAdapter = new RemeberTakeAdapter(getApplicationContext(), member);
                            listView.setAdapter(remeberTakeAdapter);
                            remeberTakeAdapter.notifyDataSetChanged();
                        }
                    }
                });
    }

    /**
     * 会员消费操作人
     */
    private void gettakename() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getpersonlistTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "会员消费操作人 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object2;
                        int status2 = 2;
                        try {
                            object2 = new JSONObject(s);
                            status2 = object2.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status2 == 1) {
                            Gson gson1 = new Gson();
                            persionPersonBean = gson1.fromJson(s, PersionPersonBean.class);
                            List<String> PersionAllName = new ArrayList<>();
                            for (int t = 0; t < persionPersonBean.getData().size(); t++) {
                                PersionAllName.add(persionPersonBean.getData().get(t).getStaffName());
                            }
                            int t = persionPersonBean.getData().size();
                            if (t > 0) {
                                String[] classifyNameArr = PersionAllName.toArray(new String[t]);
                                spinnerPeople.setItems(classifyNameArr);
                            }
                        }
                    }
                });
    }

    /**
     * 会员详情接口
     */
    public void getRemberDetail() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("cus_unique", cusId);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.ONLINE_URL + "shopUpdate/cuscheckout/findCusById.do?",
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "会员详情 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object5;
                        int status5 = 2;
                        try {
                            object5 = new JSONObject(s);
                            status5 = object5.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status5 == 1) {
                            Gson gson1 = new Gson();
                            remberbean = gson1.fromJson(s, RemberDetailBean.class);
                            Glide.with(MemberTakeActivity.this)
                                    .load(StringUtils.handledImgUrl(remberbean.getData().getCusHeadPath()))
                                    .apply(new RequestOptions().circleCrop())
                                    .into(ivHead);
                            tvName.setText(remberbean.getData().getCusName());
                            tvUnique.setText(remberbean.getData().getCusUnique());
                            tvPoints.setText(getLanguageValue("integral") + ":" + remberbean.getData().getCusPoints());
                            tvBalance.setText(getLanguageValue("balance") + ":" + remberbean.getData().getCus_balance());
                        }
                    }
                });
    }

}
