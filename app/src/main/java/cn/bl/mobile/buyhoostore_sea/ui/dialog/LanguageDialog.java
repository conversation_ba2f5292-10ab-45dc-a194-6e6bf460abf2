package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.utils.MultiLanguageUtils;

/**
 * Describe:dialog（语言切换）
 * Created by jingang on 2023/6/17
 */
@SuppressLint("NonConstantResourceId")
public class LanguageDialog extends BaseDialog {
    @BindView(R.id.ivDialogLanguage0)
    ImageView ivLanguage0;
    @BindView(R.id.ivDialogLanguage1)
    ImageView ivLanguage1;
    @BindView(R.id.ivDialogLanguage2)
    ImageView ivLanguage2;
    @BindView(R.id.ivDialogLanguage3)
    ImageView ivLanguage3;
    @BindView(R.id.ivDialogLanguage4)
    ImageView ivLanguage4;
    @BindView(R.id.ivDialogLanguage5)
    ImageView ivLanguage5;
    @BindView(R.id.ivDialogLanguage6)
    ImageView ivLanguage6;

    private static Activity mActivity;
    private String language;
    private int type;//0.中文 1.英文 2.泰文 3.韩文 4.日文 5.马来文 6.和萨克文

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        LanguageDialog.listener = listener;
        LanguageDialog dialog = new LanguageDialog(activity);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public LanguageDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_language);
        ButterKnife.bind(this);
        setCancelable(true);
        language = SharedPreferencesUtil.getInstantiation(context).getString("", Constants.SP_LANGUAGE);
        Log.e("111111", "language = " + language + " country = " + SharedPreferencesUtil.getInstantiation(context).getString("", Constants.SP_COUNTRY));
        if (TextUtils.isEmpty(language)) {
            type = 0;
            clearSelect();
            ivLanguage0.setSelected(true);
        } else {
            switch (language) {
                case "en":
                    type = 1;
                    clearSelect();
                    ivLanguage1.setSelected(true);
                    break;
                case "th":
                    type = 2;
                    clearSelect();
                    ivLanguage2.setSelected(true);
                    break;
                case "ko":
                    type = 3;
                    clearSelect();
                    ivLanguage3.setSelected(true);
                    break;
                case "ja":
                    type = 4;
                    clearSelect();
                    ivLanguage4.setSelected(true);
                    break;
                case "ms":
                    type = 5;
                    clearSelect();
                    ivLanguage5.setSelected(true);
                    break;
                case "kk":
                    type = 6;
                    clearSelect();
                    ivLanguage6.setSelected(true);
                    break;
                default:
                    type = 0;
                    clearSelect();
                    ivLanguage0.setSelected(true);
                    break;
            }
        }
    }

    @OnClick({R.id.ivDialogClose,
            R.id.relDialogLanguage0, R.id.relDialogLanguage1, R.id.relDialogLanguage2,
            R.id.relDialogLanguage3, R.id.relDialogLanguage4, R.id.relDialogLanguage5,
            R.id.relDialogLanguage6,
            R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.relDialogLanguage0:
                //中文
                if (type != 0) {
                    type = 0;
                    clearSelect();
                    ivLanguage0.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage1:
                //英文
                if (type != 1) {
                    type = 1;
                    clearSelect();
                    ivLanguage1.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage2:
                //泰文
                if (type != 2) {
                    type = 2;
                    clearSelect();
                    ivLanguage2.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage3:
                //韩文
                if (type != 3) {
                    type = 3;
                    clearSelect();
                    ivLanguage3.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage4:
                //日文
                if (type != 4) {
                    type = 4;
                    clearSelect();
                    ivLanguage4.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage5:
                //马来文
                if (type != 5) {
                    type = 5;
                    clearSelect();
                    ivLanguage5.setSelected(true);
                }
                break;
            case R.id.relDialogLanguage6:
                //和萨克文
                if (type != 6) {
                    type = 6;
                    clearSelect();
                    ivLanguage6.setSelected(true);
                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                String language, area;
                switch (type) {
                    case 1:
                        language = "en";
                        area = "US";
                        break;
                    case 2:
                        language = "th";
                        area = "TH";
                        break;
                    case 3:
                        language = "ko";
                        area = "KR";
                        break;
                    case 4:
                        language = "ja";
                        area = "JP";
                        break;
                    case 5:
                        language = "ms";
                        area = "MY";
                        break;
                    case 6:
                        language = "kk";
                        area = "KZ";
                        break;
                    default:
                        language = "zh";
                        area = "ZH";
                        break;
                }
                if (listener != null) {
                    MultiLanguageUtils.changeLanguage(mActivity, language, area);
                    listener.onLanguageClick(language, area);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 清除选择
     */
    private void clearSelect() {
        ivLanguage0.setSelected(false);
        ivLanguage1.setSelected(false);
        ivLanguage2.setSelected(false);
        ivLanguage3.setSelected(false);
        ivLanguage4.setSelected(false);
        ivLanguage5.setSelected(false);
        ivLanguage6.setSelected(false);
    }

    private static MyListener listener;

    public interface MyListener {
        void onLanguageClick(String language, String area);
    }
}
