package cn.bl.mobile.buyhoostore_sea.ui.farm.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.ShopToolData;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.BusinessActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierShiftActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierShiftHandActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.MemberActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.FeedBackActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.StockActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.MsgActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.FarmMainActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SaleStatisticsActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SettingActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.BankActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.GoodsAdjustActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ShiftRecordActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.activity.GouXActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ShopToolCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity.AggregationActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity.AggregationApplyStateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.bean.AggregatePayStatusData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.AllotActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.BalanceDifferenceActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.activity.RestockActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierActivity;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-我的
 * Created by jingang on 2023/6/12
 */
@SuppressLint("NonConstantResourceId")
public class FarmMeFragment extends BaseFragment {
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvAddress)
    TextView tvAddress;
    @BindView(R.id.rvMenu)
    RecyclerView rvMenu;

    private String shopName;

    //我的工具
    private ShopToolCateAdapter toolCateAdapter;
    private List<ShopToolData> toolList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static FarmMeFragment newInstance() {
        FarmMeFragment fragment = new FarmMeFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int getLayoutId() {
        return R.layout.fm_me_farm;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @OnClick({R.id.ivHead, R.id.ivMenu0, R.id.ivMenu1, R.id.ivMenu2})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivHead:
                //店铺设置
                startActivity(new Intent(getActivity(), SettingActivity.class)
                        .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                );
                break;
            case R.id.ivMenu0:
                //码单中心
                break;
            case R.id.ivMenu1:
                //订单中心
                break;
            case R.id.ivMenu2:
                //退货中心
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvMenu.setLayoutManager(new LinearLayoutManager(getActivity()));
        toolCateAdapter = new ShopToolCateAdapter(getActivity());
        rvMenu.setAdapter(toolCateAdapter);
        toolCateAdapter.setOnItemChildClickListener((view, position, position_child) -> {
            choosemoudle(toolList.get(position).getList().get(position_child).getModularNum());
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        getShopInfo();
        getModuleV2();
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(ShopInfoResponseModel data) {
        if (data == null) {
            return;
        }
        if (data.getData() == null) {
            return;
        }
        shopName = data.getData().getShopName();
        Glide.with(getActivity())
                .load(StringUtils.handledImgUrl(data.getData().getShopImagePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_default_head))
                .into(ivHead);
        tvShopName.setText(shopName);
        tvAddress.setText(data.getData().getShopAddress());
    }

    /**
     * 选择跳转的界面
     */
    public void choosemoudle(int modularNum) {
        switch (modularNum) {
            case 2:
                //经营统计
                goToActivity(BusinessActivity.class);
                break;
            case 3:
                //库存盘点
                goToActivity(PanActivity.class);
                break;
            case 4:
                //会员管理
                goToActivity(MemberActivity.class);
                break;
            case 5:
                //库存
                goToActivity(StockActivity.class);
                break;
            case 6:
                //销售统计
                goToActivity(SaleStatisticsActivity.class);
                break;
            case 7:
                //旧版出入库
                startActivity(new Intent(getActivity(), ScanActivity.class)
                        .putExtra("type", 1)
                );
                break;
            case 10:
                //消息中心
                startActivity(new Intent(getActivity(), MsgActivity.class)
                        .putExtra("startDate", "")
                );
                break;
            case 13:
                //交接班
                goToActivity(ShiftRecordActivity.class);
                break;
            case 14:
                //意见反馈
                goToActivity(FeedBackActivity.class);
                break;
            case 15:
                //上传二维码（改为聚合码）
                getAggregatePayStatus();
                break;
            case 17:
                //自定义分类
                goToActivity(GoodsCateActivity.class);
                break;
            case 18:
                //供货商管理
                goToActivity(SupplierActivity.class);
                break;
            case 1:
            case 21:
                //移动收银
                if (TextUtils.isEmpty(android.os.Build.MODEL)) {
                    if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
                        goToActivity(CashierShiftActivity.class);
                    } else {
                        PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
                    }
                } else {
                    if (android.os.Build.MODEL.startsWith("LANDI")) {
                        //手持设置
                        goToActivity(CashierShiftHandActivity.class);
                    } else {
                        goToActivity(CashierShiftActivity.class);
                    }
                }
                break;
            case 26:
                //账户管理（银行卡列表）
                goToActivity(BankActivity.class);
                break;
            case 27:
                //补货调价
                goToActivity(GoodsAdjustActivity.class);
                break;
            case 28:
                //余额（差价）
                goToActivity(BalanceDifferenceActivity.class);
                break;
            case 29:
                //商品调拨
                goToActivity(AllotActivity.class);
                break;
            case 30:
                //农批
                goToActivity(FarmMainActivity.class);
                break;
            case 31:
                //补货单
                goToActivity(RestockActivity.class);
                break;
            case 32:
                //采购单
                goToActivity(GouXActivity.class);
                break;
            default:
                showMessage("敬请期待");
                break;
        }

    }

    /**
     * 获取店铺信息
     */
    private void getShopInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getShopInfoUrlTWO(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        ShopInfoResponseModel data = new Gson().fromJson(s, ShopInfoResponseModel.class);
                        if (data.getStatus() == 1) {
                            setUI(data);
                        }
                    }
                });
    }

    /**
     * 查询主页的模块信息_v2
     */
    private void getModuleV2() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getString(R.string.promptcontent));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("showType", 1);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getmodule_v2(),
                map,
                ShopToolData.class,
                new RequestListListener<ShopToolData>() {
                    @Override
                    public void onResult(List<ShopToolData> list) {
                        toolList.clear();
                        toolList.addAll(list);
                        toolCateAdapter.setDataList(toolList);
                    }
                });
    }

    /**
     * 查询聚合码审核状态
     */
    private void getAggregatePayStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getAggregatePayStatus(),
                map,
                AggregatePayStatusData.DataBean.class,
                new RequestListener<AggregatePayStatusData.DataBean>() {
                    @Override
                    public void success(AggregatePayStatusData.DataBean data) {
                        if (data == null) {
                            return;
                        }
                        //聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
                        switch (data.getAggregateAuditStatus()) {
                            case 0:
                            case 2:
                                startActivity(new Intent(getActivity(), AggregationActivity.class)
                                        .putExtra("status", data.getAggregateAuditStatus())
                                        .putExtra("indexImage", data.getAggregateIndexImage())
                                        .putExtra("payImage", data.getAggregatePayImage())
                                        .putExtra("guideImage", data.getNotLegalGuideImage())
                                        .putExtra("helibao", data.getHelibaoAuthBookUrl())
                                        .putExtra("ruiyinxin", data.getRuiyinxinAuthBookUrl())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                );
                                break;
                            default:
                                startActivity(new Intent(getActivity(), AggregationApplyStateActivity.class)
                                        .putExtra("status", data.getAggregateAuditStatus())
                                        .putExtra("reason", data.getAggregateRefuseReason())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                        .putExtra("guideImage", data.getNotLegalGuideImage())
                                        .putExtra("helibao", data.getHelibaoAuthBookUrl())
                                        .putExtra("ruiyinxin", data.getRuiyinxinAuthBookUrl())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                );
                                break;
                        }
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage("因权限未开启，该功能无法使用，请去设置中开启。");
                } else {
                    goToActivity(CashierShiftActivity.class);
                }
                break;
        }
    }
}
