package cn.bl.mobile.buyhoostore_sea.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Binder;
import android.os.IBinder;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import java.util.Locale;

import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:将TextToSpeech的逻辑放入一个前台服务中运行
 * Created by jingang on 2024/6/13
 */
public class TTSForegroundService extends Service {
    private TextToSpeech mTts;
    private boolean isSuccess = false;

    public TTSForegroundService() {
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化TextToSpeech
        initializeTTS();
    }

    private void initializeTTS() {
        mTts = new TextToSpeech(this, status -> {
            Log.e("111111", "status = " + status);
            //系统语音初始化成功
            if (status == TextToSpeech.SUCCESS) {
                int languageResult = mTts.setLanguage(Locale.CHINA);
                mTts.setPitch(1.0f);//设置音调，值越大声音越尖（女生），值越小则变成男声,1.0是常规
                mTts.setSpeechRate(1.0f);
                mTts.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                    @Override
                    public void onStart(String utteranceId) {

                    }

                    @Override
                    public void onDone(String utteranceId) {

                    }

                    @Override
                    public void onError(String utteranceId) {

                    }
                });
                if (languageResult == TextToSpeech.LANG_MISSING_DATA || languageResult == TextToSpeech.LANG_NOT_SUPPORTED) {
                    //系统不支持中文播报
                    isSuccess = false;
                }
            }
        });
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 开始前台服务并设置通知
        startForegroundService();
        // 从intent中获取要朗读的文本，这里仅为示例，实际根据需求调整
        String textToSpeak = intent.getStringExtra("text");
        AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        int result = audioManager.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK);
        if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            // 现在有音频焦点，可以播放TTS
            if (isSuccess && textToSpeak != null) {
                mTts.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, null, null);
            }
        } else {
            Log.w("111111", "Audio focus request denied");
        }
        return START_STICKY;
    }

    private void startForegroundService() {
        // 创建通知渠道（针对Android 8.0及以上版本）
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            String CHANNEL_ID = "tts_channel";
            CharSequence name = "TTS Channel";
            String description = "Channel for Text to Speech service";
            int importance = NotificationManager.IMPORTANCE_DEFAULT;
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
            channel.setDescription(description);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }

        // 创建并显示前台通知
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, "tts_channel")
                .setContentTitle("Text to Speech Service")
                .setContentText("Speaking...")
                .setSmallIcon(R.mipmap.ic_logo); // 设置图标

        startForeground(1, builder.build());
    }

    @Override
    public IBinder onBind(Intent intent) {
        // 如果需要与Activity交互，可以在这里返回一个Binder实例
        return new LocalBinder();
    }

    public class LocalBinder extends Binder {
        public TTSForegroundService getService() {
            return TTSForegroundService.this;
        }
    }

    @Override
    public void onDestroy() {
        if (mTts != null) {
            mTts.stop();
            mTts.shutdown();
        }
        super.onDestroy();
    }
}
