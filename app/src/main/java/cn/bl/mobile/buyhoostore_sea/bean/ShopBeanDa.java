package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/10/23 0023.
 */
public class ShopBeanDa {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"groupId":"16572","groupName":"桶装净水","groupUnique":"100000","kindDetail":[{"kindId":16573,"kindName":"桶装净水","kindUnique":"100001"}]},{"groupId":"16574","groupName":"新鲜蔬菜","groupUnique":"110000","kindDetail":[{"kindId":16575,"kindName":"根茎类","kindUnique":"110001"},{"kindId":16576,"kindName":"果实类","kindUnique":"110002"},{"kindId":16577,"kindName":"叶菜类","kindUnique":"110003"},{"kindId":16578,"kindName":"菌类","kindUnique":"110004"}]},{"groupId":"16579","groupName":"日供水果","groupUnique":"120000","kindDetail":[{"kindId":16580,"kindName":"瓜果类","kindUnique":"120001"},{"kindId":16581,"kindName":"柑橘类","kindUnique":"120002"},{"kindId":16582,"kindName":"仁果类","kindUnique":"120003"},{"kindId":16583,"kindName":"浆果类","kindUnique":"120004"},{"kindId":16584,"kindName":"核果类","kindUnique":"120005"},{"kindId":16585,"kindName":"热带亚热带水果","kindUnique":"120006"}]},{"groupId":"16586","groupName":"食品粮油","groupUnique":"140000","kindDetail":[{"kindId":16570,"kindName":"咸菜类","kindUnique":"140009"},{"kindId":16571,"kindName":"奶类","kindUnique":"140010"},{"kindId":16587,"kindName":"主食类","kindUnique":"140001"},{"kindId":16588,"kindName":"熟食类","kindUnique":"140002"},{"kindId":16589,"kindName":"粮食","kindUnique":"140003"},{"kindId":16590,"kindName":"面食类","kindUnique":"140004"},{"kindId":16591,"kindName":"食用油","kindUnique":"140005"},{"kindId":16592,"kindName":"肉类","kindUnique":"140006"},{"kindId":16593,"kindName":"蛋类","kindUnique":"140007"},{"kindId":16594,"kindName":"厨房调料","kindUnique":"140008"}]},{"groupId":"16595","groupName":"生活用品","groupUnique":"150000","kindDetail":[{"kindId":16596,"kindName":"卫生清洁","kindUnique":"150001"},{"kindId":16597,"kindName":"纸巾类","kindUnique":"150002"},{"kindId":16598,"kindName":"保鲜类","kindUnique":"150003"},{"kindId":16599,"kindName":"衣物清洁","kindUnique":"150004"},{"kindId":16600,"kindName":"洗漱用品","kindUnique":"150005"},{"kindId":16601,"kindName":"办公用品","kindUnique":"150006"},{"kindId":16602,"kindName":"女性护理","kindUnique":"150007"},{"kindId":16603,"kindName":"纸制品","kindUnique":"150008"},{"kindId":16604,"kindName":"其他","kindUnique":"150009"},{"kindId":16643,"kindName":"餐具","kindUnique":"150010"}]},{"groupId":"16605","groupName":"方便速食","groupUnique":"130000","kindDetail":[{"kindId":16606,"kindName":"方便面","kindUnique":"130001"},{"kindId":16607,"kindName":"米粉米线","kindUnique":"130002"},{"kindId":16608,"kindName":"面包","kindUnique":"130003"},{"kindId":16609,"kindName":"火腿肠","kindUnique":"130004"},{"kindId":16610,"kindName":"八宝粥","kindUnique":"130005"}]},{"groupId":"16611","groupName":"休闲零食","groupUnique":"160000","kindDetail":[{"kindId":16612,"kindName":"糖果类","kindUnique":"160001"},{"kindId":16613,"kindName":"速食类","kindUnique":"160002"},{"kindId":16614,"kindName":"蜜饯类","kindUnique":"160003"},{"kindId":16615,"kindName":"饼干糕点类","kindUnique":"160004"},{"kindId":16616,"kindName":"膨化食品","kindUnique":"160005"},{"kindId":16617,"kindName":"口香糖","kindUnique":"160006"},{"kindId":16618,"kindName":"卤制品","kindUnique":"160007"},{"kindId":16642,"kindName":"坚果类","kindUnique":"160008"}]},{"groupId":"16619","groupName":"烟酒饮料","groupUnique":"170000","kindDetail":[{"kindId":16620,"kindName":"矿泉水","kindUnique":"170001"},{"kindId":16621,"kindName":"碳酸饮料","kindUnique":"170002"},{"kindId":16622,"kindName":"茶饮咖啡","kindUnique":"170003"},{"kindId":16623,"kindName":"功能饮料","kindUnique":"170004"},{"kindId":16624,"kindName":"冷饮","kindUnique":"170005"},{"kindId":16625,"kindName":"白酒","kindUnique":"170006"},{"kindId":16626,"kindName":"啤酒","kindUnique":"170007"},{"kindId":16627,"kindName":"果酒/红酒","kindUnique":"170008"},{"kindId":16628,"kindName":"果汁","kindUnique":"170009"},{"kindId":16629,"kindName":"乳酸饮料","kindUnique":"170010"},{"kindId":16630,"kindName":"香烟","kindUnique":"170011"}]},{"groupId":"16631","groupName":"五金电器","groupUnique":"180000","kindDetail":[{"kindId":16632,"kindName":"插座插排","kindUnique":"180001"},{"kindId":16633,"kindName":"小电器","kindUnique":"180002"},{"kindId":16634,"kindName":"五金工具","kindUnique":"180003"}]},{"groupId":"16635","groupName":"冷冻食品","groupUnique":"190000","kindDetail":[{"kindId":16636,"kindName":"鱼丸虾丸","kindUnique":"190001"},{"kindId":16637,"kindName":"水饺汤圆","kindUnique":"190002"},{"kindId":16638,"kindName":"牛羊肉","kindUnique":"190003"},{"kindId":16639,"kindName":"水产品","kindUnique":"190004"}]},{"groupId":"16640","groupName":"其他","groupUnique":"200000","kindDetail":[{"kindId":16641,"kindName":"其他","kindUnique":"200001"}]}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * groupId : 16572
         * groupName : 桶装净水
         * groupUnique : 100000
         * kindDetail : [{"kindId":16573,"kindName":"桶装净水","kindUnique":"100001"}]
         */

        private String groupId;
        private String groupName;
        private String groupUnique;
        private List<KindDetailBean> kindDetail;

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public String getGroupName() {
            return groupName;
        }

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public String getGroupUnique() {
            return groupUnique;
        }

        public void setGroupUnique(String groupUnique) {
            this.groupUnique = groupUnique;
        }

        public List<KindDetailBean> getKindDetail() {
            return kindDetail;
        }

        public void setKindDetail(List<KindDetailBean> kindDetail) {
            this.kindDetail = kindDetail;
        }

        public static class KindDetailBean {
            /**
             * kindId : 16573
             * kindName : 桶装净水
             * kindUnique : 100001
             */

            private int kindId;
            private String kindName;
            private String kindUnique;

            public int getKindId() {
                return kindId;
            }

            public void setKindId(int kindId) {
                this.kindId = kindId;
            }

            public String getKindName() {
                return kindName;
            }

            public void setKindName(String kindName) {
                this.kindName = kindName;
            }

            public String getKindUnique() {
                return kindUnique;
            }

            public void setKindUnique(String kindUnique) {
                this.kindUnique = kindUnique;
            }
        }
    }
}
