package cn.bl.mobile.buyhoostore_sea.app;

import java.util.Stack;

import android.app.Activity;
/**
 * 
 * @classname:		ActivityManager.java
 * @description:	管理所有使用的activity
 *
 * @author:			wzh
 * @version:		V1.0
 * @createdate:		2015-8-24 下午4:36:39
 * @copyright:      北京车联天下信息技术有限公司
 */
public final class ActivityManager {

	private volatile static ActivityManager sInstance = null;

	public static ActivityManager getInstance() {
		if (sInstance == null) {
			synchronized (ActivityManager.class) {
				if (sInstance == null) {
					sInstance = new ActivityManager();
				}
			}
		}

		return sInstance;

	}

	private Stack<Activity> activityStack;

	private ActivityManager() {
	}

	public void popActivity(Activity activity) {
		if (activity != null) {
			try {
				activity.finish();
				activityStack.remove(activity);
				activity = null;
			} catch (Exception e) {
			}

		}
	}

	public Activity currentActivity() {
		Activity activity = null;
		try {
			if (!activityStack.empty())
				activity = activityStack.lastElement();
		} catch (Exception e) {
		}
		return activity;
	}

	public void pushActivity(Activity activity) {
		if (activityStack == null) {
			activityStack = new Stack<Activity>();
		}
		activityStack.add(activity);

	}

	public void popAllActivityExceptOne(Class<?> cls) {
		while (true) {
			Activity activity = currentActivity();
			if (activity == null) {
				break;
			}
			if (activity.getClass().equals(cls)) {
				break;
			}
			popActivity(activity);
		}
	}
	
	public void popAllActivity() {
		while (true) {
			Activity activity = currentActivity();
			if (activity == null) {
				break;
			}
			popActivity(activity);
		}
	}


	public void clearActivities() {
        if(activityStack!=null) {
			for (Activity activity : activityStack) {
				if (!activity.isFinishing()) {
					activity.finish();
				}
			}
		}
	}


}
