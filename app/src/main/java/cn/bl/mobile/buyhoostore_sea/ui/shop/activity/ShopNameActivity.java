package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺信息-店铺名称
 * Created by jingang on 2023/10/7
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ShopNameActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String name;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_shop_name;
    }

    @Override
    public void initViews() {
        name = getIntent().getStringExtra("name");
        etName.setText(name);
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //确认
                name = etName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shop") + getLanguageValue("name"));
                    return;
                }
                postShopName();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("modification") + getLanguageValue("shop") + getLanguageValue("name"));
        etName.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shop") + getLanguageValue("name"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    /**
     * 修改店铺名称
     */
    private void postShopName() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopName", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateInfoUrlTWO(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        @SuppressLint("CommitPrefEdits")
                        SharedPreferences.Editor editor = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).edit();
                        editor.putString("shopName", name);
                        editor.commit();
                        setResult(Constants.NAME, new Intent()
                                .putExtra("name", name)
                        );
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}
