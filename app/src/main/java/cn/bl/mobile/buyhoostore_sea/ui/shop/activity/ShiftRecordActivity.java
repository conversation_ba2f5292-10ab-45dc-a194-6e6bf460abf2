package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.WebActivity;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.ShopDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.StaffDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ShiftRecordAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.ShiftRecordData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-交班记录
 * Created by jingang on 2023/8/16
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ShiftRecordActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvStaffName)
    TextView tvStaffName;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String startTime, endTime,//日期
            shopUnique,//店铺
            staffId = "-1";//收银员

    private ShiftRecordAdapter mAdapter;
    private List<ShiftRecordData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_shift_change;
    }

    @Override
    public void initViews() {
//        tvRight.setVisibility(View.VISIBLE);
//        tvRight.setText("字段说明");
        //默认筛选条件
        startTime = DateUtils.getCurrentDate();
        endTime = DateUtils.getCurrentDate();
        tvTime.setText(startTime + " " + endTime);
        shopUnique = getShop_id();
        tvShopName.setText(getShop_name());
        setAdapter();
    }

    @Override
    public void initData() {
        getShiftList();
    }

    @OnClick({R.id.ivBack, R.id.tvRight,
            R.id.linTime, R.id.linShop, R.id.linStaff})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                //字段说明
                WebActivity.toWebActivity(this, getLanguageValue("fieldDesc"), "接口");
                break;
            case R.id.linTime:
                //选择日期
                DateStartEndDialog.showDialog(this,
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            startTime = startDate;
                            endTime = endDate;
                            tvTime.setText(startTime + " " + endTime);
                            page = 1;
                            getShiftList();
                        });
                break;
            case R.id.linShop:
                //选择店铺
                ShopDialog.showDialog(this, (view1, unique, name) -> {
                    shopUnique = unique;
                    tvShopName.setText(name);
                    getShiftList();
                });
                break;
            case R.id.linStaff:
                //选择收银员
                StaffDialog.showDialog(this, shopUnique, (unique, name) -> {
                    staffId = unique;
                    tvStaffName.setText(name);
                    getShiftList();
                });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("handOverShift")+getLanguageValue("record"));
        tvStaffName.setText(getLanguageValue("whole")+getLanguageValue("cashier"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new ShiftRecordAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            dataList.get(position).setCheck(!dataList.get(position).isCheck());
            mAdapter.notifyItemChanged(position, dataList.get(position));
            recyclerView.scrollToPosition(position);
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getShiftList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getShiftList();
            }
        });
    }

    /**
     * 交接班记录
     */
    private void getShiftList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("shop_unique", shopUnique);
        map.put("sale_list_cashier", staffId);
        map.put("pageNum", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getShifts(),
                map,
                ShiftRecordData.class,
                new RequestListListener<ShiftRecordData>() {
                    @Override
                    public void onResult(List<ShiftRecordData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
