package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:商品-添加/编辑-新增规格
 * Created by jingang on 2023/5/18
 */
@SuppressLint("NonConstantResourceId")
public class GoodsSpecsActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_specs;
    }

    @Override
    public void initViews() {
        tvTitle.setText("新增规格");
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //确认
                break;
        }
    }

}
