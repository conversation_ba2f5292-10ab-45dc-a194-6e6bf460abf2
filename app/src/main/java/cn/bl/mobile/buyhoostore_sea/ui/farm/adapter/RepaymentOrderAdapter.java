package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:农批-快速还款-按单还款（适配器）
 * Created by jingang on 2023/5/30
 */
public class RepaymentOrderAdapter extends BaseAdapter<String> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RepaymentOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_repayment_order;
    }

    @Override
    public int getItemCount() {
        return 5;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {

    }
}
