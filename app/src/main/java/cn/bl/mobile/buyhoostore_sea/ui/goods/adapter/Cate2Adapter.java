package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateChildData;

/**
 * Describe: 商品分类-三级-商品页（适配器）
 * Created by jingang on 2025/5/23
 */
public class Cate2Adapter extends BaseAdapter<CateChildData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public Cate2Adapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate2;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvCate = holder.getView(R.id.tvItemCate);
        tvCate.setText(mDataList.get(position).getKindName());
        if (mDataList.get(position).isCheck()) {
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tvCate.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            tvCate.setTextColor(mContext.getResources().getColor(R.color.color_666));
        }
    }
}
