package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bin.david.form.core.SmartTable;
import com.bin.david.form.core.TableConfig;
import com.bin.david.form.data.CellInfo;
import com.bin.david.form.data.column.Column;
import com.bin.david.form.data.format.bg.BaseBackgroundFormat;
import com.bin.david.form.data.format.bg.BaseCellBackgroundFormat;
import com.bin.david.form.data.style.FontStyle;
import com.bin.david.form.utils.DensityUtils;
import com.google.gson.Gson;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.NPAccountOutInDetailSiftDialog;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.NPAccountOutInDetailBean;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.NPSiftBean;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/28.
 * Describe:
 */
public class NPAccountOutInDetailActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;

    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout listRefresh;
    private SmartTable<NPAccountOutInDetailBean> table;
    private int mPage = 1;
    private List<NPAccountOutInDetailBean> dataList = new ArrayList<>();
    private MemberBean memberBean;

    private NPSiftBean mSiftBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_account_out_in_detail;
    }

    @Override
    public void initViews() {
        tvTitle.setText("账户收支明细");
        tvRight.setText("筛选");
        tvRight.setTextColor(ContextCompat.getColor(this,R.color.green));
        Drawable drawable = ContextCompat.getDrawable(this,R.mipmap.np_sift);
        drawable.setBounds(0,0, DensityUtils.dp2px(this,20), DensityUtils.dp2px(this,20));
        tvRight.setCompoundDrawables(drawable,null,null,null);
        FontStyle.setDefaultTextSize(DensityUtils.sp2px(this, 12));
        table = findViewById(R.id.table);
        table.getConfig()
                .setShowXSequence(false)
                .setShowTableTitle(false)
                .setShowYSequence(false)
                .setColumnTitleBackground(new BaseBackgroundFormat(getResources().getColor(R.color.np_table_header_bg)))
                .setHorizontalPadding(30)
                .setVerticalPadding(30)
                .setColumnTitleVerticalPadding(30)
                .setColumnTitleStyle(new FontStyle(NPAccountOutInDetailActivity.this,15,ContextCompat.getColor(this,R.color.black)))
                .setContentCellBackgroundFormat(new BaseCellBackgroundFormat<CellInfo>() {
                    @Override
                    public int getBackGroundColor(CellInfo cellInfo) {
                        if (cellInfo.row % 2 == 1) {
                            return ContextCompat.getColor(NPAccountOutInDetailActivity.this, R.color.color_f2);
                        }
                        return TableConfig.INVALID_COLOR;
                    }

                }).setColumnCellBackgroundFormat(new BaseCellBackgroundFormat<Column>() {
                    @Override
                    public int getBackGroundColor(Column column) {
                        if ("area".equals(column.getFieldName())) {
                            return ContextCompat.getColor(NPAccountOutInDetailActivity.this, R.color.color_EEEEEE);
                        }
                        return TableConfig.INVALID_COLOR;
                    }

                    @Override
                    public int getTextColor(Column column) {
                        if ("area".equals(column.getFieldName())) {
                            return ContextCompat.getColor(NPAccountOutInDetailActivity.this, R.color.white);
                        } else {
                            return TableConfig.INVALID_COLOR;
                        }
                    }
                });
        loadData();

    }
    @OnClick({R.id.ivBack, R.id.tvRight})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                NPAccountOutInDetailSiftDialog dialog = new NPAccountOutInDetailSiftDialog(this);
                dialog.setSiftBean(mSiftBean);
                dialog.setonDialogClickListener(siftBean -> {
                    mSiftBean = siftBean;
                });
                new XPopup.Builder(this)
                        .popupAnimation(PopupAnimation.ScrollAlphaFromBottom)
                        .isDestroyOnDismiss(true)
                        .asCustom(dialog)
                        .show();
                break;
        }
    }

    private void loadData() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("pages", mPage);
        params.put("perpage", 10);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getRemember(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
//                        listRefresh.finishRefresh();
//                        listRefresh.finishLoadMore();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object;
                        int status = 2;
                        try {
                            object = new JSONObject(s);
                            status = object.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status == 1) {
                            Gson gson1 = new Gson();
                            memberBean = gson1.fromJson(s, MemberBean.class);

                            if (mPage == 1) {
                                dataList.clear();
                                int index = 0;

                                for (MemberBean.DataBean it : memberBean.getData()) {
                                    NPAccountOutInDetailBean bean = new NPAccountOutInDetailBean();
                                    bean.setCusName(it.getCusName());
                                    bean.setCusPhone(it.getCusPhone());
                                    bean.setIndex(index + "");
                                    dataList.add(bean);
                                    index += 1;
                                }
                                table.setData(dataList);
                            } else {
                                int index = 0;
                                for (MemberBean.DataBean it : memberBean.getData()) {
                                    NPAccountOutInDetailBean bean = new NPAccountOutInDetailBean();
                                    bean.setCusName(it.getCusName());
                                    bean.setCusPhone(it.getCusPhone());
                                    bean.setIndex(index + "");
                                    dataList.add(bean);
                                    index += 1;
                                }
                                table.addData(dataList, true);
                                table.getMatrixHelper().flingBottom(200);
                                table.getMatrixHelper().flingLeft(200);
                            }



                            } else {
                            ToastUtil.showToast(NPAccountOutInDetailActivity.this, memberBean.getMsg());
                        }
                    }
                });
    }

}
