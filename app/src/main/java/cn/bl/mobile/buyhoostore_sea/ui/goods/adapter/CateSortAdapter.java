package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;

/**
 * Describe:商品-分类拍讯（适配器）
 * Created by jingang on 2023/3/15
 */
public class CateSortAdapter extends BaseAdapter<CateData> {
    public CateSortAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_sort;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvTitle = holder.getView(R.id.tvItemTitle);
        LinearLayout linCate = holder.getView(R.id.linItemCate);
        TextView tvCate = holder.getView(R.id.tvItemCate);
        View vCate = holder.getView(R.id.vItemCate);
        tvTitle.setText(getLanguageValue("more") + getLanguageValue("classification"));

        tvCate.setText(mDataList.get(position).getGroupName());
//        if (mDataList.get(position).getGroupName().equals("更多分类")) {
        if (mDataList.get(position).getGroupName().equals(getLanguageValue("more") + getLanguageValue("classification"))) {
            tvTitle.setVisibility(View.VISIBLE);
            linCate.setVisibility(View.GONE);
            vCate.setVisibility(View.GONE);
        } else {
            tvTitle.setVisibility(View.GONE);
            linCate.setVisibility(View.VISIBLE);
            vCate.setVisibility(View.VISIBLE);
        }
    }
}
