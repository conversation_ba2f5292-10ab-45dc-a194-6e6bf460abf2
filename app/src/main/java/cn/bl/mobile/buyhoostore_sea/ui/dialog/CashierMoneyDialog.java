package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.DecimalDigitsInputFilter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.CashierMoneyData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（宁宇收银-快捷金额设置）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class CashierMoneyDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogNameValue)
    TextView tvNameValue;
    @BindView(R.id.etDialogName)
    EditText etName;
    @BindView(R.id.tvDialogDel)
    TextView tvDel;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    private static Activity mActivity;
    private static int id, position;
    private static double money;

    public static void showDialog(Activity context, CashierMoneyData data, int position, MyListener listener) {
        CashierMoneyDialog.mActivity = context;
        if (data != null) {
            CashierMoneyDialog.id = data.getId();
            CashierMoneyDialog.money = data.getAmountMoney();
            CashierMoneyDialog.position = position;
        } else {
            CashierMoneyDialog.id = 0;
            CashierMoneyDialog.money = 0;
        }
        CashierMoneyDialog.listener = listener;
        CashierMoneyDialog dialog = new CashierMoneyDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CashierMoneyDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_cashier_money);
        ButterKnife.bind(this);
        tvNameValue.setText(getLanguageValue("fastAmount"));
        etName.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("fastAmount"));
        tvDel.setText(getLanguageValue("delete"));
        tvConfirm.setText(getLanguageValue("preservation"));
        etName.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        if (money > 0) {
            tvTitle.setText(getLanguageValue("editor")+getLanguageValue("amount"));
            etName.setText(DFUtils.getNum4(money));
            tvDel.setVisibility(View.VISIBLE);
        } else {
            tvTitle.setText(getLanguageValue("add")+getLanguageValue("amount"));
            etName.setText("");
            tvDel.setVisibility(View.GONE);
        }
        etName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                etName.setSelection(etName.getText().length());
                if (TextUtils.isEmpty(s.toString().trim())) {
                    return;
                }
                money = Double.parseDouble(s.toString().trim());
                if (money > Constants.CASHIER_MAX_MONEY) {
                    CashierMoneyDialog.money = Constants.CASHIER_MAX_MONEY;
                    etName.setText(DFUtils.getNum4(Constants.CASHIER_MAX_MONEY));
                }
            }
        });
        showSoftInput();
    }

    @OnClick({R.id.ivDialogClose, R.id.tvDialogDel, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogDel:
                IAlertDialog.showDialog(mActivity,
                        getLanguageValue("confirmDeleteFastAmountSet")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postConfigDel(id, position);
                        });
                break;
            case R.id.tvDialogConfirm:
                //保存
                if (money <= 0) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("fastAmount"));
                    return;
                }
                postConfigEdit(id, money);
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    @Override
    public void dismiss() {
        View view = getCurrentFocus();
        if (view instanceof TextView) {
            InputMethodManager mInputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.RESULT_UNCHANGED_SHOWN);
        }
        super.dismiss();
    }

    /**
     * 显示软键盘
     */
    private void showSoftInput() {
        etName.clearFocus();
        etName.requestFocus();
        // 获取InputMethodManager实例
        InputMethodManager imm = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
        // 显示软键盘
        etName.postDelayed(() -> {
            imm.showSoftInput(etName, InputMethodManager.SHOW_IMPLICIT);
        }, 100);
    }

    /**
     * 快捷金额配置新增/编辑
     *
     * @param id
     * @param amountMoney
     */
    private void postConfigEdit(int id, double amountMoney) {
        showDialog();
        hideSoftInput(mActivity);
        String url;
        Map<String, Object> map = new HashMap<>();
        if (id == 0) {
            //新增
            url = ZURL.getShopQuickPayConfigAdd();
            map.put("shopUnique", getShop_id());
        } else {
            //编辑
            url = ZURL.getShopQuickPayConfigEdit();
            map.put("id", id);
        }
        map.put("staffId", getStaff_id());
        map.put("amountMoney", amountMoney);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        if (listener != null) {
                            listener.onEditClick();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 快捷金额配置删除
     *
     * @param id
     * @param position
     */
    private void postConfigDel(int id, int position) {
        showDialog();
        hideSoftInput(mActivity);
        Map<String, Object> map = new HashMap<>();
        map.put("staffId", getStaff_id());
        map.put("id", id);
        map.put("validStatus", 1);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getShopQuickPayConfigDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        if (listener != null) {
                            listener.onDelClick(position);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }


    private static MyListener listener;

    public interface MyListener {
        /**
         * 新增/编辑
         */
        void onEditClick();

        /**
         * 删除
         *
         * @param position
         */
        void onDelClick(int position);
    }
}
