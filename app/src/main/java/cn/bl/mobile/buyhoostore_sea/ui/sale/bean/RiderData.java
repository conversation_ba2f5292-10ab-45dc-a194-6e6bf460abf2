package cn.bl.mobile.buyhoostore_sea.ui.sale.bean;

import java.io.Serializable;

/**
 * Describe:
 * Created by jingang on 2023/5/24
 */
public class RiderData implements Serializable {
    /**
     * shop_courier_id : 11
     * courier_name : 张三
     * courier_phone : 18054589992
     */
    private int id;
    private int shop_courier_id;
    private String courier_name;
    private String courier_phone;
    private boolean check;

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getShop_courier_id() {
        return shop_courier_id;
    }

    public void setShop_courier_id(int shop_courier_id) {
        this.shop_courier_id = shop_courier_id;
    }

    public String getCourier_name() {
        return courier_name;
    }

    public void setCourier_name(String courier_name) {
        this.courier_name = courier_name;
    }

    public String getCourier_phone() {
        return courier_phone;
    }

    public void setCourier_phone(String courier_phone) {
        this.courier_phone = courier_phone;
    }
}
