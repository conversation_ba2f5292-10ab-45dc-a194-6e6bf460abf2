package cn.bl.mobile.buyhoostore_sea.bean;
import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class BaseBean<T> implements Serializable {
    @SerializedName("data")
    private List<T> data = new ArrayList<>();
    @SerializedName("msg")
    private String msg = "";
    @SerializedName("object")
    private Object objectX = new Object();
    @SerializedName("pageCount")
    private Object pageCount = new Object();
    @SerializedName("pageIndex")
    private Object pageIndex = new Object();
    @SerializedName("pageSize")
    private Object pageSize = new Object();
    @SerializedName("redundant")
    private Object redundant = new Object();
    @SerializedName("rows")
    private Object rows = new Object();
    @SerializedName("status")
    private int status = 0;
    @SerializedName("total")
    private Object total = new Object();

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getObjectX() {
        return objectX;
    }

    public void setObjectX(Object objectX) {
        this.objectX = objectX;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }
}
