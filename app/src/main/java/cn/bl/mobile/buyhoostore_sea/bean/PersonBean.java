package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Created by Administrator on 2017/9/25 0025.
 */
public class PersonBean {


    /**
     * status : 1
     * msg : 登录成功！
     * data : {"phoneType":0,"powerSupplier":1,"powerPur":1,"powerManager":1,"powerKind":1,"staffAccount":"***********","shopName":"测试店铺1","powerName":1,"powerDelete":1,"powerPrice":1,"powerAdd":1,"staffProtrait":"image/public/默认管理员头像.jpg","powerCount":1,"shopUnique":*************,"staffName":"测试","powerInprice":1,"staffPhone":"***********","powerChange":1,"managerUnique":"*************","staffPosition":3}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * phoneType : 0
         * powerSupplier : 1
         * powerPur : 1
         * powerManager : 1
         * powerKind : 1
         * staffAccount : ***********
         * shopName : 测试店铺1
         * powerName : 1
         * powerDelete : 1
         * powerPrice : 1
         * powerAdd : 1
         * staffProtrait : image/public/默认管理员头像.jpg
         * powerCount : 1
         * shopUnique : *************
         * staffName : 测试
         * powerInprice : 1
         * staffPhone : ***********
         * powerChange : 1
         * managerUnique : *************
         * staffPosition : 3
         */

        private int phoneType;
        private int powerSupplier;
        private int powerPur;
        private int powerManager;
        private int powerKind;
        private String staffAccount;
        private String shopName;
        private int powerName;
        private int powerDelete;
        private int powerPrice;
        private int powerAdd;
        private String staffProtrait;
        private int powerCount;
        private long shopUnique;
        private String staffName;
        private int powerInprice;
        private String staffPhone;
        private int powerChange;
        private String managerUnique;
        private int staffPosition;

        public int getPhoneType() {
            return phoneType;
        }

        public void setPhoneType(int phoneType) {
            this.phoneType = phoneType;
        }

        public int getPowerSupplier() {
            return powerSupplier;
        }

        public void setPowerSupplier(int powerSupplier) {
            this.powerSupplier = powerSupplier;
        }

        public int getPowerPur() {
            return powerPur;
        }

        public void setPowerPur(int powerPur) {
            this.powerPur = powerPur;
        }

        public int getPowerManager() {
            return powerManager;
        }

        public void setPowerManager(int powerManager) {
            this.powerManager = powerManager;
        }

        public int getPowerKind() {
            return powerKind;
        }

        public void setPowerKind(int powerKind) {
            this.powerKind = powerKind;
        }

        public String getStaffAccount() {
            return staffAccount;
        }

        public void setStaffAccount(String staffAccount) {
            this.staffAccount = staffAccount;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public int getPowerName() {
            return powerName;
        }

        public void setPowerName(int powerName) {
            this.powerName = powerName;
        }

        public int getPowerDelete() {
            return powerDelete;
        }

        public void setPowerDelete(int powerDelete) {
            this.powerDelete = powerDelete;
        }

        public int getPowerPrice() {
            return powerPrice;
        }

        public void setPowerPrice(int powerPrice) {
            this.powerPrice = powerPrice;
        }

        public int getPowerAdd() {
            return powerAdd;
        }

        public void setPowerAdd(int powerAdd) {
            this.powerAdd = powerAdd;
        }

        public String getStaffProtrait() {
            return staffProtrait;
        }

        public void setStaffProtrait(String staffProtrait) {
            this.staffProtrait = staffProtrait;
        }

        public int getPowerCount() {
            return powerCount;
        }

        public void setPowerCount(int powerCount) {
            this.powerCount = powerCount;
        }

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public int getPowerInprice() {
            return powerInprice;
        }

        public void setPowerInprice(int powerInprice) {
            this.powerInprice = powerInprice;
        }

        public String getStaffPhone() {
            return staffPhone;
        }

        public void setStaffPhone(String staffPhone) {
            this.staffPhone = staffPhone;
        }

        public int getPowerChange() {
            return powerChange;
        }

        public void setPowerChange(int powerChange) {
            this.powerChange = powerChange;
        }

        public String getManagerUnique() {
            return managerUnique;
        }

        public void setManagerUnique(String managerUnique) {
            this.managerUnique = managerUnique;
        }

        public int getStaffPosition() {
            return staffPosition;
        }

        public void setStaffPosition(int staffPosition) {
            this.staffPosition = staffPosition;
        }
    }
}
