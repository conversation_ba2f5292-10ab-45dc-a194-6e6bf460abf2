package cn.bl.mobile.buyhoostore_sea.bean;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class RefundOrderDetailBean {
    @SerializedName("data")
    private Data data=new Data();
    @SerializedName("msg")
    private String msg;
    @SerializedName("object")
    private Object objectX = new Object();
    @SerializedName("pageCount")
    private Object pageCount = new Object();
    @SerializedName("pageIndex")
    private Object pageIndex = new Object();
    @SerializedName("pageSize")
    private Object pageSize = new Object();
    @SerializedName("redundant")
    private Object redundant = new Object();
    @SerializedName("rows")
    private Object rows = new Object();
    @SerializedName("status")
    private int status;
    @SerializedName("total")
    private Object total = new Object();

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getObjectX() {
        return objectX;
    }

    public void setObjectX(Object objectX) {
        this.objectX = objectX;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public static class Data {
        @SerializedName("actuallyReceived")
        private double actuallyReceived;
        @SerializedName("coupon_amount")
        private double couponAmount;
        @SerializedName("dateTime")
        private String dateTime;
        @SerializedName("delivery_type")
        private int deliveryType;
        @SerializedName("delivery_status")
        private String deliveryStatus;
        @SerializedName("handleState")
        private Object handleState = new Object();
        @SerializedName("handleStateCode")
        private int handleStateCode;
        @SerializedName("detailList")
        private List<RefundGoodsBean> detailList = new ArrayList<>();
        @SerializedName("machineNum")
        private int machineNum;
        @SerializedName("payDetailList")
        private List<PayMethod> payDetailList = new ArrayList<>();
        @SerializedName("peisong_money")
        private double peisongMoney;
        @SerializedName("beans_money")
        private double beansMoney;
        @SerializedName("card_deduction")
        private double cardDeduction;
        @SerializedName("receiptDateTime")
        private String receiptDateTime;
        @SerializedName("saleListAddress")
        private String saleListAddress;
        @SerializedName("saleListCashier")
        private int saleListCashier;
        @SerializedName("saleListDelfee")
        private double saleListDelfee;
        @SerializedName("saleListId")
        private int saleListId;
        @SerializedName("saleListName")
        private String saleListName;
        @SerializedName("saleListNumber")
        private int saleListNumber;
        @SerializedName("saleListPayment")
        private String saleListPayment;
        @SerializedName("saleListPaymentCode")
        private int saleListPaymentCode;
        @SerializedName("saleListPhone")
        private String saleListPhone;
        @SerializedName("saleListPur")
        private double saleListPur;
        @SerializedName("saleListRemarks")
        private String saleListRemarks;
        @SerializedName("saleListState")
        private String saleListState;
        @SerializedName("saleListStateCode")
        private int saleListStateCode;
        @SerializedName("saleListTotal")
        private double saleListTotal;
        @SerializedName("saleListUnique") // 订单号
        private long saleListUnique;
        @SerializedName("saleType")
        private String saleType;
        @SerializedName("sendDateTime")
        private Object sendDateTime = new Object();
        @SerializedName("shipping_method")
        private int shippingMethod;
        @SerializedName("shopUnique")
        private long shopUnique;
        @SerializedName("totalCount")
        private double totalCount;
        // 申请退款总金额
        private double retListTotal;
        // 退款商品数量
        private double retListCount;
        private int retListHandlestate;
        private String retListReason;
        private String retListUnique;
        private String retListRemarks;
        private String retPayMsg;

        public double getActuallyReceived() {
            return actuallyReceived;
        }

        public void setActuallyReceived(double actuallyReceived) {
            this.actuallyReceived = actuallyReceived;
        }

        public double getCouponAmount() {
            return couponAmount;
        }

        public void setCouponAmount(double couponAmount) {
            this.couponAmount = couponAmount;
        }

        public String getDateTime() {
            return dateTime;
        }

        public void setDateTime(String dateTime) {
            this.dateTime = dateTime;
        }

        public int getDeliveryType() {
            return deliveryType;
        }

        public void setDeliveryType(int deliveryType) {
            this.deliveryType = deliveryType;
        }

        public String getDeliveryStatus() {
            return deliveryStatus;
        }

        public void setDeliveryStatus(String deliveryStatus) {
            this.deliveryStatus = deliveryStatus;
        }

        public Object getHandleState() {
            return handleState;
        }

        public void setHandleState(Object handleState) {
            this.handleState = handleState;
        }

        public int getHandleStateCode() {
            return handleStateCode;
        }

        public void setHandleStateCode(int handleStateCode) {
            this.handleStateCode = handleStateCode;
        }

        public List<RefundGoodsBean> getDetailList() {
            return detailList;
        }

        public void setDetailList(List<RefundGoodsBean> detailList) {
            this.detailList = detailList;
        }

        public int getMachineNum() {
            return machineNum;
        }

        public void setMachineNum(int machineNum) {
            this.machineNum = machineNum;
        }

        public List<PayMethod> getPayDetailList() {
            return payDetailList;
        }

        public void setPayDetailList(List<PayMethod> payDetailList) {
            this.payDetailList = payDetailList;
        }

        public double getPeisongMoney() {
            return peisongMoney;
        }

        public void setPeisongMoney(double peisongMoney) {
            this.peisongMoney = peisongMoney;
        }

        public double getBeansMoney() {
            return beansMoney;
        }

        public void setBeansMoney(double beansMoney) {
            this.beansMoney = beansMoney;
        }

        public double getCardDeduction() {
            return cardDeduction;
        }

        public void setCardDeduction(double cardDeduction) {
            this.cardDeduction = cardDeduction;
        }

        public String getReceiptDateTime() {
            return receiptDateTime;
        }

        public void setReceiptDateTime(String receiptDateTime) {
            this.receiptDateTime = receiptDateTime;
        }

        public String getSaleListAddress() {
            return saleListAddress;
        }

        public void setSaleListAddress(String saleListAddress) {
            this.saleListAddress = saleListAddress;
        }

        public int getSaleListCashier() {
            return saleListCashier;
        }

        public void setSaleListCashier(int saleListCashier) {
            this.saleListCashier = saleListCashier;
        }

        public double getSaleListDelfee() {
            return saleListDelfee;
        }

        public void setSaleListDelfee(double saleListDelfee) {
            this.saleListDelfee = saleListDelfee;
        }

        public int getSaleListId() {
            return saleListId;
        }

        public void setSaleListId(int saleListId) {
            this.saleListId = saleListId;
        }

        public String getSaleListName() {
            return saleListName;
        }

        public void setSaleListName(String saleListName) {
            this.saleListName = saleListName;
        }

        public int getSaleListNumber() {
            return saleListNumber;
        }

        public void setSaleListNumber(int saleListNumber) {
            this.saleListNumber = saleListNumber;
        }

        public String getSaleListPayment() {
            return saleListPayment;
        }

        public void setSaleListPayment(String saleListPayment) {
            this.saleListPayment = saleListPayment;
        }

        public int getSaleListPaymentCode() {
            return saleListPaymentCode;
        }

        public void setSaleListPaymentCode(int saleListPaymentCode) {
            this.saleListPaymentCode = saleListPaymentCode;
        }

        public String getSaleListPhone() {
            return saleListPhone;
        }

        public void setSaleListPhone(String saleListPhone) {
            this.saleListPhone = saleListPhone;
        }

        public double getSaleListPur() {
            return saleListPur;
        }

        public void setSaleListPur(double saleListPur) {
            this.saleListPur = saleListPur;
        }

        public String getSaleListRemarks() {
            return saleListRemarks;
        }

        public void setSaleListRemarks(String saleListRemarks) {
            this.saleListRemarks = saleListRemarks;
        }

        public String getSaleListState() {
            return saleListState;
        }

        public void setSaleListState(String saleListState) {
            this.saleListState = saleListState;
        }

        public int getSaleListStateCode() {
            return saleListStateCode;
        }

        public void setSaleListStateCode(int saleListStateCode) {
            this.saleListStateCode = saleListStateCode;
        }

        public double getSaleListTotal() {
            return saleListTotal;
        }

        public void setSaleListTotal(double saleListTotal) {
            this.saleListTotal = saleListTotal;
        }

        public long getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(long saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getSaleType() {
            return saleType;
        }

        public void setSaleType(String saleType) {
            this.saleType = saleType;
        }

        public Object getSendDateTime() {
            return sendDateTime;
        }

        public void setSendDateTime(Object sendDateTime) {
            this.sendDateTime = sendDateTime;
        }

        public int getShippingMethod() {
            return shippingMethod;
        }

        public void setShippingMethod(int shippingMethod) {
            this.shippingMethod = shippingMethod;
        }

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public double getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(double totalCount) {
            this.totalCount = totalCount;
        }

        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

        public double getRetListCount() {
            return retListCount;
        }

        public void setRetListCount(double retListCount) {
            this.retListCount = retListCount;
        }

        public int getRetListHandlestate() {
            return retListHandlestate;
        }

        public void setRetListHandlestate(int retListHandlestate) {
            this.retListHandlestate = retListHandlestate;
        }

        public String getRetListReason() {
            return retListReason;
        }

        public void setRetListReason(String retListReason) {
            this.retListReason = retListReason;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }

        public String getRetListRemarks() {
            return retListRemarks;
        }

        public void setRetListRemarks(String retListRemarks) {
            this.retListRemarks = retListRemarks;
        }

        public String getRetPayMsg() {
            return retPayMsg;
        }

        public void setRetPayMsg(String retPayMsg) {
            this.retPayMsg = retPayMsg;
        }

        public static class RefundGoodsBean {
            @SerializedName("goodsBarcode")
            private String goodsBarcode;
            @SerializedName("goodsName")
            private String goodsName;
            @SerializedName("imagePath")
            private String imagePath;
            @SerializedName("retListDetailCount")
            private double retListDetailCount;
            @SerializedName("retListDetailPrice")
            private double retListDetailPrice;
            @SerializedName("saleListDetailId")
            private int saleListDetailId;
            @SerializedName("saleListDetailPrice")
            private double saleListDetailPrice;
            @SerializedName("subTotal")
            private double subTotal;
            private String goodsUnit;

            public String getGoodsBarcode() {
                return goodsBarcode;
            }

            public void setGoodsBarcode(String goodsBarcode) {
                this.goodsBarcode = goodsBarcode;
            }

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getImagePath() {
                return imagePath;
            }

            public void setImagePath(String imagePath) {
                this.imagePath = imagePath;
            }

            public double getRetListDetailCount() {
                return retListDetailCount;
            }

            public void setRetListDetailCount(double retListDetailCount) {
                this.retListDetailCount = retListDetailCount;
            }

            public double getRetListDetailPrice() {
                return retListDetailPrice;
            }

            public void setRetListDetailPrice(double retListDetailPrice) {
                this.retListDetailPrice = retListDetailPrice;
            }

            public int getSaleListDetailId() {
                return saleListDetailId;
            }

            public void setSaleListDetailId(int saleListDetailId) {
                this.saleListDetailId = saleListDetailId;
            }

            public double getSaleListDetailPrice() {
                return saleListDetailPrice;
            }

            public void setSaleListDetailPrice(double saleListDetailPrice) {
                this.saleListDetailPrice = saleListDetailPrice;
            }

            public double getSubTotal() {
                return subTotal;
            }

            public void setSubTotal(double subTotal) {
                this.subTotal = subTotal;
            }

            public String getGoodsUnit() {
                return goodsUnit;
            }

            public void setGoodsUnit(String goodsUnit) {
                this.goodsUnit = goodsUnit;
            }
        }

        public static class PayMethod {
            @SerializedName("payTypeMsg")
            private String payTypeMsg;
            @SerializedName("payMethodStatus")
            private int payMethodStatus;
            @SerializedName("payMoney")
            private double payMoney;
            @SerializedName("payType")
            private int payType;

            public String getPayTypeMsg() {
                return payTypeMsg;
            }

            public void setPayTypeMsg(String payTypeMsg) {
                this.payTypeMsg = payTypeMsg;
            }

            public int getPayMethodStatus() {
                return payMethodStatus;
            }

            public void setPayMethodStatus(int payMethodStatus) {
                this.payMethodStatus = payMethodStatus;
            }

            public double getPayMoney() {
                return payMoney;
            }

            public void setPayMoney(double payMoney) {
                this.payMoney = payMoney;
            }

            public int getPayType() {
                return payType;
            }

            public void setPayType(int payType) {
                this.payType = payType;
            }
        }
    }
}
