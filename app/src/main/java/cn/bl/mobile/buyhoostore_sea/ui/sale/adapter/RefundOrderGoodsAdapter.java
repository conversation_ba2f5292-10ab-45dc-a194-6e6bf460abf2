package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.RefundOrderBean;

/**
 * Describe:销售订单-退款单-商品列表
 * Created by jingang on 2023/6/8
 */
public class RefundOrderGoodsAdapter extends BaseAdapter<RefundOrderBean.Detail> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RefundOrderGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_refund_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvCount = holder.getView(R.id.tvItemCount);
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvCount.setText("x" + mDataList.get(position).getRetListDetailCount());
    }
}
