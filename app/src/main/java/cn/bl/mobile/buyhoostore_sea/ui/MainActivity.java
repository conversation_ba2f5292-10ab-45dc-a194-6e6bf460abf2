package cn.bl.mobile.buyhoostore_sea.ui;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.AppOpsManager;
import android.app.Dialog;
import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.TextView;

import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.google.gson.Gson;
import com.tencent.bugly.Bugly;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SystemUtils;

import org.greenrobot.eventbus.Subscribe;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.app.ActivityManager;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.UpdateVersionModel;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.VersionDefaultDialog;
import cn.bl.mobile.buyhoostore_sea.jpush.MyReceiver;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PermissionDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.GoodsFragment;
import cn.bl.mobile.buyhoostore_sea.ui.sale.SaleFragment;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import cn.jiguang.api.utils.JCollectionAuth;
import cn.jpush.android.api.BasicPushNotificationBuilder;
import cn.jpush.android.api.CustomPushNotificationBuilder;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.MultiActionsNotificationBuilder;

/**
 * @Author: Great Han
 * @Description: 接盘添加注释：主页
 * @Date: 17:00 2019/6/13
 */
@SuppressLint("NonConstantResourceId")
public class MainActivity extends BaseActivity {
    @BindView(R.id.butGoods)
    RadioButton butGoods;
    @BindView(R.id.butSale)
    RadioButton butSale;
    @BindView(R.id.butShop)
    RadioButton butShop;

    private SaleFragment saleNewFragment;//销售（新）
    private GoodsFragment goodsFragment;//商品管理（新）
    private ShopFragment mFragment;//店铺管理
    SharedPreferences mShopSP = null;
    private FragmentManager mFm;
    private Fragment mContent;
    private int type;//0.商城 1.商品 2.销售 3.店铺

    private UpdateVersionModel mUpdateVersionModel;
    private AlertDialog mNoticeDialog;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_main;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        mShopSP = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        //设置自定义通知栏样式1
        BasicPushNotificationBuilder builder = new BasicPushNotificationBuilder(MainActivity.this);
        //指定状态栏的小图标
        builder.statusBarDrawable = R.drawable.mylogo;
        builder.notificationFlags = Notification.FLAG_AUTO_CANCEL
                | Notification.FLAG_SHOW_LIGHTS;  //设置为自动消失和呼吸灯闪烁
        builder.notificationDefaults = Notification.DEFAULT_SOUND
                | Notification.DEFAULT_VIBRATE
                | Notification.DEFAULT_LIGHTS;  // 设置为铃声、震动、呼吸灯闪烁都要
        JPushInterface.setPushNotificationBuilder(1, builder);
        //定制带按钮的Notification样式
        MultiActionsNotificationBuilder builder2 = new MultiActionsNotificationBuilder(MainActivity.this);
        //添加按钮，参数(按钮图片、按钮文字、扩展数据)
        builder2.addJPushAction(R.drawable.jpush_ic_richpush_actionbar_back, "first", "my_extra1");
        builder2.addJPushAction(R.drawable.jpush_ic_richpush_actionbar_back, "second", "my_extra2");
        builder2.addJPushAction(R.drawable.jpush_ic_richpush_actionbar_back, "third", "my_extra3");
        JPushInterface.setPushNotificationBuilder(2, builder2);

        //自定义通知栏样式3
        // 指定定制的 Notification Layout
        CustomPushNotificationBuilder builder3 = new
                CustomPushNotificationBuilder(MainActivity.this,
                R.layout.view_notification,
                R.id.icon,
                R.id.title,
                R.id.text);
        // 指定最顶层状态栏小图标
        builder3.statusBarDrawable = R.drawable.mylogo;
        // 指定下拉状态栏时显示的通知图标
        builder3.layoutIconDrawable = R.drawable.mylogo;
        JPushInterface.setPushNotificationBuilder(3, builder3);
        initView();
        initSDK();
    }

    @OnClick({R.id.butGoods, R.id.butSale, R.id.butShop})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.butGoods:
                //商品
                setStatusBar(false);
                switchContent(goodsFragment);
                break;
            case R.id.butSale:
                //销售
                setStatusBar(false);
                switchContent(saleNewFragment);
                break;
            case R.id.butShop:
                //店铺
                setStatusBar(true);
                switchContent(mFragment);
                break;
        }
    }

    @Override
    public void setText() {
        butGoods.setText(getLanguageValue("commodity"));
        butSale.setText(getLanguageValue("sale"));
        butShop.setText(getLanguageValue("shop"));
    }

    /**
     * 初始化（极光推送）
     */
    private void initSDK() {
        Log.d("TAG", "初始化SDK");
        Bugly.init(getApplicationContext(), "04c02c0cf7", false);
        //极光推送调试模式
        JPushInterface.setDebugMode(true);
        //极光推送初始化
//        JCoreInterface.setWakeEnable(getApplicationContext(),false);//关闭关联启动逻辑
        JPushInterface.init(getApplicationContext());
        JCollectionAuth.setAuth(getApplicationContext(), true);
        new Handler().postDelayed(() -> {
            updateRegistrationId(JPushInterface.getRegistrationID(getApplicationContext()));
        }, 4000);
        if (JPushInterface.isNotificationEnabled(getApplicationContext()) == 0 && (mNoticeDialog == null || !mNoticeDialog.isShowing())) {
            showSettingDialog(MainActivity.this);
        }
    }

    public void showSettingDialog(final Context mContext) {
        //提示弹窗
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        builder.setTitle("打开推送通知");
        builder.setMessage("打开通知第一时间获取相关信息");
        builder.setIcon(R.mipmap.ic_logo);
        //添加"Yes"按钮
        builder.setPositiveButton(getLanguageValue("confirm"), (dialogInterface, i) -> gotoSet(mContext));
        //添加取消
        builder.setNegativeButton(getLanguageValue("cancel"), (dialogInterface, i) -> {
//                alertDialog.cancel();
        });

        AlertDialog alertDialog = builder.create();
        alertDialog.show();
        alertDialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_POSITIVE).setTextColor(getResources().getColor(R.color.blue_1C8EFF));
        alertDialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_NEGATIVE).setTextColor(getResources().getColor(R.color.color_666));
    }

    /**
     * 打开手机设置页面
     * 假设没有开启通知权限，点击之后就需要跳转到 APP的通知设置界面，对应的Action是：Settings.ACTION_APP_NOTIFICATION_SETTINGS, 这个Action是 API 26 后增加的
     * 如果在部分手机中无法精确的跳转到 APP对应的通知设置界面，那么我们就考虑直接跳转到 APP信息界面，对应的Action是：Settings.ACTION_APPLICATION_DETAILS_SETTINGS
     */
    private static void gotoSet(Context context) {
        Intent intent = new Intent();
        if (Build.VERSION.SDK_INT >= 26) {
            // android 8.0引导
            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            intent.putExtra("android.provider.extra.APP_PACKAGE", context.getPackageName());
        } else if (Build.VERSION.SDK_INT >= 21) {
            // android 5.0-7.0
            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            intent.putExtra("app_package", context.getPackageName());
            intent.putExtra("app_uid", context.getApplicationInfo().uid);
        } else {
            // 其他
            intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
            intent.setData(Uri.fromParts("package", context.getPackageName(), null));
        }
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    //推送注册ID
    public void updateRegistrationId(String jPushId) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("registration_id", jPushId);
        params.put("registration_phone_type", 2);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateRegistrationId(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "修改推送注册ID成功！ = " + jPushId);
                    }

                    @Override
                    public void onError(String msg) {
                        Log.e(tag, "修改提送注册ID失败 = " + msg);
                    }
                });
    }

    private void initView() {
        isNotificationEnabled(getApplicationContext());
        goodsFragment = new GoodsFragment();
        saleNewFragment = new SaleFragment();
        mFragment = new ShopFragment();//店铺
        butGoods.setChecked(true);

        type = getIntent().getIntExtra("type", 0);
        switch (type) {
            case 1:
                butGoods.setChecked(true);
                setDefaultFragment(goodsFragment);
                break;
            case 2:
                butSale.setChecked(true);
                setDefaultFragment(saleNewFragment);
                break;
            case 3:
                butShop.setChecked(true);
                setDefaultFragment(mFragment);
                break;
            default:
                butGoods.setChecked(true);
                setDefaultFragment(goodsFragment);
                break;
        }
    }

    private boolean isNotificationEnabled(Context context) {
        String CHECK_OP_NO_THROW = "checkOpNoThrow";
        String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";

        @SuppressLint("WrongConstant") AppOpsManager mAppOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        ApplicationInfo appInfo = context.getApplicationInfo();
        String pkg = context.getApplicationContext().getPackageName();
        int uid = appInfo.uid;

        Class appOpsClass;
        try {
            appOpsClass = Class.forName(AppOpsManager.class.getName());
            Method checkOpNoThrowMethod = appOpsClass.getMethod(CHECK_OP_NO_THROW, Integer.TYPE, Integer.TYPE,
                    String.class);
            Field opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION);

            int value = (Integer) opPostNotificationValue.get(Integer.class);
            boolean ischoose = ((Integer) checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) == AppOpsManager.MODE_ALLOWED);
            if (ischoose == false) {
                AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                builder.setTitle(getLanguageValue("hint"));
                builder.setMessage(getLanguageValue("notificationPermissionClose") + "?");
                builder.setPositiveButton(getLanguageValue("confirm"),
                        (dialog, which) -> toSetting());
                builder.setNegativeButton(getLanguageValue("cancel"), (arg0, arg1) -> {

                });
                mNoticeDialog = builder.create();
                mNoticeDialog.show();
            }
            return ((Integer) checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) == AppOpsManager.MODE_ALLOWED);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void toSetting() {
        Intent localIntent = new Intent();
        localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (Build.VERSION.SDK_INT >= 9) {
            localIntent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
            localIntent.setData(Uri.fromParts("package", getPackageName(), null));
        } else if (Build.VERSION.SDK_INT <= 8) {
            localIntent.setAction(Intent.ACTION_VIEW);
            localIntent.setClassName("com.android.settings", "com.android.setting.InstalledAppDetails");
            localIntent.putExtra("com.android.settings.ApplicationPkgName", getPackageName());
        }
        startActivity(localIntent);
    }

    /**
     * 设置默认的fragment，即//第一次加载界面;
     */
    private void setDefaultFragment(Fragment fm) {
        mFm = getSupportFragmentManager();
        FragmentTransaction mFragmentTrans = mFm.beginTransaction();
        mFragmentTrans.add(R.id.fragment_container, fm).commit();

        mContent = fm;
        switchContent(mContent);
    }

    /**
     * 修改显示的内容 不会重新加载 *
     */
    public void switchContent(Fragment to) {
        if (mContent != to) {
            FragmentTransaction transaction = mFm.beginTransaction();
            if (!to.isAdded()) { // 先判断是否被add过
                transaction.hide(mContent).add(R.id.fragment_container, to).commit(); // 隐藏当前的fragment，add下一个到Activity中
            } else {

                transaction.hide(mContent).show(to).commit(); // 隐藏当前的fragment，显示下一个
            }
            mContent = to;
        }
    }

    /**
     * 双击退出
     */
    long exitTime = 0;

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (System.currentTimeMillis() - exitTime > 2000) {
                showMessage(getLanguageValue("pressAgainExit"));
                exitTime = System.currentTimeMillis();
            } else {
                finish();
                ActivityManager.getInstance().popAllActivity();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
        if (MyReceiver.isNewOrder) {
            switchContent(saleNewFragment);
        }
        setUpdate();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "colose":
                finish();
                break;
        }
    }

    /**
     * 设置检查更新规则：24小时检查一次
     */
    private void setUpdate() {
        long lastTime = mShopSP.getLong(Constants.SP_SHOP_KEY_LAST_SHOW_UPDATE, 0);
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTime > 1000 * 60 * 60 * 24) {
            Editor editor = mShopSP.edit();
            editor.putLong(Constants.SP_SHOP_KEY_LAST_SHOW_UPDATE, System.currentTimeMillis());
            editor.commit();
            getStorageManage();
        }
    }

    /**
     * 存储服务(所有文件访问)
     */
    private void getStorageManage() {
        if (SystemUtils.hasAllFilesAccessPermission(this)) {
            setUpdatePermission();
        } else {
            PermissionDialog.showDialog(this,
                    getLanguageValue("allFillServiceDesc"),
                    type -> {
                        if (type == 0) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                startActivity(new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION));
                            } else {
                                setUpdatePermission();
                            }
                        }
                    });
        }
    }

    /**
     * 下载所需的权限
     */
    private void setUpdatePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!Environment.isExternalStorageManager()) {
                startActivity(new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION));
            } else {
                checkUpgrade();
            }
        } else {
            if (PermissionUtils.checkPermissionsGroup(this, 3)) {
                checkUpgrade();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION, 3);
            }
        }
    }

    private void checkUpgrade() {
        HttpParams map = new HttpParams();
        map.put("app_id", "2");
        map.put("app_type", "1");
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getUpdateUrl())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e("TAG", "result = " + result);
                        mUpdateVersionModel = new Gson().fromJson(result, UpdateVersionModel.class);
                        if (mUpdateVersionModel.getStatus() == 0) {
                            if (mUpdateVersionModel.getData() == null) {
                                showMessage(mUpdateVersionModel.getMsg());
                                return null;
                            }
                            if (mUpdateVersionModel.getData().getCode() > PackageUtils.getPackageCode(MainActivity.this)) {
                                if (mUpdateVersionModel.getData().getUpdate_install() == 1) {
                                    downloadBuilder.setForceUpdateListener(() -> {
                                        finish();
                                    });
                                    return crateUIData();

                                } else {
                                    return crateUIData();
                                }
                            }
                            return null;
                        } else {
                            return null;
                        }
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {

                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
        builder.setDownloadAPKPath(this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(this);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                VersionDefaultDialog baseDialog = new VersionDefaultDialog(context, R.style.BaseDialog, R.layout.custom_download_layout);
                return baseDialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tv_progress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            VersionDefaultDialog baseDialog = new VersionDefaultDialog(context, R.style.BaseDialog, R.layout.custom_download_failed_dialog);
            return baseDialog;
        };
    }

    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            VersionDefaultDialog baseDialog = new VersionDefaultDialog(context, R.style.BaseDialog, R.layout.custom_dialog_two_layout);
            baseDialog.setCancelable(false);

            TextView textView = baseDialog.findViewById(R.id.tv_msg);
            textView.setText(versionBundle.getContent());
            TextView titleTextView = baseDialog.findViewById(R.id.tv_title);
            titleTextView.setText(versionBundle.getTitle());
            baseDialog.setCanceledOnTouchOutside(false);
            return baseDialog;
        };
    }

    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_logo)
                .setTicker("custom_ticker")
                .setContentTitle(getLanguageValue("buyhooUpgrade"))
                .setContentText(getLanguageValue("upgrading"));
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(mUpdateVersionModel.getData().getUpdate_des());
        uiData.setDownloadUrl(mUpdateVersionModel.getData().getUpdate_url());
        uiData.setContent(mUpdateVersionModel.getData().getUpdate_log());
        return uiData;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                //下载
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    checkUpgrade();
                }
                break;
        }
    }

}