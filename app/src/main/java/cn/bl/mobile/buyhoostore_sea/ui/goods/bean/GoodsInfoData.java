package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商品详情（实体类）
 * Created by jingang on 2023/8/22
 */
public class GoodsInfoData implements Serializable {
    /**
     * goodsId : 1542549482
     * goodsCount : 1.0
     * baseBarcode : 1626677469134
     * goodsPicturepath : /image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg
     * groupsName : 烟酒饮料
     * groupsUnique : 1100000
     * kindName : 烟草
     * kindUnique : 1100001
     * goodsUnit :
     * goodsBrand :
     * goodsAddress :
     * goodsRemarks :
     * goodsSold : 0.0
     * supplierUnique :
     * supplierName :
     * goodsPrice : 0.0
     * supGoodsBarcode :
     * listDetail : [{"goodsId":1542549482,"goodsBarcode":"1626677469134","goodsName":"在","goodsStandard":"","goodsUnit":"","goodsInPrice":0.03,"goodsSalePrice":0,"goodsWebSalePrice":0,"foreign_key":"1626677469134","containCount":1,"goodsContainCount":null,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"/image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg","goodsCusPrice":0,"shelfState":2,"countPresent":0,"pgoodsName":null,"pgoodsCount":null,"pgoodsUnit":null,"stock_warning_status":0,"out_stock_waring_count":0,"unsalable_count":0,"tableType":null}]
     * foreignKey : 1626677469134
     * tableType : 0
     * stockTableType : null
     * goodsChengType : 1
     */

    private int goodsId;
    private double goodsCount;//库存
    private String baseBarcode;//条码
    private String goodsPicturepath;//图片
    private String groupsName;//一级分类名称
    private String groupsUnique;//一级分类编号
    private String kindName;//二级分类名称
    private String kindUnique;//二级分类编号
    private String threeUnique;//三级分类编号
    private String threeName;//三级分类名称
    private String goodsUnit;//单位
    private String goodsBrand;//品牌
    private String goodsAddress;//产地
    private String goodsRemarks;//备注
    private double goodsSold;//销量
    private String supplierUnique;//供货商编号
    private String supplierName;//供货商名称
    private double goodsPrice;//售价
    private String supGoodsBarcode;//默认采购商品编号
    private String foreignKey;//商品包装外键
    private int tableType;//1.云库商品 2.本店商品
    private int goodsChengType;//计价类型 0.按件 1.称重
    private String goodsProd;//生产日期
    private int goodsLife;//保质期（天）
    private List<ListDetailBean> listDetail;

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getBaseBarcode() {
        return baseBarcode;
    }

    public void setBaseBarcode(String baseBarcode) {
        this.baseBarcode = baseBarcode;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGroupsName() {
        return groupsName;
    }

    public void setGroupsName(String groupsName) {
        this.groupsName = groupsName;
    }

    public String getGroupsUnique() {
        return groupsUnique;
    }

    public void setGroupsUnique(String groupsUnique) {
        this.groupsUnique = groupsUnique;
    }

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }

    public String getKindUnique() {
        return kindUnique;
    }

    public void setKindUnique(String kindUnique) {
        this.kindUnique = kindUnique;
    }

    public String getThreeUnique() {
        return threeUnique;
    }

    public void setThreeUnique(String threeUnique) {
        this.threeUnique = threeUnique;
    }

    public String getThreeName() {
        return threeName;
    }

    public void setThreeName(String threeName) {
        this.threeName = threeName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public double getGoodsSold() {
        return goodsSold;
    }

    public void setGoodsSold(double goodsSold) {
        this.goodsSold = goodsSold;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public double getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(double goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getSupGoodsBarcode() {
        return supGoodsBarcode;
    }

    public void setSupGoodsBarcode(String supGoodsBarcode) {
        this.supGoodsBarcode = supGoodsBarcode;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public int getTableType() {
        return tableType;
    }

    public void setTableType(int tableType) {
        this.tableType = tableType;
    }

    public String getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(String goodsProd) {
        this.goodsProd = goodsProd;
    }

    public int getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(int goodsLife) {
        this.goodsLife = goodsLife;
    }

    public List<ListDetailBean> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<ListDetailBean> listDetail) {
        this.listDetail = listDetail;
    }

    public static class ListDetailBean implements Serializable {
        /**
         * goodsId : 1542549482
         * goodsBarcode : 1626677469134
         * goodsName : 在
         * goodsStandard :
         * goodsUnit :
         * goodsInPrice : 0.03
         * goodsSalePrice : 0.0
         * goodsWebSalePrice : 0.0
         * foreign_key : 1626677469134
         * containCount : 1
         * goodsContainCount : null
         * goodsPromotion : 1.0
         * goodsDiscount : 1.0
         * goodsPicturepath : /image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg
         * goodsCusPrice : 0.0
         * shelfState : 2
         * countPresent : 0.0
         * pgoodsName : null
         * pgoodsCount : null
         * pgoodsUnit : null
         * stock_warning_status : 0
         * out_stock_waring_count : 0
         * unsalable_count : 0
         * tableType : null
         */

        private int goodsId;
        private String goodsBarcode;//条码
        private String goodsName;
        private String goodsStandard;//规格
        private String goodsUnit;//单位
        private double goodsInPrice;//进价
        private double goodsSalePrice;//售价
        private double goodsWebSalePrice;//网购价
        private double containCount;//包含最小单位商品数量
        private double goodsPromotion;//商品促销状态：1、促销；2、不促销
        private double goodsDiscount;//折扣
        private String goodsPicturepath;//图片
        private double goodsCusPrice;//会员价
        private int shelfState;//小程序上架状态：1、上架；2、下架
        private int pcShelfState;//收银机上架状态 1.上架 2.下架
        private double countPresent;//购满数量，为0.0时没有赠品
        private int stock_warning_status;//0:没有库存预警 1:有库存预警
        private double out_stock_waring_count;//库存预警下限
        private double unsalable_count;//库存预警上限
        private boolean check;
        private String minSaleCount;//起订量
        private double goodStockPrice;//最近入库价
        private int wholesalePriceFlg;//是否设置批发价 0.否 1.是
        private double wholesaleCount;//起批数量
        private double wholesalePrice;//批发单价
        private String goodsPosition;//货位id
        private String completePositionName;//货位名称
        private double underlinedPrice;//划线单价
        private List<GoodsWholesaleData> wholesaleList;

        public boolean isCheck() {
            return check;
        }

        public void setCheck(boolean check) {
            this.check = check;
        }

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsStandard() {
            return goodsStandard;
        }

        public void setGoodsStandard(String goodsStandard) {
            this.goodsStandard = goodsStandard;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public double getGoodsInPrice() {
            return goodsInPrice;
        }

        public void setGoodsInPrice(double goodsInPrice) {
            this.goodsInPrice = goodsInPrice;
        }

        public double getGoodsSalePrice() {
            return goodsSalePrice;
        }

        public void setGoodsSalePrice(double goodsSalePrice) {
            this.goodsSalePrice = goodsSalePrice;
        }

        public double getGoodsWebSalePrice() {
            return goodsWebSalePrice;
        }

        public void setGoodsWebSalePrice(double goodsWebSalePrice) {
            this.goodsWebSalePrice = goodsWebSalePrice;
        }

        public double getContainCount() {
            return containCount;
        }

        public void setContainCount(double containCount) {
            this.containCount = containCount;
        }

        public double getGoodsPromotion() {
            return goodsPromotion;
        }

        public void setGoodsPromotion(double goodsPromotion) {
            this.goodsPromotion = goodsPromotion;
        }

        public double getGoodsDiscount() {
            return goodsDiscount;
        }

        public void setGoodsDiscount(double goodsDiscount) {
            this.goodsDiscount = goodsDiscount;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public double getGoodsCusPrice() {
            return goodsCusPrice;
        }

        public void setGoodsCusPrice(double goodsCusPrice) {
            this.goodsCusPrice = goodsCusPrice;
        }

        public int getShelfState() {
            return shelfState;
        }

        public void setShelfState(int shelfState) {
            this.shelfState = shelfState;
        }

        public int getPcShelfState() {
            return pcShelfState;
        }

        public void setPcShelfState(int pcShelfState) {
            this.pcShelfState = pcShelfState;
        }

        public double getCountPresent() {
            return countPresent;
        }

        public void setCountPresent(double countPresent) {
            this.countPresent = countPresent;
        }

        public int getStock_warning_status() {
            return stock_warning_status;
        }

        public void setStock_warning_status(int stock_warning_status) {
            this.stock_warning_status = stock_warning_status;
        }

        public double getOut_stock_waring_count() {
            return out_stock_waring_count;
        }

        public void setOut_stock_waring_count(double out_stock_waring_count) {
            this.out_stock_waring_count = out_stock_waring_count;
        }

        public double getUnsalable_count() {
            return unsalable_count;
        }

        public void setUnsalable_count(double unsalable_count) {
            this.unsalable_count = unsalable_count;
        }

        public String getMinSaleCount() {
            return minSaleCount;
        }

        public void setMinSaleCount(String minSaleCount) {
            this.minSaleCount = minSaleCount;
        }

        public double getGoodStockPrice() {
            return goodStockPrice;
        }

        public void setGoodStockPrice(double goodStockPrice) {
            this.goodStockPrice = goodStockPrice;
        }

        public int getWholesalePriceFlg() {
            return wholesalePriceFlg;
        }

        public void setWholesalePriceFlg(int wholesalePriceFlg) {
            this.wholesalePriceFlg = wholesalePriceFlg;
        }

        public double getWholesaleCount() {
            return wholesaleCount;
        }

        public void setWholesaleCount(double wholesaleCount) {
            this.wholesaleCount = wholesaleCount;
        }

        public double getWholesalePrice() {
            return wholesalePrice;
        }

        public void setWholesalePrice(double wholesalePrice) {
            this.wholesalePrice = wholesalePrice;
        }

        public String getGoodsPosition() {
            return goodsPosition;
        }

        public void setGoodsPosition(String goodsPosition) {
            this.goodsPosition = goodsPosition;
        }

        public String getCompletePositionName() {
            return completePositionName;
        }

        public void setCompletePositionName(String completePositionName) {
            this.completePositionName = completePositionName;
        }

        public double getUnderlinedPrice() {
            return underlinedPrice;
        }

        public void setUnderlinedPrice(double underlinedPrice) {
            this.underlinedPrice = underlinedPrice;
        }

        public List<GoodsWholesaleData> getWholesaleList() {
            return wholesaleList;
        }

        public void setWholesaleList(List<GoodsWholesaleData> wholesaleList) {
            this.wholesaleList = wholesaleList;
        }
    }
}
