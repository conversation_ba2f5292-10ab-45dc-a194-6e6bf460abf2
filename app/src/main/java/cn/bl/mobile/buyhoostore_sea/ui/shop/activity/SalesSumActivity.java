package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.StaffDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.SalesSumAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.StaffData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.StatisticsTodayData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.view.CircleBarView;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-今日营业额
 * Created by jingang on 2024/5/8
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SalesSumActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvDate)
    TextView tvDate;
    @BindView(R.id.ivDate)
    ImageView ivDate;
    @BindView(R.id.linStaffName)
    LinearLayout linStaffName;
    @BindView(R.id.tvStaffName)
    TextView tvStaffName;
    @BindView(R.id.ivStaffName)
    ImageView ivStaffName;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.circleBarView)
    CircleBarView circleBarView;
    @BindView(R.id.tvSaleTotal)
    TextView tvSaleTotal;//总营业额
    @BindView(R.id.tvSaleTotalValue)
    TextView tvSaleTotalValue;
    @BindView(R.id.tvTotal)
    TextView tvTotal;//总收入
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvRefund)
    TextView tvRefund;//总退款
    @BindView(R.id.tvRefundValue)
    TextView tvRefundValue;
    @BindView(R.id.tvCount)
    TextView tvCount;//总订单数
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;

    /*线下*/
    @BindView(R.id.tvOfflineCount)
    TextView tvOfflineCount;
    @BindView(R.id.tvOfflineSaleTotal)
    TextView tvOfflineSaleTotal;
    @BindView(R.id.tvOfflineTotal)
    TextView tvOfflineTotal;
    @BindView(R.id.tvOfflineRefund)
    TextView tvOfflineRefund;
    @BindView(R.id.rvOffline)
    RecyclerView rvOffline;

    /*线上*/
    @BindView(R.id.tvOnlineCount)
    TextView tvOnlineCount;
    @BindView(R.id.tvOnlineSaleTotal)
    TextView tvOnlineSaleTotal;
    @BindView(R.id.tvOnlineTotal)
    TextView tvOnlineTotal;
    @BindView(R.id.tvOnlineRefund)
    TextView tvOnlineRefund;
    @BindView(R.id.rvOnline)
    RecyclerView rvOnline;

    private String startTime, endTime, staffId = "-1";

    //员工列表
    private List<StaffData> staffList = new ArrayList<>();

    private SalesSumAdapter offlineAdapter, onlineAdapter;
    private List<StatisticsTodayData.UnlineStatisBean.ListBean> offlineList = new ArrayList<>(),
            onlineList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_sales_sum;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        startTime = DateUtils.getOldDate(0);
        endTime = startTime;
        tvDate.setText(startTime + "~" + endTime);
//        staffId = getStaff_id();
//        tvStaffName.setText(getStaff_name());
        setAdapter();
    }

    @Override
    public void initData() {
        getStaffList();
        getStatistics();
    }

    @OnClick({R.id.ivBack, R.id.linDate, R.id.linStaffName})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linDate:
                //选择日期
                DateStartEndDialog.showDialog(this, startTime, endTime, "", (startDate, endDate) -> {
                    this.startTime = startDate;
                    this.endTime = endDate;
                    tvDate.setText(startDate + "~" + endDate);
                    getStatistics();
                });
                break;
            case R.id.linStaffName:
                //选择收银员
                StaffDialog.showDialog(this, getShop_id(), (unique, name) -> {
                    staffId = unique;
                    tvStaffName.setText(name);
                    getStatistics();
                });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("today")+getLanguageValue("turnover"));
        tvSaleTotalValue.setText(getLanguageValue("inTotal")+getLanguageValue("turnover"));
        tvTotalValue.setText(getLanguageValue("inTotal")+getLanguageValue("income"));
        tvRefundValue.setText(getLanguageValue("inTotal")+getLanguageValue("refund"));
        tvCountValue.setText(getLanguageValue("inTotal")+getLanguageValue("order")+getLanguageValue("quantity"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        offlineAdapter = new SalesSumAdapter(this);
        rvOffline.setAdapter(offlineAdapter);
        offlineAdapter.setOnItemClickListener((view, position) -> {
            if (isQuicklyClick()) {
                return;
            }
            startActivity(new Intent(this, SalesSumInfoActivity.class)
                    .putExtra("title", offlineList.get(position).getPayMethodName())
                    .putExtra("startTime", startTime)
                    .putExtra("endTime", endTime)
                    .putExtra("staffId", staffId)
                    .putExtra("payMethod", offlineList.get(position).getPayMethod())
            );
        });

        onlineAdapter = new SalesSumAdapter(this);
        rvOnline.setAdapter(onlineAdapter);
        onlineAdapter.setOnItemClickListener((view, position) -> {
            if (isQuicklyClick()) {
                return;
            }
            startActivity(new Intent(this, SalesSumInfoActivity.class)
                    .putExtra("title", onlineList.get(position).getPayMethodName())
                    .putExtra("startTime", startTime)
                    .putExtra("endTime", endTime)
                    .putExtra("staffId", staffId)
                    .putExtra("payMethod", onlineList.get(position).getPayMethod())
                    .putExtra("saleType", 1)
            );
        });

        smartRefreshLayout.setOnRefreshListener(refreshLayout -> getStatistics());
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(StatisticsTodayData data) {
        if (data == null) {
            return;
        }
        //总的
        if (data.getQueryStatisticsStatis() != null) {
            double saleTotal = data.getQueryStatisticsStatis().getSaleListTotal(),//总营业额
                    total = data.getQueryStatisticsStatis().getSaleListTotalMoney(),//总收入
                    refund = data.getQueryStatisticsStatis().getSaleListRefundMoney();//总退款
            if (refund > 0) {
                if (total > 0) {
                    circleBarView.setProgress((float) (total / saleTotal * 100));
                } else {
                    circleBarView.setProgress(100f);
                }
            } else {
                circleBarView.setProgress(100f);
            }
            tvSaleTotal.setText(DFUtils.getNum2(saleTotal));
            tvTotal.setText(DFUtils.getNum2(total));
            tvRefund.setText(DFUtils.getNum2(refund));
            tvCount.setText(String.valueOf(data.getQueryStatisticsStatis().getSaleListCount()));
        }

        //线下
        if (data.getUnlineStatis() != null) {
            if (data.getUnlineStatis().getQueryStatisticsStatis() != null) {
                tvOfflineCount.setText(getLanguageValue("offline")+getLanguageValue("collection") + data.getUnlineStatis().getQueryStatisticsStatis().getSaleListCount() + "笔");
                tvOfflineSaleTotal.setText("RM" + DFUtils.getNum2(data.getUnlineStatis().getQueryStatisticsStatis().getSaleListTotal()));
                tvOfflineTotal.setText("RM" + DFUtils.getNum2(data.getUnlineStatis().getQueryStatisticsStatis().getSaleListTotalMoney()));
                tvOfflineRefund.setText("RM" + DFUtils.getNum2(data.getUnlineStatis().getQueryStatisticsStatis().getSaleListRefundMoney()));
                offlineList.clear();
                if (data.getUnlineStatis().getList() != null) {
                    offlineList.addAll(data.getUnlineStatis().getList());
                }
                offlineAdapter.setDataList(offlineList);
            }
        }

        //线上
        if (data.getOnlineStatis() != null) {
            if (data.getOnlineStatis().getQueryStatisticsStatis() != null) {
                tvOnlineCount.setText(getLanguageValue("online")+getLanguageValue("collection") + data.getOnlineStatis().getQueryStatisticsStatis().getSaleListCount() + "笔");
                tvOnlineSaleTotal.setText("RM" + DFUtils.getNum2(data.getOnlineStatis().getQueryStatisticsStatis().getSaleListTotal()));
                tvOnlineTotal.setText("RM" + DFUtils.getNum2(data.getOnlineStatis().getQueryStatisticsStatis().getSaleListTotalMoney()));
                tvOnlineRefund.setText("Rm" + DFUtils.getNum2(data.getOnlineStatis().getQueryStatisticsStatis().getSaleListRefundMoney()));
                onlineList.clear();
                if (data.getOnlineStatis().getList() != null) {
                    onlineList.addAll(data.getOnlineStatis().getList());
                }
                onlineAdapter.setDataList(onlineList);
            }
        }

    }

    /**
     * 店铺员工查询
     */
    private void getStaffList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getpersonlistTWO(),
                map,
                StaffData.class,
                new RequestListListener<StaffData>() {
                    @Override
                    public void onResult(List<StaffData> list) {
                        staffList.clear();
                        staffList.addAll(list);
                        if (staffList.size() > 1) {
                            linStaffName.setVisibility(View.VISIBLE);
                        } else {
                            linStaffName.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        linStaffName.setVisibility(View.GONE);
                    }
                });
    }

    /**
     * 统计数据
     */
    private void getStatistics() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("staffId", staffId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getStatisticsByShop(),
                map,
                StatisticsTodayData.class,
                new RequestListener<StatisticsTodayData>() {
                    @Override
                    public void success(StatisticsTodayData data) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                    }
                });
    }
}
