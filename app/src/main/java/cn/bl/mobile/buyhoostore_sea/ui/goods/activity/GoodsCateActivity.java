package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.GoodsCateMoreDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IntroduceDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.CateIntroduceData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-分类管理  1.选择分类（用于商品转移分类）
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsCateActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivRight)
    ImageView ivRight;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvAdd)
    TextView tvAdd;

    private int type,//0.分类管理 1.选择分类
            level,//0.一级 1.二级 2.三级
            pos = -1,//一级分类列表下标
            pos1 = -1,//二级分类下标
            pos2;//三级分类下标
    private String unique,//一级分类编号
            unique1,//二级分类编号
            unique2,//三级分类编号
            name,
            name1,
            name2,
            imgId, img;

    private GoodsCateAdapter mAdapter;
    private List<CateData> dataList = new ArrayList<>();

    //分类说明
    private CateIntroduceData introduceData;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_cate;
    }

    @Override
    public void initViews() {
        type = getIntent().getIntExtra("type", 0);
        if (type == 1) {
            tvTitle.setText(getLanguageValue("choose") + getLanguageValue("classification"));
        } else {
            tvTitle.setText(getLanguageValue("classification") + getLanguageValue("administration"));
        }
        ivRight.setVisibility(View.VISIBLE);
        ivRight.setImageResource(R.mipmap.ic_question003);

        setAdapter();
    }

    @Override
    public void initData() {
        getCate();
    }

    @OnClick({R.id.ivBack, R.id.ivRight, R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivRight:
                //提示信息
                if (introduceData == null) {
                    getCateIntroduce();
                } else {
                    IntroduceDialog.showDialog(this, introduceData.getTitle(), introduceData.getContent());
                }
                break;
            case R.id.tvAdd:
                //新增一级分类
                level = 0;
                goToCateEdit(0);
                break;
        }
    }

    @Override
    public void setText() {
        tvAdd.setText(getLanguageValue("add") + getLanguageValue("level1") + getLanguageValue("classification"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsCateAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            pos = -1;
            getCate();
        });
        smartRefreshLayout.setEnableLoadMore(false);
        mAdapter.setListener(new GoodsCateAdapter.MyListener() {
            @Override
            public void onOpenClick(View view, int position) {
                //一级：展开
                dataList.get(position).setCheck(!dataList.get(position).isCheck());
                mAdapter.notifyItemChanged(position, dataList);
            }

            @Override
            public void onMoreClick(View view, int position) {
                //一级：更多
                level = 0;
                pos = position;
                unique = dataList.get(pos).getGroupUnique();
                name = dataList.get(pos).getGroupName();
                imgId = dataList.get(pos).getKindIconId();
                img = dataList.get(pos).getKindIcon();
                showDialogCateMore(dataList.get(pos).getEditType(), dataList.get(position).getValid_type());
            }

            @Override
            public void onChildItemClick(View view, int position, int position_child) {
                //二级：选择
                if (type == 1) {
                    setResult(Constants.CATE, new Intent()
                            .putExtra("unique", dataList.get(position).getKindDetail().get(position_child).getKindUnique())
                            .putExtra("name", dataList.get(position).getKindDetail().get(position_child).getKindName())
                    );
                    finish();
                }
            }

            @Override
            public void onChildOpenClick(View view, int position, int position1) {
                //二级：展开
                dataList.get(position).getKindDetail().get(position1).setCheck(!dataList.get(position).getKindDetail().get(position1).isCheck());
                mAdapter.notifyItemChanged(position, dataList);
            }

            @Override
            public void onChildMoreClick(View view, int position, int position1) {
                //二级：更多
                level = 1;
                pos = position;
                pos1 = position1;
                unique = dataList.get(pos).getGroupUnique();
                unique1 = dataList.get(pos).getKindDetail().get(pos1).getKindUnique();
                name = dataList.get(pos).getGroupName();
                name1 = dataList.get(pos).getKindDetail().get(pos1).getKindName();
                String editType = dataList.get(pos).getKindDetail().get(pos1).getEditType();
                int edit = TextUtils.isEmpty(editType) ? 1 : Integer.parseInt(editType);
                showDialogCateMore(edit, dataList.get(pos).getKindDetail().get(pos1).getValid_type());
            }

            @Override
            public void onChildChildItemClick(View view, int position, int position1, int position2) {
                //三级：选择
                if (type == 1) {
                    setResult(Constants.CATE, new Intent()
                            .putExtra("unique", dataList.get(position).getKindDetail().get(position1).getKindDetail().get(position2).getKindUnique())
                            .putExtra("name", dataList.get(position).getKindDetail().get(position1).getKindDetail().get(position2).getKindName())
                    );
                    finish();
                }
            }

            //            @Override
//            public void onChildChildEditClick(View view, int position, int position1, int position2) {
//                //三级：编辑
//                level = 2;
//                pos = position;
//                pos1 = position1;
//                pos2 = position2;
//                unique = dataList.get(pos).getGroupUnique();
//                unique1 = dataList.get(pos).getKindDetail().get(pos1).getKindUnique();
//                unique2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindUnique();
//                name = dataList.get(pos).getGroupName();
//                name1 = dataList.get(pos).getKindDetail().get(pos1).getKindName();
//                name2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindName();
//                goToCateEdit(1);
//            }
//
//            @Override
//            public void onChildChildDelClick(View view, int position, int position1, int position2) {
//                //三级：删除
//                level = 2;
//                pos = position;
//                pos1 = position1;
//                pos2 = position2;
//                unique = dataList.get(pos).getGroupUnique();
//                unique1 = dataList.get(pos).getKindDetail().get(pos1).getKindUnique();
//                unique2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindUnique();
//                name = dataList.get(pos).getGroupName();
//                name1 = dataList.get(pos).getKindDetail().get(pos1).getKindName();
//                name2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindName();
//                getGoodsCount();
//            }
            @Override
            public void onChildChildMoreClick(View view, int position, int position1, int position2) {
                //三级：更多
                level = 2;
                pos = position;
                pos1 = position1;
                pos2 = position2;
                unique = dataList.get(pos).getGroupUnique();
                unique1 = dataList.get(pos).getKindDetail().get(pos1).getKindUnique();
                unique2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindUnique();
                name = dataList.get(pos).getGroupName();
                name1 = dataList.get(pos).getKindDetail().get(pos1).getKindName();
                name2 = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getKindName();
                String editType = dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getEditType();
                int edit = TextUtils.isEmpty(editType) ? 1 : Integer.parseInt(editType);
                showDialogCateMore(edit, dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).getValid_type());
            }
        });
    }

    /**
     * 商品分类更多操作
     */
    private void showDialogCateMore(int edit, int enable) {
        GoodsCateMoreDialog.showDialog(this, level, edit, enable, new GoodsCateMoreDialog.MyListener() {
            @Override
            public void onAddClick() {
                //新增
                level++;
                goToCateEdit(0);
            }

            @Override
            public void onRenameClick() {
                //重命名
                goToCateEdit(1);
            }

            @Override
            public void onDelClick() {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("classification") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postCateDel();
                        });
            }

            @Override
            public void onEnableClick() {
                //停用
                if (enable == 1) {
                    getGoodsCount(enable);
                } else {
                    IAlertDialog.showDialog(TAG,
                            getLanguageValue("confirm") + getLanguageValue("enable") + getLanguageValue("classification") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postCateEnable(enable);
                            });
                }
            }
        });
    }

    /**
     * 跳转到分类编辑
     *
     * @param type 0.新增 1.编辑
     */
    private void goToCateEdit(int type) {
        startActivityForResult(new Intent(this, GoodsCateEditActivity.class)
                        .putExtra("type", type)
                        .putExtra("level", level)
                        .putExtra("unique", unique)
                        .putExtra("unique1", unique1)
                        .putExtra("unique2", unique2)
                        .putExtra("name", name)
                        .putExtra("name1", name1)
                        .putExtra("name2", name2)
                        .putExtra("imgId", imgId)
                        .putExtra("img", img)
                , Constants.CATE);
    }

    /**
     * 分类说明
     */
    private void getCateIntroduce() {
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getinstructions(),
                new HashMap<>(),
                CateIntroduceData.class,
                new RequestListener<CateIntroduceData>() {
                    @Override
                    public void success(CateIntroduceData data) {
                        introduceData = data;
                        IntroduceDialog.showDialog(TAG, data.getTitle(), data.getContent());
                    }
                });
    }

    /**
     * 自定义分类查询
     */
    public void getCate() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsCate(),
                params,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (pos != -1 && dataList.size() > pos) {
                            dataList.get(pos).setCheck(true);
                            if (dataList.get(pos).getKindDetail().size() > pos1 && pos1 != -1) {
                                dataList.get(pos).getKindDetail().get(pos1).setCheck(true);
                            }
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            if (pos != -1 && dataList.size() > pos) {
                                recyclerView.scrollToPosition(pos);
                            }
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 商品分类删除前，删除数量的查询
     */
    private void getGoodsCount(int enable) {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        switch (level) {
            case 1:
                params.put("goodsKindUnique", unique1);
                break;
            case 2:
                params.put("goodsKindUnique", unique2);
                break;
            default:
                params.put("goodsKindUnique", unique);
                break;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getcustomizecount(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        IAlertDialog.showDialog(TAG,
                                s,
                                getLanguageValue("confirm"),
                                (dialog, which) -> {
//                                    postCateDel();
                                    postCateEnable(enable);
                                });
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 分类操作：删除
     */
    private void postCateDel() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        switch (level) {
            case 1:
                map.put("kindUnique", unique1);
                break;
            case 2:
                map.put("kindUnique", unique2);
                break;
            default:
                map.put("kindUnique", unique);
                break;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsCateDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_CATE));
                        switch (level) {
                            case 1:
                                dataList.get(pos).getKindDetail().remove(pos1);
                                mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                break;
                            case 2:
                                dataList.get(pos).getKindDetail().get(pos1).getKindDetail().remove(pos2);
                                mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                break;
                            default:
                                dataList.remove(pos);
                                mAdapter.remove(pos);
                                if (!dataList.isEmpty()) {
                                    recyclerView.setVisibility(View.VISIBLE);
                                    linEmpty.setVisibility(View.GONE);
                                } else {
                                    recyclerView.setVisibility(View.GONE);
                                    linEmpty.setVisibility(View.VISIBLE);
                                }
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 商品分类启用/禁用
     */
    private void postCateEnable(int status) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        switch (level) {
            case 1:
                map.put("kindUnique", unique1);//分类编号
                break;
            case 2:
                map.put("kindUnique", unique2);//分类编号
                break;
            default:
                map.put("kindUnique", unique);//分类编号
                break;
        }
        map.put("validType", status == 1 ? 2 : 1);//1.启用 2.禁用
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.addcustomize(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        getCate();
//                        switch (level) {
//                            case 1:
//                                dataList.get(pos).getKindDetail().get(pos1).setValid_type(status == 1 ? 2 : 1);
//                                break;
//                            case 2:
//                                dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).setValid_type(status == 1 ? 2 : 1);
//                                break;
//                            default:
//                                dataList.get(pos).setValid_type(status == 1 ? 2 : 1);
//                                break;
//                        }
//                        mAdapter.notifyItemChanged(pos, dataList.get(pos));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.CATE:
                    //一级分类操作
                    int type = data.getIntExtra("type", 0);//0.新增 1.编辑 2.删除
                    switch (type) {
                        case 1:
                            String name = data.getStringExtra("name"),
                                    imgId = data.getStringExtra("imgId"),
                                    img = data.getStringExtra("img");
                            switch (level) {
                                case 1:
                                    if (dataList.size() > pos && pos != -1) {
                                        if (dataList.get(pos).getKindDetail().size() > pos1) {
                                            dataList.get(pos).getKindDetail().get(pos1).setKindName(name);
                                            mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                        }
                                    }
                                    break;
                                case 2:
                                    if (dataList.size() > pos && pos != -1) {
                                        if (dataList.get(pos).getKindDetail().size() > pos1) {
                                            if (dataList.get(pos).getKindDetail().get(pos1).getKindDetail().size() > pos2) {
                                                dataList.get(pos).getKindDetail().get(pos1).getKindDetail().get(pos2).setKindName(name);
                                                mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                            }
                                        }
                                    }
                                    break;
                                default:
                                    if (dataList.size() > pos && pos != -1) {
                                        dataList.get(pos).setGroupName(name);
                                        dataList.get(pos).setKindIconId(imgId);
                                        dataList.get(pos).setKindIcon(img);
                                        mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                    }
                                    break;
                            }
                            break;
                        case 2:
                            switch (level) {
                                case 1:
                                    dataList.get(pos).getKindDetail().remove(pos1);
                                    mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                    break;
                                case 2:
                                    dataList.get(pos).getKindDetail().get(pos1).getKindDetail().remove(pos2);
                                    mAdapter.notifyItemChanged(pos, dataList.get(pos));
                                    break;
                                default:
                                    dataList.remove(pos);
                                    mAdapter.remove(pos);
                                    if (!dataList.isEmpty()) {
                                        recyclerView.setVisibility(View.VISIBLE);
                                        linEmpty.setVisibility(View.GONE);
                                    } else {
                                        recyclerView.setVisibility(View.GONE);
                                        linEmpty.setVisibility(View.VISIBLE);
                                    }
                                    break;
                            }
                            break;
                        default:
                            getCate();
                            break;
                    }
                    break;
            }
        }
    }
}

