package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean;

import java.util.List;

/**
 * Describe:选择供货商-web（实体类）
 * Created by jingang on 2025/2/15
 */
public class SupplierListWebData {

    /**
     * code : 200
     * msg : 操作成功
     * data : [{"supplier_unique":4418376813134,"supplier_name":"供货商A"},{"supplier_unique":4433034147325,"supplier_name":"供货商B"}]
     */

    private int code;
    private String msg;
    private List<DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * supplier_unique : 4418376813134
         * supplier_name : 供货商A
         */

        private String supplier_unique;
        private String supplier_name;
        private int purchaseType;

        public String getSupplier_unique() {
            return supplier_unique;
        }

        public void setSupplier_unique(String supplier_unique) {
            this.supplier_unique = supplier_unique;
        }

        public String getSupplier_name() {
            return supplier_name;
        }

        public void setSupplier_name(String supplier_name) {
            this.supplier_name = supplier_name;
        }

        public int getPurchaseType() {
            return purchaseType;
        }

        public void setPurchaseType(int purchaseType) {
            this.purchaseType = purchaseType;
        }
    }
}
