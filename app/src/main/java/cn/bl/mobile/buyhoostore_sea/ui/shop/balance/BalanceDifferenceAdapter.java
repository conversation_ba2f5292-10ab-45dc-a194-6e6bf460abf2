package cn.bl.mobile.buyhoostore_sea.ui.shop.balance;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean.GapListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:余额差价-余额记录（适配器）
 * Created by jingang on 2023/4/25
 */
public class BalanceDifferenceAdapter extends BaseAdapter<GapListData.DataBean.GapListBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public BalanceDifferenceAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_balance_difference;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvType, tvPrice, tvTime, tvInfo;
        tvType = holder.getView(R.id.tvItemType);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTime = holder.getView(R.id.tvItemTime);
        tvInfo = holder.getView(R.id.tvItemInfo);
        tvInfo.setText(getLanguageValue("order")+getLanguageValue("details"));

        tvType.setText(mDataList.get(position).getGapType());
        double gap_price = mDataList.get(position).getGap_price();
        if (gap_price > 0) {
            tvPrice.setText("+" + DFUtils.getNum2(gap_price));
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvPrice.setText(DFUtils.getNum2(gap_price));
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.color_f6403f));
        }
        tvTime.setText(mDataList.get(position).getCreate_datetime());

        if (onItemClickListener != null) {
            tvInfo.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
