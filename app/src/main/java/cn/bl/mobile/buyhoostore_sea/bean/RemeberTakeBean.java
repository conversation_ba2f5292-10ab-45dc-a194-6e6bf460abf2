package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class RemeberTakeBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"saleListUnique":"15265221084202","consumptionType":"现金","staffName":"二号","salePoints":3,"recMoney":3,"type":"1","recDate":"2018-05-17","recId":28538},{"saleListUnique":"15264608549377","consumptionType":"现金","staffName":"二号","salePoints":274.5,"recMoney":274.5,"type":"1","recDate":"2018-05-16","recId":28432},{"saleListUnique":"15264515137453","consumptionType":"支付宝","staffName":"二号","salePoints":3,"recMoney":3,"type":"2","recDate":"2018-05-16","recId":28414},{"saleListUnique":"1526451490443","consumptionType":"现金","staffName":"二号","salePoints":23,"recMoney":23,"type":"1","recDate":"2018-05-16","recId":28413},{"saleListUnique":"15264512121663","consumptionType":"现金","staffName":"二号","salePoints":3,"recMoney":3,"type":"1","recDate":"2018-05-16","recId":28412},{"saleListUnique":"15264511754965","consumptionType":"现金","staffName":"二号","salePoints":3,"recMoney":3,"type":"1","recDate":"2018-05-16","recId":28411},{"saleListUnique":"15264419706693","consumptionType":"现金","staffName":"二号","salePoints":3,"recMoney":3,"type":"1","recDate":"2018-05-16","recId":28397},{"saleListUnique":"15264415779548","consumptionType":"现金","staffName":"二号","salePoints":14,"recMoney":14,"type":"1","recDate":"2018-05-16","recId":28395},{"saleListUnique":"15264414292224","consumptionType":"现金","staffName":"二号","salePoints":15,"recMoney":15,"type":"1","recDate":"2018-05-16","recId":28394}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * saleListUnique : 15265221084202
         * consumptionType : 现金
         * staffName : 二号
         * salePoints : 3.0
         * recMoney : 3.0
         * type : 1
         * recDate : 2018-05-17
         * recId : 28538
         */

        private String saleListUnique;
        private String consumptionType;
        private String staffName;
        private String salePoints;
        private String recMoney;
        private String type;
        private String recDate;
        private String cus_type;
        private String recId;
        private String recharge_method;

        public String getRecharge_method() {
            return recharge_method;
        }

        public void setRecharge_method(String recharge_method) {
            this.recharge_method = recharge_method;
        }

        public String getCus_type() {
            return cus_type;
        }

        public void setCus_type(String cus_type) {
            this.cus_type = cus_type;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getConsumptionType() {
            return consumptionType;
        }

        public void setConsumptionType(String consumptionType) {
            this.consumptionType = consumptionType;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getSalePoints() {
            return salePoints;
        }

        public void setSalePoints(String salePoints) {
            this.salePoints = salePoints;
        }

        public String getRecMoney() {
            return recMoney;
        }

        public void setRecMoney(String recMoney) {
            this.recMoney = recMoney;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getRecDate() {
            return recDate;
        }

        public void setRecDate(String recDate) {
            this.recDate = recDate;
        }

        public String getRecId() {
            return recId;
        }

        public void setRecId(String recId) {
            this.recId = recId;
        }
    }
}