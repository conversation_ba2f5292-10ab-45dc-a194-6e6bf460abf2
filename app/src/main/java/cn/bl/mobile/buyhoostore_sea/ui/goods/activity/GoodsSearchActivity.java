package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gengcon.www.jcprintersdk.callback.PrintCallback;
import com.google.gson.Gson;
import com.gprinter.command.LabelCommand;
import com.gprinter.utils.LogUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.adapter.GoodsAdapter;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.printer.PrinterSettingActivity;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrintUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.Printer;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.ThreadPoolManager;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsSearchHistoryAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ChuRuSelectActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanGoodsRecordActivity;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品搜索
 * Created by jingang on 2023/3/24
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsSearchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.linHistory)
    LinearLayout linHistory;
    @BindView(R.id.tvHistory)
    TextView tvHistory;
    @BindView(R.id.tvHistoryDel)
    TextView tvHistoryDel;
    @BindView(R.id.rvHistory)
    RecyclerView rvHistory;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private GoodsSearchActivity TAG = GoodsSearchActivity.this;
    private String keyWords;

    //商品列表
    private List<GoodsData> dataList = new ArrayList<>();
    private GoodsAdapter mAdapter;

    //商品操作
    private String goodsBarcode,//商品编码
            goodsName,//商品名称
            goodsPrice,//商品价格
            goodsUnit,//商品单位
            goodsImg;//商品图片
    private int printerStatus,//收银机上架状态 1.上架 2.下架
            appletStatus;//小程序上架状态 1.上架 2.下架
    private int pos;

    //打印价签
    private SharedPreferencesUtil sharedPreferencesUtil;
    private int printerType;//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
    private boolean isError,//是否打印错误
            isCancel;//是否取消打印
    private ArrayList<String> jsonList = new ArrayList<>(),//图像数据
            infoList = new ArrayList<>();//图像处理数据

    //本地缓存
    private SharedPreferences sp;

    //搜索记录
    private GoodsSearchHistoryAdapter historyAdapter;
    private List<String> historyList = new ArrayList<>();

    private SharedPreferences mPref;
    private SharedPreferences.Editor mEditor;
    public static final String KEY_SEARCH_HISTORY_KEYWORD = "key_search_history_keyword";

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_search;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText(getLanguageValue("commodity")+getLanguageValue("search"));
        etSearch.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("commodity")+getLanguageValue("name")+"/"+getLanguageValue("barcode"));
        keyWords = getIntent().getStringExtra("goodsBarcode");
        sp = getSharedPreferences("shop", Context.MODE_PRIVATE);
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);

        mPref = getSharedPreferences("test", Activity.MODE_PRIVATE);
        mEditor = mPref.edit();

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            //此方法会走两次 不知道啥原因
            if (!isQuicklyClick()) {
                keyWords = v.getText().toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    showMessage(getLanguageValue("enterSearchKeyword"));
                } else {
                    save();
                    getGoods();
                    return true;
                }
            }
            return false;
        });

        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() == 0) {
                    if (historyList.size() > 0) {
                        linHistory.setVisibility(View.VISIBLE);
                    } else {
                        linHistory.setVisibility(View.GONE);
                    }
                } else {
                    linHistory.setVisibility(View.GONE);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        etSearch.requestFocus();
        initSearchHistory();
        setAdapter();
    }

    @Override
    public void initData() {
        if (!TextUtils.isEmpty(keyWords)) {
            getGoods();
        }
    }

    @OnClick({R.id.ivBack, R.id.ivScan, R.id.tvHistoryDel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivScan:
                //扫码
                startActivityForResult(new Intent(this, ScanActivity.class),
                        Constants.SCAN);
                break;
//            case R.id.ivSearch:
//                //搜索
//                keyWords = etSearch.getText().toString().trim();
//                if (TextUtils.isEmpty(keyWords)) {
//                    showMessage("请输入搜索内容");
//                    return;
//                }
//                save();
//                getGoods();
//                break;
            case R.id.tvHistoryDel:
                //清除历史搜索记录
                cleanHistory();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_LIST:
                page = 1;
                getGoods();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("search"));
        etSearch.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("search") + getLanguageValue("commodity"));
        tvHistory.setText(getLanguageValue("history") + getLanguageValue("search") + getLanguageValue("record"));
        tvHistoryDel.setText(getLanguageValue("delete"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getGoods();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getGoods();
        });
        mAdapter.setListener(new GoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
//                startActivity(new Intent(TAG, GoodsAddUpdateActivity.class)
//                        .putExtra("goodsBarcode", dataList.get(position).getGoodsBarcode())
//                        .putExtra("types", "1")
//                );
                startActivity(new Intent(TAG, GoodsEditActivity.class)
                        .putExtra("goodsBarcode", dataList.get(position).getGoodsBarcode())
                        .putExtra("type", 1)
                );
            }

            @Override
            public void onMoreClick(View view, int position) {
                goodsBarcode = dataList.get(position).getGoodsBarcode();
                goodsName = dataList.get(position).getGoodsName();
                goodsImg = dataList.get(position).getGoodsPicturePath();
                goodsPrice = String.valueOf(dataList.get(position).getGoodsSalePrice());
                goodsUnit = dataList.get(position).getGoodsUnit();
                printerStatus = dataList.get(position).getPcShelfState();
                appletStatus = dataList.get(position).getShelfState();
                pos = position;
                showDialogMore();
            }

            @Override
            public void onCheckClick(View view, int position) {

            }
        });
    }

    /******************************商品操作start*******************************/

    /**
     * dialog（商品操作）
     */
    private void showDialogMore() {
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_more, null);
        dialog.setContentView(view);
        TextView tvTitle, tvStatus, tvPrint, tvChu, tvRu, tvCate, tvPan, tvChange, tvDel;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvStatus = view.findViewById(R.id.tvDialogStatus);
        tvPrint = view.findViewById(R.id.tvDialogPrint);
        tvChu = view.findViewById(R.id.tvDialogChu);
        tvRu = view.findViewById(R.id.tvDialogRu);
        tvCate = view.findViewById(R.id.tvDialogCate);
        tvPan = view.findViewById(R.id.tvDialogPan);
        tvChange = view.findViewById(R.id.tvDialogChange);
        tvDel = view.findViewById(R.id.tvDialogDel);
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvStatus.setText(getLanguageValue("commodity") + getLanguageValue("onOffShelf"));
        tvPrint.setText(getLanguageValue("printTag"));
        tvChu.setText(getLanguageValue("commodity") + getLanguageValue("outOfTheWarehouse"));
        tvRu.setText(getLanguageValue("commodity") + getLanguageValue("warehousing"));
        tvCate.setText(getLanguageValue("transferClass"));
        tvPan.setText(getLanguageValue("diskLibrary") + getLanguageValue("record"));
        tvChange.setText(getLanguageValue("change") + getLanguageValue("record"));
        tvDel.setText(getLanguageValue("delete") + getLanguageValue("commodity"));

        //转移分类
        view.findViewById(R.id.linDialogCate).setOnClickListener(v -> {
            startActivityForResult(new Intent(this, GoodsCateActivity.class)
                            .putExtra("type", 1)
                    , Constants.CATE);
            dialog.dismiss();
        });
        //打印标签
        view.findViewById(R.id.linDialogPrint).setOnClickListener(v -> {
            setPrint();
            dialog.dismiss();
        });
        //出库
        view.findViewById(R.id.linDialogChu).setOnClickListener(v -> {
            if (TextUtils.isEmpty(goodsBarcode)) {
                showMessage(getLanguageValue("noBarcode"));
                return;
            }
            startActivity(new Intent(this, ChuRuSelectActivity.class)
                    .putExtra("result", goodsBarcode)
                    .putExtra("isRu", 2)
            );
            dialog.dismiss();
        });
        //入库
        view.findViewById(R.id.linDialogRu).setOnClickListener(v -> {
            if (TextUtils.isEmpty(goodsBarcode)) {
                showMessage(getLanguageValue("noBarcode"));
                return;
            }
            startActivity(new Intent(this, ChuRuSelectActivity.class)
                    .putExtra("result", goodsBarcode)
                    .putExtra("isRu", 1)
            );
            dialog.dismiss();
        });
        //上下架
        view.findViewById(R.id.linDialogStatus).setOnClickListener(v -> {
            showDialogPlatform();
            dialog.dismiss();
        });
        //盘库记录
        view.findViewById(R.id.linDialogPan).setOnClickListener(v -> {
            startActivity(new Intent(this, PanGoodsRecordActivity.class)
                    .putExtra("goodsBarcode", goodsBarcode)
            );
            dialog.dismiss();
        });
        //变更记录
        view.findViewById(R.id.linDialogChange).setOnClickListener(v -> {
            startActivity(new Intent(this, GoodsChangeRecordActivity.class)
                    .putExtra("goodsBarcode", goodsBarcode)
                    .putExtra("goodsName", goodsName)
                    .putExtra("img", goodsImg)
            );
            dialog.dismiss();
        });
        //删除
        view.findViewById(R.id.linDialogDel).setOnClickListener(v -> {
            IAlertDialog.showDialog(this,
                    getLanguageValue("deleteProduct")+"?",
                    getLanguageValue("confirm"),
                    (dialogs, which) -> {
                        postGoodsDel();
                        dialog.dismiss();
                    });
        });

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    /**
     * dialog（选择上下架平台）
     */
    private void showDialogPlatform() {
        ImageView ivPrinterUp, ivPrinterDown, ivAppletUp, ivAppletDown;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_platform, null);
        dialog.setContentView(view);

        TextView tvTitle, tvPrinter, tvPrinterUp, tvPrinterDown, tvApplet, tvAppletUp, tvAppletDown, tvConfirm;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvPrinter = view.findViewById(R.id.tvDialogPrinter);
        tvPrinterUp = view.findViewById(R.id.tvDialogPrinterUp);
        tvPrinterDown = view.findViewById(R.id.tvDialogPrinterDown);
        tvApplet = view.findViewById(R.id.tvDialogApplet);
        tvAppletUp = view.findViewById(R.id.tvDialogAppletUp);
        tvAppletDown = view.findViewById(R.id.tvDialogAppletDown);
        tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvPrinter.setText(getLanguageValue("cashRegister"));
        tvPrinterUp.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown.setText(getLanguageValue("offTheShelf"));
        tvApplet.setText(getLanguageValue("miniProgram"));
        tvAppletUp.setText(getLanguageValue("onTheShelf"));
        tvAppletDown.setText(getLanguageValue("offTheShelf"));
        tvConfirm.setText(getLanguageValue("confirm"));

        ivPrinterUp = view.findViewById(R.id.ivDialogPrinterUp);
        ivPrinterDown = view.findViewById(R.id.ivDialogPrinterDown);
        ivAppletUp = view.findViewById(R.id.ivDialogAppletUp);
        ivAppletDown = view.findViewById(R.id.ivDialogAppletDown);

        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
        switch (printerStatus) {
            case 1:
                ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }
        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
        switch (appletStatus) {
            case 1:
                ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
        ivPrinterUp.setOnClickListener(v -> {
            //收银机上架
            if (printerStatus == 1) {
                printerStatus = 0;
                ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
            } else {
                printerStatus = 1;
                ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
            }
        });
        ivPrinterDown.setOnClickListener(v -> {
            //收银机下架
            if (printerStatus == 2) {
                printerStatus = 0;
                ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
            } else {
                printerStatus = 2;
                ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
            }
        });
        ivAppletUp.setOnClickListener(v -> {
            //小程序上架
            if (appletStatus == 1) {
                appletStatus = 0;
                ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                ivAppletDown.setImageResource(R.mipmap.ic_chose001);
            } else {
                appletStatus = 1;
                ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                ivAppletDown.setImageResource(R.mipmap.ic_chose001);
            }
        });
        ivAppletDown.setOnClickListener(v -> {
            //小程序下架
            if (appletStatus == 2) {
                appletStatus = 0;
                ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                ivAppletDown.setImageResource(R.mipmap.ic_chose001);
            } else {
                appletStatus = 2;
                ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
            }
        });

        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> {
            if (printerStatus == 0) {
                showMessage(getLanguageValue("cashRegisterStatus"));
                return;
            }
            if (appletStatus == 0) {
                showMessage(getLanguageValue("appletStatus"));
                return;
            }
            postGoods();
            dialog.dismiss();
        });
    }

    /**
     * 打印价签
     */
    private void setPrint() {
        if (TextUtils.isEmpty(goodsBarcode)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("barcode") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(goodsName)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("name") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(goodsPrice)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("sellingPrice") + getLanguageValue("notEmpty"));
            return;
        }
        if (printerType == 0) {
            if (Printer.getPortManager() != null && Printer.getConnectState()) {
                ThreadPoolManager.getInstance().addTask(() -> {
                    try {
                        if (Printer.getPortManager() == null) {
                            runOnUiThread(() -> {
                                showMessage(getLanguageValue("connectPrinter"));
                            });
                            return;
                        }
                        boolean result = Printer.getPortManager().writeDataImmediately(getLabel(goodsBarcode, goodsName, goodsPrice));
                        runOnUiThread(() -> {
                            if (result) {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                            } else {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                            }
                        });
                        LogUtils.e("send result", result);
                    } catch (Exception e) {
                        runOnUiThread(() -> {
                            tipsDialog(getLanguageValue("print") + getLanguageValue("failed") + e.getMessage());
                        });
                    } finally {
                        if (Printer.getPortManager() == null) {
                            Printer.close();
                        }
                    }
                });
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        } else {
            if (PrintUtil.isConnection() == 0) {
                printLabel();
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        }
    }

    /**
     * 标签打印测试页
     *
     * @param goodsBarcode
     * @param name
     * @param price
     * @return
     */
    public Vector<Byte> getLabel(String goodsBarcode, String name, String price) {
        int printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);
        String shopName = sp.getString("shopName", "");
        LabelCommand tsc = new LabelCommand();
        // 设置标签尺寸宽高，按照实际尺寸设置 单位mm
        tsc.addUserCommand("\r\n");
        if (printType == 4) {
            tsc.addSize(50, 30);
        } else {
            tsc.addSize(70, 38);
        }
        // 设置标签间隙，按照实际尺寸设置，如果为无间隙纸则设置为0 单位mm
        tsc.addGap(2);
        //设置纸张类型为黑标，发送BLINE 指令不能同时发送GAP指令
//        tsc.addBline(2);
        // 设置打印方向
//        tsc.addDirection(LabelCommand.DIRECTION.BACKWARD, LabelCommand.MIRROR.NORMAL);
        // 设置原点坐标
        tsc.addReference(0, 0);
        //设置浓度
        tsc.addDensity(LabelCommand.DENSITY.DNESITY4);
        // 撕纸模式开启
        tsc.addTear(LabelCommand.RESPONSE_MODE.ON);
        // 清除打印缓冲区
        tsc.addCls();
        //标签方向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //正向
            tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, name);
                    tsc.addText(415, 194, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(115, 154, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 2:
                    tsc.addText(230, 16, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, shopName);
                    tsc.addText(88, 76, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, name);
                    tsc.addText(400, 216, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(60, 185, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 4:
                    tsc.addText(155, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(44, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(255, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(35, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            25,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        } else {
            //反向
            tsc.addDirection(LabelCommand.DIRECTION.BACKWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, name);
                    tsc.addText(415, 194, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(115, 154, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 2:
                    tsc.addText(270, 16, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, shopName);
                    tsc.addText(118, 73, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, name);
                    tsc.addText(420, 216, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(100, 185, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 4:
                    tsc.addText(195, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(84, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(295, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(75, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            20,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        }
        // 打印标签
        tsc.addPrint(1, 1);
        // 打印标签后 蜂鸣器响
        tsc.addSound(2, 100);
        //开启钱箱
//        tsc.addCashdrwer(LabelCommand.FOOT.F5, 255, 255);
        Vector<Byte> datas = tsc.getCommand();
        // 发送数据
        return datas;
    }

    /**
     * 提示弹框
     *
     * @param message
     */
    private void tipsDialog(String message) {
        AlertDialog alertDialog = new AlertDialog.Builder(this)
                .setTitle(getLanguageValue("hint"))
                .setMessage(message)
                .setIcon(R.mipmap.mylogo)
                .setPositiveButton(getLanguageValue("confirm"), (dialogInterface, i) -> {
                    //添加"Yes"按钮
                }).create();
        alertDialog.show();
    }

    /**
     * 打印标签（精臣）
     */
    private void printLabel() {
        // 检查是否连接了打印机
        if (PrintUtil.isConnection() != 0) {
            showMessage(getLanguageValue("printerNotConnect"));
            return;
        }
        isError = false;
        isCancel = false;
        jsonList.clear();
        infoList.clear();
        PrintUtil.getInstance().setTotalPrintQuantity(1);
        /*
         * 参数1：打印浓度 ，参数2:纸张类型 参数3:打印模式
         * 打印浓度 B50/B50W/T6/T7/T8 建议设置6或8，Z401/B32建议设置8，B3S/B21/B203/B1建议设置3
         */
        PrintUtil.getInstance().startPrintJob(4, 1, 1, new PrintCallback() {
            @Override
            public void onProgress(int pageIndex, int quantityIndex, HashMap<String, Object> hashMap) {
                // 更新打印进度
                String progressMessage = "打印进度:已打印到第" + pageIndex + "页,第" + quantityIndex + "份";
                Log.d(tag, "测试:" + progressMessage);
                // 处理打印完成情况
                if (pageIndex == 1 && quantityIndex == 1) {
                    Log.d(tag, "测试:onProgress: 结束打印");
                    //endJob，使用方法含义更明确的endPrintJob
                    runOnUiThread(() -> {
                        if (PrintUtil.getInstance().endPrintJob()) {
                            Log.d(tag, "结束打印成功");
                            tipsDialog(getLanguageValue("send")+getLanguageValue("succeed"));
                        } else {
                            Log.d(tag, "结束打印失败");
                            tipsDialog(getLanguageValue("send")+getLanguageValue("failed"));
                        }
                    });

                }
            }


            @Override
            public void onError(int i) {

            }


            @Override
            public void onError(int errorCode, int printState) {
                Log.d(tag, "测试：报错");
                isError = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("printError")+" errorCode = " + errorCode + " printState = " + printState);
                });
            }

            @Override
            public void onCancelJob(boolean isSuccess) {
                Log.d(tag, "onCancelJob: " + isSuccess);
                isCancel = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("cancelPrint"));
                });
            }

            /**
             * SDK缓存空闲回调，可以在此处传入打印数据
             *
             * @param pageIndex 当前回调函数处理下一页的打印索引
             * @param bufferSize 缓存空间的大小
             */
            @Override
            public void onBufferFree(int pageIndex, int bufferSize) {
                // 如果出现错误、已取消打印，或 pageIndex 超过总页数，则返回
                if (isError || isCancel) {
                    return;
                }
                setPrinterData();
            }
        });
    }

    /**
     * 生成打印数据(精臣)
     */
    private void setPrinterData() {
        String printPrice;
        if (TextUtils.isEmpty(goodsUnit)) {
            printPrice = goodsPrice;
        } else {
            printPrice = goodsPrice + "/" + goodsUnit;
        }
        float width = 70, height = 38;
        /*
         * 设置画布⼤⼩
         *
         * @param width 画布宽度(mm)
         * @param height 画布⾼度(mm)
         * @param orientation 画布旋转⻆度
         * @param fontDir 字体路径暂不可⽤，默认""即可
         *
         */
        PrintUtil.getInstance().drawEmptyLabel(width, height, 0, "");

        //标签方向：0.正向 1.反向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //标签样式 1. 2. 3.
            if (sharedPreferencesUtil.getInt(2, Constants.PRINT_TYPE) == 3) {
                /**
                 * 绘制⽂本
                 * @param x 位置x
                 * @param y 位置y
                 * @param width 宽
                 * @param height ⾼
                 * @param value 内容
                 * @param fontFamily 字体名称,未传输字体为空字符串时使⽤默认字体,暂时⽤默认字体
                 * @param fontSize 字体⼤⼩
                 * @param rotate 旋转
                 * @param textAlignHorizontal ⽔平对⻬⽅式：0:左对⻬ 1:居中对⻬ 2:右对⻬
                 * @param textAlignVertical 垂直 对⻬⽅式：0:顶对⻬ 1:垂直居中 2:底对⻬
                 * @param lineModel 1:宽⾼固定，内容⼤⼩⾃适应（字号/字符间距/⾏间距 按⽐例缩放）
                2:宽度固定，⾼度⾃适应
                3:宽⾼固定，超出内容后⾯加...
                4:宽⾼固定,超出内容直裁切
                6:宽⾼固定，内容超过预设宽⾼时⾃动缩⼩（字号/字符间距/⾏间距 按⽐例缩放）
                 * @param letterSpace 字⺟之间的标准间隔，单位mm
                 * @param lineSpace ⾏间距（倍距），单位mm
                 * @param mFontStyles 字体样式[加粗，斜体，下划线，删除下划线（预留）]
                 */
                PrintUtil.getInstance().drawLabelText(40f, 13f, 30f, 5f, goodsName, "", 6f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                /**
                 * 绘制⼀维码
                 *
                 * @param x ⽔平坐标
                 * @param y 垂直坐标
                 * @param width 宽度,单位mm
                 * @param height ⾼度,单位mm
                 * @param codeType ⼀维码类型20:CODE128 ,21:UPC-A,22:UPC-E,23:EAN8,24:EAN13,
                 * 25:CODE93,26:CODE39,27:CODEBAR, 28:ITF25
                 * @param value ⽂本内容
                 * @param fontSize ⽂本字号
                 * @param rotate 旋转⻆度，仅⽀持0,90,180,270
                 * @param textHeight ⽂本⾼度
                 * @param textPosition ⽂本位置，int,⼀维码⽂字识别码显示位置,0:下⽅显示,1:上⽅显
                示,2:不显示
                 */
                PrintUtil.getInstance().drawLabelBarCode(25f, 22f, 40f, 7f, 20, goodsBarcode, 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(40f, 27f, 30f, 7f, "RM" + printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            } else {
                PrintUtil.getInstance().drawLabelText(31f, 0f, 39f, 5f, BaseApplication.getInstance().getShop_name(), "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelText(12f, 7f, 55f, 5f, goodsName, "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelBarCode(10f, 23f, 28f, 8f, 20, goodsBarcode, 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(48f, 25f, 23f, 7f, printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            }
        } else {
            PrintUtil.getInstance().drawLabelText(0f, 30f, 40f, 5f, BaseApplication.getInstance().getShop_name(), "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelText(0f, 22f, 60f, 5f, goodsName, "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelBarCode(34f, 5f, 28f, 8f, 20, goodsBarcode, 3f,
                    180, 3f, 0);
            PrintUtil.getInstance().drawLabelText(0f, 3f, 23f, 7f, printPrice, "", 7f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
        }

        //生成打印数据
        byte[] jsonByte = PrintUtil.getInstance().generateLabelJson();
        jsonList.add(new String(jsonByte));
        //除B32/Z401/T8的printMultiple为11.81，其他的为8
        String jsonInfo = "{  " + "\"printerImageProcessingInfo\": " + "{    " + "\"orientation\":" + 0 + "," + "   \"margin\": [      0,      0,      0,      0    ], " + "   \"printQuantity\": " + 1 + ",  " + "  \"horizontalOffset\": 0,  " + "  \"verticalOffset\": 0,  " + "  \"width\":" + width + "," + "   \"height\":" + height + "," + "\"printMultiple\":" + 8f + "," + "  \"epc\": \"\"  }}";
        infoList.add(jsonInfo);
        PrintUtil.getInstance().commitData(jsonList, infoList);
    }

    /******************************商品操作end*******************************/

    /**
     * 历史记录
     */
    public void initSearchHistory() {
        String history = mPref.getString(KEY_SEARCH_HISTORY_KEYWORD, "");
        if (!TextUtils.isEmpty(history)) {
            for (Object o : history.split(",")) {
                historyList.add((String) o);
            }
        }
        if (historyList.size() > 0) {
            if (TextUtils.isEmpty(keyWords)) {
                linHistory.setVisibility(View.VISIBLE);
            }
        } else {
            linHistory.setVisibility(View.GONE);
        }
        rvHistory.setLayoutManager(new LinearLayoutManager(this));
        historyAdapter = new GoodsSearchHistoryAdapter(this);
        rvHistory.setAdapter(historyAdapter);
        historyAdapter.setDataList(historyList);
        historyAdapter.setOnItemClickListener((view, position) -> {
            linHistory.setVisibility(View.GONE);
            keyWords = historyList.get(position);
            etSearch.setText(keyWords);
            if (TextUtils.isEmpty(keyWords)) {
                return;
            }
            getGoods();
        });
    }

    public void save() {
        String oldText = mPref.getString(KEY_SEARCH_HISTORY_KEYWORD, "");
        System.out.println("zlw=======" + oldText);
        if (!TextUtils.isEmpty(keyWords) && !oldText.contains(keyWords)) {
            if (historyList.size() > 4) {
//                showMessage("最多保存5条历史");
                historyList.remove(historyList.size() - 1);  //去掉最后一条数据
            }
            historyList.add(0, keyWords); //第一条数据添加上
            //重新加，提交
            if (historyList.size() > 0) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < historyList.size(); i++) {
                    sb.append(historyList.get(i) + ",");
                }
                mEditor.putString(KEY_SEARCH_HISTORY_KEYWORD, sb.toString()).commit();
            } else {
                mEditor.putString(KEY_SEARCH_HISTORY_KEYWORD, keyWords + ",").commit();
            }
        }
        historyAdapter.setDataList(historyList);
    }

    public void cleanHistory() {
        mEditor.clear();
        mEditor.putString(KEY_SEARCH_HISTORY_KEYWORD, "");
        mEditor.commit();
        historyList.clear();
        historyAdapter.clear();
        linHistory.setVisibility(View.GONE);
        showMessage(getLanguageValue("delete")+getLanguageValue("succeed"));
    }

    /**
     * 得到搜索的内容
     */
    public void getGoods() {
        showDialog();
        hideSoftInput(this);
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        params.put("orderType", 1);
        params.put("goodsMessage", keyWords);
        params.put("groupUnique", "-1");
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSelectGoods(),
                params,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> goodsData) {
                        hideDialog();
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(goodsData);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            mAdapter.clear();
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.deletegoods(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "删除商品 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                        }
                    }
                });
    }

    /**
     * 商品上架/下架
     */
    private void postGoods() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        params.put("pcShelfState", printerStatus);
        params.put("shelfState", appletStatus);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getGoodsShelfState(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            dataList.get(pos).setPcShelfState(printerStatus);
                            dataList.get(pos).setShelfState(appletStatus);
                            mAdapter.notifyItemChanged(pos, dataList);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        showMessage(msg);
                        hideDialog();
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.BLUETOOTH:
                    //蓝牙返回mac地址
                    printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
                    setPrint();
                    break;
                case Constants.SCAN:
                    keyWords = data.getStringExtra("result");
                    etSearch.setText(keyWords);
                    save();
                    getGoods();
                    break;
            }
        }
    }
}

