package cn.bl.mobile.buyhoostore_sea.ui.shop.allot;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity.AllotInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotGoodsDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotListData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:调拨单
 * Created by jingang on 2023/5/19
 */
@SuppressLint("NonConstantResourceId")
public class AllotFragment extends LazyBaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int status;//不传.全部 2.待调出 3.待调入 4.已完成
    private boolean isVisible,//当前fragment是否显示
            isState;//订单状态是否改变

    private AllotAdapter mAdapter;
    private List<AllotData> dataList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static AllotFragment newInstance(int status) {
        AllotFragment fragment = new AllotFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("status", status);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout;
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        assert getArguments() != null;
        status = getArguments().getInt("status", 0);
        getAllotList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (getActivity() != null) {
            if (isVisible || isState) {
                page = 1;
                getAllotList();
            }
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "state":
                //订单状态改变
                if (isVisible) {
                    page = 1;
                    getAllotList();
                } else {
                    isState = true;
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new AllotAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getAllotList();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getAllotList();
        });
        mAdapter.setListener(new AllotAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(getActivity(), AllotInfoActivity.class)
                        .putExtra("data", dataList.get(position))
                );
            }

            @Override
            public void onGoodsClick(View view, int position) {
                //商品
                showDialogGoods(dataList.get(position).getDetailInfoList());
            }

            @Override
            public void onOperationClick(View view, int position) {
                //操作
                switch (dataList.get(position).getAllocationStatus()) {
                    case 2:
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm")+getLanguageValue("revoke")+getLanguageValue("transfer")+"?",
                                getLanguageValue("confirm"),
                                (DialogInterface.OnClickListener) (dialog, which) -> {
                                    postAllotQuash(dataList.get(position).getPurchaseListId(), position);
                                });
                        break;
                    case 3:
                        IAlertDialog.showDialog(getActivity(),
                                getLanguageValue("confirm")+getLanguageValue("receipt")+"?",
                                getLanguageValue("confirm"),
                                (DialogInterface.OnClickListener) (dialog, which) -> {
                                    postAllotFinish(dataList.get(position).getPurchaseListId(), position);
                                });
                        break;
                }
            }
        });
    }

    /**
     * dialog（商品详情）
     *
     * @param detailInfoList
     */
    private void showDialogGoods(List<AllotData.DetailInfoListBean> detailInfoList) {
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_allot_goods, null);
        dialog.setContentView(view);

        TextView tvTitle= view.findViewById(R.id.tvDialogTitle);
        RecyclerView rvGoods = view.findViewById(R.id.rvDialogGoods);
        tvTitle.setText(getLanguageValue("commodity")+getLanguageValue("details"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        view.findViewById(R.id.vDialog).setOnClickListener(v -> dialog.dismiss());
        view.findViewById(R.id.ivDialogClose).setOnClickListener(v -> dialog.dismiss());

        rvGoods.setLayoutManager(new LinearLayoutManager(getActivity()));
        AllotGoodsDialogAdapter adapter = new AllotGoodsDialogAdapter(getActivity());
        rvGoods.setAdapter(adapter);
        adapter.setDataList(detailInfoList);
        adapter.setOnItemClickListener((view1, position) -> {

        });
    }

    /**
     * 调拨单列表
     */
    private void getAllotList() {
        Map<String, Object> map = new HashMap<>();
        if (status != 1) {
            map.put("allocationStatus", status);
        }
        if (!TextUtils.isEmpty(AllotActivity.startTime)) {
            map.put("startTime", AllotActivity.startTime);
        }
        if (!TextUtils.isEmpty(AllotActivity.endTime)) {
            map.put("endTime", AllotActivity.endTime);
        }
        map.put("pullStoreOfId", getShop_id());//调出 店铺 ID
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getAllot_list(),
                map,
                AllotListData.class,
                new RequestListener<AllotListData>() {
                    @Override
                    public void success(AllotListData allotListData) {
                        isState = false;
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (allotListData.getRows() == null) {
                            if (page == 1) {
                                dataList.clear();
                                mAdapter.clear();
                            }
                            if (dataList.size() > 0) {
                                recyclerView.setVisibility(View.VISIBLE);
                                linEmpty.setVisibility(View.GONE);
                            } else {
                                recyclerView.setVisibility(View.GONE);
                                linEmpty.setVisibility(View.VISIBLE);
                            }
                            return;
                        }
                        dataList.addAll(allotListData.getRows());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 调拨-撤销
     *
     * @param id
     * @param position
     */
    private void postAllotQuash(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getAllot_quash(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            dataList.remove(position);
                            mAdapter.remove(position);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 调拨-确认收货
     *
     * @param id
     * @param position
     */
    private void postAllotFinish(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("recipientsUserId", getStaff_id());
        map.put("recipientsUserIdName", getStaff_name());
        RXHttpUtil.requestByBodyPostAsOriginalResponse(getActivity(),
                ZURL.getAllot_finish(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            if (status == 1) {
                                dataList.get(position).setAllocationStatus(4);
                                mAdapter.notifyItemChanged(position, dataList.get(position));
                            } else {
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

}
