package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsKuCunBean;
import cn.bl.mobile.buyhoostore_sea.adapter.KuCunWarningAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-库存-库存预警
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class StockYuJingActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.tvType2)
    TextView tvType2;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvDownValue)
    TextView tvDownValue;
    @BindView(R.id.tvUpValue)
    TextView tvUpValue;
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int type;//0.全部 1.不足 2.超额
    private String goods_kind_unique = "-1",
            goods_kind_parunique = "-1",
            message = "";

    private KuCunWarningAdapter mAdapter;
    private List<GoodsKuCunBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_ku_cun_yu_jing;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        message = getIntent().getStringExtra("message");
        goods_kind_unique = getIntent().getStringExtra("goods_kind_unique");
        goods_kind_parunique = getIntent().getStringExtra("goods_kind_parunique");
        if (message == null) {
            message = "";
        }
        if (goods_kind_unique == null) {
            goods_kind_unique = "-1";
        }
        if (goods_kind_parunique == null) {
            goods_kind_parunique = "-1";
        }
        setAdapter();
    }

    @Override
    public void initData() {
        getshopmessage();
    }

    @OnClick({R.id.ivBack, R.id.ivScan, R.id.ivSearch, R.id.tvType0, R.id.tvType1, R.id.tvType2})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivScan:
                //扫一哈子
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.ivSearch:
                //搜索
                goods_kind_unique = "-1";
                goods_kind_parunique = "-1";
                message = etSearch.getText().toString();
                page = 1;
                getshopmessage();
                break;
            case R.id.tvType0:
                //全部
                if (type != 0) {
                    type = 0;
                    tvType0.setBackgroundResource(R.drawable.shape_blue_left_22);
                    tvType0.setTextColor(getResources().getColor(R.color.white));
                    tvType1.setBackgroundResource(0);
                    tvType1.setTextColor(getResources().getColor(R.color.color_333));
                    tvType2.setBackgroundResource(0);
                    tvType2.setTextColor(getResources().getColor(R.color.color_333));
                    page = 1;
                    getshopmessage();
                }
                break;
            case R.id.tvType1:
                //不足
                if (type != 1) {
                    type = 1;
                    tvType1.setBackgroundColor(getResources().getColor(R.color.blue));
                    tvType1.setTextColor(getResources().getColor(R.color.white));
                    tvType0.setBackgroundResource(0);
                    tvType0.setTextColor(getResources().getColor(R.color.color_333));
                    tvType2.setBackgroundResource(0);
                    tvType2.setTextColor(getResources().getColor(R.color.color_333));
                    page = 1;
                    getshopmessage();
                }
                break;
            case R.id.tvType2:
                //超额
                if (type != 2) {
                    type = 2;
                    tvType2.setBackgroundResource(R.drawable.shape_blue_right_22);
                    tvType2.setTextColor(getResources().getColor(R.color.white));
                    tvType0.setBackgroundResource(0);
                    tvType0.setTextColor(getResources().getColor(R.color.color_333));
                    tvType1.setBackgroundResource(0);
                    tvType1.setTextColor(getResources().getColor(R.color.color_333));
                    page = 1;
                    getshopmessage();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("inventory")+getLanguageValue("warn"));
        etSearch.setHint(getLanguageValue("input")+getLanguageValue("commodity")+getLanguageValue("name")+"/"+getLanguageValue("barcode"));
        tvType0.setText(getLanguageValue("whole"));
        tvType1.setText(getLanguageValue("insufficient"));
        tvType2.setText(getLanguageValue("excess"));
        tvNameValue.setText(getLanguageValue("commodity")+getLanguageValue("name"));
        tvDownValue.setText(getLanguageValue("lowerLimit"));
        tvUpValue.setText(getLanguageValue("upperLimit"));
        tvCountValue.setText(getLanguageValue("inventory")+getLanguageValue("quantity"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new KuCunWarningAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setEnableAutoLoadMore(true);////是否启用列表惯性滑动到底部时自动加载更多
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getshopmessage();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getshopmessage();
        });
    }

    /**
     * 库存预警
     */
    public void getshopmessage() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsMessage", message);
        params.put("goods_kind_unique", goods_kind_unique);
        params.put("goods_kind_parunique", goods_kind_parunique);
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        params.put("warningType", type);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getyujing(),
                params,
                GoodsKuCunBean.class,
                new RequestListListener<GoodsKuCunBean>() {
                    @Override
                    public void onResult(List<GoodsKuCunBean> list) {
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    message = data.getStringExtra("result");
                    etSearch.setText(message);
                    page = 1;
                    getshopmessage();
                    break;
            }
        }
    }

}
