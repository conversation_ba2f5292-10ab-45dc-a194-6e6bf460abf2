package cn.bl.mobile.buyhoostore_sea.printer;

import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrinterData;

/**
 * Describe:打印机列表
 * Created by jingang on 2024/4/16
 */
public class PrinterListAdapter extends BaseAdapter<PrinterData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public PrinterListAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_printer_list;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvStatus = holder.getView(R.id.tvItemStatus);
        //连接状态 0.已连接 1.连接中 2.连接失败 10.未配对 11.配对中 12.已配对
        switch (mDataList.get(position).getState()) {
            case 0:
                tvStatus.setText("已连接");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 1:
                tvStatus.setText("连接中");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_333));
                break;
            case 2:
                tvStatus.setText("连接失败");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            case 10:
                tvStatus.setText("未配对");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_666));
                break;
            case 11:
                tvStatus.setText("配对中");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_666));
                break;
            case 12:
                tvStatus.setText("已配对");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_333));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvName, tvStatus, tvAddress;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvAddress = holder.getView(R.id.tvItemAddress);

        tvName.setText(mDataList.get(position).getName());
        tvAddress.setText(mDataList.get(position).getAddress());
        //连接状态 0.已连接 1.连接中 2.连接失败 10.未配对 11.配对中 12.已配对
        switch (mDataList.get(position).getState()) {
            case 0:
                tvStatus.setText("已连接");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 1:
                tvStatus.setText("连接中");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_333));
                break;
            case 2:
                tvStatus.setText("连接失败");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            case 10:
                tvStatus.setText("未配对");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_666));
                break;
            case 11:
                tvStatus.setText("配对中");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_666));
                break;
            case 12:
                tvStatus.setText("已配对");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_333));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }
}
