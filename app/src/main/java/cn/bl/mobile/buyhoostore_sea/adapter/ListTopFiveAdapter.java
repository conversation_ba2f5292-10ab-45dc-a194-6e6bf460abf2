package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.TopFiveBean;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * 经营统计-热销商品TOP5（适配器）
 */
public class ListTopFiveAdapter extends BaseAdapter<TopFiveBean> {

    public ListTopFiveAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_listtopfive_tab;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(com.yxl.commonlibrary.base.ViewHolder holder, int position) {
        TextView tvName, tvCount, tvPre, tvCountPre;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPre = holder.getView(R.id.tvItemPre);
        LinearLayout linPre = holder.getView(R.id.linItemPre);
        tvCountPre = holder.getView(R.id.tvItemCountPre);

        tvName.setText(mDataList.get(position).getGoodsName());
        tvCount.setText(DFUtils.getNum2(mDataList.get(position).getSaleCount()));
        tvPre.setText(DFUtils.getNum2(mDataList.get(position).getTotalPre()) + "%");
        tvCountPre.setText(DFUtils.getNum2(mDataList.get(position).getSaleCountPre()) + "%");
        ViewGroup.LayoutParams lp = linPre.getLayoutParams();
        lp.width = (int) (100 * mDataList.get(position).getTotalPre() / 100);
        linPre.setLayoutParams(lp);
    }

}

