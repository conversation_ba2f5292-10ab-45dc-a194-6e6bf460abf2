package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:店铺（实体类）
 * Created by jingang on 2023/5/25
 */
public class ShopData implements Serializable {
    /**
     * shopUnique : *************
     * shopName : 沃商赢
     * shopPhone : ***********
     * shopHours : 06:00-22:00
     * shopImagePath : image/*************/*************.png
     * shopAddress : 山东省临沂市兰山区柳青街道智圣路3号
     * area_dict_num : 371312
     * staffList : [{"staffId":5,"staffName":"二号","staffAccount":"***********","powerPrice":1,"powerCount":1,"powerSupplier":1,"powerKind":1,"powerInPrice":1,"powerName":1,"powerDelete":1,"powerPur":1,"powerAdd":1,"powerRecharge":1}]
     */

    private String shopUnique;//唯一标识
    private String shopName;
    private String shopPhone;
    private String shopHours;
    private String shopImagePath;
    private String shopAddress;
    private String area_dict_num;
    private boolean check;
    private List<StaffListBean> staffList;

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopPhone() {
        return shopPhone;
    }

    public void setShopPhone(String shopPhone) {
        this.shopPhone = shopPhone;
    }

    public String getShopHours() {
        return shopHours;
    }

    public void setShopHours(String shopHours) {
        this.shopHours = shopHours;
    }

    public String getShopImagePath() {
        return shopImagePath;
    }

    public void setShopImagePath(String shopImagePath) {
        this.shopImagePath = shopImagePath;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public String getArea_dict_num() {
        return area_dict_num;
    }

    public void setArea_dict_num(String area_dict_num) {
        this.area_dict_num = area_dict_num;
    }

    public List<StaffListBean> getStaffList() {
        return staffList;
    }

    public void setStaffList(List<StaffListBean> staffList) {
        this.staffList = staffList;
    }

    public static class StaffListBean implements Serializable{
        /**
         * staffId : 5
         * staffName : 二号
         * staffAccount : ***********
         * powerPrice : 1
         * powerCount : 1
         * powerSupplier : 1
         * powerKind : 1
         * powerInPrice : 1
         * powerName : 1
         * powerDelete : 1
         * powerPur : 1
         * powerAdd : 1
         * powerRecharge : 1
         */

        private int staffId;
        private String staffName;
        private String staffAccount;
        private int powerPrice;
        private int powerCount;
        private int powerSupplier;
        private int powerKind;
        private int powerInPrice;
        private int powerName;
        private int powerDelete;
        private int powerPur;
        private int powerAdd;
        private int powerRecharge;

        public int getStaffId() {
            return staffId;
        }

        public void setStaffId(int staffId) {
            this.staffId = staffId;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getStaffAccount() {
            return staffAccount;
        }

        public void setStaffAccount(String staffAccount) {
            this.staffAccount = staffAccount;
        }

        public int getPowerPrice() {
            return powerPrice;
        }

        public void setPowerPrice(int powerPrice) {
            this.powerPrice = powerPrice;
        }

        public int getPowerCount() {
            return powerCount;
        }

        public void setPowerCount(int powerCount) {
            this.powerCount = powerCount;
        }

        public int getPowerSupplier() {
            return powerSupplier;
        }

        public void setPowerSupplier(int powerSupplier) {
            this.powerSupplier = powerSupplier;
        }

        public int getPowerKind() {
            return powerKind;
        }

        public void setPowerKind(int powerKind) {
            this.powerKind = powerKind;
        }

        public int getPowerInPrice() {
            return powerInPrice;
        }

        public void setPowerInPrice(int powerInPrice) {
            this.powerInPrice = powerInPrice;
        }

        public int getPowerName() {
            return powerName;
        }

        public void setPowerName(int powerName) {
            this.powerName = powerName;
        }

        public int getPowerDelete() {
            return powerDelete;
        }

        public void setPowerDelete(int powerDelete) {
            this.powerDelete = powerDelete;
        }

        public int getPowerPur() {
            return powerPur;
        }

        public void setPowerPur(int powerPur) {
            this.powerPur = powerPur;
        }

        public int getPowerAdd() {
            return powerAdd;
        }

        public void setPowerAdd(int powerAdd) {
            this.powerAdd = powerAdd;
        }

        public int getPowerRecharge() {
            return powerRecharge;
        }

        public void setPowerRecharge(int powerRecharge) {
            this.powerRecharge = powerRecharge;
        }
    }
}
