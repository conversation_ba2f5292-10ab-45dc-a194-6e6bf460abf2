package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class RemberNewBean {


    /**
     * status : 1
     * msg : 查询成功
     * totals : 0
     * data : {"saleListOrder":6,"saleTotalSum":66.66,"singlePrice":555.55,"saleDay":1,"saleHistory":{"one":20,"four":10,"eight":5,"twelve":40,"sixteen":20,"twenty":5},"saleKindTop3":[{"kind_name":"水果","kind_val":20},{"kind_name":"烟酒","kind_val":20},{"kind_name":"生活用品","kind_val":20}],"saleGoodsTop3":[{"goods_picturepath":"1.jpg"},{"goods_picturepath":"2.jpg"},{"goods_picturepath":"3.jpg"}]}
     */

    private int status;
    private String msg;
    private int totals;
    private DataBean data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getTotals() {
        return totals;
    }

    public void setTotals(int totals) {
        this.totals = totals;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * saleListOrder : 6
         * saleTotalSum : 66.66
         * singlePrice : 555.55
         * saleDay : 1
         * saleHistory : {"one":20,"four":10,"eight":5,"twelve":40,"sixteen":20,"twenty":5}
         * saleKindTop3 : [{"kind_name":"水果","kind_val":20},{"kind_name":"烟酒","kind_val":20},{"kind_name":"生活用品","kind_val":20}]
         * saleGoodsTop3 : [{"goods_picturepath":"1.jpg"},{"goods_picturepath":"2.jpg"},{"goods_picturepath":"3.jpg"}]
         */

        private int saleListOrder;
        private String saleTotalSum;
        private String singlePrice;
        private String saleDay;
        private SaleHistoryBean saleHistory;
        private List<SaleKindTop3Bean> saleKindTop3;
        private List<SaleGoodsTop3Bean> saleGoodsTop3;

        public int getSaleListOrder() {
            return saleListOrder;
        }

        public void setSaleListOrder(int saleListOrder) {
            this.saleListOrder = saleListOrder;
        }

        public String getSaleTotalSum() {
            return saleTotalSum;
        }

        public void setSaleTotalSum(String saleTotalSum) {
            this.saleTotalSum = saleTotalSum;
        }

        public String getSinglePrice() {
            return singlePrice;
        }

        public void setSinglePrice(String singlePrice) {
            this.singlePrice = singlePrice;
        }

        public String getSaleDay() {
            return saleDay;
        }

        public void setSaleDay(String saleDay) {
            this.saleDay = saleDay;
        }

        public SaleHistoryBean getSaleHistory() {
            return saleHistory;
        }

        public void setSaleHistory(SaleHistoryBean saleHistory) {
            this.saleHistory = saleHistory;
        }

        public List<SaleKindTop3Bean> getSaleKindTop3() {
            return saleKindTop3;
        }

        public void setSaleKindTop3(List<SaleKindTop3Bean> saleKindTop3) {
            this.saleKindTop3 = saleKindTop3;
        }

        public List<SaleGoodsTop3Bean> getSaleGoodsTop3() {
            return saleGoodsTop3;
        }

        public void setSaleGoodsTop3(List<SaleGoodsTop3Bean> saleGoodsTop3) {
            this.saleGoodsTop3 = saleGoodsTop3;
        }

        public static class SaleHistoryBean {
            /**
             * one : 20
             * four : 10
             * eight : 5
             * twelve : 40
             * sixteen : 20
             * twenty : 5
             */

            private int zero;
            private int four;
            private int eight;
            private int twelve;
            private int sixteen;
            private int twenty;

            public int getZero() {
                return zero;
            }

            public void setZero(int zero) {
                this.zero = zero;
            }

            public int getFour() {
                return four;
            }

            public void setFour(int four) {
                this.four = four;
            }

            public int getEight() {
                return eight;
            }

            public void setEight(int eight) {
                this.eight = eight;
            }

            public int getTwelve() {
                return twelve;
            }

            public void setTwelve(int twelve) {
                this.twelve = twelve;
            }

            public int getSixteen() {
                return sixteen;
            }

            public void setSixteen(int sixteen) {
                this.sixteen = sixteen;
            }

            public int getTwenty() {
                return twenty;
            }

            public void setTwenty(int twenty) {
                this.twenty = twenty;
            }
        }

        public static class SaleKindTop3Bean {
            /**
             * kind_name : 水果
             * kind_val : 20
             */

            private String kind_name;
            private int kind_val;

            public String getKind_name() {
                return kind_name;
            }

            public void setKind_name(String kind_name) {
                this.kind_name = kind_name;
            }

            public int getKind_val() {
                return kind_val;
            }

            public void setKind_val(int kind_val) {
                this.kind_val = kind_val;
            }
        }

        public static class SaleGoodsTop3Bean {
            /**
             * goods_picturepath : 1.jpg
             */

            private String goods_picturepath;

            public String getGoods_picturepath() {
                return goods_picturepath;
            }

            public void setGoods_picturepath(String goods_picturepath) {
                this.goods_picturepath = goods_picturepath;
            }
        }
    }
}