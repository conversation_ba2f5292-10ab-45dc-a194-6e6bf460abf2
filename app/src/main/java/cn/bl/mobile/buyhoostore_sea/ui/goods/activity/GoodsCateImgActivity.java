package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsCateImgAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsCateImgData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品分类-图标
 * Created by jingang on 2023/8/22
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class GoodsCateImgActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String imgId;

    private GoodsCateImgAdapter mAdapter;
    private List<GoodsCateImgData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_cate_img;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("choose")+getLanguageValue("classification")+getLanguageValue("icon"));
        imgId = getIntent().getStringExtra("imgId");
        setAdapter();
    }

    @Override
    public void initData() {
        getCateImg();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new GridLayoutManager(this, 4));
        mAdapter = new GoodsCateImgAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            setResult(Constants.CATE, new Intent()
                    .putExtra("id", String.valueOf(dataList.get(position).getGoods_kind_icon_id()))
                    .putExtra("img", dataList.get(position).getGoods_kind_icon_picture())
            );
            finish();
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getCateImg();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 分类图标
     */
    private void getCateImg() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("icon_type", 1);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsCateImg(),
                map,
                GoodsCateImgData.class,
                new RequestListListener<GoodsCateImgData>() {
                    @Override
                    public void onResult(List<GoodsCateImgData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (!TextUtils.isEmpty(imgId)) {
                            for (int i = 0; i < dataList.size(); i++) {
                                if(String.valueOf(dataList.get(i).getGoods_kind_icon_id()).equals(imgId)){
                                    dataList.get(i).setCheck(true);
                                }
                            }
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
