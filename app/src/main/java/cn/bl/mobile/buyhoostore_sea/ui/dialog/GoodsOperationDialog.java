package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.yxl.commonlibrary.utils.DensityUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（商品-操作）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class GoodsOperationDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvDialogTitle;
    @BindView(R.id.tvDialogStatus)
    TextView tvDialogStatus;
    @BindView(R.id.tvDialogPrint)
    TextView tvDialogPrint;
    @BindView(R.id.tvDialogChu)
    TextView tvDialogChu;
    @BindView(R.id.tvDialogRu)
    TextView tvDialogRu;
    @BindView(R.id.tvDialogCate)
    TextView tvDialogCate;
    @BindView(R.id.tvDialogPan)
    TextView tvDialogPan;
    @BindView(R.id.tvDialogChange)
    TextView tvDialogChange;
    @BindView(R.id.tvDialogDel)
    TextView tvDialogDel;

    public static void showDialog(Context context, MyListener listener) {
        GoodsOperationDialog.listener = listener;
        GoodsOperationDialog dialog = new GoodsOperationDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public GoodsOperationDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_more);
        ButterKnife.bind(this);
        tvDialogTitle.setText(getLanguageValue("commodity")+getLanguageValue("operation"));
        tvDialogStatus.setText(getLanguageValue("commodity")+getLanguageValue("onOffShelf"));
        tvDialogPrint.setText(getLanguageValue("printTag"));
        tvDialogChu.setText(getLanguageValue("commodity")+getLanguageValue("outOfTheWarehouse"));
        tvDialogRu.setText(getLanguageValue("commodity")+getLanguageValue("warehousing"));
        tvDialogCate.setText(getLanguageValue("transferClass"));
        tvDialogPan.setText(getLanguageValue("diskLibrary")+getLanguageValue("record"));
        tvDialogChange.setText(getLanguageValue("change")+getLanguageValue("record"));
        tvDialogDel.setText(getLanguageValue("delete")+getLanguageValue("commodity"));
    }

    @OnClick({R.id.linDialogStatus, R.id.linDialogPrint,
            R.id.linDialogChu, R.id.linDialogRu,
            R.id.linDialogCate, R.id.linDialogPan,
            R.id.linDialogDel})
    public void onViewClicked(View view) {
        if (listener == null) {
            return;
        }
        switch (view.getId()) {
            case R.id.linDialogStatus:
                //商品上下架
                listener.onStatus();
                break;
            case R.id.linDialogPrint:
                //打印价签
                listener.onPrint();
                break;
            case R.id.linDialogChu:
                //商品出库
                listener.onChu();
                break;
            case R.id.linDialogRu:
                //商品入库
                listener.onRu();
                break;
            case R.id.linDialogCate:
                //转移分类
                listener.onCate();
                break;
            case R.id.linDialogPan:
                //盘库记录
                listener.onPan();
                break;
            case R.id.linDialogDel:
                //删除商品
                listener.onDel();
                break;
        }
        dismiss();
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        void onStatus();

        void onPrint();

        void onChu();

        void onRu();

        void onCate();

        void onPan();

        void onDel();
    }
}
