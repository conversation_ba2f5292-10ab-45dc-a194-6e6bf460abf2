package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.ConditionData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ConditionAdapter;


/**
 * Describe:条件选择弹窗
 * Created by jingang on 2022/12/6
 */
public class ConditionDialog extends Dialog {
    private TextView tvTitle;
    private ImageView ivClose;
    private RecyclerView recyclerView;

    private String title;

    private List<ConditionData> list = new ArrayList<>();
    private ConditionAdapter adapter;

    public static void showDialog(Context mContext, String title, List<ConditionData> list, MyOnClickListener onClickListener) {
        ConditionDialog dialog = new ConditionDialog(mContext);
        dialog.setTitle(title);
        dialog.setDate(list);
        dialog.setOnClickListener(onClickListener);
        dialog.show();
    }

    public ConditionDialog(Context context) {
        super(context, R.style.dialog_style);
        setContentView(R.layout.dialog_condition);
        getWindow().setWindowAnimations(R.style.dialog_bottom);
        getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);

        tvTitle = findViewById(R.id.tvDialogTitle);
        ivClose = findViewById(R.id.ivDialogClose);
        recyclerView = findViewById(R.id.rvDialog);
        ivClose.setOnClickListener(v -> dismiss());

        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new ConditionAdapter(getContext());
        recyclerView.setAdapter(adapter);
        adapter.setOnItemClickListener((view, position) -> {
            if (!list.get(position).isCheck()) {
                for (int i = 0; i < list.size(); i++) {
                    list.get(i).setCheck(false);
                }
                list.get(position).setCheck(true);
                adapter.setDataList(list);
                if (onClickListener != null) {
                    onClickListener.onClick(list.get(position).getName(), list.get(position).getValue(), list);
                }
                dismiss();
            }
        });
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
    }

    /**
     * 设置标题信息
     *
     * @param title
     */
    public ConditionDialog setTitle(String title) {
        this.title = title;
        if (tvTitle != null && title != null) {
            tvTitle.setText(title);
        }
        return this;
    }

    /**
     * 设置条件列表信息
     *
     * @param list
     */
    public ConditionDialog setDate(List<ConditionData> list) {
        this.list = list;
        if (recyclerView != null && list != null && adapter != null) {
            adapter.setDataList(list);
        }
        return this;
    }

    private MyOnClickListener onClickListener;

    public void setOnClickListener(MyOnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }

    public interface MyOnClickListener {
        void onClick(String name, int value, List<ConditionData> list);
    }

}
