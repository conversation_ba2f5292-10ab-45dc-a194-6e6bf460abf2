package cn.bl.mobile.buyhoostore_sea.bean;

public class SwitchResponseModel {


	/**
	 * status : 1
	 * msg : 查询成功！
	 * data : {"shopFlower":2,"shopLaundry":1,"shopPur":1,"shopFruit":2,"shopUnique":8302016134121,"shopDeliverwater":1,"shopHomemarking":2,"shopCake":1,"shopExpress":1}
	 * pageIndex : null
	 * pageSize : null
	 * pageCount : null
	 * redundant : null
	 */

	private int status;
	private String msg;
	private DataBean data;
	private Object pageIndex;
	private Object pageSize;
	private Object pageCount;
	private Object redundant;

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public DataBean getData() {
		return data;
	}

	public void setData(DataBean data) {
		this.data = data;
	}

	public Object getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(Object pageIndex) {
		this.pageIndex = pageIndex;
	}

	public Object getPageSize() {
		return pageSize;
	}

	public void setPageSize(Object pageSize) {
		this.pageSize = pageSize;
	}

	public Object getPageCount() {
		return pageCount;
	}

	public void setPageCount(Object pageCount) {
		this.pageCount = pageCount;
	}

	public Object getRedundant() {
		return redundant;
	}

	public void setRedundant(Object redundant) {
		this.redundant = redundant;
	}

	public static class DataBean {
		/**
		 * shopFlower : 2
		 * shopLaundry : 1
		 * shopPur : 1
		 * shopFruit : 2
		 * shopUnique : 8302016134121
		 * shopDeliverwater : 1
		 * shopHomemarking : 2
		 * shopCake : 1
		 * shopExpress : 1
		 */

		private String shopFlower;
		private String shopLaundry;
		private String shopPur;
		private String shopFruit;
		private String shopUnique;
		private String shopDeliverwater;
		private String shopHomemarking;
		private String shopCake;
		private String shopExpress;

		public String getShopFlower() {
			return shopFlower;
		}

		public void setShopFlower(String shopFlower) {
			this.shopFlower = shopFlower;
		}

		public String getShopLaundry() {
			return shopLaundry;
		}

		public void setShopLaundry(String shopLaundry) {
			this.shopLaundry = shopLaundry;
		}

		public String getShopPur() {
			return shopPur;
		}

		public void setShopPur(String shopPur) {
			this.shopPur = shopPur;
		}

		public String getShopFruit() {
			return shopFruit;
		}

		public void setShopFruit(String shopFruit) {
			this.shopFruit = shopFruit;
		}

		public String getShopUnique() {
			return shopUnique;
		}

		public void setShopUnique(String shopUnique) {
			this.shopUnique = shopUnique;
		}

		public String getShopDeliverwater() {
			return shopDeliverwater;
		}

		public void setShopDeliverwater(String shopDeliverwater) {
			this.shopDeliverwater = shopDeliverwater;
		}

		public String getShopHomemarking() {
			return shopHomemarking;
		}

		public void setShopHomemarking(String shopHomemarking) {
			this.shopHomemarking = shopHomemarking;
		}

		public String getShopCake() {
			return shopCake;
		}

		public void setShopCake(String shopCake) {
			this.shopCake = shopCake;
		}

		public String getShopExpress() {
			return shopExpress;
		}

		public void setShopExpress(String shopExpress) {
			this.shopExpress = shopExpress;
		}
	}
}
