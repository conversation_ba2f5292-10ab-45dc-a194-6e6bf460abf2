package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.SupplierDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（选择供货商）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class SupplierDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private static String goodsBarcode;
    private static int type;//1.列表添加全部供货商选项

    private SupplierDialogAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    public static void showDialog(Context context, String goodsBarcode, MyListener listener) {
        SupplierDialog.goodsBarcode = goodsBarcode;
        SupplierDialog.listener = listener;
        SupplierDialog dialog = new SupplierDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public static void showDialog(Context context, String goodsBarcode, int type, MyListener listener) {
        SupplierDialog.goodsBarcode = goodsBarcode;
        SupplierDialog.type = type;
        SupplierDialog.listener = listener;
        SupplierDialog dialog = new SupplierDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public SupplierDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_supplier);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("supplier")+getLanguageValue("choose"));
        setAdapter();
        getSupplierList();
    }

    @OnClick({R.id.ivDialogClose})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new SupplierDialogAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onClick(dataList.get(position));
                dismiss();
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getSupplierList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
//        if(TextUtils.isEmpty(goodsBarcode)){
//            map.put("goodsBarcode", "");
//        }else{
//            map.put("goodsBarcode", goodsBarcode);
//        }
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getRestockSupplierList(),
                map,
                SupplierData.class,
                new RequestListListener<SupplierData>() {
                    @Override
                    public void onResult(List<SupplierData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            if (type == 1) {
//                                dataList.add(0, new SupplierData("全部供货商", "",0));
                                dataList.add(0, new SupplierData(getLanguageValue("whole")+getLanguageValue("supplier"), "",0));
                            }
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(SupplierData data);
    }
}
