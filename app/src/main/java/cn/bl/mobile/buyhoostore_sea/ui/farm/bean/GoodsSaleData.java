package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:
 * Created by jingang on 2023/5/29
 */
public class GoodsSaleData implements Serializable {

    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"percentage":97.26,"count":136.62,"sum":31586.44,"goodsName":"大白菜"},{"percentage":1.23,"count":20.42,"sum":400.98,"goodsName":"小麦种子"},{"percentage":0.37,"count":3,"sum":120,"goodsName":"芹菜"},{"percentage":0.31,"count":8.35,"sum":100.7,"goodsName":"香飘飘奶茶原味"},{"percentage":0.25,"count":8.93,"sum":82.5,"goodsName":"猪肉"},{"percentage":0.12,"count":4,"sum":40,"goodsName":"VG很快就"},{"percentage":0.1,"count":6.35,"sum":34,"goodsName":"土鸡"},{"percentage":0.09,"count":2,"sum":30,"goodsName":"鱼香肉丝盖浇面"},{"percentage":0.07,"count":0.83,"sum":23.24,"goodsName":"小米"},{"percentage":0.03,"count":1,"sum":9,"goodsName":"小王子铜锣烧红豆味"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : {"saleCount":270.97,"standardSaleCount":228.06,"saleSum":32473.3587,"chengSaleCount":88.91}
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private ObjectBean object;
    private Object count;
    private Object total;
    private Object rows;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public ObjectBean getObject() {
        return object;
    }

    public void setObject(ObjectBean object) {
        this.object = object;
    }

    public Object getCount() {
        return count;
    }

    public void setCount(Object count) {
        this.count = count;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class ObjectBean {
        /**
         * saleCount : 270.97
         * standardSaleCount : 228.06
         * saleSum : 32473.3587
         * chengSaleCount : 88.91
         */

        private double saleCount;
        private double standardSaleCount;
        private double saleSum;
        private double chengSaleCount;

        public double getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(double saleCount) {
            this.saleCount = saleCount;
        }

        public double getStandardSaleCount() {
            return standardSaleCount;
        }

        public void setStandardSaleCount(double standardSaleCount) {
            this.standardSaleCount = standardSaleCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getChengSaleCount() {
            return chengSaleCount;
        }

        public void setChengSaleCount(double chengSaleCount) {
            this.chengSaleCount = chengSaleCount;
        }
    }

    public static class DataBean {
        /**
         * percentage : 97.26
         * count : 136.62
         * sum : 31586.44
         * goodsName : 大白菜
         */

        private double percentage;
        private double count;
        private double sum;
        private String goodsName;
        private String goodsPicturePath;
        private String goodsChengType;
        private String goodsType;

        public double getPercentage() {
            return percentage;
        }

        public void setPercentage(double percentage) {
            this.percentage = percentage;
        }

        public double getCount() {
            return count;
        }

        public void setCount(double count) {
            this.count = count;
        }

        public double getSum() {
            return sum;
        }

        public void setSum(double sum) {
            this.sum = sum;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsPicturePath() {
            return goodsPicturePath;
        }

        public void setGoodsPicturePath(String goodsPicturePath) {
            this.goodsPicturePath = goodsPicturePath;
        }

        public String getGoodsChengType() {
            return goodsChengType;
        }

        public void setGoodsChengType(String goodsChengType) {
            this.goodsChengType = goodsChengType;
        }

        public String getGoodsType() {
            return goodsType;
        }

        public void setGoodsType(String goodsType) {
            this.goodsType = goodsType;
        }
    }
}
