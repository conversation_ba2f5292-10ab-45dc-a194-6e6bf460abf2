package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;


import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.OrderInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.FarmOrderGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-订单-详情
 * Created by jingang on 2023/5/25
 */
@SuppressLint("NonConstantResourceId")
public class FarmOrderInfoActivity extends BaseActivity {
    @BindView(R.id.linTop)
    LinearLayout linTop;
    @BindView(R.id.tvNo)
    TextView tvNo;
    @BindView(R.id.tvType)
    TextView tvType;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvPrice)
    TextView tvPrice;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.rvGoods)
    RecyclerView rvGoods;
    @BindView(R.id.tvCancel)
    TextView tvCancel;

    private String unique;

    private FarmOrderGoodsAdapter mAdapter;
    private List<OrderInfoData.DataBean.ListDetailBean> goodsList = new ArrayList<>();

    @Override
    public int getLayoutId() {
        return R.layout.activity_farm_order_info;
    }

    @Override
    public void initViews() {
        unique = getIntent().getStringExtra("unique");
        setAdapter();
    }

    @Override
    public void initData() {
        getOrder_info();
    }

    @OnClick({R.id.ivBack, R.id.tvCancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCancel:
                //作废
                postOrder_cancel();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new FarmOrderGoodsAdapter(this);
        rvGoods.setAdapter(mAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void setUI(OrderInfoData.DataBean data) {
        //4.已结清 5.已作废
        switch (data.getHandleStateCode()) {
            case 4:
                linTop.setBackgroundColor(getResources().getColor(R.color.green));
                tvType.setText("已结清");
                tvType.setTextColor(getResources().getColor(R.color.green));
                tvCancel.setVisibility(View.VISIBLE);
                break;
            case 5:
                linTop.setBackgroundColor(getResources().getColor(R.color.grey_b2b2b2));
                tvType.setText("已作废");
                tvType.setTextColor(getResources().getColor(R.color.grey_b2b2b2));
                tvCancel.setVisibility(View.GONE);
                break;
        }
        tvNo.setText(String.valueOf(data.getSaleListUnique()));
        tvType.setText(data.getSaleListPayment());
        tvName.setText(data.getSaleListName());
        tvPrice.setText("RM" + DFUtils.getNum2(data.getSaleListTotal()));
        tvTime.setText(data.getDateTime() + " " + data.getStaffName());
        tvTotal.setText("合计：RM" + DFUtils.getNum2(data.getSaleListTotal()));
        //商品
        goodsList.clear();
        goodsList.addAll(data.getListDetail());
        mAdapter.setDataList(goodsList);
    }

    /**
     * 销售订单详情
     */
    private void getOrder_info() {
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getOrderDetailUrlTWO(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderInfoData data = new Gson().fromJson(s, OrderInfoData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setUI(data.getData());
                        }
                    }
                });
    }

    /**
     * 订单作废
     */
    private void postOrder_cancel() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("saleListUnique", unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getFarm_salelist_cancel(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            setResult(Constants.ORDER, new Intent());
                            finish();
                        }
                    }
                });
    }
}
