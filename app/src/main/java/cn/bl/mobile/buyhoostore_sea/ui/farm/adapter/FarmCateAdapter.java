package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;

/**
 * Describe:农批-分类（适配器）
 * Created by jingang on 2023/5/24
 */
public class FarmCateAdapter extends BaseAdapter<CateData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public FarmCateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_farm;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin = holder.getView(R.id.linItem);
        View v = holder.getView(R.id.vDialog);
        TextView tvCate = holder.getView(R.id.tvItemCate);
        tvCate.setText(mDataList.get(position).getGroupName());
        if (mDataList.get(position).isCheck()) {
            lin.setBackgroundResource(R.drawable.shape_33cc67_right_10);
            v.setVisibility(View.VISIBLE);
            tvCate.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            lin.setBackgroundResource(0);
            v.setVisibility(View.INVISIBLE);
            tvCate.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
    }
}
