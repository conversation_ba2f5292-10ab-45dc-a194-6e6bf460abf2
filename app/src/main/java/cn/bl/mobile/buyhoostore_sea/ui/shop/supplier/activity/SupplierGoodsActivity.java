package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gengcon.www.jcprintersdk.callback.PrintCallback;
import com.google.gson.Gson;
import com.gprinter.command.LabelCommand;
import com.gprinter.utils.LogUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.printer.PrinterSettingActivity;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrintUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.Printer;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.ThreadPoolManager;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsChangeRecordActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsEditActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ChuRuSelectActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanGoodsRecordActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-供货商管理-供货商详情-所供商品
 * Created by jingang on 2023/9/4
 */
@SuppressLint("NonConstantResourceId")
public class SupplierGoodsActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String supplierUnique, supplierName;
    private int type = 1;//0.未建档 1.已建档

    private SupplierGoodsAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();

    private String goodsBarcode,//商品编码
            goodsName,//商品名称
            goodsPrice,//商品价格
            goodsUnit,//商品单位
            goodsImg;//商品图片
    private int goodsId,
            printerStatus,//收银机上架状态 1.上架 2.下架
            appletStatus;//小程序上架状态 1.上架 2.下架

    //打印价签
    private SharedPreferencesUtil sharedPreferencesUtil;
    private int printerType;//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
    private boolean isError,//是否打印错误
            isCancel;//是否取消打印
    private ArrayList<String> jsonList = new ArrayList<>(),//图像数据
            infoList = new ArrayList<>();//图像处理数据

    //本地缓存
    private SharedPreferences sp;
    public static String powerAdd;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier_goods;
    }

    @Override
    public void initViews() {
        supplierUnique = getIntent().getStringExtra("unique");
        supplierName = getIntent().getStringExtra("name");
        tvTitle.setText(getLanguageValue("goodsSupplied"));
        tvType0.setText(getLanguageValue("filed")+getLanguageValue("commodity"));
        tvType1.setText(getLanguageValue("notFiled")+getLanguageValue("commodity"));
        sp = getSharedPreferences("shop", Context.MODE_PRIVATE);
        powerAdd = sp.getString("power_add", "0");
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
        setAdapter();
    }

    @Override
    public void initData() {
        getGoodsList();
    }

    @OnClick({R.id.ivBack, R.id.tvType0, R.id.tvType1})
    public void onViewClick(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvType0:
                //已建档商品
                if (type != 1) {
                    type = 1;
                    clearTextBg();
                    tvType0.setBackgroundResource(R.drawable.shape_blue_tm_kuang_8);
                    tvType0.setTextColor(getResources().getColor(R.color.blue));
                    page = 1;
                    getGoodsList();
                }
                break;
            case R.id.tvType1:
                //未建档商品
                if (type != 0) {
                    type = 0;
                    clearTextBg();
                    tvType1.setBackgroundResource(R.drawable.shape_blue_tm_kuang_8);
                    tvType1.setTextColor(getResources().getColor(R.color.blue));
                    page = 1;
                    getGoodsList();
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_LIST:
                page = 1;
                getGoodsList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new SupplierGoodsAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierGoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
            }

            @Override
            public void onMoreClick(View view, int position) {
                //更多（商品操作）
                goodsId = dataList.get(position).getId();
                goodsBarcode = dataList.get(position).getGoodsBarcode();
                goodsName = dataList.get(position).getGoodsName();
                goodsImg = dataList.get(position).getGoodsPicturePath();
                goodsPrice = String.valueOf(dataList.get(position).getGoodsSalePrice());
                goodsUnit = dataList.get(position).getGoodsUnit();
                printerStatus = dataList.get(position).getPcShelfState();
                appletStatus = dataList.get(position).getShelfState();
                showDialogMore();
            }

            @Override
            public void onConfirmClick(View view, int position) {
                //建档
                if ("1".equals(powerAdd)) {
                    getGoodsDetail(dataList.get(position).getGoodsBarcode(), dataList.get(position));
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsList();
            }
        });
    }

    /**
     * 清除按钮背景样式
     */
    private void clearTextBg() {
        tvType0.setBackgroundResource(R.drawable.shape_white_8);
        tvType0.setTextColor(getResources().getColor(R.color.color_333));
        tvType1.setBackgroundResource(R.drawable.shape_white_8);
        tvType1.setTextColor(getResources().getColor(R.color.color_333));
    }

    /**
     * 所供商品
     */
    private void getGoodsList() {
        if (mAdapter != null) {
            mAdapter.setType(type);
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        map.put("recordStatus", type);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierInfoGoods(),
                map,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /******************************dialog-start*********************************/

    //单个操作
    private Dialog dialogMore;

    //上下架平台
    private Dialog dialogPlatform;
    private ImageView ivPrinterUp, ivPrinterDown, ivAppletUp, ivAppletDown;

    /**
     * dialog（商品操作）
     */
    private void showDialogMore() {
        dialogMore = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_more, null);
        dialogMore.setContentView(view);

        TextView tvTitle, tvStatus, tvPrint, tvChu, tvRu, tvCate, tvPan, tvChange, tvDel;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvStatus = view.findViewById(R.id.tvDialogStatus);
        tvPrint = view.findViewById(R.id.tvDialogPrint);
        tvChu = view.findViewById(R.id.tvDialogChu);
        tvRu = view.findViewById(R.id.tvDialogRu);
        tvCate = view.findViewById(R.id.tvDialogCate);
        tvPan = view.findViewById(R.id.tvDialogPan);
        tvChange = view.findViewById(R.id.tvDialogChange);
        tvDel = view.findViewById(R.id.tvDialogDel);

        view.findViewById(R.id.linDialogCate).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogPrint).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogChu).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogRu).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogStatus).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogPan).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogChange).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogDel).setOnClickListener(new MyClickListener());

        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvStatus.setText(getLanguageValue("commodity") + getLanguageValue("onOffShelf"));
        tvPrint.setText(getLanguageValue("printTag"));
        tvChu.setText(getLanguageValue("commodity") + getLanguageValue("outOfTheWarehouse"));
        tvRu.setText(getLanguageValue("commodity") + getLanguageValue("warehousing"));
        tvCate.setText(getLanguageValue("transferClass"));
        tvPan.setText(getLanguageValue("diskLibrary") + getLanguageValue("record"));
        tvChange.setText(getLanguageValue("change") + getLanguageValue("record"));
        tvDel.setText(getLanguageValue("delete") + getLanguageValue("commodity"));

        Window window = dialogMore.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialogMore.show();
    }

    /**
     * dialog（选择上下架平台）
     */
    private void showDialogPlatform() {
        dialogPlatform = new Dialog(TAG, R.style.dialog_bottom);
        View view = View.inflate(TAG, R.layout.dialog_platform, null);
        dialogPlatform.setContentView(view);

        TextView tvTitle, tvPrinter, tvPrinterUp, tvPrinterDown, tvApplet, tvAppletUp, tvAppletDown, tvConfirm;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvPrinter = view.findViewById(R.id.tvDialogPrinter);
        tvPrinterUp = view.findViewById(R.id.tvDialogPrinterUp);
        tvPrinterDown = view.findViewById(R.id.tvDialogPrinterDown);
        tvApplet = view.findViewById(R.id.tvDialogApplet);
        tvAppletUp = view.findViewById(R.id.tvDialogAppletUp);
        tvAppletDown = view.findViewById(R.id.tvDialogAppletDown);
        tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        ivPrinterUp = view.findViewById(R.id.ivDialogPrinterUp);
        ivPrinterDown = view.findViewById(R.id.ivDialogPrinterDown);
        ivAppletUp = view.findViewById(R.id.ivDialogAppletUp);
        ivAppletDown = view.findViewById(R.id.ivDialogAppletDown);
        ivPrinterUp.setOnClickListener(new MyClickListener());
        ivPrinterDown.setOnClickListener(new MyClickListener());
        ivAppletUp.setOnClickListener(new MyClickListener());
        ivAppletDown.setOnClickListener(new MyClickListener());
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvPrinter.setText(getLanguageValue("cashRegister"));
        tvPrinterUp.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown.setText(getLanguageValue("offTheShelf"));
        tvApplet.setText(getLanguageValue("miniProgram"));
        tvAppletUp.setText(getLanguageValue("onTheShelf"));
        tvAppletDown.setText(getLanguageValue("offTheShelf"));
        tvConfirm.setText(getLanguageValue("confirm"));

        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
        switch (printerStatus) {
            case 1:
                ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }
        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
        switch (appletStatus) {
            case 1:
                ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }

        Window window = dialogPlatform.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialogPlatform.show();

        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> {
            if (printerStatus == 0) {
                showMessage(getLanguageValue("cashRegisterStatus"));
                return;
            }
            if (appletStatus == 0) {
                showMessage(getLanguageValue("appletStatus"));
                return;
            }
            postGoods();
            dialogPlatform.dismiss();
        });
    }

    public class MyClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            Log.e(tag, "v = " + v.getId());
            switch (v.getId()) {
                //商品操作
                case R.id.linDialogCate:
                    //转移分类
                    startActivityForResult(new Intent(TAG, GoodsCateActivity.class)
                                    .putExtra("type", 1)
                            , Constants.CATE);
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogPrint:
                    //打印价签
                    setPrint();
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogChu:
                    //商品出库
                    if (TextUtils.isEmpty(goodsBarcode)) {
                        showMessage("该商品暂无条码");
                        return;
                    }
                    startActivity(new Intent(TAG, ChuRuSelectActivity.class)
                            .putExtra("result", goodsBarcode)
                            .putExtra("isRu", 2)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogRu:
                    //商品入库
                    if (TextUtils.isEmpty(goodsBarcode)) {
                        showMessage(getLanguageValue("noBarcode"));
                        return;
                    }
                    startActivity(new Intent(TAG, ChuRuSelectActivity.class)
                            .putExtra("result", goodsBarcode)
                            .putExtra("isRu", 1)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogStatus:
                    //上下架
                    showDialogPlatform();
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogPan:
                    //盘库记录
                    startActivity(new Intent(TAG, PanGoodsRecordActivity.class)
                            .putExtra("goodsBarcode", goodsBarcode)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogChange:
                    //变更记录
                    startActivity(new Intent(TAG, GoodsChangeRecordActivity.class)
                            .putExtra("goodsBarcode", goodsBarcode)
                            .putExtra("goodsName", goodsName)
                            .putExtra("img", goodsImg)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogDel:
                    //删除商品
                    IAlertDialog.showDialog(TAG,
                            getLanguageValue("deleteProduct") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postGoodsDel();
                                if (dialogMore != null) {
                                    dialogMore.dismiss();
                                }
                            });
                    break;
                //选择上下架平台
                case R.id.ivDialogPrinterUp:
                    //收银机上架
                    if (printerStatus == 1) {
                        printerStatus = 0;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        printerStatus = 1;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    }
                    break;
                case R.id.ivDialogPrinterDown:
                    //收银机下架
                    if (printerStatus == 2) {
                        printerStatus = 0;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        printerStatus = 2;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                    }
                    break;
                case R.id.ivDialogAppletUp:
                    //小程序上架
                    if (appletStatus == 1) {
                        appletStatus = 0;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        appletStatus = 1;
                        ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    }
                    break;
                case R.id.ivDialogAppletDown:
                    //小程序下架
                    if (appletStatus == 2) {
                        appletStatus = 0;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        appletStatus = 2;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                    }
                    break;
            }
        }
    }

    /**
     * 商品上架/下架
     */
    private void postGoods() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        params.put("pcShelfState", printerStatus);
        params.put("shelfState", appletStatus);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getGoodsShelfState(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            page = 1;
                            getGoodsList();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        showMessage(msg);
                        hideDialog();
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.deletegoods(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "删除商品 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            postSupplierGoodsDel();
                        } else {
                            showMessage(data.getMsg());
                        }
                    }
                });
    }

    /**
     * 删除商品（根据已建档商品ID和店铺编码删除已建档商品信息）
     */
    private void postSupplierGoodsDel() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("id", goodsId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoGoodsDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        page = 1;
                        getGoodsList();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 商品详情的接口
     *
     * @param barcode
     * @param goodsData
     */
    public void getGoodsDetail(String barcode, GoodsData goodsData) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", barcode);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getSelecDetail(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "商品详情 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object;
                        int status;
                        try {
                            object = new JSONObject(s);
                            status = object.getInt("status");
                            switch (status) {
                                case 1:
                                    startActivity(new Intent(TAG, GoodsEditActivity.class)
                                            .putExtra("supplierGoodsData", goodsData)
                                            .putExtra("goodsBarcode", barcode)
                                            .putExtra("type", 1)
                                            .putExtra("typeRecord", 1)
                                    );
                                    break;
                                case 2:
//                                    showMessage("未查询到商品信息");
                                    startActivity(new Intent(TAG, GoodsEditActivity.class)
                                            .putExtra("typeRecord", 1)
                                            .putExtra("supplierGoodsData", goodsData)
                                            .putExtra("supplierUnique", supplierUnique)
                                            .putExtra("supplierName", supplierName)
                                    );
                                    break;
                                default:
                                    showMessage("请求失败");
                                    break;
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }


    /******************************dialog-end*********************************/

    /***********************打印价签start**********************/
    /**
     * 打印价签
     */
    private void setPrint() {
        if (printerType == 0) {
            if (Printer.getPortManager() != null && Printer.getConnectState()) {
                if (TextUtils.isEmpty(goodsBarcode)) {
                    showMessage(getLanguageValue("commodity") + getLanguageValue("barcode") + getLanguageValue("notEmpty"));
                    return;
                }
                if (TextUtils.isEmpty(goodsName)) {
                    showMessage(getLanguageValue("commodity") + getLanguageValue("name") + getLanguageValue("notEmpty"));
                    return;
                }
                if (TextUtils.isEmpty(goodsPrice)) {
                    showMessage(getLanguageValue("commodity") + getLanguageValue("sellingPrice") + getLanguageValue("notEmpty"));
                    return;
                }

                ThreadPoolManager.getInstance().addTask(() -> {
                    try {
                        if (Printer.getPortManager() == null) {
                            runOnUiThread(() -> {
                                showMessage(getLanguageValue("connectPrinter"));
                            });
                            return;
                        }
                        boolean result = Printer.getPortManager().writeDataImmediately(getLabel(goodsBarcode, goodsName, goodsPrice));
                        runOnUiThread(() -> {
                            if (result) {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                            } else {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                            }
                        });
                        LogUtils.e("send result", result);
                    } catch (Exception e) {
                        runOnUiThread(() -> {
                            tipsDialog(getLanguageValue("print") + getLanguageValue("failed") + e.getMessage());
                        });
                    } finally {
                        if (Printer.getPortManager() == null) {
                            Printer.close();
                        }
                    }
                });
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        } else {
            if (PrintUtil.isConnection() == 0) {
                printLabel();
            } else {
                startActivityForResult(new Intent(this, PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        }
    }

    /**
     * 标签打印测试页
     *
     * @param goodsBarcode
     * @param name
     * @param price
     * @return
     */
    public Vector<Byte> getLabel(String goodsBarcode, String name, String price) {
        int printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);
        LabelCommand tsc = new LabelCommand();
        // 设置标签尺寸宽高，按照实际尺寸设置 单位mm
        tsc.addUserCommand("\r\n");
        if (printType == 4) {
            tsc.addSize(50, 30);
        } else {
            tsc.addSize(70, 38);
        }
        // 设置标签间隙，按照实际尺寸设置，如果为无间隙纸则设置为0 单位mm
        tsc.addGap(2);
        //设置纸张类型为黑标，发送BLINE 指令不能同时发送GAP指令
//        tsc.addBline(2);
        // 设置打印方向
//        tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
        // 设置原点坐标
        tsc.addReference(0, 0);
        //设置浓度
        tsc.addDensity(LabelCommand.DENSITY.DNESITY4);
        // 撕纸模式开启
        tsc.addTear(LabelCommand.RESPONSE_MODE.ON);
        // 清除打印缓冲区
        tsc.addCls();
        //标签方向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //正向
            tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, name);
                    tsc.addText(415, 194, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(115, 154, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 2:
                    tsc.addText(230, 16, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, getShop_name());
                    tsc.addText(88, 76, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, name);
                    tsc.addText(400, 216, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(60, 185, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 4:
                    tsc.addText(155, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(44, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(255, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(35, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            25,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        } else {
            //反向
            tsc.addDirection(LabelCommand.DIRECTION.BACKWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, name);
                    tsc.addText(415, 194, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(115, 154, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 2:
                    tsc.addText(270, 16, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, getShop_name());
                    tsc.addText(118, 73, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, name);
                    tsc.addText(420, 216, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
                    tsc.add1DBarcode(100, 185, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
                    break;
                case 4:
                    tsc.addText(195, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            getShop_name());
                    tsc.addText(84, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(295, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(75, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            20,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        }
//        if (sharedPreferencesUtil.getString("1", "printType").equals("1")) {
//            tsc.addText(124, 25, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, name);
//            tsc.addText(415, 194, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
//            tsc.add1DBarcode(115, 154, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
//        } else {
//            tsc.addText(280, 16, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, shopName);
//            tsc.addText(138, 66, LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1, name);
//            tsc.addText(420, 216, LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2, price);
//            tsc.add1DBarcode(115, 185, LabelCommand.BARCODETYPE.CODE128, 40, LabelCommand.READABEL.EANBEL, LabelCommand.ROTATION.ROTATION_0, goodsBarcode);
//        }

//        //打印繁体
//        tsc.addUnicodeText(30,50, LabelCommand.FONTTYPE.TRADITIONAL_CHINESE, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"BIG5碼繁體中文","BIG5");
//        //打印韩文
//        tsc.addUnicodeText(30,80, LabelCommand.FONTTYPE.KOREAN, LabelCommand.ROTATION.ROTATION_0, LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"Korean 지아보 하성","EUC_KR");
        //英数字
//        tsc.addText(240,20, LabelCommand.FONTTYPE.FONT_1, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,goodsadd_shoujia1.getText().toString());
//        tsc.addText(250,20, LabelCommand.FONTTYPE.FONT_2, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"2");
//        tsc.addText(270,20, LabelCommand.FONTTYPE.FONT_3, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"3");
//        tsc.addText(300,20, LabelCommand.FONTTYPE.FONT_4, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"4");
//        tsc.addText(330,20, LabelCommand.FONTTYPE.FONT_5, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"5");
//        tsc.addText(240,40, LabelCommand.FONTTYPE.FONT_6, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"6");
//        tsc.addText(250,40, LabelCommand.FONTTYPE.FONT_7, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"7");
//        tsc.addText(270,40, LabelCommand.FONTTYPE.FONT_8, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"8");
//        tsc.addText(300,60, LabelCommand.FONTTYPE.FONT_9, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_1, LabelCommand.FONTMUL.MUL_1,"9");

//        tsc.addText(400,180, LabelCommand.FONTTYPE.FONT_4, LabelCommand.ROTATION.ROTATION_0,LabelCommand.FONTMUL.MUL_2, LabelCommand.FONTMUL.MUL_2,goodsadd_shoujia1.getText().toString());
//        Bitmap b = BitmapFactory.decodeResource(GoodsAddUpdateActivity.this.getResources(), R.mipmap.mylogo);
//        // 绘制图片
//        tsc.drawImage(30, 100, 300, b);
//        Bitmap b2= BitmapFactory.decodeResource(context.getResources(), R.drawable.main_fenlei);
//        tsc.drawJPGImage(200,250,200,b2);
        //绘制二维码
//        tsc.addQRCode(30,250, LabelCommand.EEC.LEVEL_L, 5, LabelCommand.ROTATION.ROTATION_0, " www.smarnet.cc");
        // 绘制一维条码
        // 打印标签
        tsc.addPrint(1, 1);
        // 打印标签后 蜂鸣器响
        tsc.addSound(2, 100);
        //开启钱箱
//        tsc.addCashdrwer(LabelCommand.FOOT.F5, 255, 255);
        Vector<Byte> datas = tsc.getCommand();
        // 发送数据
        return datas;
    }

    /**
     * 提示弹框
     *
     * @param message
     */
    private void tipsDialog(String message) {
        AlertDialog alertDialog = new AlertDialog.Builder(this)
                .setTitle(getLanguageValue("hint"))
                .setMessage(message)
                .setIcon(R.mipmap.mylogo)
                .setPositiveButton(getLanguageValue("confirm"), (dialogInterface, i) -> {
                    //添加"Yes"按钮
                }).create();
        alertDialog.show();
    }

    /**
     * 打印标签（精臣）
     */
    private void printLabel() {
        // 检查是否连接了打印机
        if (PrintUtil.isConnection() != 0) {
            showMessage(getLanguageValue("printerNotConnect"));
            return;
        }
        isError = false;
        isCancel = false;
        jsonList.clear();
        infoList.clear();
        PrintUtil.getInstance().setTotalPrintQuantity(1);
        /*
         * 参数1：打印浓度 ，参数2:纸张类型 参数3:打印模式
         * 打印浓度 B50/B50W/T6/T7/T8 建议设置6或8，Z401/B32建议设置8，B3S/B21/B203/B1建议设置3
         */
        PrintUtil.getInstance().startPrintJob(4, 1, 1, new PrintCallback() {
            @Override
            public void onProgress(int pageIndex, int quantityIndex, HashMap<String, Object> hashMap) {
                // 更新打印进度
                String progressMessage = "打印进度:已打印到第" + pageIndex + "页,第" + quantityIndex + "份";
                Log.d(tag, "测试:" + progressMessage);
                // 处理打印完成情况
                if (pageIndex == 1 && quantityIndex == 1) {
                    Log.d(tag, "测试:onProgress: 结束打印");
                    //endJob，使用方法含义更明确的endPrintJob
                    runOnUiThread(() -> {
                        if (PrintUtil.getInstance().endPrintJob()) {
                            Log.d(tag, "结束打印成功");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                        } else {
                            Log.d(tag, "结束打印失败");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                        }
                    });

                }
            }


            @Override
            public void onError(int i) {

            }


            @Override
            public void onError(int errorCode, int printState) {
                Log.d(tag, "测试：报错");
                isError = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("printError") + " errorCode = " + errorCode + " printState = " + printState);
                });
            }

            @Override
            public void onCancelJob(boolean isSuccess) {
                Log.d(tag, "onCancelJob: " + isSuccess);
                isCancel = true;
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("cancelPrint"));
                });
            }

            /**
             * SDK缓存空闲回调，可以在此处传入打印数据
             *
             * @param pageIndex 当前回调函数处理下一页的打印索引
             * @param bufferSize 缓存空间的大小
             */
            @Override
            public void onBufferFree(int pageIndex, int bufferSize) {
                // 如果出现错误、已取消打印，或 pageIndex 超过总页数，则返回
                if (isError || isCancel) {
                    return;
                }
                setPrinterData();
            }
        });
    }

    /**
     * 生成打印数据(精臣)
     */
    private void setPrinterData() {
        String printPrice;
        if (TextUtils.isEmpty(goodsUnit)) {
            printPrice = goodsPrice;
        } else {
            printPrice = goodsPrice + "/" + goodsUnit;
        }
        float width = 70, height = 38;
        /*
         * 设置画布⼤⼩
         *
         * @param width 画布宽度(mm)
         * @param height 画布⾼度(mm)
         * @param orientation 画布旋转⻆度
         * @param fontDir 字体路径暂不可⽤，默认""即可
         *
         */
        PrintUtil.getInstance().drawEmptyLabel(width, height, 0, "");

        //标签方向：0.正向 1.反向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //标签样式 1. 2. 3.
            if (sharedPreferencesUtil.getInt(2, Constants.PRINT_TYPE) == 3) {
                /**
                 * 绘制⽂本
                 * @param x 位置x
                 * @param y 位置y
                 * @param width 宽
                 * @param height ⾼
                 * @param value 内容
                 * @param fontFamily 字体名称,未传输字体为空字符串时使⽤默认字体,暂时⽤默认字体
                 * @param fontSize 字体⼤⼩
                 * @param rotate 旋转
                 * @param textAlignHorizontal ⽔平对⻬⽅式：0:左对⻬ 1:居中对⻬ 2:右对⻬
                 * @param textAlignVertical 垂直 对⻬⽅式：0:顶对⻬ 1:垂直居中 2:底对⻬
                 * @param lineModel 1:宽⾼固定，内容⼤⼩⾃适应（字号/字符间距/⾏间距 按⽐例缩放）
                2:宽度固定，⾼度⾃适应
                3:宽⾼固定，超出内容后⾯加...
                4:宽⾼固定,超出内容直裁切
                6:宽⾼固定，内容超过预设宽⾼时⾃动缩⼩（字号/字符间距/⾏间距 按⽐例缩放）
                 * @param letterSpace 字⺟之间的标准间隔，单位mm
                 * @param lineSpace ⾏间距（倍距），单位mm
                 * @param mFontStyles 字体样式[加粗，斜体，下划线，删除下划线（预留）]
                 */
                PrintUtil.getInstance().drawLabelText(40f, 13f, 30f, 5f, goodsName, "", 6f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                /**
                 * 绘制⼀维码
                 *
                 * @param x ⽔平坐标
                 * @param y 垂直坐标
                 * @param width 宽度,单位mm
                 * @param height ⾼度,单位mm
                 * @param codeType ⼀维码类型20:CODE128 ,21:UPC-A,22:UPC-E,23:EAN8,24:EAN13,
                 * 25:CODE93,26:CODE39,27:CODEBAR, 28:ITF25
                 * @param value ⽂本内容
                 * @param fontSize ⽂本字号
                 * @param rotate 旋转⻆度，仅⽀持0,90,180,270
                 * @param textHeight ⽂本⾼度
                 * @param textPosition ⽂本位置，int,⼀维码⽂字识别码显示位置,0:下⽅显示,1:上⽅显
                示,2:不显示
                 */
                PrintUtil.getInstance().drawLabelBarCode(25f, 22f, 40f, 7f, 20, goodsBarcode, 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(40f, 27f, 30f, 7f, "RM" + printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            } else {
                PrintUtil.getInstance().drawLabelText(31f, 0f, 39f, 5f, getShop_name(), "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelText(12f, 7f, 55f, 5f, goodsName, "", 6f,
                        0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                PrintUtil.getInstance().drawLabelBarCode(10f, 23f, 28f, 8f, 20, goodsBarcode, 3f, 0, 3f, 0);
                PrintUtil.getInstance().drawLabelText(48f, 25f, 23f, 7f, printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            }
        } else {
            PrintUtil.getInstance().drawLabelText(0f, 30f, 40f, 5f, getShop_name(), "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelText(0f, 22f, 60f, 5f, goodsName, "", 6f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

            PrintUtil.getInstance().drawLabelBarCode(34f, 5f, 28f, 8f, 20, goodsBarcode, 3f,
                    180, 3f, 0);
            PrintUtil.getInstance().drawLabelText(0f, 3f, 23f, 7f, printPrice, "", 7f,
                    180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
        }
        //生成打印数据
        byte[] jsonByte = PrintUtil.getInstance().generateLabelJson();
        jsonList.add(new String(jsonByte));
        //除B32/Z401/T8的printMultiple为11.81，其他的为8
        String jsonInfo = "{  " + "\"printerImageProcessingInfo\": " + "{    " + "\"orientation\":" + 0 + "," + "   \"margin\": [      0,      0,      0,      0    ], " + "   \"printQuantity\": " + 1 + ",  " + "  \"horizontalOffset\": 0,  " + "  \"verticalOffset\": 0,  " + "  \"width\":" + width + "," + "   \"height\":" + height + "," + "\"printMultiple\":" + 8f + "," + "  \"epc\": \"\"  }}";
        infoList.add(jsonInfo);
        PrintUtil.getInstance().commitData(jsonList, infoList);
    }

    /***********************打印价签end**********************/

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.BLUETOOTH:
                    //蓝牙返回mac地址
                    printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
                    setPrint();
                    break;
                case Constants.KU_INOUT:
                    //速录、扫码、添加商品
                    //出入库
                    page = 1;
                    getGoodsList();
                    break;
            }
        }
    }

}
