package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;


import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:分类管理-一级商品分类新增、编辑
 * Created by jingang on 2023/8/22
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsCateEditActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.linCate)
    LinearLayout linCate;
    @BindView(R.id.tvCateValue)
    TextView tvCateValue;
    @BindView(R.id.tvCate)
    TextView tvCate;//上级分类
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.linImg)
    LinearLayout linImg;
    @BindView(R.id.tvImg)
    TextView tvImg;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.tvEdit)
    TextView tvEdit;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private int type,//0.新增 1.编辑
            level;//0.一级 1.二级 2.三级
    private String unique,//一级分类编号
            unique1,//二级分类编号
            unique2,//三级分类编号
            name,
            name1,
            name2,
            imgId, img;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_cate_edit;
    }

    @Override
    public void initViews() {
        type = getIntent().getIntExtra("type", 0);
        level = getIntent().getIntExtra("level", 0);
        unique = getIntent().getStringExtra("unique");
        unique1 = getIntent().getStringExtra("unique1");
        unique2 = getIntent().getStringExtra("unique2");
        name = getIntent().getStringExtra("name");
        name1 = getIntent().getStringExtra("name1");
        name2 = getIntent().getStringExtra("name2");
        imgId = getIntent().getStringExtra("imgId");
        img = getIntent().getStringExtra("img");

        if (type == 0) {
            tvRight.setVisibility(View.GONE);
            switch (level) {
                case 1:
                    tvTitle.setText(getLanguageValue("add") + getLanguageValue("level2") + getLanguageValue("classification"));
                    linCate.setVisibility(View.VISIBLE);
                    tvCate.setText(name);
                    linImg.setVisibility(View.GONE);
                    break;
                case 2:
                    tvTitle.setText(getLanguageValue("add") + getLanguageValue("level3") + getLanguageValue("classification"));
                    linCate.setVisibility(View.VISIBLE);
                    tvCate.setText(name1);
                    linImg.setVisibility(View.GONE);
                    break;
                default:
                    tvTitle.setText(getLanguageValue("add") + getLanguageValue("level1") + getLanguageValue("classification"));
                    linCate.setVisibility(View.GONE);
                    linImg.setVisibility(View.VISIBLE);
                    break;
            }
        } else {
            tvRight.setVisibility(View.VISIBLE);
            switch (level) {
                case 1:
                    tvTitle.setText(getLanguageValue("editor") + getLanguageValue("level2") + getLanguageValue("classification"));
                    linCate.setVisibility(View.VISIBLE);
                    tvCate.setText(name);
                    etName.setText(name1);
                    linImg.setVisibility(View.GONE);
                    break;
                case 2:
                    tvTitle.setText(getLanguageValue("editor") + getLanguageValue("level3") + getLanguageValue("classification"));
                    linCate.setVisibility(View.VISIBLE);
                    tvCate.setText(name1);
                    etName.setText(name2);
                    linImg.setVisibility(View.GONE);
                    break;
                default:
                    tvTitle.setText(getLanguageValue("editor") + getLanguageValue("level1") + getLanguageValue("classification"));
                    linCate.setVisibility(View.GONE);
                    etName.setText(name);
                    linImg.setVisibility(View.VISIBLE);
                    Glide.with(this)
                            .load(StringUtils.handledImgUrl(img))
                            .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                            .into(ivImg);
                    break;
            }
        }

    }

    @OnClick({R.id.ivBack, R.id.tvRight, R.id.ivImg, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("classification") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postCateDel();
                        });
                break;
            case R.id.ivImg:
                //图标
                startActivityForResult(new Intent(this, GoodsCateImgActivity.class)
                                .putExtra("imgId", imgId)
                        , Constants.CATE
                );
                break;
            case R.id.tvConfirm:
                //保存
                if (TextUtils.isEmpty(etName.getText().toString().trim())) {
                    showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("classification") + getLanguageValue("name"));
                    return;
                }
                postCateEdit();
                break;
        }
    }

    @Override
    public void setText() {
        tvRight.setText(getLanguageValue("delete"));
        tvRight.setTextColor(getResources().getColor(R.color.red));
        tvCateValue.setText(getLanguageValue("upOneLevel") + getLanguageValue("classification"));
        tvName.setText(getLanguageValue("classification") + getLanguageValue("name"));
        etName.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("classification") + getLanguageValue("name"));
        tvImg.setText(getLanguageValue("classification") + getLanguageValue("icon"));
        tvEdit.setText(getLanguageValue("modification"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 分类操作
     */
    private void postCateEdit() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        if (type == 0) {
            switch (level) {
                case 1:
                    map.put("goodsKindParunique", unique);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    break;
                case 2:
                    map.put("goodsKindParunique", unique1);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    break;
                default:
                    map.put("goodsKindParunique", 0);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    map.put("kindIconId", imgId);
                    break;
            }
        } else {
            switch (level) {
                case 1:
                    map.put("kindUnique", unique1);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    break;
                case 2:
                    map.put("kindUnique", unique2);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    break;
                default:
                    map.put("kindUnique", unique);
                    map.put("goodsKindName", etName.getText().toString().trim());
                    map.put("kindIconId", imgId);
                    break;
            }
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.addcustomize(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_CATE));
                        setResult(Constants.CATE, new Intent()
                                        .putExtra("type", type)
                                        .putExtra("name", etName.getText().toString().trim())
                                        .putExtra("imgId", imgId)
                                        .putExtra("img", img)
//                                .putExtra("pos", position)
                        );
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 分类操作：删除
     */
    private void postCateDel() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        switch (level) {
            case 1:
                map.put("kindUnique", unique1);
                break;
            case 2:
                map.put("kindUnique", unique2);
                break;
            default:
                map.put("kindUnique", unique);
                break;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.addcustomize(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_CATE));
                        setResult(Constants.CATE, new Intent()
                                .putExtra("type", 2)
                        );
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.CATE:
                    imgId = data.getStringExtra("id");
                    img = data.getStringExtra("img");
                    Glide.with(this)
                            .load(StringUtils.handledImgUrl(img))
                            .apply(RequestOptions.bitmapTransform(new CircleCrop()).error(R.mipmap.ic_nothing))
                            .into(ivImg);
                    break;
            }
        }
    }
}
