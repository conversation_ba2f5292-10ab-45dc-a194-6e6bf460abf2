package cn.bl.mobile.buyhoostore_sea.ui.hand;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.ConditionData;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanAddHandActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanPreviewActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.adapter.PanAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.bean.PanData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.dialog.PanAddDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.ConditionDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-库存盘点（手持）
 * Created by jingang on 2023/6/25
 */
@SuppressLint("NonConstantResourceId")
public class PanHandActivity extends BaseActivity {
    private PanHandActivity TAG = PanHandActivity.this;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvType)
    TextView tvType;
    @BindView(R.id.tvDate)
    TextView tvDate;
    @BindView(R.id.tvAdd)
    TextView tvAdd;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    //选择条件
    private List<ConditionData> conditionList = new ArrayList<>();
    private int type = 0;
    private String startTime, endTime, keyWords;

    private PanAdapter mAdapter;
    private List<PanData.TaskListBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_pan;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        if (getStaffPosition().equals("3")) {
            tvAdd.setVisibility(View.VISIBLE);
        } else {
            tvAdd.setVisibility(View.GONE);
        }
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getTaskList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @Override
    public void initData() {
        conditionList.clear();
        conditionList.add(new ConditionData("全部类型", 0, true));
        conditionList.add(new ConditionData("已盘点", 2, false));
        conditionList.add(new ConditionData("待提交", 1, false));
        getTaskList();
    }

    @SuppressLint("SetTextI18n")
    @OnClick({R.id.ivBack, R.id.ivClear,
            R.id.linType, R.id.linDate,
            R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                page = 1;
                getTaskList();
                break;
            case R.id.linType:
                //选择类型
                ConditionDialog.showDialog(this,
                        "选择类型",
                        conditionList,
                        (name, value, list) -> {
                            conditionList = list;
                            type = value;
                            tvType.setText(name);
                            page = 1;
                            getTaskList();
                        });
                break;
            case R.id.linDate:
                //选择日期
                DateStartEndDialog.showDialog(this,
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            startTime = startDate;
                            endTime = endDate;
                            tvDate.setText(startDate + " - " + endDate);
                            page = 1;
                            getTaskList();
                        });
                break;
            case R.id.tvAdd:
                //新增盘点
                PanAddDialog.showDialog(this, "", this::postTaskAdd);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case "pan":
                etSearch.setText("");
                page = 1;
                getTaskList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new PanAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setEnableAutoLoadMore(true);////是否启用列表惯性滑动到底部时自动加载更多
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getTaskList();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getTaskList();
        });
        mAdapter.setListener(new PanAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情-是否为手持设备
                String model = android.os.Build.MODEL;
                if (model.equals("95W Series") || model.equals("NLS-MT66")) {
                    startActivity(new Intent(TAG, PanAddHandActivity.class)
                            .putExtra("id", String.valueOf(dataList.get(position).getTaskId()))
                            .putExtra("status", dataList.get(position).getTaskStatus()));
                } else {
                    startActivity(new Intent(TAG, PanAddActivity.class)
                            .putExtra("id", String.valueOf(dataList.get(position).getTaskId()))
                            .putExtra("status", dataList.get(position).getTaskStatus()));
                }
            }

            @Override
            public void onPreviewClick(View view, int position) {
                //预览
                startActivity(new Intent(TAG, PanPreviewActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getTaskId()))
                        .putExtra("status", dataList.get(position).getTaskStatus())
                );
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        "确认删除该盘点任务？",
                        "确认",
                        (dialog, which) -> {
                            postTaskDel(String.valueOf(dataList.get(position).getTaskId()), position);
                        });
            }
        });
    }

    /**
     * 新增盘点任务
     *
     * @param name
     */
    private void postTaskAdd(String name) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskName", name);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        page = 1;
                        getTaskList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 盘点任务列表
     */
    private void getTaskList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        if (!TextUtils.isEmpty(startTime)) {
            map.put("startDate", startTime);
        }
        if (!TextUtils.isEmpty(endTime)) {
            map.put("endDate", endTime);
        }
        if (type != 0) {
            map.put("taskStatus", type);
        }
        map.put("searchKey", keyWords);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskList(),
                map,
                PanData.class,
                new RequestListener<PanData>() {
                    @Override
                    public void success(PanData taskData) {
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(taskData.getTaskList());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 删除盘点任务
     *
     * @param id
     */
    private void postTaskDel(String id, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("taskId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

}
