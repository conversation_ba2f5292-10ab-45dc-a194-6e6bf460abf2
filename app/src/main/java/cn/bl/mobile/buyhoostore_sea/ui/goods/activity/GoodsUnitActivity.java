package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CircularBeadDialog_center;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsUnitAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsUnitData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品单位
 * Created by jingang on 2023/8/11
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class GoodsUnitActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keyWords;

    private GoodsUnitAdapter mAdapter;
    private List<GoodsUnitData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_unit;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("choose")+getLanguageValue("unit"));
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText(getLanguageValue("add")+getLanguageValue("unit"));
        etSearch.setHint(getLanguageValue("unit")+getLanguageValue("search"));
        tvTips.setText("*"+getLanguageValue("leftDelete"));

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            getGoodsUnitList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getGoodsUnitList();
    }

    @OnClick({R.id.ivBack, R.id.tvRight, R.id.ivClear})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                //新增
                showDialogUnit("", "", 0);
                break;
            case R.id.ivClear:
                //清除搜索
                etSearch.setText("");
                getGoodsUnitList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsUnitAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new GoodsUnitAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                setResult(Constants.CHOOSE_UNIT, new Intent()
                        .putExtra("id", dataList.get(position).getGoods_unit_id())
                        .putExtra("name", dataList.get(position).getGoods_unit())
                );
                finish();
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                showDialogUnit(dataList.get(position).getGoods_unit_id(), dataList.get(position).getGoods_unit(), position);
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("unit")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postGoodsUnitDel(dataList.get(position).getGoods_unit_id(), position);
                        });
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getGoodsUnitList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * dialog（单位新增、编辑）
     */
    private void showDialogUnit(String id, String name, int position) {
        try {
            View view = getLayoutInflater().inflate(R.layout.add_danwei_dialog, null);
            CircularBeadDialog_center dialog = new CircularBeadDialog_center(this, view, R.style.Dialog);
            EditText edit_danwei = dialog.findViewById(R.id.edit_danwei);
            Button btn_dwqueren = dialog.findViewById(R.id.btn_dwqueren);
            Button btn_dwcancle = dialog.findViewById(R.id.btn_dwcancle);
            TextView text_title = dialog.findViewById(R.id.text_title);
            if (TextUtils.isEmpty(id)) {
                text_title.setText(getLanguageValue("add")+getLanguageValue("unit"));
            } else {
                text_title.setText(getLanguageValue("editor")+getLanguageValue("unit"));
                edit_danwei.setText(name);
            }
            edit_danwei.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("unit")+getLanguageValue("name"));
            btn_dwqueren.setText(getLanguageValue("confirm"));
            btn_dwcancle.setText(getLanguageValue("cancel"));
            btn_dwqueren.setOnClickListener(v -> {
                String goods_unit = edit_danwei.getText().toString().trim();
                if (TextUtils.isEmpty(goods_unit)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("unit")+getLanguageValue("name"));
                    return;
                }
                if (TextUtils.isEmpty(id)) {
                    postGoodsUnitAdd(goods_unit);
                } else {
                    postGoodsUnitEdit(id, goods_unit, position);
                }
                dialog.dismiss();
            });
            btn_dwcancle.setOnClickListener(v -> dialog.dismiss());
            dialog.setCanceledOnTouchOutside(false);
            dialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 单位列表
     */
    private void getGoodsUnitList() {
        showDialog();
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("goods_unit", keyWords);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsUnitList(),
                map,
                GoodsUnitData.class,
                new RequestListListener<GoodsUnitData>() {
                    @Override
                    public void onResult(List<GoodsUnitData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 单位新增
     */
    private void postGoodsUnitAdd(String name) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        map.put("goods_unit", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        getGoodsUnitList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 单位编辑
     *
     * @param id
     * @param name
     * @param position
     */
    private void postGoodsUnitEdit(String id, String name, int position) {
        showDialog();
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("goods_unit_id", id);
        map.put("goods_unit", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.get(position).setGoods_unit(name);
                        mAdapter.notifyItemChanged(position, dataList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 单位删除
     *
     * @param id
     * @param position
     */
    private void postGoodsUnitDel(String id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("goods_unit_id", id);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}
