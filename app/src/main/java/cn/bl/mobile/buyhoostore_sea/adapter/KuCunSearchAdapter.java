package cn.bl.mobile.buyhoostore_sea.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.nostra13.universalimageloader.core.ImageLoader;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsKuSearchBean;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:库存查询（适配器）
 * Created by jingang on 2022/12/17
 */
public class KuCunSearchAdapter extends BaseAdapter<GoodsKuSearchBean.DataBean.GoodsListBean> {
    public KuCunSearchAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_kucun_search;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView kc_name;
        TextView kc_xiaxian;
        TextView kc_shangxian;
        TextView kc_num;
        ImageView goods_img;
        TextView goods_barcode;

        kc_name =holder.getView(R.id.kc_name);
        kc_xiaxian = holder.getView(R.id.kc_xiaxian);
        kc_shangxian = holder.getView(R.id.kc_shangxian);
        kc_num = holder.getView(R.id.kc_num);
        goods_img = holder.getView(R.id.goods_img);
        goods_barcode = holder.getView(R.id.goods_barcode);

        GoodsKuSearchBean.DataBean.GoodsListBean  categoryBean = mDataList.get(position);
        kc_name.setText(categoryBean.getGoods_name());
        kc_xiaxian.setText(categoryBean.getGoods_count()+"");
        kc_shangxian.setText("RM"+categoryBean.getGoods_in_price()+"");
        kc_num.setText("RM"+categoryBean.getGoods_sale_price()+"");
        String img =categoryBean.getGoodsPicturePath();
        String img_url = StringUtils.handledImgUrl(img);
//        holder.goods_img.setImageUrl(img_url);
        ImageLoader.getInstance().displayImage(img_url,goods_img);
        goods_barcode.setText("("+categoryBean.getGoods_barcode()+")");
    }
}
