package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商品变更记录（实体类）
 * Created by jingang on 2023/8/29
 */
public class GoodsChangeRecordData implements Serializable {
    /**
     * id : 31
     * goodsId : null
     * goodsBarcode : 2100005
     * shopUnique : 1536215939565
     * operType : 2
     * deviceSource : 0
     * deviceSourceMsg : sfsdfsdfsd
     * userId : 3586
     * userName :
     * userType : 0
     * createTime : 1692955839000
     * operSource : 0
     */

    private int id;
    private int goodsId;
    private String goodsBarcode;
    private String shopUnique;
    private String operType;//操作类型
    private String deviceSource;//修改记录来源
    private String deviceSourceMsg;//操作员信息
    private int userId;//操作员id
    private String userName;//操作员名称
    private String userType;//客户类型
    private String createTime;
    private String operSource;//操作来源
    private String goodsOperClass;//更改内容
    private List<RecordDetailBean> recordDetail;
    private String lastCreateTime;//上次修改记录时间
    private String lastUserid;//上次修改人ID
    private String lastUserName;//上次修改人名称
    private String lastDeviceSource;//上次修改记录来源

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public String getDeviceSource() {
        return deviceSource;
    }

    public void setDeviceSource(String deviceSource) {
        this.deviceSource = deviceSource;
    }

    public String getDeviceSourceMsg() {
        return deviceSourceMsg;
    }

    public void setDeviceSourceMsg(String deviceSourceMsg) {
        this.deviceSourceMsg = deviceSourceMsg;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOperSource() {
        return operSource;
    }

    public void setOperSource(String operSource) {
        this.operSource = operSource;
    }

    public String getGoodsOperClass() {
        return goodsOperClass;
    }

    public void setGoodsOperClass(String goodsOperClass) {
        this.goodsOperClass = goodsOperClass;
    }

    public List<RecordDetailBean> getRecordDetail() {
        return recordDetail;
    }

    public void setRecordDetail(List<RecordDetailBean> recordDetail) {
        this.recordDetail = recordDetail;
    }

    public String getLastCreateTime() {
        return lastCreateTime;
    }

    public void setLastCreateTime(String lastCreateTime) {
        this.lastCreateTime = lastCreateTime;
    }

    public String getLastUserid() {
        return lastUserid;
    }

    public void setLastUserid(String lastUserid) {
        this.lastUserid = lastUserid;
    }

    public String getLastUserName() {
        return lastUserName;
    }

    public void setLastUserName(String lastUserName) {
        this.lastUserName = lastUserName;
    }

    public String getLastDeviceSource() {
        return lastDeviceSource;
    }

    public void setLastDeviceSource(String lastDeviceSource) {
        this.lastDeviceSource = lastDeviceSource;
    }

    public static class RecordDetailBean{
        private String goodsOperClass;
        private String valueSource;
        private String valueNew;

        public String getGoodsOperClass() {
            return goodsOperClass;
        }

        public void setGoodsOperClass(String goodsOperClass) {
            this.goodsOperClass = goodsOperClass;
        }

        public String getValueSource() {
            return valueSource;
        }

        public void setValueSource(String valueSource) {
            this.valueSource = valueSource;
        }

        public String getValueNew() {
            return valueNew;
        }

        public void setValueNew(String valueNew) {
            this.valueNew = valueNew;
        }
    }
}
