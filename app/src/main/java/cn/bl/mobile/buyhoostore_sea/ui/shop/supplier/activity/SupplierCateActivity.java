package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.TableGroupDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierCateData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商管理-分类管理
 * Created by jingang on 2023/9/20
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class SupplierCateActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvAdd)
    TextView tvAdd;

    private int type;//0.查看 1.选择

    private SupplierCateAdapter mAdapter;
    private List<SupplierCateData> dataList = new ArrayList<>();
    private boolean isFirst;

    @Override
    protected int getLayoutId() {
        return R.layout.layout_list;
    }

    @Override
    public void initViews() {
        type = getIntent().getIntExtra("type", 0);
        if (type == 1) {
            tvTitle.setText(getLanguageValue("choose")+getLanguageValue("classification"));
        } else {
            tvTitle.setText(getLanguageValue("classification")+getLanguageValue("administration"));
        }
        tvAdd.setText(getLanguageValue("addTo")+getLanguageValue("classification"));
        setAdapter();
    }

    @Override
    public void initData() {
        getCateList();
    }

    @OnClick({R.id.ivBack, R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvAdd:
                //新增
                showDialogCate("", "", -1);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new SupplierCateAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierCateAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (type == 1) {
                    setResult(Constants.CHOOSE, new Intent()
                            .putExtra("unique", dataList.get(position).getSupplierKindUnique())
                            .putExtra("name", dataList.get(position).getSupplierKindName())
                    );
                    finish();
                }
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                showDialogCate(dataList.get(position).getSupplierKindUnique(), dataList.get(position).getSupplierKindName(), position);
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("classification")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postCateEdit(dataList.get(position).getSupplierKindUnique(), "", 2, position);
                        });
            }

            @Override
            public void onSortClick(View view, int position) {
                //排序
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getCateList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    @SuppressLint("NotifyDataSetChanged")
    private void setItemTouchHelper() {
        //添加拖拽事件
        if (isFirst) {
            return;
        }
        isFirst = true;
        new ItemTouchHelper(new ItemTouchHelper.Callback() {
            @Override
            public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                return makeMovementFlags(ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT, 0);
            }

            @Override
            public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
                recyclerView.getParent().requestDisallowInterceptTouchEvent(true);
                //得到当拖拽的viewHolder的Position
                int fromPosition = viewHolder.getAdapterPosition();
                //拿到当前拖拽到的item的viewHolder
                int toPosition = target.getAdapterPosition();
                if (fromPosition < toPosition) {
                    for (int i = fromPosition; i < toPosition; i++) {
                        Collections.swap(dataList, i, i + 1);
                    }
                } else {
                    for (int i = fromPosition; i > toPosition; i--) {
                        Collections.swap(dataList, i, i - 1);
                    }
                }
                mAdapter.notifyItemMoved(fromPosition, toPosition);
                return true;
            }

            @Override
            public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {

            }

            @Override
            public boolean isLongPressDragEnabled() {
                return true;
            }

            @Override
            public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                super.clearView(recyclerView, viewHolder);
                JSONArray array = new JSONArray();
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject object = new JSONObject();
                    try {
                        object.put("supplierKindUnique", dataList.get(i).getSupplierKindUnique());
                        array.put(object);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                Log.e(tag, "array = " + array);
                postCateSort(array.toString());
            }
        }).attachToRecyclerView(recyclerView);
    }

    /**
     * dialog（分类新增、编辑）
     *
     * @param unique
     * @param name
     * @param position
     */
    private void showDialogCate(String unique, String name, int position) {
        TableGroupDialog.showDialog(this, name, new TableGroupDialog.MyListener() {
            @Override
            public void onDelClick() {
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("classification")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postCateEdit(dataList.get(position).getSupplierKindUnique(), "", 2, position);
                        });
            }

            @Override
            public void onClick(String name) {
                if (TextUtils.isEmpty(unique)) {
                    //新增
                    postCateAdd(name);
                } else {
                    //编辑
                    postCateEdit(unique, name, 1, position);
                }
            }
        });
    }

    /**
     * 分类列表
     */
    private void getCateList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierCateList(),
                map,
                SupplierCateData.class,
                new RequestListListener<SupplierCateData>() {
                    @Override
                    public void onResult(List<SupplierCateData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            setItemTouchHelper();
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 分类新增
     *
     * @param name
     */
    private void postCateAdd(String name) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("supKindName", name);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(getLanguageValue("addTo")+getLanguageValue("succeed"));
                        getCateList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 分类编辑或删除
     *
     * @param unique
     * @param name
     * @param type   1.编辑 2.删除
     */
    private void postCateEdit(String unique, String name, int type, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("supKindUnique", unique);
        if (!TextUtils.isEmpty(name)) {
            map.put("supKindName", name);
        }
        map.put("modifyType", type);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        if (type == 1) {
                            dataList.get(position).setSupplierKindName(name);
                            mAdapter.notifyItemChanged(position, dataList.get(position));
                        } else {
                            dataList.remove(position);
                            mAdapter.remove(position);
                            if (dataList.size() > 0) {
                                recyclerView.setVisibility(View.VISIBLE);
                                linEmpty.setVisibility(View.GONE);
                            } else {
                                recyclerView.setVisibility(View.GONE);
                                linEmpty.setVisibility(View.VISIBLE);
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 排序
     *
     * @param array
     */
    private void postCateSort(String array) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supKindSortList", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateSort(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        getCateList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        getCateList();
                    }
                });
    }

}
