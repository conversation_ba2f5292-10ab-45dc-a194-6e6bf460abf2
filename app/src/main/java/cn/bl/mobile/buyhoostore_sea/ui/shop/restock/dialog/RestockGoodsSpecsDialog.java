package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.BaseDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter.RestockGoodsSpecsAdapter;

/**
 * Describe:dialog（创建补货计划）
 * Created by jingang on 2023/6/20
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RestockGoodsSpecsDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.rvDialogGoods)
    RecyclerView recyclerView;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    private RestockGoodsSpecsAdapter mAdapter;
    private static List<GoodsInfoData.ListDetailBean> dataList;
    private static int id;//商品id

    public static void showDialog(Context context, List<GoodsInfoData.ListDetailBean> dataList, int id, MyListener listener) {
        RestockGoodsSpecsDialog.dataList = dataList;
        RestockGoodsSpecsDialog.id = id;
        RestockGoodsSpecsDialog.listener = listener;
        RestockGoodsSpecsDialog dialog = new RestockGoodsSpecsDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RestockGoodsSpecsDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_restock_goods_specs);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("choose") + getLanguageValue("commodity") + getLanguageValue("spec"));
        tvConfirm.setText(getLanguageValue("confirm"));
        setAdapter();
    }

    @OnClick({R.id.ivDialogClose, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (listener != null) {
                    if (id == 0) {
                        showMessage(getLanguageValue("pleaseSelect")+getLanguageValue("spec"));
                        return;
                    }
                    for (int i = 0; i < dataList.size(); i++) {
                        if (dataList.get(i).getGoodsId() == id) {
                            listener.onClick(dataList.get(i));
                            dismiss();
                        }
                    }
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    @Override
    public void dismiss() {
        View view = getCurrentFocus();
        if (view instanceof TextView) {
            InputMethodManager mInputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.RESULT_UNCHANGED_SHOWN);
        }
        super.dismiss();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new RestockGoodsSpecsAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setDataList(dataList);
        for (int i = 0; i < dataList.size(); i++) {
            if (dataList.get(i).isCheck()) {
                id = dataList.get(i).getGoodsId();
            }
        }
        mAdapter.setOnItemClickListener((view, position) -> {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setCheck(false);
            }
            dataList.get(position).setCheck(true);
            id = dataList.get(position).getGoodsId();
            mAdapter.setDataList(dataList);
        });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(GoodsInfoData.ListDetailBean data);
    }
}
