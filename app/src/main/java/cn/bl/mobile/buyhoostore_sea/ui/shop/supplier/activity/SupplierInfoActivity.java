package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment.SupplierInfoFragment;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment.SupplierOrderFragment;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment.SupplierPaymentFragment;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-供货商管理-供货商详情
 * Created by jingang on 2023/9/4
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.tvUser)
    TextView tvUser;
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvMoneyValue)
    TextView tvMoneyValue;
    @BindView(R.id.tvMoney)
    TextView tvMoney;
    @BindView(R.id.tvRepayment)
    TextView tvRepayment;
    @BindView(R.id.tabLayout)
    TabLayout tabLayout;
    @BindView(R.id.viewPager)
    ViewPager viewPager;

    private String supplierUnique, supplierName;
    private SupplierData data;

    private String[] titles = new String[]{getLanguageValue("businessInformation"),
            getLanguageValue("purchaseAndSalesOrder"),
            getLanguageValue("pay")+getLanguageValue("record")};
    private SimpleFragmentPagerAdapter sfpAdapter;
    private List<Fragment> fragmentList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier_info;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText(getLanguageValue("supplier")+getLanguageValue("details"));
        tvStatus.setText(getLanguageValue("deactivated"));
        tvMoneyValue.setText(getLanguageValue("unsettled")+getLanguageValue("amount"));
        tvRepayment.setText(getLanguageValue("repayment"));
        supplierUnique = getIntent().getStringExtra("unique");
        supplierName = getIntent().getStringExtra("name");
        setFragment();
    }

    @Override
    public void initData() {
        getSupplierInfo();
    }

    @OnClick({R.id.ivBack, R.id.ivHead, R.id.tvRepayment})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivHead:
                //编辑
                startActivity(new Intent(this, SupplierEditActivity.class)
                        .putExtra("data", data)
                );
                break;
            case R.id.tvRepayment:
                //还款
                startActivity(new Intent(this, SupplierPaymentActivity.class)
                        .putExtra("unique", data.getSupplierUnique())
                );
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_LIST:
                //刷新详情
                getSupplierInfo();
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI(SupplierData data) {
        if (data == null) {
            return;
        }
        this.data = data;
        //1.购销 2.自采（本地）
        if (data.getPurchaseType() == 2) {
            tvName.setText(data.getSupplierName() + "("+getLanguageValue("local")+")");
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(data.getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
        tvUser.setText(data.getContacts() + " " + data.getContactMobile());
        //启用状态:1-启用2停用
        if (data.getEnableStatus() == 2) {
            tvStatus.setVisibility(View.VISIBLE);
        } else {
            tvStatus.setVisibility(View.GONE);
        }
        tvMoney.setText(DFUtils.getNum2(data.getDebts()));
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        fragmentList.clear();
        fragmentList.add(SupplierInfoFragment.newInstance(supplierUnique, supplierName));
        fragmentList.add(SupplierOrderFragment.newInstance(supplierUnique));
        fragmentList.add(SupplierPaymentFragment.newInstance(supplierUnique));
        sfpAdapter = new SimpleFragmentPagerAdapter(getSupportFragmentManager(), fragmentList, titles);
        viewPager.setAdapter(sfpAdapter);
        viewPager.setCurrentItem(0);
        viewPager.setOffscreenPageLimit(titles.length);
        tabLayout.setupWithViewPager(viewPager);
    }

    /**
     * 供货商详情
     */
    private void getSupplierInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfo(),
                map,
                SupplierData.class,
                new RequestListener<SupplierData>() {
                    @Override
                    public void success(SupplierData data) {
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }
}
