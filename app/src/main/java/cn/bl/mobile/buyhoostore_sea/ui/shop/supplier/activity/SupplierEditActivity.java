package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-供货商管理-新增、编辑
 * Created by jingang on 2023/9/4
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierEditActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvContactValue)
    TextView tvContactValue;
    @BindView(R.id.etContact)
    EditText etContact;
    @BindView(R.id.tvMobileValue)
    TextView tvMobileValue;
    @BindView(R.id.etMobile)
    EditText etMobile;
    @BindView(R.id.tvAddressValue)
    TextView tvAddressValue;
    @BindView(R.id.etAddress)
    EditText etAddress;
    @BindView(R.id.tvCateValue)
    TextView tvCateValue;
    @BindView(R.id.tvCate)
    TextView tvCate;
    @BindView(R.id.tvTypeValue)
    TextView tvTypeValue;
    @BindView(R.id.ivType0)
    ImageView ivType0;
    @BindView(R.id.tvType0Value)
    TextView tvType0Value;
    @BindView(R.id.ivType1)
    ImageView ivType1;
    @BindView(R.id.tvType1Value)
    TextView tvType1Value;
    @BindView(R.id.tvEnableValue)
    TextView tvEnableValue;
    @BindView(R.id.ivEnable)
    ImageView ivEnable;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private SupplierData data;
    private String id, name, contacts, contactMobile, address, cateUnique, cateName;
    private int type = 1, isEnable = 2;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_supplier_edit;
    }

    @Override
    public void initViews() {
        data = (SupplierData) getIntent().getSerializableExtra("data");
        if (data == null) {
            tvTitle.setText(getLanguageValue("addTo")+getLanguageValue("supplier"));
        } else {
            tvTitle.setText(getLanguageValue("editor")+getLanguageValue("supplier"));
            setUI();
        }
    }

    @OnClick({R.id.ivBack, R.id.tvCate, R.id.ivType0, R.id.ivType1, R.id.ivEnable, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCate:
                //选择分类
                startActivityForResult(new Intent(this, SupplierCateActivity.class)
                                .putExtra("type", 1)
                        , Constants.CHOOSE
                );
                break;
            case R.id.ivType0:
                //采购类型：自采
                if (type != 2) {
                    type = 2;
                    ivType0.setImageResource(R.mipmap.ic_chosen001);
                    ivType1.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivType1:
                //采购类型：购销
                if (type != 1) {
                    type = 1;
                    ivType1.setImageResource(R.mipmap.ic_chosen001);
                    ivType0.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivEnable:
                //启用状态
                if (isEnable == 2) {
                    isEnable = 1;
                    ivEnable.setImageResource(R.mipmap.ic_chosen004);
                } else {
                    isEnable = 2;
                    ivEnable.setImageResource(R.mipmap.ic_chosen005);
                }
                break;
            case R.id.tvConfirm:
                //保存
                name = etName.getText().toString().trim();
                contacts = etContact.getText().toString().trim();
                contactMobile = etMobile.getText().toString().trim();
                address = etAddress.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("supplier")+getLanguageValue("name"));
                    return;
                }
                if (TextUtils.isEmpty(contacts)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("contactPerson"));
                    return;
                }
                if (TextUtils.isEmpty(contactMobile)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("contact")+getLanguageValue("phone"));
                    return;
                }
                if (TextUtils.isEmpty(cateUnique)) {
                    showMessage(getLanguageValue("pleaseSelect")+getLanguageValue("belonging")+getLanguageValue("classification"));
                    return;
                }
                postSupplierEdit();
                break;
        }
    }

    @Override
    public void setText() {
        tvNameValue.setText(getLanguageValue("supplier")+getLanguageValue("name"));
        etName.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("supplier")+getLanguageValue("name"));
        tvContactValue.setText(getLanguageValue("contactPerson"));
        etContact.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("contactPerson"));
        tvMobileValue.setText(getLanguageValue("contact")+getLanguageValue("phone"));
        etMobile.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("contact")+getLanguageValue("phone"));
        tvAddressValue.setText(getLanguageValue("locationAddress"));
        etAddress.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("locationAddress"));
        tvCateValue.setText(getLanguageValue("belonging")+getLanguageValue("classification"));
        tvCate.setHint(getLanguageValue("pleaseSelect")+getLanguageValue("belonging")+getLanguageValue("classification"));
        tvTypeValue.setText(getLanguageValue("procurement")+getLanguageValue("type"));
        tvEnableValue.setText(getLanguageValue("enable")+getLanguageValue("status"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        id = String.valueOf(data.getId());
        etName.setText(data.getSupplierName());
        etContact.setText(data.getContacts());
        etMobile.setText(data.getContactMobile());
        etAddress.setText(data.getAddress());
        cateUnique = data.getSupplierKindUnique();
        cateName = data.getSupplierKindName();
        tvCate.setText(cateName);
        type = data.getPurchaseType();
        if (type == 1) {
            ivType0.setImageResource(R.mipmap.ic_chose001);
            ivType1.setImageResource(R.mipmap.ic_chosen001);
        } else {
            ivType0.setImageResource(R.mipmap.ic_chosen001);
            ivType1.setImageResource(R.mipmap.ic_chose001);
        }
        isEnable = data.getEnableStatus();
        if (isEnable == 1) {
            ivEnable.setImageResource(R.mipmap.ic_chosen004);
        } else {
            ivEnable.setImageResource(R.mipmap.ic_chosen005);
        }
    }

    /**
     * 供货商新增、编辑
     */
    private void postSupplierEdit() {
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        if (TextUtils.isEmpty(id)) {
            url = ZURL.getSupplierAdd();
        } else {
            url = ZURL.getSupplierEdit();
            map.put("id", id);
        }
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("supplierName", name);
        map.put("contacts", contacts);
        map.put("contactMobile", contactMobile);
        map.put("address", address);
        map.put("supplierKindUnique", cateUnique);
//        map.put("purchaseType", type);
        map.put("enableStatus", isEnable);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        if (TextUtils.isEmpty(id)) {
                            showMessage(getLanguageValue("addTo")+getLanguageValue("succeed"));
                        } else {
                            showMessage(getLanguageValue("editor")+getLanguageValue("succeed"));
                        }
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) {
            return;
        }
        switch (requestCode) {
            case Constants.CHOOSE:
                cateUnique = data.getStringExtra("unique");
                cateName = data.getStringExtra("name");
                tvCate.setText(cateName);
                break;
        }
    }
}
