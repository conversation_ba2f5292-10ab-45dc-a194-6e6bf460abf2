package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class GoodsKuSearchBean {


    /**
     * status : 1
     * msg : null
     * data : {"goods_count_sum":222,"goods_in_price_sum":666,"stock_warning_num":10,"goods_list":[{"goods_barcode":111,"goods_name":666,"goods_picturepath":"1.jpg","goods_count":55,"goods_in_price":10,"goods_sale_price":15},{"goods_barcode":111,"goods_name":666,"goods_picturepath":"1.jpg","goods_count":55,"goods_in_price":10,"goods_sale_price":15}]}
     */

    private int status;
    private Object msg;
    private DataBean data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * goods_count_sum : 222
         * goods_in_price_sum : 666
         * stock_warning_num : 10
         * goods_list : [{"goods_barcode":111,"goods_name":666,"goods_picturepath":"1.jpg","goods_count":55,"goods_in_price":10,"goods_sale_price":15},{"goods_barcode":111,"goods_name":666,"goods_picturepath":"1.jpg","goods_count":55,"goods_in_price":10,"goods_sale_price":15}]
         */

        private String goods_count_sum;
        private String goods_in_price_sum;
        private String stock_warning_num;
        private String goods_sale_price_sum;
        private List<GoodsListBean> goods_list;

        public String getGoods_count_sum() {
            return goods_count_sum;
        }

        public void setGoods_count_sum(String goods_count_sum) {
            this.goods_count_sum = goods_count_sum;
        }

        public String getGoods_in_price_sum() {
            return goods_in_price_sum;
        }

        public void setGoods_in_price_sum(String goods_in_price_sum) {
            this.goods_in_price_sum = goods_in_price_sum;
        }

        public String getStock_warning_num() {
            return stock_warning_num;
        }

        public void setStock_warning_num(String stock_warning_num) {
            this.stock_warning_num = stock_warning_num;
        }

        public String getGoods_sale_price_sum() {
            return goods_sale_price_sum;
        }

        public void setGoods_sale_price_sum(String goods_sale_price_sum) {
            this.goods_sale_price_sum = goods_sale_price_sum;
        }

        public List<GoodsListBean> getGoods_list() {
            return goods_list;
        }

        public void setGoods_list(List<GoodsListBean> goods_list) {
            this.goods_list = goods_list;
        }

        public static class GoodsListBean {
            /**
             * goods_barcode : 111
             * goods_name : 666
             * goods_picturepath : 1.jpg
             * goods_count : 55
             * goods_in_price : 10
             * goods_sale_price : 15
             */

            private String goods_barcode;
            private String goods_name;
            private String goodsPicturePath;
            private String goods_count;
            private String goods_in_price;
            private String goods_sale_price;

            public String getGoodsPicturePath() {
                return goodsPicturePath;
            }

            public void setGoodsPicturePath(String goodsPicturePath) {
                this.goodsPicturePath = goodsPicturePath;
            }

            public String getGoods_barcode() {
                return goods_barcode;
            }

            public void setGoods_barcode(String goods_barcode) {
                this.goods_barcode = goods_barcode;
            }

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public String getGoods_count() {
                return goods_count;
            }

            public void setGoods_count(String goods_count) {
                this.goods_count = goods_count;
            }

            public String getGoods_in_price() {
                return goods_in_price;
            }

            public void setGoods_in_price(String goods_in_price) {
                this.goods_in_price = goods_in_price;
            }

            public String getGoods_sale_price() {
                return goods_sale_price;
            }

            public void setGoods_sale_price(String goods_sale_price) {
                this.goods_sale_price = goods_sale_price;
            }
        }
    }
}