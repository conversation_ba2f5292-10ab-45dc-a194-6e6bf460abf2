package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.SystemUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PermissionDialog;
import cn.bl.mobile.buyhoostore_sea.ui.login.PoiActivity;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺信息-店铺位置
 * Created by jingang on 2023/10/7
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ShopAddressActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvAddress)
    TextView tvAddress;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String address;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_shop_address;
    }

    @Override
    public void initViews() {
        address = getIntent().getStringExtra("address");
        tvAddress.setText(address);
    }

    @OnClick({R.id.ivBack, R.id.tvAddress, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvAddress:
                //地图选择地址
                getLocationManage();
                break;
            case R.id.tvConfirm:
                //确认
                if (TextUtils.isEmpty(address)) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("shop") + getLanguageValue("address"));
                    return;
                }
                postShopAddress();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("shop") + getLanguageValue("address"));
        tvAddress.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("shop") + getLanguageValue("address"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    /**
     * 定位服务
     */
    private void getLocationManage() {
        if (!SystemUtils.hasGPSDevice(this)) {
            getStorageManage();
            return;
        }
        if (SystemUtils.isLocationEnabled(this)) {
            getStorageManage();
        } else {
            PermissionDialog.showDialog(this,
                    getLanguageValue("locationService"),
                    type -> {
                        if (type == 0) {
                            startActivityForResult(new Intent().setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                                    , Constants.LOCATION_ENABLED
                            );
                        }
                    });
        }
    }

    /**
     * 存储服务(所有文件访问)
     */
    private void getStorageManage() {
        if (SystemUtils.hasAllFilesAccessPermission(this)) {
            getLocationPermission();
        } else {
            PermissionDialog.showDialog(this,
                    getLanguageValue("allFillServiceDesc"),
                    type -> {
                        if (type == 0) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                startActivityForResult(new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                                                .setData(Uri.parse("package:" + getApplicationContext().getPackageName()))
                                        , Constants.LOCATION_STORAGE_MANAGE);
                            } else {
                                getLocationPermission();
                            }
                        }
                    });
        }
    }

    /**
     * 定位权限
     */
    private void getLocationPermission() {
        if (PermissionUtils.checkPermissionsGroup(this, 1)) {
            goToActivityForResult(PoiActivity.class, Constants.CHOOSE_LOCATION);
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
        }
    }

    /**
     * 修改店铺名称
     */
    private void postShopAddress() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopAddress", address);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateInfoUrlTWO(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        setResult(Constants.ADDRESS, new Intent()
                                .putExtra("address", address)
                        );
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    goToActivityForResult(PoiActivity.class, Constants.CHOOSE_LOCATION);
                }
                break;
            case Constants.PERMISSION1:
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    if (!Environment.isExternalStorageManager()) {
                        showMessage(getLanguageValue("funNotSet"));
                    } else {
                        if (PermissionUtils.checkPermissionsGroup(this, 1)) {
                            goToActivityForResult(PoiActivity.class, Constants.CHOOSE_LOCATION);
                        } else {
                            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
                        }
                    }
                } else {
                    if (PermissionUtils.checkPermissionsGroup(this, 1)) {
                        goToActivityForResult(PoiActivity.class, Constants.CHOOSE_LOCATION);
                    } else {
                        PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
                    }
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.CHOOSE_LOCATION:
                //选择定位
                if (data != null) {
                    address = data.getStringExtra("address");
                    tvAddress.setText(address);
                }
                break;
            case Constants.LOCATION_ENABLED:
                //定位服务开关
                if (SystemUtils.isLocationEnabled(this)) {
                    getStorageManage();
                } else {
                    showMessage(getLanguageValue("locationServiceNotTurn"));
                }
                break;
            case Constants.LOCATION_STORAGE_MANAGE:
                //存储开关
                if (SystemUtils.hasAllFilesAccessPermission(this)) {
                    getLocationPermission();
                } else {
                    showMessage(getLanguageValue("allFillServiceNotTurn"));
                }
                break;
        }
    }
}
