package cn.bl.mobile.buyhoostore_sea.app;

import android.content.Context;
import android.util.Log;

import androidx.multidex.MultiDex;

import com.amap.api.location.AMapLocationClient;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;

import cn.bl.mobile.buyhoostore_sea.BuildConfig;
import cn.bl.mobile.buyhoostore_sea.utils.MultiLanguageUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 *
 */
public class MyApplication extends BaseApplication {

    @Override
    public void onCreate() {
        super.onCreate();
        setApplication(this);
        getUserInfo();//用户信息
        //高德隐私合规接口说明
        AMapLocationClient.updatePrivacyShow(this, true, true);
        AMapLocationClient.updatePrivacyAgree(this, true);
        if (BuildConfig.DEBUG) {
            setUrl();
        }
        //多语言设置
        registerActivityLifecycleCallbacks(MultiLanguageUtils.callbacks);
    }

    @Override
    protected void attachBaseContext(Context base) {
        //系统语言等设置发生改变时会调用此方法，需要要重置app语言
        super.attachBaseContext(MultiLanguageUtils.attachBaseContext(base));
        MultiDex.install(this);
    }

    /**
     * 设置环境
     */
    public static void setUrl() {
        //0.测试 1.正式
        int i = SharedUtils.get("test", IPreference.DataType.INTEGER);
        String input = SharedUtils.get("testInput", IPreference.DataType.STRING);
        Log.e("111111", "当前环境 = " + i + " input = " + input);
        switch (i) {
            case 1:
                ZURL.ONLINE_URL = ZURL.CONSTANT_ONLINE_URL;
                ZURL.COMMON_Mall_URL = ZURL.CONSTANT_ONLINE_MALL_URL;
                break;
            case 2:
                ZURL.ONLINE_URL = input;
                ZURL.COMMON_Mall_URL = input;
                break;
            case 3:
                ZURL.ONLINE_URL = ZURL.DEV_THREE_URL;
                ZURL.COMMON_Mall_URL = ZURL.CONSTANT_ONLINE_MALL_URL;
                break;
            default:
                ZURL.ONLINE_URL = ZURL.TEST_THREE_URL;
                ZURL.COMMON_Mall_URL = ZURL.TEST_CONSTANT_ONLINE_MALL_URL;
                break;
        }
    }

}