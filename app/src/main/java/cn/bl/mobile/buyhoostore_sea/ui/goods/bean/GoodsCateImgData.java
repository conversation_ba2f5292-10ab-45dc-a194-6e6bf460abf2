package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

import java.io.Serializable;

/**
 * Describe:商品分类图标（实体类）
 * Created by jingang on 2023/8/22
 */
public class GoodsCateImgData implements Serializable {
    /**
     * goods_kind_icon_picture : /image/goods_kind/system/baonuan.png
     * goods_kind_icon_id : 7
     */

    private String goods_kind_icon_picture;
    private int goods_kind_icon_id;
    private boolean check;

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public String getGoods_kind_icon_picture() {
        return goods_kind_icon_picture;
    }

    public void setGoods_kind_icon_picture(String goods_kind_icon_picture) {
        this.goods_kind_icon_picture = goods_kind_icon_picture;
    }

    public int getGoods_kind_icon_id() {
        return goods_kind_icon_id;
    }

    public void setGoods_kind_icon_id(int goods_kind_icon_id) {
        this.goods_kind_icon_id = goods_kind_icon_id;
    }
}
