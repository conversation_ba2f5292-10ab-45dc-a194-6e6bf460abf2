package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Describe:经营统计-分类销售占比（实体类）
 * Created by jingang on 2024/11/23
 */
public class StatisticsSaleSumTrendData {
    /**
     * kindName : 日用百货
     * sum : 184.76
     */

    private String kindName;
    private double sum;

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }

    public double getSum() {
        return sum;
    }

    public void setSum(double sum) {
        this.sum = sum;
    }
}
