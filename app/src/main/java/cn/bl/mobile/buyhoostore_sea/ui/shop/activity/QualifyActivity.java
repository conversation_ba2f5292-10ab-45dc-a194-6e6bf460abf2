package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;

import androidx.annotation.NonNull;

import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.ShopZiZhiBean;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 店铺-设置-店铺资质信息
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class QualifyActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;

    @BindView(R.id.tvInfo0Value)
    TextView tvInfo0Value;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvMobileValue)
    TextView tvMobileValue;
    @BindView(R.id.etMobile)
    EditText etMobile;
    @BindView(R.id.tvShopNameValue)
    TextView tvShopNameValue;
    @BindView(R.id.etShopName)
    EditText etShopName;
    @BindView(R.id.tvShopName1Value)
    TextView tvShopName1Value;
    @BindView(R.id.etShopName1)
    EditText etShopName1;

    @BindView(R.id.tvInfo1Value)
    TextView tvInfo1Value;
    @BindView(R.id.tvInfo1Tips)
    TextView tvInfo1Tips;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.tv0Value)
    TextView tv0Value;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.tv1Value)
    TextView tv1Value;
    @BindView(R.id.ivImg2)
    ImageView ivImg2;
    @BindView(R.id.tv2Value)
    TextView tv2Value;
    @BindView(R.id.ivImg3)
    ImageView ivImg3;
    @BindView(R.id.tv3Value)
    TextView tv3Value;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    //法人姓名、联系电话、注册店名、实用店名
    private String name, mobile, shopName, shopName1;

    //图片：营业执照、银行卡正面、身份证人像面、身份证国徽面
    private String img0, img1, img2, img3;

    public static int checked = 0;
    public static int bitmap_num;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_zi_zhi;
    }

    @Override
    public void initViews() {
    }

    @Override
    public void initData() {
        getShopInfo();
        getShopZiZhi();
    }

    @OnClick({R.id.ivBack,
            R.id.ivImg0, R.id.ivImg1, R.id.ivImg2, R.id.ivImg3,
            R.id.tvConfirm})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivImg0:
                //营业执照
                checked = 1;
                getImg();
                break;
            case R.id.ivImg1:
                //银行卡正面照
                bitmap_num = 3;
                checked = 4;
                getImg();
                break;
            case R.id.ivImg2:
                //身份证人像面
                checked = 2;
                getImg();
                break;
            case R.id.ivImg3:
                //身份证国徽面
                checked = 3;
                getImg();
                break;
            case R.id.tvConfirm:
                //提交
                name = etName.getText().toString().trim();
                mobile = etMobile.getText().toString().trim();
                shopName = etShopName.getText().toString().trim();
                shopName1 = etShopName1.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("legalPerson") + getLanguageValue("name"));
                    return;
                }
                if (TextUtils.isEmpty(mobile)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("contact") + getLanguageValue("phone"));
                    return;
                }
                if (TextUtils.isEmpty(shopName)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("shop") + getLanguageValue("name"));

                    return;
                }
                if (TextUtils.isEmpty(shopName1)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("shop")+getLanguageValue("name"));
                    return;
                }
                setUpdateDetail();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("shop") + getLanguageValue("qualifications") + getLanguageValue("information"));
        tvInfo0Value.setText(getLanguageValue("basics") + getLanguageValue("information"));
        tvNameValue.setText(getLanguageValue("legalPerson") + getLanguageValue("name"));
        etName.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("legalPerson") + getLanguageValue("name"));
        tvMobileValue.setText(getLanguageValue("contact") + getLanguageValue("phone"));
        etMobile.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("contact") + getLanguageValue("phone"));
        tvShopNameValue.setText(getLanguageValue("regist")+getLanguageValue("shop")+getLanguageValue("name"));
        etShopName.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("regist")+getLanguageValue("shop")+getLanguageValue("name"));
        tvShopName1Value.setText(getLanguageValue("shop")+getLanguageValue("name"));
        etShopName1.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("shop")+getLanguageValue("name"));
        tvInfo1Value.setText(getLanguageValue("shop") + getLanguageValue("information"));
        tvInfo1Tips.setText(getLanguageValue("promptConsistent"));
        tv0Value.setText(getLanguageValue("businessLicense"));
        tv1Value.setText(getLanguageValue("bankCardFrontPhone"));
        tv2Value.setText(getLanguageValue("identityWitnessFace"));
        tv3Value.setText(getLanguageValue("indentityEmblem"));
        tvConfirm.setText(getLanguageValue("submit"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(this, 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }


    //获取店铺信息
    public void getShopInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getShopInfoUrlTWO(),
                params,
                ShopInfoResponseModel.DataBean.class,
                new RequestListener<ShopInfoResponseModel.DataBean>() {
                    @Override
                    public void success(ShopInfoResponseModel.DataBean data) {
                        etShopName.setText(data.getShopName());
                        etMobile.setText(data.getShopPhone());
                    }
                });
    }

    //获取店铺资质信息
    public void getShopZiZhi() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getzizhi(),
                params,
                ShopZiZhiBean.DataBean.class,
                new RequestListener<ShopZiZhiBean.DataBean>() {
                    @Override
                    public void success(ShopZiZhiBean.DataBean data) {
                        Glide.with(TAG)
                                .load(StringUtils.handledImgUrl(data.getYingyezhao_img()))
                                .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                                .into(ivImg0);
                        Glide.with(TAG)
                                .load(StringUtils.handledImgUrl(data.getBank_card_img()))
                                .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                                .into(ivImg1);
                        Glide.with(TAG)
                                .load(StringUtils.handledImgUrl(data.getCard_zheng_img()))
                                .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                                .into(ivImg2);
                        Glide.with(TAG)
                                .load(StringUtils.handledImgUrl(data.getCard_fan_img()))
                                .apply(new RequestOptions().error(R.mipmap.ic_default_img))
                                .into(ivImg3);
                        etName.setText(data.getLegal_name());
                        etMobile.setText(data.getShop_phone());
                        etShopName.setText(data.getRegister_shop_name());
                        etShopName1.setText(data.getUse_shop_name());
                    }
                });
    }

    /***
     * 资质认证提交
     */
    public void setUpdateDetail() {
        Log.e("111111", "img0 = " + img0
                + "\nimg1 = " + img1
                + "\nimg2 = " + img2
                + "\nimg3 = " + img3);
        if (TextUtils.isEmpty(img0)) {
            showMessage(getLanguageValue("please") + getLanguageValue("upload") + getLanguageValue("businessLicense"));
            return;
        }
        if (TextUtils.isEmpty(img1)) {
            showMessage(getLanguageValue("please") + getLanguageValue("upload") + getLanguageValue("identityWitnessFace"));
            return;
        }
        if (TextUtils.isEmpty(img2)) {
            showMessage(getLanguageValue("please") + getLanguageValue("upload") + getLanguageValue("indentityEmblem"));
            return;
        }
        if (TextUtils.isEmpty(img3)) {
            showMessage(getLanguageValue("please") + getLanguageValue("upload") + getLanguageValue("bankCardFrontPhone"));
            return;
        }
        OkHttpClient mOkHttpClient = new OkHttpClient();
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("shop_unique", getShop_id())
                .addFormDataPart("legal_name", name)
                .addFormDataPart("shop_phone", mobile)
                .addFormDataPart("register_shop_name", shopName)
                .addFormDataPart("use_shop_name", shopName1)
                .addFormDataPart("yingyezhao_img", img0, RequestBody.create(MediaType.parse("image/jpg"), new File(img0)))
                .addFormDataPart("card_zheng_img", img2, RequestBody.create(MediaType.parse("image/jpg"), new File(img2)))
                .addFormDataPart("card_fan_img", img3, RequestBody.create(MediaType.parse("image/jpg"), new File(img3)))
                .addFormDataPart("bank_card_img", img1, RequestBody.create(MediaType.parse("image/jpg"), new File(img1)))
                .build();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getSubmitShopQualification())
                .post(requestBody)
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i("TAG", "请求失败" + e);
                showMessage(getLanguageValue("uploadFailTry"));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        BaseData data = new Gson().fromJson(response.body().string(), BaseData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            goToActivity(ZiZhiSuccessActivity.class);
                            finish();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (IOException e) {
                        e.printStackTrace();
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });

    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        LocalMedia media = result.get(0);
                        String path = media.getAvailablePath();

                        switch (checked) {
                            case 1:
                                img0 = path;
                                Glide.with(TAG)
                                        .load(img0)
                                        .into(ivImg0);
                                break;
                            case 2:
                                img2 = path;
                                Glide.with(TAG)
                                        .load(img2)
                                        .into(ivImg2);
                                break;
                            case 3:
                                img3 = path;
                                Glide.with(TAG)
                                        .load(img3)
                                        .into(ivImg3);
                                break;
                            case 4:
                                img1 = path;
                                Glide.with(TAG)
                                        .load(img1)
                                        .into(ivImg1);
                                break;
                        }
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        LocalMedia media = result.get(0);
                        String path = media.getAvailablePath();

                        switch (checked) {
                            case 1:
                                img0 = path;
                                Glide.with(TAG)
                                        .load(img0)
                                        .into(ivImg0);
                                break;
                            case 2:
                                img2 = path;
                                Glide.with(TAG)
                                        .load(img2)
                                        .into(ivImg2);
                                break;
                            case 3:
                                img3 = path;
                                Glide.with(TAG)
                                        .load(img3)
                                        .into(ivImg3);
                                break;
                            case 4:
                                img1 = path;
                                Glide.with(TAG)
                                        .load(img1)
                                        .into(ivImg1);
                                break;
                        }
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

}
