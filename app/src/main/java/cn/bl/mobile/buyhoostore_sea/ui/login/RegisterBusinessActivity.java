package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.leaf.library.StatusBarUtil;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:注册-营业执照
 * Created by jingang on 2023/3/9
 */
@SuppressLint("NonConstantResourceId")
public class RegisterBusinessActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.ivCamera)
    ImageView ivCamera;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String img;//营业执照
    private String account,//账号
            pwd, //密码
            shopName,//店铺名称
            mobile, //联系电话
            address, lat, lng, province, city, district, area;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_register_business;
    }

    @Override
    public void initViews() {
        StatusBarUtil.setTransparentForWindow(this);
        StatusBarUtil.setDarkMode(this);
        account = getIntent().getStringExtra("account");
        pwd = getIntent().getStringExtra("pwd");
        shopName = getIntent().getStringExtra("shopName");
        mobile = getIntent().getStringExtra("mobile");
        address = getIntent().getStringExtra("address");
        lat = getIntent().getStringExtra("lat");
        lng = getIntent().getStringExtra("lng");
        province = getIntent().getStringExtra("province");
        city = getIntent().getStringExtra("city");
        district = getIntent().getStringExtra("district");
        area = getIntent().getStringExtra("area");
    }

    @OnClick({R.id.ivBack, R.id.flImg, R.id.tvConfirm})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.flImg:
                //选择图片
                getImg();
                break;
            case R.id.tvConfirm:
                //提交
//                if (TextUtils.isEmpty(img)) {
//                    showMessage("请上传营业执照");
//                    return;
//                }
                postRegister();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("businessLicense"));
        tvTips.setText(getLanguageValue("uploadClearPhoto"));
        tvConfirm.setText(getLanguageValue("submit"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(this, 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(this, R.style.dialog_bottom);
        View view = View.inflate(this, R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvCamera.setText(getLanguageValue("photograph"));
        tvAlbum.setText(getLanguageValue("phoneSelect"));
        tvCancel.setText(getLanguageValue("cancel"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (TextUtils.isEmpty(img)) {
            ivImg.setVisibility(View.GONE);
            ivCamera.setVisibility(View.VISIBLE);
//            tvConfirm.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        ivImg.setVisibility(View.VISIBLE);
        ivCamera.setVisibility(View.GONE);
        Glide.with(this)
                .load(img)
                .into(ivImg);
//        tvConfirm.setBackgroundResource(R.drawable.shape_blue_22);
    }

    /***
     * 上传图片
     *
     * @param file
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        String str = Objects.requireNonNull(response.body()).string();
                        Log.e(tag, "str = " + str);
                        UrlData data = new Gson().fromJson(str, UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            img = data.getData().getUrl();
                            setUI();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        Log.e(tag, "上传失败 e = " + e);
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }


    //注册
    public void postRegister() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("manager_account", account);
        params.put("manager_pwd", pwd);
        params.put("shop_name", navticeEncode(shopName));
        params.put("shop_phone", mobile);
        params.put("shop_address_detail", navticeEncode(address));
        params.put("shop_latitude", lat);
        params.put("shop_longitude", lng);
        params.put("province", navticeEncode(province));
        params.put("city", navticeEncode(city));
        params.put("district", navticeEncode(district));
        params.put("area_dict_num", area);
        params.put("license", img);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getRegister(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "注册 = " + s);
                        hideDialog();
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
//                        JSONObject object;
//                        int status;
//                        try {
//                            object = new JSONObject(s);
//                            status = object.getInt("status");
//                            if (status == Constants.SUCCESS_CODE) {
//                                showMessage(getLanguageValue("waitApproval"));
//                                setResult(Constants.REGISTER, new Intent());
//                                finish();
//                            } else {
//                                showMessage(object.getString("msg"));
//                            }
//                        } catch (JSONException e) {
//                            e.printStackTrace();
//                        }
                        JSONObject object;
                        int code;
                        try {
                            object = new JSONObject(s);
                            code = object.getInt("code");
                            if (code == 200) {
                                showMessage(getLanguageValue("waitApproval"));
                                setResult(Constants.REGISTER, new Intent());
                                finish();
                            } else {
                                showMessage(object.getString("msg"));
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 转码
     *
     * @param url
     * @return
     */
    private String navticeEncode(String url) {
        String resulUrl = "";
        try {
            resulUrl = URLEncoder.encode(url, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resulUrl;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }
}
