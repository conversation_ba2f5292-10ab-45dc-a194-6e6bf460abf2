package cn.bl.mobile.buyhoostore_sea.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.graphics.Bitmap;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cc.fussen.cache.Cache;
import cc.fussen.cache.disklrucache.Util;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.GeRenBean;
import cn.bl.mobile.buyhoostore_sea.bean.MessageBean;
import cn.bl.mobile.buyhoostore_sea.bean.User;
import cn.bl.mobile.buyhoostore_sea.bean.ShopToolData;
import cn.bl.mobile.buyhoostore_sea.evevtbus.FirstEvent;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.BusinessActivity;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierQuickActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierQuickHandActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierShiftActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.CashierShiftHandActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.FarmMainActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.FeedBackActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SaleStatisticsActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SalesSumActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ShiftRecordActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ShopSwitchActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.BankActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.BeanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.MemberActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.NoticeActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.StockActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.GoodsAdjustActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SystemActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.ShopOrderCountData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.activity.GouXActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.location.LocationActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.MsgActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.SettingActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ShopToolCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity.AggregationActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity.AggregationApplyStateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.bean.AggregatePayStatusData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.AllotActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.BalanceDifferenceActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.activity.RestockActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierActivity;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.view.LooperTextView;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺
 * Created by jingang on 2023/5/13
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class ShopFragment extends BaseFragment {
    @BindView(R.id.ivHead)
    ImageView ivHead;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvStaffName)
    TextView tvStaffName;
    @BindView(R.id.tvShopSwitch)
    TextView tvShopSwitch;

    @BindView(R.id.tvDateValue)
    TextView tvDateValue;
    @BindView(R.id.tvDate)
    TextView tvDate;
    @BindView(R.id.tvSaleSum)
    TextView tvSaleSum;//今日销售额
    @BindView(R.id.tvSaleSumValue)
    TextView tvSaleSumValue;
    @BindView(R.id.tvOrderCount)
    TextView tvOrderCount;//订单量
    @BindView(R.id.tvOrderCountValue)
    TextView tvOrderCountValue;
    @BindView(R.id.tvBeans)
    TextView tvBeans;//百货豆
    @BindView(R.id.tvBeansValue)
    TextView tvBeansValue;
    @BindView(R.id.tvProfit)
    TextView tvProfit;//毛利润
    @BindView(R.id.tvProfitValue)
    TextView tvProfitValue;
    @BindView(R.id.tvProfitRate)
    TextView tvProfitRate;//毛利率
    @BindView(R.id.tvProfitRateValue)
    TextView tvProfitRateValue;
    @BindView(R.id.tvUnitPrice)
    TextView tvUnitPrice;//客单价
    @BindView(R.id.tvUnitPriceValue)
    TextView tvUnitPriceValue;

    @BindView(R.id.linNotice)
    LinearLayout linNotice;
    @BindView(R.id.looperTextView)
    LooperTextView looperTextView;

    @BindView(R.id.rvTool)
    RecyclerView rvTool;

    private SharedPreferences sp;
    private ShopInfoResponseModel.DataBean shopInfoBean;
    private String now_time = "";
    private String staffAccount = "";
    private User user;
    private String imageUrl = "";
    private String imagePath;

    private int scanType;//进去含有扫码的页面 0.扫码 1.移动收银 2.快速收银

    //公告
    private List<String> noticeList = new ArrayList<>();

    //我的工具
    private ShopToolCateAdapter toolCateAdapter;
    private List<ShopToolData> toolList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_shop;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        sp = getActivity().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        staffAccount = sp.getString("staffAccount", "");
        user = new User();//初始化缓存对象
        now_time = DateUtils.getOldDate(0);//当天的日期
        tvDate.setText(now_time);
        setAdapter();
    }

    @Override
    public void initData() {
        lazyLoad();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            lazyLoad();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            lazyLoad();
        }
    }

    private void lazyLoad() {
        if (isNetworkConnected(getActivity())) {
            staffAccount = sp.getString("staffAccount", "");
            getShopUserInfo();
            getShopOrderCount();
            getMsgList();
            getModule_v2();
        }
    }

    @Override
    public void onResume() {
        sp = getActivity().getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
        getShopInfo();
        getShopOrderCount();
        getModule_v2();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.CHOOSE_SHOP:
                //切换店铺
                lazyLoad();

                //刷新网单页
                EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_ORSER_LIST));
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_CATE));
                break;
        }
    }

    @OnClick({R.id.tvPan,
            R.id.ivHead, R.id.tvShopSwitch,
            R.id.tvDate, R.id.linSalesSum, R.id.linBeans,
            R.id.linNotice})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.tvPan:
                //盘点
                goToActivity(PanActivity.class);
                break;
            case R.id.ivHead:
                //店铺设置
                startActivity(new Intent(getActivity(), SettingActivity.class)
                        .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                );
                break;
            case R.id.tvShopSwitch:
                //店铺切换
                goToActivity(ShopSwitchActivity.class);
                break;
            case R.id.tvDate:
                //选择日期
                DateDialog.showDialog(getActivity(), now_time, date -> {
                    Editor editor = sp.edit();
                    editor.putString("shop_time", date);
                    editor.commit();
                    now_time = date;
                    tvDate.setText(date);
                    getShopOrderCount();
                });
                break;
            case R.id.linSalesSum:
                //今日营业额
                goToActivity(SalesSumActivity.class);
                break;
            case R.id.linBeans:
                //百货豆
                goToActivity(BeanActivity.class);
                break;
            case R.id.linNotice:
                //公告（更多）
                goToActivity(NoticeActivity.class);
                break;
        }
    }

    @Override
    public void setText() {
        tvShopSwitch.setText(getLanguageValue("shop") + getLanguageValue("switch"));
        tvDateValue.setText(getLanguageValue("todayStoreData"));
        tvSaleSumValue.setText(getLanguageValue("today") + getLanguageValue("turnover"));
        tvOrderCountValue.setText(getLanguageValue("order") + getLanguageValue("quantity"));
        tvUnitPriceValue.setText(getLanguageValue("passengerUnitPrice"));
        tvBeansValue.setText(getLanguageValue("departmentStoreBeans"));
        tvProfitValue.setText(getLanguageValue("grossProfit"));
        tvProfitRateValue.setText(getLanguageValue("grossProfitMargin"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        toolCateAdapter = new ShopToolCateAdapter(getActivity());
        rvTool.setAdapter(toolCateAdapter);
        toolCateAdapter.setOnItemChildClickListener((view, position, position_child) -> {
            choosemoudle(toolList.get(position).getList().get(position_child).getModularNum());
        });
    }

    /**
     * 判断是否有网络连接
     *
     * @param context
     * @return
     */
    @SuppressLint("MissingPermission")
    public boolean isNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                if (mNetworkInfo.isAvailable()) {
                    // 6.0以上的网络判断
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        NetworkCapabilities networkCapabilities = mConnectivityManager.getNetworkCapabilities(mConnectivityManager.getActiveNetwork());
                        if (networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                            //网络可用
                        } else {
                            //网络不可用
                            User user = Cache.with(context).path(Util.getCacheDir(context)).getCache("user1", User.class);
                            shopInfoBean = Cache.with(context).path(Util.getCacheDir(context)).getCache("ShopINfo", ShopInfoResponseModel.DataBean.class);
                            if (user != null && shopInfoBean != null) {
                                tvShopName.setText(shopInfoBean.getShopName());
                                tvStaffName.setText(user.people_name);
                                tvSaleSum.setText(user.shop_money);
                                tvOrderCount.setText(user.d_count);
                                tvBeans.setText(user.buyhoo_beans);
                                tvProfit.setText(user.m_lirun);
                                tvProfitRate.setText(user.m_lirunlv);
                                String k_price = user.k_price;
                                tvUnitPrice.setText(k_price);
                                //公告
                                noticeList.clear();
                                noticeList.add(user.title1);
                                noticeList.add(user.title2);
                                looperTextView.setTipList(noticeList);

                                imageUrl = shopInfoBean.getShopImagePath();
                                imagePath = shopInfoBean.image_path;
                                Bitmap cacheBitmap = Cache.with(context).path(imagePath).getImageCache(imageUrl);
                                if (cacheBitmap != null) {
                                    ivHead.setImageBitmap(cacheBitmap);
                                }
                            }
                        }
                        return networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                    } else {   //6.0以下的判断  直接拿出上次缓存的信息
                        User user = Cache.with(context).path(Util.getCacheDir(context)).getCache("user1", User.class);
                        shopInfoBean = Cache.with(context).path(Util.getCacheDir(context)).getCache("ShopINfo", ShopInfoResponseModel.DataBean.class);
                        if (user != null && shopInfoBean != null) {
                            tvShopName.setText(shopInfoBean.getShopName());
                            tvStaffName.setText(user.people_name);
                            tvSaleSum.setText(user.shop_money);
                            tvOrderCount.setText(user.d_count);
                            tvBeans.setText(user.buyhoo_beans);
                            tvProfit.setText(user.m_lirun);
                            tvProfitRate.setText(user.m_lirunlv);
                            String k_price = user.k_price;
                            tvUnitPrice.setText(k_price);
                            imageUrl = shopInfoBean.getShopImagePath();
                            imagePath = shopInfoBean.image_path;
                            Bitmap cacheBitmap = Cache.with(context).path(imagePath).getImageCache(imageUrl);
                            if (cacheBitmap != null) {
                                ivHead.setImageBitmap(cacheBitmap);
                            }
                        }
                    }
                }
                return mNetworkInfo.isAvailable();
            } else {
                User user = Cache.with(context).path(Util.getCacheDir(context)).getCache("user1", User.class);
                shopInfoBean = Cache.with(context).path(Util.getCacheDir(context)).getCache("ShopINfo", ShopInfoResponseModel.DataBean.class);
                if (user != null && shopInfoBean != null) {
                    tvShopName.setText(shopInfoBean.getShopName());
                    tvStaffName.setText(user.people_name);
                    tvSaleSum.setText(user.shop_money);
                    tvOrderCount.setText(user.d_count);
                    tvBeans.setText(user.buyhoo_beans);
                    tvProfit.setText(user.m_lirun);
                    tvProfitRate.setText(user.m_lirunlv);
                    String k_price = user.k_price;
                    tvUnitPrice.setText(k_price);
                    imageUrl = shopInfoBean.getShopImagePath();
                    imagePath = shopInfoBean.image_path;
                    Bitmap cacheBitmap = Cache.with(context).path(imagePath).getImageCache(imageUrl);
                    if (cacheBitmap != null) {
                        ivHead.setImageBitmap(cacheBitmap);
                    }
                }
            }
        }
        return false;
    }

    /**
     * 选择跳转的界面
     */
    public void choosemoudle(int modularNum) {

        switch (modularNum) {
            case 2:
                //经营统计
                goToActivity(BusinessActivity.class);
                break;
            case 3:
                //库存盘点
                goToActivity(PanActivity.class);
                break;
            case 4:
                //会员管理
                goToActivity(MemberActivity.class);
                break;
            case 5:
                //库存
                goToActivity(StockActivity.class);
                break;
            case 6:
                //销售统计
                goToActivity(SaleStatisticsActivity.class);
                break;
            case 7:
                //旧版出入库
                scanType = 0;
                gotoScanActivity();
                break;
            case 10:
                //消息中心
                startActivity(new Intent(getActivity(), MsgActivity.class)
                        .putExtra("startDate", "")
                );
                break;
            case 13:
                //交接班
                goToActivity(ShiftRecordActivity.class);
                break;
            case 14:
                //意见反馈
                goToActivity(FeedBackActivity.class);
                break;
            case 15:
                //上传二维码（改为聚合码）
                getAggregatePayStatus();
                break;
            case 17:
                //自定义分类
                goToActivity(GoodsCateActivity.class);
                break;
            case 18:
                //供货商管理
                goToActivity(SupplierActivity.class);
                break;
            case 1:
            case 21:
                //移动收银
                if (TextUtils.isEmpty(android.os.Build.MODEL)) {
                    scanType = 1;
                    gotoScanActivity();
                } else {
                    if (android.os.Build.MODEL.startsWith("LANDI")) {
                        //手持设置
                        goToActivity(CashierShiftHandActivity.class);
                    } else {
                        scanType = 1;
                        gotoScanActivity();
                    }
                }
                break;
            case 26:
                //账户管理（银行卡列表）
                goToActivity(BankActivity.class);
                break;
            case 27:
                //补货调价
                goToActivity(GoodsAdjustActivity.class);
                break;
            case 28:
                //余额（差价）
                goToActivity(BalanceDifferenceActivity.class);
                break;
            case 29:
                //商品调拨
                goToActivity(AllotActivity.class);
                break;
            case 30:
                //农批
                goToActivity(FarmMainActivity.class);
                break;
            case 31:
                //补货单
                goToActivity(RestockActivity.class);
                break;
            case 32:
                //采购单
                goToActivity(GouXActivity.class);
                break;
            case 33:
                //快速收银
                if (TextUtils.isEmpty(android.os.Build.MODEL)) {
                    scanType = 2;
                    gotoScanActivity();
                } else {
                    if (android.os.Build.MODEL.startsWith("LANDI")) {
                        //手持设置
                        goToActivity(CashierQuickHandActivity.class);
                    } else {
                        scanType = 2;
                        gotoScanActivity();
                    }
                }
                break;
            case 34:
                //系统设置
                goToActivity(SystemActivity.class);
                break;
            case 35:
                //货位管理
                goToActivity(LocationActivity.class);
                break;
            default:
                showMessage(getLanguageValue("stayTuned"));
                break;
        }

    }

    /**
     * 进去含有扫码的页面
     */
    private void gotoScanActivity() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
            switch (scanType) {
                case 0:
                    //出入库
                    startActivity(new Intent(getActivity(), ScanActivity.class)
                            .putExtra("type", 1)
                    );
                    break;
                case 1:
                    //移动收银
                    goToActivity(CashierShiftActivity.class);
                    break;
                case 2:
                    //快速收银
                    goToActivity(CashierQuickActivity.class);
                    break;
            }
        } else {
            PermissionUtils.requestPermissions(ShopFragment.this, Constants.PERMISSION, 0);
        }
    }

    /*****************************接口交互start****************************/

    /**
     * 获取管理员信息
     */
    private void getShopUserInfo() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", staffAccount);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getPersonInfoUrlTWO(),
                params,
                GeRenBean.DataBean.class,
                new RequestListener<GeRenBean.DataBean>() {
                    @Override
                    public void success(GeRenBean.DataBean data) {
                        imageUrl = StringUtils.handledImgUrl(data.getStaffProtrait());
                        Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("user1", user);
                        imagePath = Util.getCacheDir(getActivity()) + File.separator + "image";
                        user.image_path = imagePath;
                        user.image_url = imageUrl;
                        String people_name = data.getStaffName();
                        tvStaffName.setText(people_name);
                        user.shop_name = getShop_name();//缓存店铺名
                        user.people_name = people_name;
                        Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("user1", user);
                    }
                });
    }

    /**
     * 主界面信息统计（门店数据）
     */
    private void getShopOrderCount() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("datetime", now_time);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getShopMessagenew(),
                map,
                ShopOrderCountData.class,
                new RequestListener<ShopOrderCountData>() {
                    @Override
                    public void success(ShopOrderCountData data) {
                        tvSaleSum.setText("RM" + DFUtils.getNum2(data.getSaleSum()));
                        tvOrderCount.setText(String.valueOf(data.getListCount()));
                        tvBeans.setText(DFUtils.getNum2(data.getBeans_use()));
                        tvProfit.setText(DFUtils.getNum4(data.getProfit()));
                        tvProfitRate.setText(DFUtils.getNum4(data.getProfitRatio()) + "%");
                        tvUnitPrice.setText(DFUtils.getNum2(data.getUnitPrice()));

                        user.d_count = data.getListCount() + "";
                        user.buyhoo_beans = data.getBeans_use() + "";
                        user.m_lirun = DFUtils.getNum4(data.getProfit());
                        user.m_lirunlv = DFUtils.getNum4(data.getProfitRatio()) + "%";
                        user.k_price = data.getUnitPrice() + "";
                        Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("user1", user);
                        user.shop_money = data.getSaleSum() + "";
                    }
                });

    }

    /**
     * 获取店铺信息
     */
    private void getShopInfo() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getShopInfoUrlTWO(),
                params,
                ShopInfoResponseModel.DataBean.class,
                new RequestListener<ShopInfoResponseModel.DataBean>() {
                    @Override
                    public void success(ShopInfoResponseModel.DataBean data) {
                        shopInfoBean = data;
                        tvShopName.setText(shopInfoBean.getShopName());
                        String shopAddress = shopInfoBean.getShopAddress();
                        Editor editor = sp.edit();
                        editor.putString(Constants.CONSTANT_SHOP_ADDRESS, shopAddress);
                        editor.putString("staffProtrait", StringUtils.handledImgUrl(data.getShopImagePath()));
                        editor.commit();
                        imagePath = Util.getCacheDir(getActivity()) + File.separator + "image";
                        shopInfoBean.image_path = imagePath;
                        Cache.with(getActivity()).path(imagePath).saveImage(imageUrl);
                        Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("ShopINfo", shopInfoBean);
                        Glide.with(getActivity())
                                .load(StringUtils.handledImgUrl(data.getShopImagePath()))
                                .circleCrop()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .apply(new RequestOptions().error(R.mipmap.ic_default_head))
                                .into(ivHead);
                    }
                });
    }

    /**
     * 消息列表（公告）
     */
    private void getMsgList() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("pages", 1);
        params.put("perpage", 20);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getShopMessagetwo(),
                params,
                MessageBean.DataBean.class,
                new RequestListListener<MessageBean.DataBean>() {
                    @Override
                    public void onResult(List<MessageBean.DataBean> list) {
                        if (list == null) {
                            linNotice.setVisibility(View.GONE);
                            return;
                        }
                        if (list.size() < 1) {
                            linNotice.setVisibility(View.GONE);
                            return;
                        }
                        linNotice.setVisibility(View.VISIBLE);
                        noticeList.clear();
                        for (int i = 0; i < list.size(); i++) {
                            noticeList.add(list.get(i).getShopMsgTitle());
                        }
                        looperTextView.setTipList(noticeList);
                        if (list.size() > 1) {
                            String time1 = list.get(0).getTime() + list.get(0).getTimeType() + getLanguageValue("front");
                            String time2 = list.get(1).getTime() + list.get(1).getTimeType() + getLanguageValue("front");
                            user.title1 = list.get(0).getShopMsgTitle();
                            user.title2 = list.get(1).getShopMsgTitle();
                            user.title_time1 = time1;
                            user.title_time2 = time2;
                            Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("user1", user);
                        } else if (list.size() == 1) {
                            String time1 = list.get(0).getTime() + list.get(0).getTimeType() + getLanguageValue("front");
                            user.title1 = list.get(0).getShopMsgTitle();
                            user.title_time1 = time1;
                            Cache.with(getActivity()).path(Util.getCacheDir(getActivity())).saveCache("user1", user);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        linNotice.setVisibility(View.GONE);
                    }
                });
    }

    /**
     * 查询主页的模块信息_v2
     */
    private void getModule_v2() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("showType", 1);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getmodule_v2(),
                map,
                ShopToolData.class,
                new RequestListListener<ShopToolData>() {
                    @Override
                    public void onResult(List<ShopToolData> list) {
                        toolList.clear();
                        toolList.addAll(list);
                        toolCateAdapter.setDataList(toolList);
                    }
                });
    }

    /**
     * 查询聚合码审核状态
     */
    private void getAggregatePayStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAggregatePayStatus(),
                map,
                AggregatePayStatusData.DataBean.class,
                new RequestListener<AggregatePayStatusData.DataBean>() {
                    @Override
                    public void success(AggregatePayStatusData.DataBean data) {
                        if (data == null) {
                            return;
                        }
                        //聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
                        switch (data.getAggregateAuditStatus()) {
                            case 0:
                            case 2:
                                startActivity(new Intent(getActivity(), AggregationActivity.class)
                                        .putExtra("status", data.getAggregateAuditStatus())
                                        .putExtra("indexImage", data.getAggregateIndexImage())
                                        .putExtra("payImage", data.getAggregatePayImage())
                                        .putExtra("guideImage", data.getNotLegalGuideImage())
                                        .putExtra("helibao", data.getHelibaoAuthBookUrl())
                                        .putExtra("ruiyinxin", data.getRuiyinxinAuthBookUrl())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                );
                                break;
                            default:
                                startActivity(new Intent(getActivity(), AggregationApplyStateActivity.class)
                                        .putExtra("status", data.getAggregateAuditStatus())
                                        .putExtra("reason", data.getAggregateRefuseReason())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                        .putExtra("guideImage", data.getNotLegalGuideImage())
                                        .putExtra("helibao", data.getHelibaoAuthBookUrl())
                                        .putExtra("ruiyinxin", data.getRuiyinxinAuthBookUrl())
                                        .putExtra("applyType", data.getAggregateApplyType())
                                );
                                break;
                        }
                    }
                });
    }

    /*****************************接口交互end****************************/

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    switch (scanType) {
                        case 0:
                            //出入库
                            startActivity(new Intent(getActivity(), ScanActivity.class)
                                    .putExtra("type", 1)
                            );
                            break;
                        case 1:
                            //快速收银
                            goToActivity(CashierShiftActivity.class);
                            break;
                        case 2:
                            //宁宇收银
                            goToActivity(CashierQuickActivity.class);
                            break;
                    }
                }
                break;
        }
    }
}
