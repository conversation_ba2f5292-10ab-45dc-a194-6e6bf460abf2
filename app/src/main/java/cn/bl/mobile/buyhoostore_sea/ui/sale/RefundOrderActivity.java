package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.android.material.tabs.TabLayout;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.StringCallback;
import com.lzy.okgo.model.Response;
import com.yxl.commonlibrary.base.BaseActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.evevtbus.FirstEvent;
import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerWhiteAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:销售-退款单
 * Created by jingang on 2023/3/24
 */
@SuppressLint("NonConstantResourceId")
public class RefundOrderActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tabRefundOrder)
    TabLayout tabLayout;
    @BindView(R.id.refundOrderViewPager)
    ViewPager viewPager;

    private SimpleFragmentPagerWhiteAdapter mPagerAdapter;
    private List<String> mTitle = new ArrayList<>();
    private List<Fragment> mFragment = new ArrayList<>();
    private List<Integer> mBadgeCountList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_refund_order;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        tvTitle.setText(getLanguageValue("refund")+getLanguageValue("order"));
        initFragments();
        upDateCount();
        initView();
        initData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    private void initFragments() {
        mTitle.add(getLanguageValue("whole"));
        mTitle.add(getLanguageValue("beReviewed"));
        mTitle.add(getLanguageValue("refunded"));
        mTitle.add(getLanguageValue("refused"));
        for (int i = 0; i < mTitle.size(); i++) {
            mBadgeCountList.add(0);
        }
        mFragment.add(RefundOrderListFragment.getNewInstance(-1));
        mFragment.add(RefundOrderListFragment.getNewInstance(1));
        mFragment.add(RefundOrderListFragment.getNewInstance(3));
        mFragment.add(RefundOrderListFragment.getNewInstance(4));
    }


    private void initView() {
        mPagerAdapter = new SimpleFragmentPagerWhiteAdapter(
                this,
                getSupportFragmentManager(),
                mFragment,
                mTitle,
                mBadgeCountList
        );
        viewPager.setAdapter(mPagerAdapter);
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                updateTabTextView(tab, true);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                updateTabTextView(tab, false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        tabLayout.setupWithViewPager(viewPager);
        setUpTabBadge();

    }

    @Subscribe
    public void onEventMainThread(FirstEvent event) {
        String msg = event.getMsg();
        if (Constants.CONSTANT_REFRESH_LIST_COUNT.equals(msg)) {
            upDateCount();
        }
    }

    /**
     * 设置Tablayout上的标题的角标
     */
    private void setUpTabBadge() {
        // 2. 最实用
        for (int i = 0; i < mFragment.size(); i++) {
            TabLayout.Tab tab = tabLayout.getTabAt(i);
            // 更新Badge前,先remove原来的customView,否则Badge无法更新
            View customView = tab.getCustomView();
            if (customView != null) {
                ViewParent parent = customView.getParent();
                if (parent != null) {
                    try {
                        ((ViewGroup) parent).removeView(customView);
                    } catch (Exception ignored) {
                    }
                }
            }
            // 更新CustomView
            tab.setCustomView(mPagerAdapter.getTabItemView(i));
        }

        // 需加上以下代码,不然会出现更新Tab角标后,选中的Tab字体颜色不是选中状态的颜色
        TabLayout.Tab tabAt = tabLayout.getTabAt(tabLayout.getSelectedTabPosition());
        updateTabTextView(tabAt, true);
    }

    private void updateTabTextView(TabLayout.Tab tab, boolean isSelect) {
        if (tab == null) {
            return;
        }
        if (tab.getCustomView() == null) {
            return;
        }
        TextView tabSelect = tab.getCustomView().findViewById(R.id.tvItemName);
        View line = tab.getCustomView().findViewById(R.id.vItem);
        tabSelect.setText(tab.getText());
        if (isSelect) {
            //选中加粗
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            line.setVisibility(View.VISIBLE);
        } else {
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            line.setVisibility(View.GONE);
        }
    }

    private void upDateCount() {
        OkGo.<String>post(ZURL.getshopsSaleListCount())
                .tag(this)
                .params("shopUnique", getShop_id())
                .execute(new StringCallback() {
                    @Override
                    public void onSuccess(Response<String> response) {
                        JSONObject jsonObject = JSON.parseObject(response.body());
                        try {
                            if (jsonObject.getInteger("status") == 1) {
                                JSONArray data = jsonObject.getJSONArray("data");
                                for (int i = 0; i < data.size(); i++) {
                                    JSONObject objc = data.getJSONObject(i);
                                    if (objc.getInteger("handleState") == -2) {
                                        mBadgeCountList.set(1, objc.getInteger("count"));
                                        setUpTabBadge();
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Log.e(tag, e.toString());
                        }
                    }
                });
    }

}
