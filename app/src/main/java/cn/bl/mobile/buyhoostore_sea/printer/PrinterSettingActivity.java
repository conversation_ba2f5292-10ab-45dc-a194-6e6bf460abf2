package cn.bl.mobile.buyhoostore_sea.printer;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.gprinter.bean.PrinterDevices;
import com.gprinter.utils.CallbackListener;
import com.gprinter.utils.Command;
import com.gprinter.utils.ConnMethod;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.utils.SystemUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jc.BluetoothUtils;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrintUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrinterData;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.Printer;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;

/**
 * Describe:连接蓝牙打印机
 * Created by jingang on 2024/4/18
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class PrinterSettingActivity extends BaseActivity implements CallbackListener {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvPrinterTitle)
    TextView tvPrinterTitle;
    @BindView(R.id.tvPrinterName)
    TextView tvPrinterName;
    @BindView(R.id.ivPrinterImg)
    ImageView ivPrinterImg;
    @BindView(R.id.tvPrinterTypeSelect)
    TextView tvPrinterTypeSelect;
    @BindView(R.id.tvPrinterType)
    TextView tvPrinterType;//打印机型号
    @BindView(R.id.tvBluetoothAddress)
    TextView tvBluetoothAddress;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.tvTagSelect)
    TextView tvTagSelect;
    @BindView(R.id.tvDirection)
    TextView tvDirection;
    @BindView(R.id.tvDirection0)
    TextView tvDirection0;
    @BindView(R.id.tvDirection1)
    TextView tvDirection1;
    @BindView(R.id.ivDirection0)
    ImageView ivDirection0;
    @BindView(R.id.ivDirection1)
    ImageView ivDirection1;
    @BindView(R.id.linTag0)
    LinearLayout linTag0;
    @BindView(R.id.ivTag0)
    ImageView ivTag0;
    @BindView(R.id.ivTag1)
    ImageView ivTag1;
    @BindView(R.id.linTag2)
    LinearLayout linTag2;
    @BindView(R.id.ivTag2)
    ImageView ivTag2;
    @BindView(R.id.linTag3)
    LinearLayout linTag3;
    @BindView(R.id.ivTag3)
    ImageView ivTag3;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    //打印机列表
    private PrinterListAdapter mAdapter;
    private List<PrinterData> dataList = new ArrayList<>();

    private BluetoothAdapter mBluetoothAdapter;

    public static final int REQUEST_ENABLE_BT = 2;
    public static final int REQUEST_ENABLE_GPS = 3;
    private SharedPreferencesUtil sharedPreferencesUtil;

    private int printerType,//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
            printDirection,//标签方向 0.正向 1.反向
            printType;//标签模版 1.无店铺名称 2.有店铺名称 3.优乐购 4.小版

    @Override
    protected int getLayoutId() {
        return R.layout.activity_printer_setting;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("connectBluetoothPrinter"));
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        setUI();
        setAdapter();
        initPermission();
    }

    @OnClick({R.id.ivBack, R.id.linPrinter, R.id.linPrinterType, R.id.linTag0, R.id.linDirection0, R.id.linDirection1, R.id.linTag1, R.id.linTag2, R.id.linTag3, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linPrinter:
                //搜索
                searchBluetoothJC();
                break;
            case R.id.linPrinterType:
                //打印机型号
                startActivityForResult(new Intent(this, PrinterTypeActivity.class), Constants.CHOOSE);
                break;
            case R.id.linDirection0:
                //标签方向（正向）
                if (printDirection != 0) {
                    sharedPreferencesUtil.putInt(0, Constants.PRINT_DIRECTION);
                    setUI();
                }
                break;
            case R.id.linDirection1:
                //标签方向（反向）
                if (printDirection != 1) {
                    sharedPreferencesUtil.putInt(1, Constants.PRINT_DIRECTION);
                    setUI();
                }
                break;
            case R.id.linTag0:
                //标签纸样式0
                if (printType != 1) {
                    sharedPreferencesUtil.putInt(1, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.linTag1:
                //标签纸样式1
                if (printType != 2) {
                    sharedPreferencesUtil.putInt(2, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.linTag2:
                //标签纸样式2
                if (printType != 3) {
                    sharedPreferencesUtil.putInt(3, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.linTag3:
                //标签纸样式3
                if (printerType != 4) {
                    sharedPreferencesUtil.putInt(4, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.tvConfirm:
                //保存设置并打印
                setResult(Constants.BLUETOOTH, new Intent());
                this.finish();
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mBluetoothAdapter != null) {
            mBluetoothAdapter.cancelDiscovery();
        }
        if (receiver != null) {
            try {
                unregisterReceiver(receiver);
            } catch (Exception ignored) {

            }
        }
    }

    @Override
    public void onConnecting() {
        Log.e(tag, "连接中");
        tvPrinterTitle.setText("设备连接中");
        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_999));
        tvPrinterName.setText("连接中");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(1);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onCheckCommand() {
        Log.e(tag, "onCheckCommand");
    }

    @Override
    public void onSuccess(PrinterDevices printerDevices) {
        //连接成功
        Log.e(tag, "连接成功 = " + printerDevices.getMacAddress());
        tvPrinterTitle.setText("设备已连接");
        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_333));
        tvPrinterName.setText("蓝牙打印机已连接）" + printerDevices.getBlueName() + "）");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img002);
        tvConfirm.setVisibility(View.VISIBLE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(0);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onReceive(byte[] bytes) {
        Log.e(tag, "onReceive = " + bytes);
    }

    @Override
    public void onFailure() {
        //连接失败
        Log.e(tag, "连接失败");
        tvPrinterTitle.setText("设备未连接");
        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_999));
        tvPrinterName.setText("请连接蓝牙打印机");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(2);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onDisconnect() {
        //断开连接
        Log.e(tag, "断开连接");
        tvPrinterTitle.setText("设备断开连接");
        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_999));
        tvPrinterName.setText("请连接蓝牙打印机");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(2);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void setText() {
        tvPrinterTypeSelect.setText(getLanguageValue("selectPrinterModel"));
        tvBluetoothAddress.setText(getLanguageValue("clickBluetoothAddress"));
        tvTagSelect.setText(getLanguageValue("selectLabelStyle")+"(70mm*38mm)");
        tvDirection.setText(getLanguageValue("labelOrientation")+":");
        tvDirection0.setText(getLanguageValue("positive"));
        tvDirection1.setText(getLanguageValue("reverse"));
        tvConfirm.setText(getLanguageValue("savePrint"));
    }

    /**
     * 更新UI
     */
    private void setUI() {
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
        printDirection = sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION);
        printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);

        //标签方向
        if (printDirection == 0) {
            ivDirection0.setImageResource(R.mipmap.ic_chosen001);
            ivDirection1.setImageResource(R.mipmap.ic_chose001);
        } else {
            ivDirection0.setImageResource(R.mipmap.ic_chose001);
            ivDirection1.setImageResource(R.mipmap.ic_chosen001);
        }

        //打印机类型
        if (printerType == 0) {
            tvPrinterType.setText("佳博 CP-M322");
            linTag0.setVisibility(View.VISIBLE);
            linTag2.setVisibility(View.GONE);
            linTag3.setVisibility(View.VISIBLE);
            //打印模版
            switch (printType) {
                case 1:
                    ivTag0.setSelected(true);
                    ivTag1.setSelected(false);
                    ivTag2.setSelected(false);
                    ivTag3.setSelected(false);
                    break;
                case 2:
                    ivTag0.setSelected(false);
                    ivTag1.setSelected(true);
                    ivTag2.setSelected(false);
                    ivTag3.setSelected(false);
                    break;
                case 4:
                    ivTag0.setSelected(false);
                    ivTag1.setSelected(false);
                    ivTag2.setSelected(false);
                    ivTag3.setSelected(true);
                    break;
                default:
                    sharedPreferencesUtil.putInt(1, Constants.PRINT_TYPE);
                    ivTag0.setSelected(true);
                    ivTag1.setSelected(false);
                    ivTag2.setSelected(false);
                    ivTag3.setSelected(false);
                    break;
            }
        } else {
            tvPrinterType.setText("精臣 NIIMBOT B3S");
            linTag0.setVisibility(View.GONE);
            linTag3.setVisibility(View.GONE);
            if (printDirection == 0) {
                linTag2.setVisibility(View.VISIBLE);
                switch (printType) {
                    case 2:
                        ivTag0.setSelected(false);
                        ivTag1.setSelected(true);
                        ivTag2.setSelected(false);
                        ivTag3.setSelected(false);
                        break;
                    case 3:
                        ivTag0.setSelected(false);
                        ivTag1.setSelected(false);
                        ivTag2.setSelected(true);
                        ivTag3.setSelected(false);
                        break;
                    case 4:
                        ivTag0.setSelected(false);
                        ivTag1.setSelected(false);
                        ivTag2.setSelected(false);
                        ivTag3.setSelected(true);
                        break;
                    default:
                        sharedPreferencesUtil.putInt(2, Constants.PRINT_TYPE);
                        ivTag0.setSelected(false);
                        ivTag1.setSelected(true);
                        ivTag2.setSelected(false);
                        ivTag3.setSelected(false);
                        break;
                }
            } else {
                linTag2.setVisibility(View.GONE);
                if (printType != 2) {
                    sharedPreferencesUtil.putInt(2, Constants.PRINT_TYPE);
                }
                ivTag0.setSelected(false);
                ivTag1.setSelected(true);
                ivTag2.setSelected(false);
                ivTag3.setSelected(false);
            }
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new PrinterListAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            pos = position;
            if (mBluetoothAdapter.isDiscovering()) {
                mBluetoothAdapter.cancelDiscovery();
            }
            connectBluetooth();
        });
    }

    /**
     * 动态申请权限
     */
    private void initPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (PermissionUtils.checkPermissionsGroup(this, 6)) {
                initBluetooth();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION, 6);
            }
        } else {
            if (PermissionUtils.checkPermissionsGroup(this, 1)) {
                initBluetooth();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
            }
        }
    }

    /**
     * 初始化蓝牙
     */
    private void initBluetooth() {
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (mBluetoothAdapter == null) {
            showMessage("设备不支持蓝牙");
            finish();
            return;
        }
        if (mBluetoothAdapter.isEnabled()) {
            if (SystemUtils.hasGPSDevice(this)) {
                if (SystemUtils.isLocationEnabled(this)) {
                    registerReceiver();
                } else {
                    //添加"Yes"按钮
                    new AlertDialog.Builder(TAG)
                            .setTitle("提示")
                            .setMessage(getString(R.string.gps_permission))
                            .setIcon(R.mipmap.mylogo)
                            .setPositiveButton(getString(R.string.confirm), (dialogInterface, i) -> {
                                Intent intent = new Intent();
                                intent.setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivityForResult(intent, REQUEST_ENABLE_GPS);
                            }).create().show();
                }
            } else {
                registerReceiver();
            }
        } else {
            startActivityForResult(new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE), REQUEST_ENABLE_BT);
        }
    }

    /**
     * 注册广播
     */
    private void registerReceiver() {
        //注册广播
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(BluetoothDevice.ACTION_FOUND);
        intentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        intentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        intentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_PAIRING_REQUEST);
        registerReceiver(receiver, intentFilter);
        Log.d(tag, "初始化: 注册完成");
        //注册线程池
        ThreadFactory threadFactory = runnable -> {
            Thread thread = new Thread(runnable);
            thread.setName("connect_activity_pool_%d");
            return thread;
        };
        executorService = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        searchBluetoothJC();
    }

    /**
     * 搜索设备(精臣)
     */
    private void searchBluetoothJC() {
        Log.e(tag, "开始搜索设备");
        if (!mBluetoothAdapter.isEnabled()) {
            showMessage("蓝牙未开启");
        }
        dataList.clear();
        if (mBluetoothAdapter.isDiscovering()) {
            if (mBluetoothAdapter.cancelDiscovery()) {
                executorService.execute(() -> {
                    try {
                        //取消后等待1s后再次搜索
                        Thread.sleep(1000);
                        mBluetoothAdapter.startDiscovery();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });
            }
        } else {
            mBluetoothAdapter.startDiscovery();
        }
    }

    /**
     * 连接设备
     */
    private void connectBluetooth() {
        if (dataList.size() <= pos) {
            return;
        }
        //打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
        if (printerType == 0) {
            tvPrinterTitle.setText("设备连接中");
            tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_999));
            tvPrinterName.setText("连接中");
            PrinterDevices blueTooth = new PrinterDevices.Build().setContext(this).setConnMethod(ConnMethod.BLUETOOTH).setMacAddress(dataList.get(pos).getAddress()).setCommand(Command.TSC).setCallbackListener(this).build();
            Printer.connect(blueTooth);
        } else {
            if (dataList.get(pos).getState() != 12) {
                //未配对
                BluetoothDevice bluetoothDevice = mBluetoothAdapter.getRemoteDevice(dataList.get(pos).getAddress());
                executorService.submit(() -> {
                    Log.d(tag, "配对: 开始");
                    boolean returnValue = false;
                    try {
                        returnValue = BluetoothUtils.createBond(bluetoothDevice);
                    } catch (Exception e) {
                        Log.d(tag, "闪退日志" + e.getMessage());
                    }
                    Log.d(tag, "配对: 进行中:" + returnValue);

                });
            } else {
                //已配对
                runOnUiThread(() -> {
                    Log.e(tag, "连接中");
                    PrintUtil.setConnectedType(-1);
                    int connectResult = PrintUtil.connectBluetoothPrinter(dataList.get(pos).getAddress());
                    Log.d(tag, "测试：连接结果 " + connectResult);
                    if (connectResult == 0) {
                        dataList.get(pos).setState(0);
                        mAdapter.notifyItemChanged(pos);
                        tvPrinterTitle.setText("设备已连接");
                        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_333));
                        tvPrinterName.setText("蓝牙打印机已连接）" + dataList.get(pos).getName() + "）");
                        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img002);
                        tvConfirm.setVisibility(View.VISIBLE);
                    } else {
                        tvPrinterTitle.setText("设备未连接");
                        tvPrinterTitle.setTextColor(getResources().getColor(R.color.color_999));
                        tvPrinterName.setText("请连接蓝牙打印机");
                        ivPrinterImg.setImageResource(R.mipmap.ic_printer_img001);
                        tvConfirm.setVisibility(View.GONE);
                    }
                });
            }
        }
    }

    /***********************精臣start**********************/

    private ExecutorService executorService;
    private int pos;

    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case BluetoothDevice.ACTION_FOUND:
                    //蓝牙发现
                    BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    if (device == null) {
                        return;
                    }
                    Log.e(tag, "device = " + device.getName());
                    if (TextUtils.isEmpty(device.getName())) {
                        return;
                    }
                    //printerType;//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
//                    if (printerType == 0 && !device.getName().startsWith("GP")) {
//                        return;
//                    }
                    if (printerType == 1 && !device.getName().startsWith("B3S")) {
                        return;
                    }

                    PrinterData data = new PrinterData();
                    data.setName(device.getName());
                    data.setAddress(device.getAddress());
                    data.setState(device.getBondState());

                    for (PrinterData p : dataList) {
                        if (p.getAddress().equals(data.getAddress())) {//防止重复添加
                            return;
                        }
                    }
                    dataList.add(0, data);
                    mAdapter.setDataList(dataList);
                    break;
                case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                    Log.e(tag, "开始搜索");
                    break;
                case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                    Log.e(tag, "搜索结束");
                    break;
                case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                    //配对状态改变
                    int state = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1);
                    Log.d(tag, "测试:配对状态改变:0 " + state);
                    if (dataList.size() > pos) {
                        dataList.get(pos).setState(state);
                        mAdapter.notifyItemChanged(pos);
                        if (state == 12 && !isQuicklyClick()) {
                            connectBluetooth();
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    };

    /***********************精臣end**********************/

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage("因权限未开启，该功能无法使用，请去设置中开启。");
                } else {
                    initBluetooth();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    registerReceiver();
                } else {
                    showMessage("蓝牙未开启");
                    finish();
                }
                break;
            case REQUEST_ENABLE_GPS:
                registerReceiver();
                break;
            case Constants.CHOOSE:
                if (data != null) {
                    setUI();
                    initPermission();
                }
                break;
        }
    }
}
