package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotSelectShopAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.ShopData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（选择店铺）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class ShopDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.etDialogSearch)
    EditText etSearch;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keywords;

    private AllotSelectShopAdapter mAdapter;
    private List<ShopData> dataList = new ArrayList<>();

    public static void showDialog(Context context, MyListener listener) {
        ShopDialog.listener = listener;
        ShopDialog dialog = new ShopDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public ShopDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_shop);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("shop")+getLanguageValue("choose"));
        etSearch.setHint(getLanguageValue("shop")+getLanguageValue("name")+"/"+getLanguageValue("number"));
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            getShop_list();
            return true;
        });
        setAdapter();
        getShop_list();
    }

    @OnClick({R.id.ivDialogClose})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new AllotSelectShopAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onClick(view, dataList.get(position).getShopUnique(), dataList.get(position).getShopName());
                dismiss();
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getShop_list();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 店铺列表
     */
    private void getShop_list() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("managerUnique", getManager_unique());
        map.put("shopUnique", getShop_id());
        map.put("staffPosition", getStaffPosition());
        map.put("shop_name", keywords);
//        map.put("pageNum", page);
//        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getshoplistTWO(),
                map,
                ShopData.class,
                new RequestListListener<ShopData>() {
                    @Override
                    public void onResult(List<ShopData> shopData) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(shopData);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(View view, String unique, String name);
    }
}
