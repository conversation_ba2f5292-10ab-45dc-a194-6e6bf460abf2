package cn.bl.mobile.buyhoostore_sea.jpush;

import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;
import android.util.Log;

import com.yxl.commonlibrary.base.BaseApplication;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;

import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.bean.OnReceiveGoodsEvent;
import cn.bl.mobile.buyhoostore_sea.ui.MainActivity;
import cn.bl.mobile.buyhoostore_sea.utils.SystemTTS;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.helper.Logger;


/**
 * Created by Administrator on 2017/3/6 0006.
 */

public class MyReceiver extends BroadcastReceiver {
    private static final String TAG = "buyhoostoreMyReceiver";
    public static final String MESSAGE_RECEIVED_ACTION = "com.example.jpushdemo.MESSAGE_RECEIVED_ACTION";
    public static final String KEY_MESSAGE = "message";
    public static final String KEY_EXTRAS = "extras";
    private SharedPreferences sp = null;
    private NotificationManager nm;
    private OnResfreshListener mOnResfreshListener;
    private String registerid = "";
    public static String register_id;
    private String sale_list_unique = "";
    public static boolean isNewOrder;
    SystemTTS systemTTS = SystemTTS.getInstance(BaseApplication.getInstance());

    final Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == 1) {
                String response = msg.obj.toString();
                Log.i("TAG", "json:" + response);
                int status = 0;
                String message = "";
                JSONObject object;
                try {
                    object = new JSONObject(response);
                    status = object.getInt("status");
                    message = object.getString("msg");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (status == 1) {
//					Editor editor = sp.edit();
//					editor.putString("start_denglu","0");
//					editor.commit();
                }
            }
        }


    };

    @Override
    public void onReceive(Context context, Intent intent) {
        try {
        /*if (null == nm) {
            nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        }*/
            sp = context.getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
            Bundle bundle = intent.getExtras();
            Log.e("111111", "推送消息 = " + new JSONObject(bundle.getString(JPushInterface.EXTRA_EXTRA)));

            if (JPushInterface.ACTION_REGISTRATION_ID.equals(intent.getAction())) {
                registerid = JPushInterface.getRegistrationID(context);
                if (!registerid.equals("")) {
                    register_id = registerid;
                }

            } else if (JPushInterface.ACTION_MESSAGE_RECEIVED.equals(intent.getAction())) {
                Log.d(TAG, "接受到推送下来的自定义消息");
                //processCustomMessage(context, bundle);
                processMessage(context, bundle);

            } else if (JPushInterface.ACTION_NOTIFICATION_RECEIVED.equals(intent.getAction())) {
                Log.d(TAG, "接受到推送下来的通知");
                int notifactionId = bundle.getInt(JPushInterface.EXTRA_NOTIFICATION_ID);
                Logger.d(TAG, "[MyReceiver] 接收到推送下来的通知的ID: " + notifactionId);

                receivingNotification(context, bundle);

            } else if (JPushInterface.ACTION_NOTIFICATION_OPENED.equals(intent.getAction())) {
                Log.d(TAG, "用户点击打开了通知" + sale_list_unique);
                String extras = bundle.getString(JPushInterface.EXTRA_EXTRA);
                Log.d(TAG, "extras : " + extras);
                try {
                    JSONObject jsonObject = new JSONObject(extras);
                    sale_list_unique = jsonObject.getString("sale_list_unique");
                    int msgType = jsonObject.getInt("msgType");
                    if (msgType == 9) {
                        int goodsId = jsonObject.getInt("goodsId");
                        EventBus.getDefault().postSticky(new OnReceiveGoodsEvent(goodsId));
                        Intent i = new Intent(context, MainActivity.class);
                        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                        context.startActivity(i);

                    } else {
                        if (!sale_list_unique.isEmpty()) {
                            isNewOrder = true;
                            //打开自定义的Activity
                            Intent i = new Intent(context, MainActivity.class);
                            i.putExtras(bundle);
                            //i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            context.startActivity(i);
                        }
                    }
                    Log.d(TAG, "用户点击打开了通知 sale_list_unique: " + sale_list_unique);

                } catch (JSONException e) {

                }
                //EventBus.getDefault().post(new FirstEvent(sale_list_unique));
//            mOnResfreshListener.onResfresh(sale_list_unique);
//            Intent intent2 = new Intent(context,SaleOrderDetailsActivity.class);
//            intent2.putExtra("sale_list_unique",sale_list_unique);
//            intent2.putExtra("p","1");
//            context.startActivity(intent2);
//            openNotification(context,bundle);


            } else {
                Log.d(TAG, "Unhandled intent - " + intent.getAction());
            }
        } catch (Exception e) {

        }
    }


    private void receivingNotification(Context context, Bundle bundle) {
        String title = bundle.getString(JPushInterface.EXTRA_NOTIFICATION_TITLE);
        Log.d(TAG, " title : " + title);
        String message = bundle.getString(JPushInterface.EXTRA_ALERT);
        Log.d(TAG, "message : " + message);
        String extras = bundle.getString(JPushInterface.EXTRA_EXTRA);
        Log.d(TAG, "extras : " + extras);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(extras);
//            String sale_list_unique = jsonObject.getString("sale_list_unique");
//            if (!sale_list_unique.isEmpty()) {
////                systemTTS.playText("您有新订单请及时处理！");
//                EventBus.getDefault().post(new FirstEvent("refershNewOrdersList"));
//                //请求接口，更新订单数量
//                EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
//            }
            String msgType = jsonObject.getString("msgType");
            if (TextUtils.isEmpty(msgType)) {
                return;
            }
            if (!msgType.equals("11")) {
                return;
            }
            showMsg(message);
        } catch (JSONException e) {
            e.printStackTrace();
        }


    }

    private void openNotification(Context context, Bundle bundle) {
        String extras = bundle.getString(JPushInterface.EXTRA_EXTRA);
        String myValue = "";
        try {
            JSONObject extrasJson = new JSONObject(extras);
            myValue = extrasJson.optString("myKey");
        } catch (Exception e) {
            Log.w(TAG, "Unexpected: extras is not a valid json", e);
            return;
        }
    }

    // 打印所有的 intent extra 数据
    private static String printBundle(Bundle bundle) {
        StringBuilder sb = new StringBuilder();
        for (String key : bundle.keySet()) {
            if (key.equals(JPushInterface.EXTRA_NOTIFICATION_ID)) {
                sb.append("\nkey:" + key + ", value:" + bundle.getInt(key));
            } else if (key.equals(JPushInterface.EXTRA_CONNECTION_CHANGE)) {
                sb.append("\nkey:" + key + ", value:" + bundle.getBoolean(key));
            } else if (key.equals(JPushInterface.EXTRA_EXTRA)) {
                if (TextUtils.isEmpty(bundle.getString(JPushInterface.EXTRA_EXTRA))) {
                    Log.i(TAG, "This message has no Extra data");
                    continue;
                }

                try {
                    JSONObject json = new JSONObject(bundle.getString(JPushInterface.EXTRA_EXTRA));
                    Iterator<String> it = json.keys();

                    while (it.hasNext()) {
                        String myKey = it.next().toString();
                        sb.append("\nkey:" + key + ", value: [" +
                                myKey + " - " + json.optString(myKey) + "]");
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Get message extra JSON error!");
                }

            } else {
                sb.append("\nkey:" + key + ", value:" + bundle.getString(key));
            }
        }
        return sb.toString();
    }


    public interface OnResfreshListener {
        void onResfresh(String isSelect);
    }

    public void setResfreshListener(OnResfreshListener mOnResfreshListener) {
        this.mOnResfreshListener = mOnResfreshListener;
    }


    //send msg to MainActivity
    private void processCustomMessage(Context context, Bundle bundle) {
        String message = bundle.getString(JPushInterface.EXTRA_MESSAGE);
        String extras = bundle.getString(JPushInterface.EXTRA_EXTRA);
        Intent msgIntent = new Intent(MESSAGE_RECEIVED_ACTION);
        msgIntent.putExtra(KEY_MESSAGE, message);
        if (!TextUtils.isEmpty(extras)) {
            try {
                JSONObject extraJson = new JSONObject(extras);
                if (extraJson.length() > 0) {
                    msgIntent.putExtra(KEY_EXTRAS, extras);
                }
            } catch (JSONException e) {

            }

        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(msgIntent);
    }


    private void processMessage(Context context, Bundle bundle) {
        String title = bundle.getString(JPushInterface.EXTRA_TITLE);
        Log.d(TAG, " title : " + title);
        String content_type = bundle.getString(JPushInterface.EXTRA_CONTENT_TYPE);
        Log.d(TAG, "content_type : " + content_type);
        String extras = bundle.getString(JPushInterface.EXTRA_EXTRA);
        Log.d(TAG, "extras : " + extras);
        String message = bundle.getString(JPushInterface.EXTRA_MESSAGE);
        Log.d(TAG, "message : " + message);
        if (!message.isEmpty()) {
//            systemTTS.playText(message);
            showMsg(message);
        }
    }

    /**
     * 语音播报
     *
     * @param msg
     */
    private void showMsg(String msg) {
        systemTTS.playText(msg);
//        Intent intent = new Intent(BaseApplication.getInstance(), TTSForegroundService.class);
//        intent.putExtra("text", msg);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            BaseApplication.getInstance().startForegroundService(intent);
//        } else {
//            BaseApplication.getInstance().startService(intent);
//        }
    }

}
