package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.bean;

/**
 * Describe:查询聚合码审核状态（实体类）
 * Created by jingang on 2023/5/11
 */
public class AggregatePayStatusData {
    /**
     * status : 1
     * msg : 查询成功
     * data : {"aggregateAuditStatus":1,"aggregateRefuseReason":null,"aggregatePayImage":null,"notLegalGuideImage":"https://file.buyhoo.cc/publicImage/xieyi/demo1.png","aggregateIndexImage":"https://file.buyhoo.cc/publicImage/xieyi/demo.png","helibaoAuthBookUrl":"https://file.buyhoo.cc/publicImage/xieyi/helibao_auth.doc","ruiyinxinAuthBookUrl":"https://file.buyhoo.cc/publicImage/xieyi/helibao_auth.doc"}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object count;
    private Object total;
    private Object rows;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getCount() {
        return count;
    }

    public void setCount(Object count) {
        this.count = count;
    }

    public Object getTotal() {
        return total;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

    public Object getRows() {
        return rows;
    }

    public void setRows(Object rows) {
        this.rows = rows;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * aggregateAuditStatus : 1
         * aggregateRefuseReason : null
         * aggregatePayImage : null
         * notLegalGuideImage : https://file.buyhoo.cc/publicImage/xieyi/demo1.png
         * aggregateIndexImage : https://file.buyhoo.cc/publicImage/xieyi/demo.png
         * helibaoAuthBookUrl : https://file.buyhoo.cc/publicImage/xieyi/helibao_auth.doc
         * ruiyinxinAuthBookUrl : https://file.buyhoo.cc/publicImage/xieyi/helibao_auth.doc
         */

        private int aggregateAuditStatus;//聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败
        private String aggregateRefuseReason;//aggregatePayStatus=3返回，审核失败原因，英文分号分割
        private String aggregatePayImage;//aggregatePayStatus=2返回，聚合码字符串，前端根据内容生成二维码
        private String notLegalGuideImage;//非法人引导页
        private String aggregateIndexImage;//申请开通聚合码首页
        private String helibaoAuthBookUrl;//合利宝授权书
        private String ruiyinxinAuthBookUrl;//瑞银信授权书
        private int aggregateApplyType;//申请人类型0未知1法人申请2非法人申请

        public int getAggregateAuditStatus() {
            return aggregateAuditStatus;
        }

        public void setAggregateAuditStatus(int aggregateAuditStatus) {
            this.aggregateAuditStatus = aggregateAuditStatus;
        }

        public String getAggregateRefuseReason() {
            return aggregateRefuseReason;
        }

        public void setAggregateRefuseReason(String aggregateRefuseReason) {
            this.aggregateRefuseReason = aggregateRefuseReason;
        }

        public String getAggregatePayImage() {
            return aggregatePayImage;
        }

        public void setAggregatePayImage(String aggregatePayImage) {
            this.aggregatePayImage = aggregatePayImage;
        }

        public String getNotLegalGuideImage() {
            return notLegalGuideImage;
        }

        public void setNotLegalGuideImage(String notLegalGuideImage) {
            this.notLegalGuideImage = notLegalGuideImage;
        }

        public String getAggregateIndexImage() {
            return aggregateIndexImage;
        }

        public void setAggregateIndexImage(String aggregateIndexImage) {
            this.aggregateIndexImage = aggregateIndexImage;
        }

        public String getHelibaoAuthBookUrl() {
            return helibaoAuthBookUrl;
        }

        public void setHelibaoAuthBookUrl(String helibaoAuthBookUrl) {
            this.helibaoAuthBookUrl = helibaoAuthBookUrl;
        }

        public String getRuiyinxinAuthBookUrl() {
            return ruiyinxinAuthBookUrl;
        }

        public void setRuiyinxinAuthBookUrl(String ruiyinxinAuthBookUrl) {
            this.ruiyinxinAuthBookUrl = ruiyinxinAuthBookUrl;
        }

        public int getAggregateApplyType() {
            return aggregateApplyType;
        }

        public void setAggregateApplyType(int aggregateApplyType) {
            this.aggregateApplyType = aggregateApplyType;
        }
    }
}
