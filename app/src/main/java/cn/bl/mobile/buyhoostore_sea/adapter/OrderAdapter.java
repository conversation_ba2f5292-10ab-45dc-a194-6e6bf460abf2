package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.bl.mobile.buyhoostore_sea.R;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.bean.OrderListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:销售-订单（适配器）
 * Created by jingang on 2022/12/10
 */
public class OrderAdapter extends BaseAdapter<OrderListData> {
    public OrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivHead = holder.getView(R.id.ivItemHead);
        TextView tvName, tvOrderNo, tvTime, tvState, tvCount, tvPayType, tvPrice, tvDelivery, tvOperation;
        tvName = holder.getView(R.id.tvItemName);
        tvOrderNo = holder.getView(R.id.tvItemOrderNo);
        tvTime = holder.getView(R.id.tvItemTime);
        LinearLayout linRefund = holder.getView(R.id.linItemRefund);
        tvState = holder.getView(R.id.tvItemState);
        View vLine = holder.getView(R.id.vItemLine);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPayType = holder.getView(R.id.tvItemPayType);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvDelivery = holder.getView(R.id.tvItemDelivery);
        TextView tvDelivery_personal, tvDelivery_mobile;
        tvDelivery_personal = holder.getView(R.id.tvItemDelivery_personnel);
        tvDelivery_mobile = holder.getView(R.id.tvItemDelivery_mobile);
        tvOperation = holder.getView(R.id.tvItemOperation);
        RecyclerView rvGoods = holder.getView(R.id.rvItemGoods);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getCusProtrait()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivHead);
        tvName.setText(mDataList.get(position).getSaleListName());
        tvTime.setText(mDataList.get(position).getDateTime());
        tvOrderNo.setText(String.valueOf(mDataList.get(position).getSaleListUnique()));

        //状态 1.无效订单 2.新订单（待发货） 3.已发货（待收货）4.已完成（已收货）5.已取消 6.待评论 7.待骑手配送 8.待付款 9.待自提 10.配送异常
        int state = mDataList.get(position).getHandleStateCode();
        //配送方式 1.送货上门 2.自提
        int shipping_method = mDataList.get(position).getShipping_method();
        //送货方式，0.自配送 1.美团配送 2一刻钟配送
        int delivery_type = mDataList.get(position).getDelivery_type();
        switch (state) {
            case 2:
                tvState.setText(getLanguageValue("toBeDelivered"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("acceptOrder"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 3:
                tvState.setText(getLanguageValue("toBeReceived"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("receipt"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 4:
                tvState.setText(getLanguageValue("completed"));
                tvOperation.setVisibility(View.GONE);
                break;
            case 5:
                tvState.setText(getLanguageValue("cancelled"));
                tvOperation.setVisibility(View.GONE);
                break;
            case 6:
                tvState.setText(getLanguageValue("completed") + "-" + getLanguageValue("beEvaluated"));
                tvOperation.setVisibility(View.GONE);
                break;
            case 7:
                //配送单待确认
                tvState.setText(getLanguageValue("beDelivered"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("cancel") + getLanguageValue("delivery"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 8:
                tvState.setText(getLanguageValue("toBePaid"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("cancel") + getLanguageValue("order"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_kuang_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.blue));
                break;
            case 9:
                tvState.setText(getLanguageValue("toBePickedUp"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("pickup"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 10:
                tvState.setText(getLanguageValue("abnormal") + getLanguageValue("order"));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("reacceptOrder"));
                tvOperation.setBackgroundResource(R.drawable.shape_blue_22);
                tvOperation.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            default:
                tvState.setText(mDataList.get(position).getHandleState());
                tvOperation.setVisibility(View.GONE);
                break;
        }
        if (state == 5) {
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvOrderNo.setTextColor(mContext.getResources().getColor(R.color.red));
            tvState.setTextColor(mContext.getResources().getColor(R.color.red));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvDelivery.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvPayType.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvDelivery_personal.setTextColor(mContext.getResources().getColor(R.color.color_999));
            tvDelivery_mobile.setTextColor(mContext.getResources().getColor(R.color.color_999));
        } else {
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvOrderNo.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvState.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvDelivery.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvPayType.setTextColor(mContext.getResources().getColor(R.color.black));
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvDelivery_personal.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvDelivery_mobile.setTextColor(mContext.getResources().getColor(R.color.blue));
        }
        //退款单
        if (!TextUtils.isEmpty(mDataList.get(position).getRetListUnique())) {
            tvState.setText(getLanguageValue("refund") + getLanguageValue("order"));
            tvState.setTextColor(mContext.getResources().getColor(R.color.red));
            vLine.setVisibility(View.VISIBLE);
        } else {
            vLine.setVisibility(View.GONE);
        }
        //配送方式
        String delivery;
        switch (shipping_method) {
            case 1:
                switch (delivery_type) {
                    case 0:
                        delivery = getLanguageValue("deliverByYourself");
                        break;
                    case 1:
                        delivery = getLanguageValue("meituan") + getLanguageValue("delivery");
                        break;
                    case 2:
                        delivery = getLanguageValue("aQuarterOfAnHour") + getLanguageValue("delivery");
                        break;
                    default:
                        delivery = "";
                        break;
                }
                break;
            case 2:
                delivery = getLanguageValue("pickUpStore");
                break;
            default:
                delivery = "";
                break;
        }
        if (!TextUtils.isEmpty(mDataList.get(position).getSaleListName())) {
            if (mDataList.get(position).getSaleListName().equals(getLanguageValue("localCashier"))) {
                delivery = getLanguageValue("localCashier");
            }
        }
        tvDelivery.setText(delivery);

        //商品数量、支付方式、price0
        tvCount.setText(getLanguageValue("aTotalOf") + DFUtils.getNum4(mDataList.get(position).getTotalCount()) + getLanguageValue("pieces"));
        tvPayType.setText(mDataList.get(position).getSaleListPayment());
        if (mDataList.get(position).getSaleListDelfee() == 0) {
            tvPrice.setText("RM" + mDataList.get(position).getActuallyReceived());
        } else {
            tvPrice.setText("RM" + mDataList.get(position).getActuallyReceived() + "(" + getLanguageValue("includeDeliverFee") + ":" + mDataList.get(position).getSaleListDelfee() + ")");
        }

        if (state == 7) {
            tvDelivery_personal.setVisibility(View.VISIBLE);
            tvDelivery_mobile.setVisibility(View.GONE);
            tvDelivery_personal.setText(getLanguageValue("distributor") + ":" + getLanguageValue("snatchOrder") + "...");
        } else if (state == 3) {
            if (TextUtils.isEmpty(mDataList.get(position).getDriverName())) {
                tvDelivery_personal.setVisibility(View.GONE);
                tvDelivery_mobile.setVisibility(View.GONE);
            } else {
                tvDelivery_personal.setVisibility(View.VISIBLE);
                tvDelivery_mobile.setVisibility(View.VISIBLE);
                tvDelivery_personal.setText(getLanguageValue("distributor") + ":" + mDataList.get(position).getDriverName());
                tvDelivery_mobile.setText(mDataList.get(position).getDriverPhone());
            }
        } else {
            tvDelivery_personal.setVisibility(View.GONE);
            tvDelivery_mobile.setVisibility(View.GONE);
        }

        //商品列表
        if (mDataList.get(position).getListDetail().size() > 0) {
            rvGoods.setVisibility(View.VISIBLE);
            rvGoods.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
            OrderGoodsAdapter adapter = new OrderGoodsAdapter(mContext);
            rvGoods.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getListDetail());
            adapter.setOnItemClickListener((view, position1) -> {
                if (listener != null) {
                    listener.onInfoClick(view, position);
                }
            });
        } else {
            rvGoods.setVisibility(View.GONE);
        }
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onInfoClick(v, position));
            tvOperation.setOnClickListener(v -> listener.onOperationClick(v, position));
            linRefund.setOnClickListener(v -> listener.onRefundInfoClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onInfoClick(View view, int position);

        void onOperationClick(View view, int position);

        void onRefundInfoClick(View view, int position);
    }
}
