package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class SelectBankBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"bankImg":"1","bank":1,"bankName":"工商银行"},{"bankImg":"1","bank":2,"bankName":"中国银行"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * bankImg : 1
         * bank : 1
         * bankName : 工商银行
         */

        private String bankImg;
        private int bank;
        private String bankName;

        public String getBankImg() {
            return bankImg;
        }

        public void setBankImg(String bankImg) {
            this.bankImg = bankImg;
        }

        public int getBank() {
            return bank;
        }

        public void setBank(int bank) {
            this.bank = bank;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }
    }
}
