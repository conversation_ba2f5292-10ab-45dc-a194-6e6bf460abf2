package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:农批-商品列表（适配器）
 * Created by jingang on 2023/5/25
 */
public class FarmGoodsData implements Serializable {
    /**
     * goodsList : [{"goodsBarcode":"123123123","goodsName":"土豆","goodsChengType":"散装","goodsSaleType":"纯自营","goodsCount":1231232.23},{"goodsBarcode":"123123123","goodsName":"土豆","goodsChengType":"散装","goodsSaleType":"纯自营","goodsCount":1231232.23}]
     * goodsTotal : 12323
     */

    private int goodsTotal;
    private List<GoodsListBean> goodsList;

    public int getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(int goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public static class GoodsListBean implements Serializable {
        /**
         * goodsBarcode : 123123123
         * goodsName : 土豆
         * goodsChengType : 散装
         * goodsSaleType : 纯自营
         * goodsCount : 1231232.23
         */

        private String goodsBarcode;
        private String goodsName;
        private String goodsChengType;
        private String goodsSaleType;
        private double goodsCount;
        private String goodsUnit;
        private String kindUnique;//分类unique
        private String kindName;//分类名称
        private double goodsPrice;//推荐价格
        private boolean check;

        public boolean isCheck() {
            return check;
        }

        public void setCheck(boolean check) {
            this.check = check;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsChengType() {
            return goodsChengType;
        }

        public void setGoodsChengType(String goodsChengType) {
            this.goodsChengType = goodsChengType;
        }

        public String getGoodsSaleType() {
            return goodsSaleType;
        }

        public void setGoodsSaleType(String goodsSaleType) {
            this.goodsSaleType = goodsSaleType;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public String getKindUnique() {
            return kindUnique;
        }

        public void setKindUnique(String kindUnique) {
            this.kindUnique = kindUnique;
        }

        public String getKindName() {
            return kindName;
        }

        public void setKindName(String kindName) {
            this.kindName = kindName;
        }

        public double getGoodsPrice() {
            return goodsPrice;
        }

        public void setGoodsPrice(double goodsPrice) {
            this.goodsPrice = goodsPrice;
        }
    }
}
