package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.SupplierWebDialog;
import cn.bl.mobile.buyhoostore_sea.utils.DecimalDigitsInputFilter;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCropEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CameraDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CateDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 商品速录
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsQuickAddActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.tvSupplierValue)
    TextView tvSupplierValue;
    @BindView(R.id.tvSupplier)
    TextView tvSupplier;//供货商
    @BindView(R.id.tvCateValue)
    TextView tvCateValue;
    @BindView(R.id.tvCate)
    TextView tvCate;//商品分类
    @BindView(R.id.tvChengTypeValue)
    TextView tvChengTypeValue;
    @BindView(R.id.tvChengType0)
    TextView tvChengType0;//计价类型：计件
    @BindView(R.id.tvChengType1)
    TextView tvChengType1;//计价类型：称重
    @BindView(R.id.tvCodeValue)
    TextView tvCodeValue;
    @BindView(R.id.etCode)
    EditText etCode;//商品条码
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.etName)
    EditText etName;//商品名称
    @BindView(R.id.tvUnitValue)
    TextView tvUnitValue;
    @BindView(R.id.tvUnit)
    TextView tvUnit;
    @BindView(R.id.tvStockValue)
    TextView tvStockValue;
    @BindView(R.id.etStock)
    EditText etStock;//商品库存
    @BindView(R.id.tvInPriceValue)
    TextView tvInPriceValue;
    @BindView(R.id.etInPrice)
    EditText etInPrice;//采购价
    @BindView(R.id.tvSalePriceValue)
    TextView tvSalePriceValue;
    @BindView(R.id.etSalePrice)
    EditText etSalePrice;
    @BindView(R.id.tvAppletValue)
    TextView tvAppletValue;
    @BindView(R.id.ivAppletUp)
    ImageView ivAppletUp;//小程序上架
    @BindView(R.id.tvAppletUpValue)
    TextView tvAppletUpValue;
    @BindView(R.id.ivAppletDown)
    ImageView ivAppletDown;//小程序下架
    @BindView(R.id.tvAppletDownValue)
    TextView tvAppletDownValue;
    @BindView(R.id.tvPrinterValue)
    TextView tvPrinterValue;
    @BindView(R.id.ivPrinterUp)
    ImageView ivPrinterUp;//收银机上架
    @BindView(R.id.tvPrinterUpValue)
    TextView tvPrinterUpValue;
    @BindView(R.id.ivPrinterDown)
    ImageView ivPrinterDown;//收银机下架
    @BindView(R.id.tvPrinterDownValue)
    TextView tvPrinterDownValue;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.tvContinue)
    TextView tvContinue;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private SharedPreferences sharedPreferences;
    //用户权限：商品库存  供货商   分类  名称 进价  售价  删除
    private String powerCount, powerSupplier, powerKind, powerName, powerInprice, powerPrice, powerDelete;

    private String img,
            supplierUnique,//供货商编号
            cateUnique,
            cateUnique1,//二级分类编号
            cateUnique2,
            barcode,//商品条码
            name,//商品名称
            unit = "个";//商品单位
    private int chengType,//计价类型 0.计件 1.称重
            printerStatus = 1,//收银机上架状态：1、已上架；2、已下架
            appletStatus = 1;//小程序上架状态：1、已上架；2、已下架
    private double goodsStock,//库存
            inPrice,//入库单价
            salePrice;//零售单价

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_quick_add;
    }

    @Override
    public void initViews() {
        sharedPreferences = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        powerCount = sharedPreferences.getString("power_count", "0");
        powerSupplier = sharedPreferences.getString("power_supplier", "0");
        powerKind = sharedPreferences.getString("power_kind", "0");
        powerName = sharedPreferences.getString("power_name", "0");
        powerInprice = sharedPreferences.getString("power_inprice", "0");
        powerPrice = sharedPreferences.getString("power_price", "0");
        powerDelete = sharedPreferences.getString("power_delete", "0");

        etStock.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etInPrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        etSalePrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});

        etInPrice.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @SuppressLint("SetTextI18n")
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    return;
                }
                if (TextUtils.isEmpty(etSalePrice.getText().toString().trim())) {
                    etSalePrice.setText(s.toString().trim());
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        ivPrinterUp.setSelected(true);
        ivAppletUp.setSelected(true);
    }

    @Override
    public void initData() {
        getGoodsInfoScan(getIntent().getStringExtra("goodsBarcode"));
    }

    @OnClick({R.id.ivBack, R.id.ivImg, R.id.tvSupplier, R.id.tvCate, R.id.tvChengType0, R.id.tvChengType1,
            R.id.ivCodeScan, R.id.tvUnit,
            R.id.ivAppletUp, R.id.ivAppletDown, R.id.ivPrinterUp, R.id.ivPrinterDown,
            R.id.tvContinue, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivImg:
                //选择图片
                if (PermissionUtils.checkPermissionsGroup(this, 0)) {
                    showDialogCamera();
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0);
                }
                break;
            case R.id.tvSupplier:
                //选择供货商
                if ("1".equals(powerSupplier)) {
//                    SupplierDialog.showDialog(this, "", data -> {
//                        supplierUnique = data.getSupplierUnique();
//                        tvSupplier.setText(data.getSupplierName());
//                    });
                    SupplierWebDialog.showDialog(this, "", data -> {
                        supplierUnique = data.getSupplier_unique();
                        tvSupplier.setText(data.getSupplier_name());
                    });
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
                break;
            case R.id.tvCate:
                //商品分类
                CateDialog.showDialog(this, cateUnique, cateUnique1, cateUnique2, (cateId, cateName, cateId1, cateName1, cateId2, cateName2) -> {
                    cateUnique = cateId;
                    cateUnique1 = cateId2;
                    cateUnique2 = cateId2;
                    String cates = "";
                    if (!TextUtils.isEmpty(cateName)) {
                        cates = cateName;
                    }
                    if (!TextUtils.isEmpty(cateName1)) {
                        cates = cates + "-" + cateName1;
                    }
                    if (!TextUtils.isEmpty(cateName2)) {
                        cates = cates + "-" + cateName2;
                    }
                    tvCate.setText(cates);
                });
                break;
            case R.id.tvChengType0:
                //计价类型：计件
                if (chengType != 0) {
                    chengType = 0;
                    setChengType();
                }
                break;
            case R.id.tvChengType1:
                //计价类型：称重
                if (chengType != 1) {
                    chengType = 1;
                    setChengType();
                }
                break;
            case R.id.ivCodeScan:
                //扫码获取商品条码
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.tvUnit:
                //选择商品单位
                startActivityForResult(new Intent(this, ChoosePackActivity.class), Constants.CHOOSE_UNIT);
                break;
            case R.id.ivAppletUp:
                //小程序上架
                if (appletStatus != 1) {
                    appletStatus = 1;
                    ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivAppletDown:
                //小程序下架
                if (appletStatus != 2) {
                    appletStatus = 2;
                    ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                }
                break;
            case R.id.ivPrinterUp:
                //收银机上架
                if (printerStatus != 1) {
                    printerStatus = 1;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivPrinterDown:
                //收银机下架
                if (printerStatus != 2) {
                    printerStatus = 2;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                }
                break;
            case R.id.tvContinue:
                //保存并继续
                postGoodsEdit(0);
                break;
            case R.id.tvConfirm:
                //保存
                postGoodsEdit(1);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("quick") + getLanguageValue("addTo"));
        tvSupplierValue.setText(getLanguageValue("supplier"));
        tvSupplier.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("supplier"));
        tvCateValue.setText(getLanguageValue("commodity") + getLanguageValue("classification"));
        tvCate.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("classification"));
        tvChengTypeValue.setText(getLanguageValue("pricing") + getLanguageValue("type"));
        tvChengType0.setText(getLanguageValue("piecework"));
        tvChengType1.setText(getLanguageValue("weigh"));
        tvCodeValue.setText(getLanguageValue("commodity") + getLanguageValue("barcode"));
        etCode.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("barcode"));
        tvNameValue.setText(getLanguageValue("commodity") + getLanguageValue("name"));
        etName.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("name"));
        tvUnitValue.setText(getLanguageValue("commodity") + getLanguageValue("unit"));
        tvUnit.setHint(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("unit"));
        tvStockValue.setText(getLanguageValue("commodity") + getLanguageValue("inventory"));
        etStock.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("inventory"));
        tvInPriceValue.setText(getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        etInPrice.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
        tvSalePriceValue.setText(getLanguageValue("retailPrice"));
        etSalePrice.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("retailPrice"));
        tvPrinterValue.setText(getLanguageValue("cashRegister"));
        tvPrinterUpValue.setText(getLanguageValue("onTheShelf"));
        tvPrinterDownValue.setText(getLanguageValue("offTheShelf"));
        tvAppletValue.setText(getLanguageValue("miniProgram"));
        tvAppletUpValue.setText(getLanguageValue("onTheShelf"));
        tvAppletDownValue.setText(getLanguageValue("offTheShelf"));
        tvTips.setText(getLanguageValue("singleProduct"));
        tvContinue.setText(getLanguageValue("saveContinue"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 计价类型
     */
    private void setChengType() {
        //计价类型
        if (chengType == 0) {
            tvChengType0.setBackgroundResource(R.drawable.shape_e5efff_22);
            tvChengType0.setTextColor(getResources().getColor(R.color.blue));
            tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tvChengType1.setBackgroundResource(R.drawable.shape_f7_22);
            tvChengType1.setTextColor(getResources().getColor(R.color.color_666));
            tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        } else {
            chengType = 1;
            tvChengType1.setBackgroundResource(R.drawable.shape_e5efff_22);
            tvChengType1.setTextColor(getResources().getColor(R.color.blue));
            tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tvChengType0.setBackgroundResource(R.drawable.shape_f7_22);
            tvChengType0.setTextColor(getResources().getColor(R.color.color_666));
            tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsInfoData data) {
        if (data == null) {
            return;
        }
        if (data.getListDetail() == null) {
            return;
        }
        if (data.getListDetail().size() < 1) {
            return;
        }
        GoodsInfoData.ListDetailBean infoData = data.getListDetail().get(0);
        img = data.getGoodsPicturepath();
        Glide.with(this)
                .load(StringUtils.handledImgUrl(img))
                .apply(new RequestOptions().error(R.mipmap.ic_camera003))
                .into(ivImg);
        supplierUnique = data.getSupplierUnique();
        tvSupplier.setText(data.getSupplierName());
//        cateUnique1 = data.getKindUnique();
//        if (TextUtils.isEmpty(data.getGroupsName())) {
//            if (TextUtils.isEmpty(data.getKindName())) {
//                tvCate.setText("");
//            } else {
//                tvCate.setText(data.getKindName());
//            }
//        } else {
//            if (TextUtils.isEmpty(data.getKindName())) {
//                tvCate.setText(data.getGroupsName());
//            } else {
//                tvCate.setText(data.getGroupsName() + "-" + data.getKindName());
//            }
//        }
        cateUnique = data.getGroupsUnique();
        cateUnique1 = data.getKindUnique();
        cateUnique2 = data.getThreeUnique();
        String cates = "";
        if (!TextUtils.isEmpty(data.getGroupsName())) {
            cates = data.getGroupsName();
        }
        if (!TextUtils.isEmpty(data.getKindName())) {
            cates = cates + "-" + data.getKindName();
        }
        if (!TextUtils.isEmpty(data.getThreeName())) {
            cates = cates + "-" + data.getThreeName();
        }
        tvCate.setText(cates);
        chengType = data.getGoodsChengType();
        setChengType();
        name = infoData.getGoodsName();
        etName.setText(name);
        goodsStock = data.getGoodsCount();
        etStock.setText(DFUtils.getNum4(goodsStock));
        inPrice = infoData.getGoodsInPrice();
        etInPrice.setText(DFUtils.getNum4(inPrice));
        salePrice = infoData.getGoodsSalePrice();
        etSalePrice.setText(DFUtils.getNum4(salePrice));
        printerStatus = infoData.getPcShelfState();
        if (printerStatus == 1) {
            ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
            ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
        } else {
            ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
            ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
        }
        appletStatus = infoData.getShelfState();
        if (appletStatus == 1) {
            ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
            ivAppletDown.setImageResource(R.mipmap.ic_chose001);
        } else {
            ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
            ivAppletUp.setImageResource(R.mipmap.ic_chose001);
        }
    }

    /**
     * 更新UI（清除所有选择）
     */
    private void setUIClear() {
        /*商品信息*/
        supplierUnique = "";
        tvSupplier.setText("");
        cateUnique = "";
        cateUnique1 = "";
        cateUnique2 = "";
        tvCate.setText("");
        chengType = 0;
        tvChengType0.setBackgroundResource(R.drawable.shape_e5efff_22);
        tvChengType0.setTextColor(getResources().getColor(R.color.blue));
        tvChengType0.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        tvChengType1.setBackgroundResource(R.drawable.shape_f7_22);
        tvChengType1.setTextColor(getResources().getColor(R.color.color_666));
        tvChengType1.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        etStock.setText("");

        img = "";
        ivImg.setImageResource(R.mipmap.ic_camera003);
        etCode.setText("");
        etName.setText("");
        etInPrice.setText("");
        etSalePrice.setText("");
    }

    /**
     * 选择图片方式弹窗
     */
    private void showDialogCamera() {
        CameraDialog.showDialog(this, (view, type) -> {
            //type: 0.拍照 1.从相册选择
            if (type == 1) {
                pickPhoto();
            } else {
                takePhoto();
            }
        });
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .setCropEngine(new ImageFileCropEngine(this))
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCropEngine(new ImageFileCropEngine(this))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            Log.e(tag, "img = " + data.getData().getUrl());
                            img = data.getData().getUrl();
                            Glide.with(TAG)
                                    .load(data.getData().getUrl())
                                    .apply(new RequestOptions().error(R.mipmap.ic_camera002))
                                    .into(ivImg);
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 扫描获取商品详情
     */
    private void getGoodsInfoScan(String code) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", code);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSelecScan(),
                map,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        switch (data.getTableType()) {
                            case 1:
                                //云库商品-直接赋值
                                etCode.setText(code);
                                setUI(data);
                                break;
                            case 2:
                                //本店商品-获取商品详情
                                if (data.getListDetail().size() > 0) {
                                    IAlertDialog.showDialog(TAG,
                                            getLanguageValue("barcodeBeUsed") + "【" + data.getListDetail().get(0).getGoodsName() + "】" + getLanguageValue("occupation"),
                                            getLanguageValue("editor"),
                                            (dialog, which) -> {
                                                startActivity(new Intent(TAG, GoodsEditActivity.class)
                                                        .putExtra("goodsBarcode", code)
                                                        .putExtra("type", 1)
                                                );
                                                finish();
                                            });
                                }
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 商品新增
     *
     * @param addType 0.保存并继续 1.保存并返回
     */
    private void postGoodsEdit(int addType) {
        List array = new ArrayList();
        if (TextUtils.isEmpty(cateUnique1) && TextUtils.isEmpty(cateUnique2)) {
            showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("classification"));
            return;
        }
        if (!TextUtils.isEmpty(etStock.getText().toString().trim())) {
            goodsStock = Double.parseDouble(etStock.getText().toString().trim());
        }
        barcode = etCode.getText().toString().trim();
        if (TextUtils.isEmpty(barcode)) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("barcode"));
            return;
        }
        name = etName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("name"));
            return;
        }
        if (TextUtils.isEmpty(unit)) {
            showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("commodity") + getLanguageValue("unit"));
            return;
        }
        if (TextUtils.isEmpty(etInPrice.getText().toString().trim())) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
            return;
        }
        inPrice = Double.parseDouble(etInPrice.getText().toString().trim());
        if (inPrice == 0) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("warehousing") + getLanguageValue("unitPrice"));
            return;
        }
        if (TextUtils.isEmpty(etSalePrice.getText().toString().trim())) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("retailPrice"));
            return;
        }
        salePrice = Double.parseDouble(etSalePrice.getText().toString().trim());
        if (salePrice == 0) {
            showMessage(getLanguageValue("pleaseEnter") + getLanguageValue("retailPrice"));
            return;
        }
        Map object0 = new HashMap();
        object0.put("goodsPicturePath", img);
        object0.put("goodsBarcode", barcode);
        object0.put("goodsName", name);
        object0.put("goodsInPrice", inPrice);
        object0.put("goodsSalePrice", salePrice);
        object0.put("goodsUnit", unit);
        object0.put("goodsContain", 1);
        object0.put("shelfState", appletStatus);//线上上架状态：1、已上架；2、已下架
        object0.put("pcShelfState", printerStatus);//pc收银上架状态：1、已上架；2、已下架
        array.add(object0);

        Log.e(tag, "array = " + array);
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("goodsCount", goodsStock);//库存
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);//供货商编号
        if (TextUtils.isEmpty(cateUnique2)) {
            map.put("goodsKindUnique", cateUnique1);//二级分类编号
        } else {
            map.put("goodsKindUnique", cateUnique2);//二级分类编号
        }
        map.put("goodsChengType", chengType);//计价类型 0.计件 1.称重
        map.put("foreignKey", barcode);//包装外键（最小规格的商品条码）
        map.put("goodsMessage", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGoodsAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        if (addType == 1) {
                            finish();
                        } else {
                            setUIClear();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    //扫码
                    getGoodsInfoScan(data.getStringExtra("result"));
                    break;
                case Constants.CHOOSE_UNIT:
                    //选择单位
                    unit = data.getStringExtra("name");
                    tvUnit.setText(unit);
                    break;
            }
        }
    }

}
