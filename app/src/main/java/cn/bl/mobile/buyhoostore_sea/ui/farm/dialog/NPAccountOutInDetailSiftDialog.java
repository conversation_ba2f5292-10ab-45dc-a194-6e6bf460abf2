package cn.bl.mobile.buyhoostore_sea.ui.farm.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.lxj.xpopup.impl.FullScreenPopupView;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.DateStartEndDialog;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.NPAddCustomerActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.NPSiftBean;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
@SuppressLint("NonConstantResourceId")
public class NPAccountOutInDetailSiftDialog extends FullScreenPopupView {

    @BindView(R.id.tv_cancel)
    TextView cancel;
    @BindView(R.id.tv_start_time)
    TextView startTimeTV;
    @BindView(R.id.tv_end_time)
    TextView endTimeTV;

    @BindView(R.id.tv_sift_left)
    TextView leftTV;
    @BindView(R.id.tv_sift_right)
    TextView rightTV;

    String shopId;
    String staffId;

    public void setSiftBean(NPSiftBean siftBean) {
        this.siftBean = siftBean;
    }

    private NPSiftBean siftBean;

    private List<MemberBean.DataBean> dataList = new ArrayList<>();

    private OnDialogClickListener onDialogClickListener;
    /**
     * 会员编号
     */
    private String cusUnique;


    /**
     * 选择的id
     * @param cusUnique
     */
    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    /**
     * 是否筛选用
     */
    private boolean isSift = false;

    private String startTime, endTime;

    public NPAccountOutInDetailSiftDialog(@NonNull Context context) {
        super(context);
    }
    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_np_account_out_in_detail;
    }
    @Override
    protected void onCreate() {
        super.onCreate();
        //初始化
        ButterKnife.bind(this);
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
        if (null != siftBean) {
            endTimeTV.setText(siftBean.getEndDate());
            startTimeTV.setText(siftBean.getStartDate());
        }

    }
    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.REFRESH_MEMBER_LIST:
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

    }
    private void close(){
        dismiss();

    }


    public void setonDialogClickListener(OnDialogClickListener dialogClickListener) {
        this.onDialogClickListener = dialogClickListener;
    }
    @OnClick({R.id.tv_cancel,R.id.tv_start_time,R.id.tv_end_time,R.id.rv_container,R.id.tv_sift_left,R.id.tv_sift_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_cancel:
            case R.id.rv_container:
                close();
                break;
            case R.id.tv_start_time:
            case R.id.tv_end_time:
                DateStartEndDialog.showDialog(getContext(),
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            startTimeTV.setText(startDate);
                            startTime = startDate;
                            endTime = endDate;
                            endTimeTV.setText(endDate);
//                            for (int i = 0; i < timeList.size(); i++) {
//                                timeList.get(i).setCheck(false);
//                            }
                        });
                break;
            case R.id.tvAdd:
                getContext().startActivity(new Intent(getContext(), NPAddCustomerActivity.class));
                break;
            case R.id.ll_container:
                break;
            case R.id.tv_sift_left:{
                if (onDialogClickListener != null) {
                    NPSiftBean siftBean = new NPSiftBean();
                    onDialogClickListener.onConfirm(siftBean);
                    dismiss();
                }
                break;}
            case R.id.tv_sift_right:
                if (onDialogClickListener != null) {
                    NPSiftBean siftBean = new NPSiftBean();
                    siftBean.setStartDate(startTime);
                    siftBean.setEndDate(endTime);
                    onDialogClickListener.onConfirm(siftBean);
                    dismiss();
                }
                break;
        }
    }


    public interface OnDialogClickListener {

        void onConfirm(NPSiftBean siftBean);

    }



}
