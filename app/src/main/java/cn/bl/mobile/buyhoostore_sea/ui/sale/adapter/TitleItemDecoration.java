package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;
import java.util.Objects;

import cn.bl.mobile.buyhoostore_sea.bean.OrderListData;

/**
 * Describe:有分类title的 ItemDecoration
 * Created by jingang on 2023/5/20
 */
public class TitleItemDecoration extends RecyclerView.ItemDecoration {
    private List<OrderListData> mDatas;
    private Paint mPaint;
    private Rect mBounds;//用于存放测量文字Rect

    private int mTitleHeight;//title的高
    private static int COLOR_TITLE_BG = Color.parseColor("#FFf2f2f2");
    private static int COLOR_TITLE_FONT = Color.parseColor("#FF000000");
    private static int mTitleFontSize;//title字体大小

    public TitleItemDecoration(Context context, List<OrderListData> datas) {
        super();
        mDatas = datas;
        mPaint = new Paint();
        mBounds = new Rect();
        mTitleHeight = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 40, context.getResources().getDisplayMetrics());
        mTitleFontSize = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 16, context.getResources().getDisplayMetrics());
        mPaint.setTextSize(mTitleFontSize);
        mPaint.setStrokeWidth(0.8f);
        mPaint.setAntiAlias(true);
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);
        if (mDatas.size() < 1) {
            return;
        }
        final int left = parent.getPaddingLeft();
        final int right = parent.getWidth() - parent.getPaddingRight();
        final int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            final View child = parent.getChildAt(i);
            final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
            int position = params.getViewLayoutPosition();
            //我记得Rv的item position在重置时可能为-1.保险点判断一下吧
            if (position > -1) {
                if (position == 0) {//等于0肯定要有title的
                    drawTitleArea(c, left, right, child, params, position);
                } else {//其他的通过判断
                    String dateTime = mDatas.get(position).getDateTime();
                    String dateTime0 = mDatas.get(position - 1).getDateTime();
                    if (!TextUtils.isEmpty(dateTime) && !TextUtils.isEmpty(dateTime0)) {
                        String time = dateTime.substring(0, dateTime.indexOf(" "));
                        String time0 = dateTime0.substring(0, dateTime0.indexOf(" "));
                        if (!time.equals(time0)) {
                            //不为空 且跟前一个tag不一样了，说明是新的分类，也要title
                            drawTitleArea(c, left, right, child, params, position);
                        }
                    }
                }
            }
        }
    }

    /**
     * 绘制Title区域背景和文字的方法
     *
     * @param c
     * @param left
     * @param right
     * @param child
     * @param params
     * @param position
     */
    private void drawTitleArea(Canvas c, int left, int right, View child, RecyclerView.LayoutParams params, int position) {//最先调用，绘制在最下层
        mPaint.setColor(COLOR_TITLE_BG);
        c.drawRect(left, child.getTop() - params.topMargin - mTitleHeight, right, child.getTop() - params.topMargin, mPaint);
        mPaint.setColor(COLOR_TITLE_FONT);

        String dateTime = mDatas.get(position).getDateTime();
        String time = "    " + dateTime.substring(0, dateTime.indexOf(" "));
        mPaint.getTextBounds(time, 0, time.length(), mBounds);
        c.drawText(time,
                child.getPaddingLeft(),
                child.getTop() - params.topMargin - (mTitleHeight / 2 - mBounds.height() / 2),
                mPaint);
    }

    @Override
    public void onDrawOver(Canvas c, RecyclerView parent, RecyclerView.State state) {
        //最后调用 绘制在最上层
        if (mDatas.size() < 1) {
            return;
        }
        int pos = ((LinearLayoutManager) (parent.getLayoutManager())).findFirstVisibleItemPosition();
        String dateTime = mDatas.get(pos).getDateTime();
        String tag = "    " + dateTime.substring(0, dateTime.indexOf(" "));
        View child = Objects.requireNonNull(parent.findViewHolderForLayoutPosition(pos)).itemView;//出现一个奇怪的bug，有时候child为空，所以将 child = parent.getChildAt(i)。-》 parent.findViewHolderForLayoutPosition(pos).itemView
        mPaint.setColor(COLOR_TITLE_BG);
        c.drawRect(parent.getPaddingLeft(), parent.getPaddingTop(), parent.getRight() - parent.getPaddingRight(), parent.getPaddingTop() + mTitleHeight, mPaint);
        mPaint.setColor(COLOR_TITLE_FONT);
        mPaint.getTextBounds(tag, 0, tag.length(), mBounds);
        c.drawText(tag, child.getPaddingLeft(),
                parent.getPaddingTop() + mTitleHeight - (mTitleHeight / 2 - mBounds.height() / 2),
                mPaint);
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        if (mDatas.size() < 1) {
            return;
        }
        int position = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
        //我记得Rv的item position在重置时可能为-1.保险点判断一下吧
        if (position > -1) {
            if (position == 0) {//等于0肯定要有title的
                outRect.set(0, mTitleHeight, 0, 0);
            } else {//其他的通过判断
                String dateTime = mDatas.get(position).getDateTime();
                String dateTime0 = mDatas.get(position - 1).getDateTime();
                if (!TextUtils.isEmpty(dateTime) && !TextUtils.isEmpty(dateTime0)) {
                    String time = dateTime.substring(0, dateTime.indexOf(" "));
                    String time0 = dateTime0.substring(0, dateTime0.indexOf(" "));
                    if (!time.equals(time0)) {
                        outRect.set(0, mTitleHeight, 0, 0);//不为空 且跟前一个tag不一样了，说明是新的分类，也要title
                    } else {
                        outRect.set(0, 0, 0, 0);
                    }
                } else {
                    outRect.set(0, 0, 0, 0);
                }
            }
        } else {
            outRect.set(0, 0, 0, 0);
        }
    }

}