package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseApplication;

import org.json.JSONException;

import java.util.Timer;

import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Descride:dialog
 * Created by jingang on 2015/9/2.
 */
public class IAlertDialog extends Dialog {
    private TextView tvMsg;
    private TextView positiveButton;
    private TextView negativeButton;
    private ImageView closeButton;
    private String negativeMsg;
    private String positiveMsg;
    private String title;
    private String message;
    private OnClickListener positiveOnClickListener;
    private OnClickListener negativeOnClickListener;
    private int type = 0;  //显示控制标记
    private int gravity; //显示位置 @Gravity....
    Timer timer;

    public static IAlertDialog instance;

    public static IAlertDialog getInstance() {
        return instance;
    }

    public enum LayoutStyle {

        /**
         * 白色背景。title和message以及确定取消按钮一体。
         */
        DEFAULT

    }

    public static void showDialog(Context mContext, String msg, String positiveMsg, OnClickListener positiveOnClickListener) {
        IAlertDialog dialog = new IAlertDialog(mContext, LayoutStyle.DEFAULT, Gravity.CENTER);
        dialog.setMessage(msg);
        dialog.setPositiveMsg(positiveMsg);
        dialog.setPositiveOnClickListener(positiveOnClickListener);
        dialog.setNegativeOnClickListener((dialog1, which) -> {

        });
        dialog.show();
    }

    public static void showDialog(Context mContext,String msg, String positiveMsg, OnClickListener positiveOnClickListener, OnClickListener negativeOnClickListener) {
        IAlertDialog dialog = new IAlertDialog(mContext, LayoutStyle.DEFAULT, Gravity.CENTER);
        dialog.setMessage(msg);
        dialog.setPositiveMsg(positiveMsg);
        dialog.setPositiveOnClickListener(positiveOnClickListener);
        dialog.setNegativeOnClickListener(negativeOnClickListener);
        dialog.show();
    }

    //提示对话框
    public IAlertDialog(Context context, int gravity) {
        this(context, LayoutStyle.DEFAULT, gravity);
    }

    public IAlertDialog(Context context, LayoutStyle theme, int gravity) {
        super(context, R.style.dialog_style);
        this.gravity = gravity;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        switch (theme) {
            case DEFAULT:
                setContentView(R.layout.dialog_default);
                break;
            default:
                setContentView(R.layout.dialog_default);
                break;
        }
        tvMsg = (TextView) findViewById(R.id.tvMsg);
        positiveButton = (TextView) findViewById(R.id.tv_tips_dialog_confirm);

        negativeButton = (TextView) findViewById(R.id.tv_tips_dialog_cancel);
        positiveButton.setOnClickListener(v -> {
            if (positiveOnClickListener != null) {
                positiveOnClickListener.onClick(IAlertDialog.this, v.getId());
            }
            dismiss();
        });
        negativeButton.setOnClickListener(v -> {
            if (negativeOnClickListener != null) {
                negativeOnClickListener.onClick(IAlertDialog.this, v.getId());
            }
            dismiss();

        });

//        setCanceledOnTouchOutside(false);
//        setCancelable(false);

        tvMsg.setText(message == null ? "" : message);
        negativeButton.setText(negativeMsg == null ? getLanguageValue("cancel") : negativeMsg);
        positiveButton.setText(positiveMsg == null ? getLanguageValue("confirm") : positiveMsg);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        instance = this;
    }

    /**
     * 获取右侧按钮
     *
     * @return
     */
    public TextView getPositiveButton() {
        return positiveButton;
    }

    /**
     * 获取左侧按钮
     *
     * @return
     */
    public TextView getNegativeButton() {
        return negativeButton;
    }

    /**
     * 获取关闭按钮
     *
     * @return
     */
    public ImageView getCloseButton() {
        return closeButton;
    }

    @Override
    public void show() {
        super.show();
        DisplayMetrics outMetrics = new DisplayMetrics();
        getWindow().getWindowManager().getDefaultDisplay().getMetrics(outMetrics);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        if (type == 1) {
            params.width = outMetrics.widthPixels * 9 / 10;
        }
        if (type == 2) {
            params.width = outMetrics.widthPixels;
        } else {
            params.width = outMetrics.widthPixels * 4 / 5;
        }
        getWindow().setGravity(gravity);
        getWindow().setAttributes(params);

    }

    /**
     * 设置negativeButton要显示的信息
     *
     * @param negativeMsg
     */
    public IAlertDialog setNegativeMsg(String negativeMsg) {
        this.negativeMsg = negativeMsg;
        if (negativeButton != null && negativeMsg != null) {
            negativeButton.setText(negativeMsg);
        }
        return this;
    }

    /**
     * 设置positiveButton要显示的信息
     *
     * @param positiveMsg
     */
    public IAlertDialog setPositiveMsg(String positiveMsg) {
        this.positiveMsg = positiveMsg;
        if (positiveButton != null && positiveMsg != null) {
            positiveButton.setText(positiveMsg);
        }
        return this;
    }

    /**
     * 设置提示信息
     *
     * @param message
     */
    public IAlertDialog setMessage(String message) {
        this.message = message;
        if (tvMsg != null && message != null) {
            tvMsg.setText(message);
        }
        return this;
    }

    /**
     * 设置确定键的响应接口
     *
     * @param positiveOnClickListener
     */
    public IAlertDialog setPositiveOnClickListener(OnClickListener positiveOnClickListener) {
        this.positiveOnClickListener = positiveOnClickListener;
        return this;
    }

    /**
     * 设置取消键的回调接口
     *
     * @param negativeOnClickListener
     */
    public IAlertDialog setNegativeOnClickListener(OnClickListener negativeOnClickListener) {
        this.negativeOnClickListener = negativeOnClickListener;
        return this;
    }
    /**
     * 获取多语言值
     *
     * @param key
     * @return
     */
    public String getLanguageValue(String key) {
        String value;
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        if (BaseApplication.getLanguageValueJson() == null) {
            value = "";
        } else {
            try {
                value = BaseApplication.getLanguageValueJson().getString(key);
            } catch (JSONException e) {
                e.printStackTrace();
                value = "";
            }
        }
        return value;
    }


}
