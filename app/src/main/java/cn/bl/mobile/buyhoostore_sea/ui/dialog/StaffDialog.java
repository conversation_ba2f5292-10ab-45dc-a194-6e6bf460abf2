package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.StaffListAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.StaffData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（选择员工）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class StaffDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.etDialogSearch)
    EditText etSearch;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keywords;
    private static String shopUnique;//店铺编号

    private StaffListAdapter mAdapter;
    private List<StaffData> dataList = new ArrayList<>();

    public static void showDialog(Context context, String shopUnique, MyListener listener) {
        StaffDialog.shopUnique = shopUnique;
        StaffDialog.listener = listener;
        StaffDialog dialog = new StaffDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public StaffDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_staff);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("staff") + getLanguageValue("choose"));
        etSearch.setHint(getLanguageValue("staff") + getLanguageValue("name") + "/" + getLanguageValue("number"));
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            getStaffList();
            return true;
        });
        setAdapter();
        getStaffList();
    }

    @OnClick({R.id.ivDialogClose})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new StaffListAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onClick(String.valueOf(dataList.get(position).getStaffId()), dataList.get(position).getStaffName());
                dismiss();
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getStaffList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 店铺员工查询
     */
    private void getStaffList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", shopUnique);
        map.put("staffId", keywords);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getpersonlistTWO(),
                map,
                StaffData.class,
                new RequestListListener<StaffData>() {
                    @Override
                    public void onResult(List<StaffData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.add(new StaffData(getLanguageValue("whole")+getLanguageValue("cashier"), -1));
                        dataList.addAll(list);
                        recyclerView.setVisibility(View.VISIBLE);
                        linEmpty.setVisibility(View.GONE);
                        mAdapter.setDataList(dataList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(String unique, String name);
    }
}
