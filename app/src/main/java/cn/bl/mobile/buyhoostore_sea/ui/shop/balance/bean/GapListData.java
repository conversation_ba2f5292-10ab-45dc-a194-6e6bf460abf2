package cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean;

import java.util.List;

/**
 * Describe:店铺-余额（差价）（实体类）
 * Created by jingang on 2023/5/6
 */
public class GapListData {
    /**
     * status : 1
     * msg : 查询成功!
     * data : {"shop_balance":23.02,"gap_count":123,"gap_list":[{"gap_type":1,"gapType":"下单","bill_type":1,"gap_price":1.25,"order_no":"DD12312312","create_datetime":"2023-02-05 23:23:43"},{"gap_type":1,"gapType":"下单","bill_type":1,"gap_price":1.25,"order_no":"DD12312312","create_datetime":"2023-02-05 23:23:43"}]}
     */

    private int status;
    private String msg;
    private DataBean data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * shop_balance : 23.02
         * gap_count : 123
         * gap_list : [{"gap_type":1,"gapType":"下单","bill_type":1,"gap_price":1.25,"order_no":"DD12312312","create_datetime":"2023-02-05 23:23:43"},{"gap_type":1,"gapType":"下单","bill_type":1,"gap_price":1.25,"order_no":"DD12312312","create_datetime":"2023-02-05 23:23:43"}]
         */

        private double shop_balance;
        private int gap_count;
        private List<GapListBean> gap_list;

        public double getShop_balance() {
            return shop_balance;
        }

        public void setShop_balance(double shop_balance) {
            this.shop_balance = shop_balance;
        }

        public int getGap_count() {
            return gap_count;
        }

        public void setGap_count(int gap_count) {
            this.gap_count = gap_count;
        }

        public List<GapListBean> getGap_list() {
            return gap_list;
        }

        public void setGap_list(List<GapListBean> gap_list) {
            this.gap_list = gap_list;
        }

        public static class GapListBean {
            /**
             * gap_type : 1
             * gapType : 下单
             * bill_type : 1
             * gap_price : 1.25
             * order_no : DD12312312
             * create_datetime : 2023-02-05 23:23:43
             */

            private int gap_type;//差价产生方式：1下单、2确认收货、3订单退款、4订单取消、5筐押金退款
            private String gapType;
            private int bill_type;//差价类型：1、增加余额；2、减少余额
            private double gap_price;
            private String order_no;
            private String create_datetime;
            private int order_status;//订单状态：1、待付款(返回主订单编号，查询详情时，需上传main_order_no)；0、其他
            private String shop_unique;

            public String getShop_unique() {
                return shop_unique;
            }

            public void setShop_unique(String shop_unique) {
                this.shop_unique = shop_unique;
            }

            public int getGap_type() {
                return gap_type;
            }

            public void setGap_type(int gap_type) {
                this.gap_type = gap_type;
            }

            public String getGapType() {
                return gapType;
            }

            public void setGapType(String gapType) {
                this.gapType = gapType;
            }

            public int getBill_type() {
                return bill_type;
            }

            public void setBill_type(int bill_type) {
                this.bill_type = bill_type;
            }

            public double getGap_price() {
                return gap_price;
            }

            public void setGap_price(double gap_price) {
                this.gap_price = gap_price;
            }

            public String getOrder_no() {
                return order_no;
            }

            public void setOrder_no(String order_no) {
                this.order_no = order_no;
            }

            public String getCreate_datetime() {
                return create_datetime;
            }

            public void setCreate_datetime(String create_datetime) {
                this.create_datetime = create_datetime;
            }

            public int getOrder_status() {
                return order_status;
            }

            public void setOrder_status(int order_status) {
                this.order_status = order_status;
            }
        }
    }
}
