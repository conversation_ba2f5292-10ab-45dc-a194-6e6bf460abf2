package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;

/**
 * Describe:店铺骑手（实体类）
 * Created by jingang on 2023/11/1
 */
public class ShopRiderData implements Serializable {
    /**
     * shop_unique : 1536215939565
     * update_time : 1669768938000
     * courier_name : 刘
     * create_times : 2022-11-30 08:42:18
     * create_time : 1669768938000
     * id : 709
     * update_times : 2022-11-30 08:42:18
     * courier_phone : 18520569999
     */

    private String courier_name;
    private int id;
    private String courier_phone;

    public String getCourier_name() {
        return courier_name;
    }

    public void setCourier_name(String courier_name) {
        this.courier_name = courier_name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCourier_phone() {
        return courier_phone;
    }

    public void setCourier_phone(String courier_phone) {
        this.courier_phone = courier_phone;
    }
}
