package cn.bl.mobile.buyhoostore_sea.ui.shop.balance;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.balance.bean.BasketData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-余额（差价）-订单详情
 * Created by jingang on 2023/4/25
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class BalanceDifferenceOrderActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvShopNameValue)
    TextView tvShopNameValue;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.tvBasketValue)
    TextView tvBasketValue;
    @BindView(R.id.rvBasket)
    RecyclerView rvBasket;

    //订单信息
    @BindView(R.id.tvOrderInfoValue)
    TextView tvOrderInfoValue;
    @BindView(R.id.tvStatusValue)
    TextView tvStatusValue;
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvTime_applyValue)
    TextView tvTime_applyValue;
    @BindView(R.id.tvTime_apply)
    TextView tvTime_apply;
    @BindView(R.id.tvUser_applyValue)
    TextView tvUser_applyValue;
    @BindView(R.id.tvUser_apply)
    TextView tvUser_apply;
    @BindView(R.id.tvMobileValue)
    TextView tvMobileValue;
    @BindView(R.id.tvMobile)
    TextView tvMobile;
    @BindView(R.id.tvTime_returnValue)
    TextView tvTime_returnValue;
    @BindView(R.id.tvTime_return)
    TextView tvTime_return;
    @BindView(R.id.tvUser_returnValue)
    TextView tvUser_returnValue;
    @BindView(R.id.tvUser_return)
    TextView tvUser_return;

    private String order_no, mobile;

    private BasketAdapter mAdapter;
    private List<BasketData.BucketlistBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_balance_difference_order;
    }

    @Override
    public void initViews() {
        order_no = getIntent().getStringExtra("no");
        setAdapter();
    }

    @Override
    public void initData() {
        getBasketRecoveryInfo();
    }

    @OnClick({R.id.ivBack, R.id.tvMobile})
    public void onViewClick(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvMobile:
                //联系客服
                if (PermissionUtils.checkPermissionsGroup(this, 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION, 4);
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("basket") + getLanguageValue("refund") + getLanguageValue("details"));
        tvShopNameValue.setText(getLanguageValue("shop"));
        tvTotalValue.setText(getLanguageValue("anticipated") + getLanguageValue("refund") + getLanguageValue("amount"));
        tvBasketValue.setText(getLanguageValue("basket") + getLanguageValue("information"));
        tvOrderInfoValue.setText(getLanguageValue("order") + getLanguageValue("information"));
        tvStatusValue.setText(getLanguageValue("order") + getLanguageValue("status") + ":");
        tvTime_applyValue.setText(getLanguageValue("apply") + getLanguageValue("time") + ":");
        tvUser_applyValue.setText(getLanguageValue("apply") + getLanguageValue("people") + ":");
        tvMobileValue.setText(getLanguageValue("phoneNum") + ":");
        tvTime_returnValue.setText(getLanguageValue("refund") + getLanguageValue("time") + ":");
        tvUser_returnValue.setText(getLanguageValue("refund") + getLanguageValue("people") + ":");
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvBasket.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new BasketAdapter(this);
        rvBasket.setAdapter(mAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(BasketData data) {
        mobile = data.getStaffer_phone();
        tvShopName.setText(data.getShop_name());
        tvTotal.setText("RM" + data.getReturn_money_total());
        dataList.clear();
        dataList.addAll(data.getBucketlist());
        mAdapter.setDataList(dataList);
        //订单信息
        tvStatus.setText(data.getRefund_status());
        tvTime_apply.setText(data.getCreate_time());
        tvUser_apply.setText(data.getApplicant());
        tvMobile.setText(mobile);
        tvTime_return.setText(data.getReturn_time());
        tvUser_return.setText(data.getRetrun_people());
    }

    /**
     * 筐押金退款详情
     */
    private void getBasketRecoveryInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("order_no", order_no);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getBasketRecoveryInfo(),
                map,
                BasketData.class,
                new RequestListener<BasketData>() {
                    @Override
                    public void success(BasketData basketData) {
                        setUI(basketData);
                    }
                });
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(this,
                getLanguageValue("confirmCall")+":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == Constants.PERMISSION) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                callPhone(mobile);
            } else {
                showMessage(getLanguageValue("requiredCallPermission"));
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

}
