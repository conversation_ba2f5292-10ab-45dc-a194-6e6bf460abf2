package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CommonMessageBean;

/**
 * Describe:退款订单-拒绝-拒绝原因（适配器）
 * Created by jingang on 2024/11/14
 */
public class RefundRefuseReasonAdapter extends BaseAdapter<CommonMessageBean> {

    public RefundRefuseReasonAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_refund_refuse_reason;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvPos, tvName;
        ImageView ivEdit, ivDel;
        tvPos = holder.getView(R.id.tvItemPos);
        tvName = holder.getView(R.id.tvItemName);
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);

        tvPos.setText(String.valueOf(position + 1));
        tvName.setText(mDataList.get(position).getMsg());

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
