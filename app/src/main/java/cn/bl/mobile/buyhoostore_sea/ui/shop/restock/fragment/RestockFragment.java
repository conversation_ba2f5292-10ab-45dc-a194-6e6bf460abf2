package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.activity.RestockEditActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.activity.RestockPreviewActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter.RestockAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPlanData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-补货单
 * Created by jingang on 2023/9/6
 */
@SuppressLint("NonConstantResourceId")
public class RestockFragment extends LazyBaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private int status;//1.待生成 2.已生成 3.已取消
    private boolean isVisible,
            isRefresh;

    private RestockAdapter mAdapter;
    private List<RestockPlanData> dataList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static RestockFragment newInstance(int status) {
        RestockFragment fragment = new RestockFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("status", status);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout_top16;
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        status = getArguments().getInt("status", 0);
        getRestockPlanList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isVisible && isRefresh) {
            page = 1;
            getRestockPlanList();
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.RESTOCK_PLAN_LIST:
                //刷新列表
                if (isVisible) {
                    page = 1;
                    getRestockPlanList();
                } else {
                    isRefresh = true;
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new RestockAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new RestockAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(getActivity(), RestockEditActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getShopRestockplanId()))
                        .putExtra("status", dataList.get(position).getStatus())
                );
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(getActivity(),
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("replenishment")+getLanguageValue("plan")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanDel(dataList.get(position).getShopRestockplanId(), position);
                        });
            }

            @Override
            public void onAgainClick(View view, int position) {
                //再次补货
                IAlertDialog.showDialog(getActivity(),
                        getLanguageValue("confirm")+getLanguageValue("replenishAgain")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanAgain(dataList.get(position).getShopRestockplanId());
                        });
            }

            @Override
            public void onCancelClick(View view, int position) {
                //取消补货
                IAlertDialog.showDialog(getActivity(),
                        getLanguageValue("confirm")+getLanguageValue("cancelReplenish")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanStatus(dataList.get(position).getShopRestockplanId(), 3, position);
                        });
            }

            @Override
            public void onPreviewClick(View view, int position) {
                //预览
                if (dataList.get(position).getGoodsList().size() < 1) {
                    showMessage(getLanguageValue("please")+getLanguageValue("addTo")+getLanguageValue("commodity"));
                    return;
                }
                startActivity(new Intent(getActivity(), RestockPreviewActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getShopRestockplanId()))
                );
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRestockPlanList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRestockPlanList();
            }
        });
    }

    /**
     * 补货计价列表
     */
    private void getRestockPlanList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("planStatus", status);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getRestockPlanList(),
                map,
                RestockPlanData.class,
                new RequestListListener<RestockPlanData>() {
                    @Override
                    public void onResult(List<RestockPlanData> list) {
                        hideDialog();
                        isRefresh = false;
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 修改补货计划状态信息
     *
     * @param id
     * @param planStatus 1.未生成 2.已生成 3.已取消
     * @param position
     */
    private void postRestockPlanStatus(int id, int planStatus, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("restockPlanId", id);
        map.put("planStatus", planStatus);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getRestockPlanStatus(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        if (status == 3) {
                            dataList.get(position).setStatus(planStatus);
                            mAdapter.notifyItemChanged(position, dataList.get(position));
                        } else {
                            dataList.remove(position);
                            mAdapter.remove(position);
                            if (dataList.size() < 1) {
                                page = 1;
                                getRestockPlanList();
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除补货计划
     *
     * @param id
     * @param position
     */
    private void postRestockPlanDel(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopRestockplanId", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getRestockPlanDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() < 1) {
                            page = 1;
                            getRestockPlanList();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 再次补货
     *
     * @param id
     */
    private void postRestockPlanAgain(int id) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopRestockplanId", id);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(getActivity(),
                ZURL.getRestockPlanAgain(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE && data.getData() != null) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_PLAN_LIST));
                            startActivity(new Intent(getActivity(), RestockEditActivity.class)
                                    .putExtra("id", DFUtils.getNum((Double) data.getData()))
                                    .putExtra("status", 1)
                            );
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}
