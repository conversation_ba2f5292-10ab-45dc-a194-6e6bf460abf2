package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;

/**
 * Describe:补货单-商品-选择规格（适配器）
 * Created by jingang on 2023/9/8
 */
public class RestockGoodsSpecsAdapter extends BaseAdapter<GoodsInfoData.ListDetailBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RestockGoodsSpecsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_goods_specs;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivCheck, ivImg;
        TextView tvName, tvBarcode;
        ivCheck = holder.getView(R.id.ivItemCheck);
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);

        if (mDataList.get(position).isCheck()) {
            ivCheck.setImageResource(R.mipmap.ic_chosen001);
        } else {
            ivCheck.setImageResource(R.mipmap.ic_chose001);
        }
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        if(onItemClickListener != null){
            ivCheck.setOnClickListener(v -> onItemClickListener.onItemClick(v,position));
        }
    }
}
