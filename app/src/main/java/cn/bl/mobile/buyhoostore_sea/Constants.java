package cn.bl.mobile.buyhoostore_sea;

/**
 * SmartCar 所需要的常量都在这里定义使用
 *
 * <AUTHOR>
 */
public interface Constants {
    int limit = 10;
    int SUCCESS_CODE = 1;//接口请求成功code

    int CASHIER_MAX_MONEY = 50000;//收银最大限制金额
    int SEARCH_DELAY_MILLIS = 2000;//搜索延迟时间（mm）

    String SP_SHOP = "shop";
    String CASHIER_SHIFT_IS_PRINT = "cashier_shift_is_print";//移动收银-是否打印小票
    String CASHIER_QUICK_IS_PRINT = "cashier_quick_is_print";//快速收银-是否打印小票
    String PRINTER_TYPE = "printer_type";//打印机型号
    String PRINT_DIRECTION = "print_direction";//打印方向
    String PRINT_TYPE = "print_type";//打印模版
    String SP_LANGUAGE = "SP_LANGUAGE";//语言
    String SP_COUNTRY = "SP_COUNTRY";//国家
    String IS_SHOW_ORDER_CANCEL = "show_order_cancel";//是否展示已取消订单

    String SP_SHOP_KEY_LAST_SHOW_UPDATE = "SP_SHOP_KEY_LAST_SHOW_UPDATE";
    String CONSTANT_REFRESH_LIST_COUNT = "refreshSaleOrderSum";//刷新order数量
    String CONSTANT_REFRESH_ORSER_REFUND_LIST = "refreshRefundOrderList";//刷新退款单
    String CONSTANT_REFRESH_ORSER_LIST = "refreshOrderList";//刷新销售订单0.

    String REFRESH_MEMBER_LIST = "refresh_customer_list";//刷新客户列表

    String RESTOCK_PLAN_LIST = "restock_plan_list";//补货单列表
    String GOUX_LIST = "goux_list";//购销单列表
    String SUPPLIER_LIST = "supplier_list";//供货商列表

    String GOODS_CATE = "goods_cate";//刷新-商品分类
    String GOODS_LIST = "goods_list";//刷新-商品列表

    String CONSTANT_SHOP_ADDRESS = "constantShopAddress";//店铺地址

    String REFUND_REFUSE_REASON = "refund_refuse_reason";//退款订单-拒绝话术

    String CHOOSE_SHOP = "choose_shop";//切换店铺

    String PHONE = "400-7088-365";//客服电话


    /*微信开放平台*/
    String WX_AppId = "wx38618456447b4af2";
    String WX_AppSecret = "";

    int SCAN = 0x01;//扫码
    int KU_INOUT = 0x02;//出入库
    int PAN = 0x03;//盘点
    int PERMISSION = 0x04;//权限
    int CREDIT = 0x05;//赊销
    int BANK = 0x06;//银行卡
    int CATE = 0x07;//商品分类
    int REGISTER = 0x08;//注册
    int BLUETOOTH = 0x09;//蓝牙
    int PERMISSION1 = 0x10;//权限
    int RIDER = 0x11;//骑手
    int ADD = 0x12;//添加
    int ORDER = 0x13;//订单
    int CHOOSE_LOCATION = 0x14;//选择货位
    int CHOOSE_BASKET = 0x15;//选择筐
    int SEARCH = 0x16;//搜索
    int CHOOSE_UNIT = 0x18;//选择单位
    int CHOOSE = 0x22;//选择
    int ADDRESS = 0x23;//地址
    int NAME = 0x24;//名称
    int PAY = 0x25;//支付
    int LOCATION_ENABLED = 0x26;//定位服务设置
    int LOCATION_STORAGE_MANAGE = 0x27;//定位所需存储权限

    //钉钉业务告警Webhook
    String dd_webhook = "https://oapi.dingtalk.com/robot/send?access_token=7f20ecd2378b5bf04d5dfc733354faa020af2476af71cd7799249461ded492d7";
    String dd_sign = "SEC7f94c99fde3c143402dedc1d48fb7991cc3841f808b3917cf96418293b106079";

    String release_sha1 = "E9:5A:0A:25:19:F6:3C:46:26:A3:23:7D:EB:8E:3E:80:AC:ED:F1:7F";
    String debug_sha1 = "AF:F8:F9:3E:93:B0:02:A7:80:55:07:FC:F7:F1:18:78:E6:8B:EA:AF";

}
