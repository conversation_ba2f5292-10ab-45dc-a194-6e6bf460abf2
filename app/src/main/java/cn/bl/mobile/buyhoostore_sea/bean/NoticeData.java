package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;

/**
 * Describe:公告列表（实体类）
 * Created by jingang on 2022/12/30
 */
public class NoticeData implements Serializable {
    /**
     * shopMsgRead : 0
     * picPath : image/public/moren.jpg
     * shopMsgDate : 2022-12-30
     * shopMsgInfo : 12.29 日全网产品热销排行top20!
     * shopMsgTitle : 12.29 日全网产品热销排行top20!
     * shopMsgType : 1
     * timeType : 小时
     * shopMsgId : 17679299
     * time : 14
     */

    private int shopMsgRead;
    private String picPath;
    private String shopMsgDate;
    private String shopMsgInfo;
    private String shopMsgTitle;
    private int shopMsgType;
    private String timeType;
    private int shopMsgId;
    private int time;

    public int getShopMsgRead() {
        return shopMsgRead;
    }

    public void setShopMsgRead(int shopMsgRead) {
        this.shopMsgRead = shopMsgRead;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public String getShopMsgDate() {
        return shopMsgDate;
    }

    public void setShopMsgDate(String shopMsgDate) {
        this.shopMsgDate = shopMsgDate;
    }

    public String getShopMsgInfo() {
        return shopMsgInfo;
    }

    public void setShopMsgInfo(String shopMsgInfo) {
        this.shopMsgInfo = shopMsgInfo;
    }

    public String getShopMsgTitle() {
        return shopMsgTitle;
    }

    public void setShopMsgTitle(String shopMsgTitle) {
        this.shopMsgTitle = shopMsgTitle;
    }

    public int getShopMsgType() {
        return shopMsgType;
    }

    public void setShopMsgType(int shopMsgType) {
        this.shopMsgType = shopMsgType;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public int getShopMsgId() {
        return shopMsgId;
    }

    public void setShopMsgId(int shopMsgId) {
        this.shopMsgId = shopMsgId;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }
}
