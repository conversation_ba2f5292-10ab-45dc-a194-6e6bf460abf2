package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockGoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:补货单-创建补货单-商品列表（适配器）
 * Created by jingang on 2023/9/6
 */
public class RestockInfoGoodsAdapter extends BaseAdapter<RestockGoodsData> {

    public RestockInfoGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvCount, tvPrice, tvBarcode, tvUnit, tvDel;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvDel = holder.getView(R.id.tvItemDel);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f7));
        }

        tvName.setText(mDataList.get(position).getGoodsName());
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsTotal()));
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
            tvUnit.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        } else {
            tvUnit.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()) + "/" + mDataList.get(position).getGoodsUnit());
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            tvDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);
    }

}
