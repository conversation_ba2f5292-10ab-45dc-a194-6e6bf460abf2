package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.github.gzuliyujiang.wheelpicker.entity.TimeEntity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.pickerview.TimeWheelLayout;

/**
 * Describe:时间选择弹窗
 * Created by jingang on 2022/12/6
 */
@SuppressLint({"NonConstantResourceId", "SimpleDateFormat"})
public class TimeDialog extends BaseDialog {
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.tvDialogStart)
    TextView tvStart;
    @BindView(R.id.vDialogStart)
    View vStart;
    @BindView(R.id.tvDialogEnd)
    TextView tvEnd;
    @BindView(R.id.vDialogEnd)
    View vEnd;
    @BindView(R.id.timeWheelLayout)
    TimeWheelLayout timeWheelLayout;

    private int type;//0.开始 1.结束
    private static String startTime, endTime;

    public static void showDialog(Context mContext, String startTime, String endTime, MyListener listener) {
        TimeDialog.startTime = startTime;
        TimeDialog.endTime = endTime;
        TimeDialog dialog = new TimeDialog(mContext);
        dialog.setRange(startTime, "");
        dialog.setListener(listener);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    @SuppressLint("SetTextI18n")
    public TimeDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_time);
        ButterKnife.bind(this);
        tvCancel.setText(getLanguageValue("cancel"));
        tvTitle.setText(getLanguageValue("choose")+getLanguageValue("time"));
        tvConfirm.setText(getLanguageValue("confirm"));
        tvStart.setHint(getLanguageValue("choose")+getLanguageValue("start")+getLanguageValue("time"));
        tvEnd.setHint(getLanguageValue("choose")+getLanguageValue("end")+getLanguageValue("time"));

        tvEnd.setText(endTime);
        timeWheelLayout.setSelectedTextColor(getContext().getResources().getColor(R.color.black));//选中字体颜色
        timeWheelLayout.setSelectedTextSize(16 * getContext().getResources().getDisplayMetrics().scaledDensity);//选中字体大小
        timeWheelLayout.setTextColor(getContext().getResources().getColor(R.color.color_999));//字体颜色
        timeWheelLayout.setTextSize(14 * getContext().getResources().getDisplayMetrics().scaledDensity);//字体大小
        timeWheelLayout.setIndicatorColor(getContext().getResources().getColor(R.color.transparent)); //横线颜色
        timeWheelLayout.setOnTimeSelectedListener((hour, minute, second) -> {
            String hours_str, minute_str;
            if (hour < 10) {
                hours_str = "0" + hour;
            } else {
                hours_str = String.valueOf(hour);
            }
            if (minute < 10) {
                minute_str = "0" + minute;
            } else {
                minute_str = String.valueOf(minute);
            }
            if (type == 0) {
                startTime = hours_str + ":" + minute_str;
                tvStart.setText(startTime);
            } else {
                endTime = hours_str + ":" + minute_str;
                tvEnd.setText(endTime);
            }
        });
    }

    @OnClick({R.id.tvDialogCancel, R.id.tvDialogConfirm, R.id.linDialogStart, R.id.linDialogEnd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogCancel:
                //取消
                if (listener != null) {
                    listener.onClick("", "");
                }
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(startTime)) {
                    showMessage(getLanguageValue("pleaseSelect")+getLanguageValue("start")+getLanguageValue("time"));
                    return;
                }
                if (TextUtils.isEmpty(endTime)) {
                    showMessage(getLanguageValue("pleaseSelect")+getLanguageValue("end")+getLanguageValue("time"));
                    return;
                }
                if (listener != null) {
                    listener.onClick(startTime, endTime);
                }
                dismiss();
                break;
            case R.id.linDialogStart:
                //选择开始时间
                if (type != 0) {
                    type = 0;
                    tvStart.setTextColor(getContext().getResources().getColor(R.color.blue));
                    vStart.setBackgroundColor(getContext().getResources().getColor(R.color.blue));
                    tvEnd.setTextColor(getContext().getResources().getColor(R.color.color_999));
                    vEnd.setBackgroundColor(getContext().getResources().getColor(R.color.color_f2));
                    setRange(startTime, "");
                }
                break;
            case R.id.linDialogEnd:
                //选择结束时间
                if (type != 1) {
                    type = 1;
                    tvStart.setTextColor(getContext().getResources().getColor(R.color.color_999));
                    vStart.setBackgroundColor(getContext().getResources().getColor(R.color.color_f2));
                    tvEnd.setTextColor(getContext().getResources().getColor(R.color.blue));
                    vEnd.setBackgroundColor(getContext().getResources().getColor(R.color.blue));
                    setRange(startTime, endTime);
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置时间范围
     */
    private void setRange(String startTime, String endTime) {
        TimeEntity entityStart = new TimeEntity(),
                entityEnd = new TimeEntity(),
                entityNow;
        SimpleDateFormat df = new SimpleDateFormat("hh:mm");
        Calendar calendar = Calendar.getInstance();
        if (type == 0) {
            //开始时间
            entityStart.setHour(0);
            entityStart.setMinute(0);
            //当前时间
            if (TextUtils.isEmpty(startTime)) {
                entityNow = TimeEntity.now();
            } else {
                try {
                    calendar.setTime(Objects.requireNonNull(df.parse(startTime)));
                    entityNow = new TimeEntity();
                    entityNow.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                    entityNow.setMinute(calendar.get(Calendar.MINUTE));
                } catch (ParseException e) {
                    e.printStackTrace();
                    entityNow = TimeEntity.now();
                }
            }
        } else {
            //开始时间
            if (TextUtils.isEmpty(startTime)) {
                entityStart.setHour(0);
                entityStart.setMinute(1);
            } else {
                try {
                    calendar.setTime(Objects.requireNonNull(df.parse(startTime)));
                    entityStart.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                    int minute = calendar.get(Calendar.MINUTE);
                    if (minute < 59) {
                        entityStart.setMinute(minute + 1);
                    } else {
                        entityStart.setMinute(minute);
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                    entityStart.setHour(0);
                    entityStart.setMinute(1);
                }
            }
            //当前时间
            if (TextUtils.isEmpty(endTime)) {
                if (TextUtils.isEmpty(startTime)) {
                    entityNow = TimeEntity.now();
                } else {
                    try {
                        calendar.setTime(Objects.requireNonNull(df.parse(startTime)));
                        entityNow = new TimeEntity();
                        entityNow.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                        entityNow.setMinute(calendar.get(Calendar.MINUTE));
                    } catch (ParseException e1) {
                        e1.printStackTrace();
                        entityNow = TimeEntity.now();
                    }
                }
            } else {
                try {
                    calendar.setTime(Objects.requireNonNull(df.parse(endTime)));
                    entityNow = new TimeEntity();
                    entityNow.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                    entityNow.setMinute(calendar.get(Calendar.MINUTE));
                } catch (ParseException e) {
                    e.printStackTrace();
                    if (TextUtils.isEmpty(startTime)) {
                        entityNow = TimeEntity.now();
                    } else {
                        try {
                            calendar.setTime(Objects.requireNonNull(df.parse(startTime)));
                            entityNow = new TimeEntity();
                            entityNow.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                            entityNow.setMinute(calendar.get(Calendar.MINUTE));
                        } catch (ParseException e1) {
                            e1.printStackTrace();
                            entityNow = TimeEntity.now();
                        }
                    }
                }
            }
        }
        //结束时间
        entityEnd.setHour(23);
        entityEnd.setMinute(59);
        if (timeWheelLayout != null) {
            timeWheelLayout.setRange(entityStart, entityEnd, entityNow);
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String startDate, String endDate);
    }
}
