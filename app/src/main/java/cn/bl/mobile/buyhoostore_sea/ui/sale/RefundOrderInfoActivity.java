package cn.bl.mobile.buyhoostore_sea.ui.sale;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bin.david.form.utils.DensityUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;

import org.greenrobot.eventbus.EventBus;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.RefundOrderDetailBean;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import cn.bl.mobile.buyhoostore_sea.evevtbus.FirstEvent;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.RefundRefuseReasonDialog;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:销售-退款单-详情
 * Created by jingang on 2023/4/18
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RefundOrderInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView title_name;
    @BindView(R.id.btn_agree)
    Button btn_agree;
    @BindView(R.id.btn_refuse)
    Button btn_refuse;
    @BindView(R.id.ll_look_order)
    LinearLayout ll_look_order;
    @BindView(R.id.goodsRV)
    RecyclerView goodsRV;
    @BindView(R.id.tv_reason)
    TextView tv_reason;
    @BindView(R.id.rl_progress)
    RelativeLayout rl_progress;
    @BindView(R.id.tv_sum)
    TextView tv_sum;
    @BindView(R.id.tv_refund_desc)
    TextView tv_refund_desc;
    @BindView(R.id.operaterBtnsContainer)
    ConstraintLayout operaterBtnsContainer;
    @BindView(R.id.tv_status)
    TextView tv_status;
    @BindView(R.id.tv_status_tip)
    TextView tv_status_tip;
    @BindView(R.id.tv_desc)
    TextView tv_desc;
    @BindView(R.id.iv_progress3)
    ImageView iv_progress3;
    @BindView(R.id.iv_progress4)
    ImageView iv_progress4;
    @BindView(R.id.iv_progress5)
    ImageView iv_progress5;
    @BindView(R.id.rl_status_info)
    RelativeLayout rl_status_info;
    @BindView(R.id.scroll_view)
    View scroll_view;
    @BindView(R.id.tvStatus0)
    TextView tvStatus0;
    @BindView(R.id.tvStatus1)
    TextView tvStatus1;
    @BindView(R.id.tvStatus2)
    TextView tvStatus2;
    @BindView(R.id.tvStatus3)
    TextView tvStatus3;
    @BindView(R.id.tvStatus4)
    TextView tvStatus4;
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvOrderValue)
    TextView tvOrderValue;
    @BindView(R.id.tvReasonValue)
    TextView tvReasonValue;
    @BindView(R.id.tvGoodsValue)
    TextView tvGoodsValue;

    private String staffId = "";

    private String orderId = "";
    private GoodsAdapter goodsAdapter;
    private RefundOrderDetailBean.Data refundOrderDetailBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_refund_order_info;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        SharedPreferences sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        staffId = sp.getString("staffId", "");

        goodsAdapter = new GoodsAdapter(this);
        goodsRV.setAdapter(goodsAdapter);
    }

    @Override
    public void initData() {
        orderId = getIntent().getStringExtra("orderId");
        if (!TextUtils.isEmpty(orderId)) {
            loadOrderDetail();
        }
    }

    @OnClick({R.id.ivBack, R.id.btn_agree, R.id.btn_refuse, R.id.ll_look_order})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.btn_agree:
                IAlertDialog.showDialog(this,
                        getLanguageValue("confirm") + getLanguageValue("refund") + "?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            handleRefund(true, "");
                        });
                break;
            case R.id.btn_refuse:
                RefundRefuseReasonDialog.showDialog(this, msg -> {
                    handleRefund(false, msg);
                });
                break;
            case R.id.ll_look_order:
                startActivity(new Intent(this, OrderInfoActivity.class)
                        .putExtra("unique", String.valueOf(refundOrderDetailBean.getSaleListUnique()))
                );
                break;
        }
    }

    @Override
    public void setText() {
        title_name.setText(getLanguageValue("refund") + getLanguageValue("order") + getLanguageValue("details"));
        tvStatus0.setText(getLanguageValue("submit") + getLanguageValue("apply"));
        tvStatus1.setText(getLanguageValue("merchant") + getLanguageValue("audit"));
        tvStatus2.setText(getLanguageValue("merchant") + getLanguageValue("receipt"));
        tvStatus3.setText(getLanguageValue("refund"));
        tvStatus4.setText(getLanguageValue("finish"));
        tvTotalValue.setText(getLanguageValue("actual") + getLanguageValue("refund") + getLanguageValue("amount"));
        tvOrderValue.setText(getLanguageValue("viewOriginalOrder"));
        tvReasonValue.setText(getLanguageValue("refund") + getLanguageValue("reason"));
        tvGoodsValue.setText(getLanguageValue("refund") + getLanguageValue("commodity"));
        btn_refuse.setText(getLanguageValue("denied"));
        btn_agree.setText(getLanguageValue("agree") + getLanguageValue("refund"));
    }

    private void refreshView() {
        tv_reason.setText(refundOrderDetailBean.getRetListReason());
        rl_progress.setVisibility(View.VISIBLE);
        tv_sum.setText(String.format("RM%s", refundOrderDetailBean.getRetListTotal()));
        tv_refund_desc.setText(refundOrderDetailBean.getRetPayMsg());
        operaterBtnsContainer.setVisibility(View.GONE);
        switch (refundOrderDetailBean.getRetListHandlestate()) {
            case 1:
                tv_status.setText(getLanguageValue("wait") + getLanguageValue("spply"));
                tv_status_tip.setText(getLanguageValue("wait") + getLanguageValue("spply"));
                tv_desc.setText(getLanguageValue("refund") + getLanguageValue("order") + getLanguageValue("wait") + getLanguageValue("spply"));
                operaterBtnsContainer.setVisibility(View.VISIBLE);
                break;
            case 3:
                tv_status.setText(getLanguageValue("refund") + getLanguageValue("succeed"));
                iv_progress3.setImageDrawable(getResources().getDrawable(R.mipmap.check_blue));
                iv_progress4.setImageDrawable(getResources().getDrawable(R.mipmap.check_blue));
                iv_progress5.setImageDrawable(getResources().getDrawable(R.mipmap.check_blue));
                tv_status_tip.setText(getLanguageValue("refund") + getLanguageValue("succeed"));
                StringBuilder strbuild = new StringBuilder();
                strbuild.append("，");
                for (int i = 0; i < refundOrderDetailBean.getPayDetailList().size(); i++) {
                    if (refundOrderDetailBean.getPayDetailList().get(i).getPayType() == 8) {
                        strbuild.append(refundOrderDetailBean.getPayDetailList().get(i).getPayTypeMsg());
                        strbuild.append(getLanguageValue("return"));
                        strbuild.append((refundOrderDetailBean.getPayDetailList().get(i).getPayMoney() * 100));
                        strbuild.append("颗");
                    } else {
                        strbuild.append(refundOrderDetailBean.getPayDetailList().get(i).getPayTypeMsg());
                        strbuild.append(getLanguageValue("refund") + "RM");
                        strbuild.append(refundOrderDetailBean.getPayDetailList().get(i).getPayMoney());
                    }
                }
                strbuild.delete(0, 1);
                tv_desc.setText(strbuild.toString());
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) scroll_view.getLayoutParams();
                params.bottomMargin = 0;
                scroll_view.setLayoutParams(params);
                break;
            case 4:
                tv_status.setText(getLanguageValue("refused"));
                tv_status.setTextColor(getResources().getColor(R.color.color_F6403F));
                ConstraintLayout.LayoutParams params1 = (ConstraintLayout.LayoutParams) rl_status_info.getLayoutParams();
                params1.topMargin = DensityUtils.dp2px(this, 20.0F);
                rl_status_info.setLayoutParams(params1);
                rl_progress.setVisibility(View.GONE);
                tv_status_tip.setText(getLanguageValue("refused") + getLanguageValue("refund"));
                tv_desc.setText(refundOrderDetailBean.getRetListRemarks());
                LinearLayout.LayoutParams params2 = (LinearLayout.LayoutParams) scroll_view.getLayoutParams();
                params2.bottomMargin = 0;
                scroll_view.setLayoutParams(params2);
                break;
        }

    }

    /**
     * 订单详情
     */
    private void loadOrderDetail() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("retListUnique", orderId);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundOrderDetail(),
                map,
                RefundOrderDetailBean.Data.class,
                new RequestListener<RefundOrderDetailBean.Data>() {
                    @Override
                    public void success(RefundOrderDetailBean.Data data) {
                        goodsAdapter.setList(data.getDetailList());
                        goodsAdapter.notifyDataSetChanged();
                        refundOrderDetailBean = data;
                        refreshView();
                    }
                });
    }


    /**
     * 退款
     */
    private void handleRefund(boolean isAgree, String reason) {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        int status = 4;
        if (isAgree) {
            status = 3;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("retListHandlestate", status);
        map.put("retListUnique", refundOrderDetailBean.getRetListUnique());
        map.put("retListRemarks", reason);
        map.put("staffId", staffId);
        map.put("macId", "安卓Klip");
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getHandelRefundUrL(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        loadOrderDetail();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                        EventBus.getDefault().post(new FirstEvent(Constants.CONSTANT_REFRESH_LIST_COUNT));
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    public static class GoodsAdapter extends BaseQuickAdapter<RefundOrderDetailBean.Data.RefundGoodsBean, BaseViewHolder> {
        private final Context mContext;

        public GoodsAdapter(Context context) {
            super(R.layout.item_refund_goods);
            mContext = context;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder baseViewHolder, RefundOrderDetailBean.Data.RefundGoodsBean bean) {
            baseViewHolder.setText(R.id.tv_name, bean.getGoodsName());
            baseViewHolder.setText(R.id.tv_price, String.format("RM%s", bean.getRetListDetailPrice()));
            baseViewHolder.setText(R.id.tv_unit, bean.getGoodsUnit());
            baseViewHolder.setText(R.id.tv_count, String.format("x%s", bean.getRetListDetailCount()));

            ImageView imgView = baseViewHolder.getView(R.id.iv_goods_image);
            Glide.with(mContext)
                    .load(StringUtils.handledImgUrl(bean.getImagePath()))
                    .fitCenter()
                    .transform(new RoundedCorners(16))
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(imgView);
        }
    }

}
