package cn.bl.mobile.buyhoostore_sea.ui.goods.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.GoodsChangeRecordInfoAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsChangeRecordData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品变更记录-详情
 * Created by jingang on 2023/8/23
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsChangeRecordInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.tvShopName)
    TextView tvShopName;
    @BindView(R.id.tvUser)
    TextView tvUser;
    @BindView(R.id.tvPlatformValue)
    TextView tvPlatformValue;
    @BindView(R.id.tvPlatform)
    TextView tvPlatform;
    @BindView(R.id.tvChangeValue)
    TextView tvChangeValue;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvCode)
    TextView tvCode;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvBeforeValue)
    TextView tvBeforeValue;
    @BindView(R.id.tvOfterValue)
    TextView tvOfterValue;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.tvLast)
    TextView tvLast;
    @BindView(R.id.linLast)
    LinearLayout linLast;
    @BindView(R.id.tvUserLast)
    TextView tvUserLast;
    @BindView(R.id.tvPlatformLastValue)
    TextView tvPlatformLastValue;
    @BindView(R.id.tvPlatformLast)
    TextView tvPlatformLast;
    @BindView(R.id.tvChangeLastValue)
    TextView tvChangeLastValue;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String img, goodsBarcode, goodsName, id;

    private GoodsChangeRecordInfoAdapter mAdapter;
    private List<GoodsChangeRecordData.RecordDetailBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_goods_change_record_info;
    }

    @Override
    public void initViews() {
        img = getIntent().getStringExtra("img");
        goodsBarcode = getIntent().getStringExtra("goodsBarcode");
        goodsName = getIntent().getStringExtra("goodsName");
        id = getIntent().getStringExtra("id");
        setAdapter();
    }

    @Override
    public void initData() {
        getGoodsChangeRecordInfo();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("change")+getLanguageValue("record"));
        tvPlatformValue.setText(getLanguageValue("through"));
        tvChangeValue.setText(getLanguageValue("change"));
        tvNameValue.setText(getLanguageValue("attribute")+getLanguageValue("name"));
        tvBeforeValue.setText(getLanguageValue("change")+getLanguageValue("front"));
        tvOfterValue.setText(getLanguageValue("change")+getLanguageValue("after"));
        tvPlatformLastValue.setText(getLanguageValue("through"));
        tvChangeLastValue.setText(getLanguageValue("change"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsChangeRecordInfoAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            smartRefreshLayout.finishRefresh();
            getGoodsChangeRecordInfo();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsChangeRecordData data) {
        if (data == null) {
            return;
        }

        tvShopName.setText(getShop_name());
        tvUser.setText(data.getUserName() + " " + data.getCreateTime());
        tvPlatform.setText(data.getDeviceSource());

        Glide.with(this)
                .load(StringUtils.handledImgUrl(img))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(goodsName);
        tvCode.setText(goodsBarcode);

        //上次变更
        if (TextUtils.isEmpty(data.getLastCreateTime())) {
            tvLast.setText(getLanguageValue("lastTime"+getLanguageValue("change")+":"+getLanguageValue("none")));
            linLast.setVisibility(View.GONE);
        } else {
            tvLast.setText(getLanguageValue("lastTime"+getLanguageValue("change")+":"));
            linLast.setVisibility(View.VISIBLE);
            tvUserLast.setText(data.getLastUserName() + " " + data.getLastCreateTime());
            tvPlatformLast.setText(data.getLastDeviceSource());
        }

        if (data.getRecordDetail() == null) {
            return;
        }
        dataList.clear();
        dataList.addAll(data.getRecordDetail());
        mAdapter.setDataList(dataList);
    }

    /**
     * 商品变更记录-详情
     */
    private void getGoodsChangeRecordInfo() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        map.put("recordGoodsOperId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGoodsChangeRecordInfo(),
                map,
                GoodsChangeRecordData.class,
                new RequestListener<GoodsChangeRecordData>() {
                    @Override
                    public void success(GoodsChangeRecordData data) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                    }
                });
    }

}
