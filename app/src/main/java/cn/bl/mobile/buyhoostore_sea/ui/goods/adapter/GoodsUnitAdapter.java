package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsUnitData;

/**
 * Describe:商品单位（适配器）
 * Created by jingang on 2023/8/11
 */
public class GoodsUnitAdapter extends BaseAdapter<GoodsUnitData> {

    public GoodsUnitAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_unit;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getGoods_unit());
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvEdit, tvDel;
        tvName = holder.getView(R.id.tvItemName);
        tvEdit = holder.getView(R.id.tvItemEdit);
        tvDel = holder.getView(R.id.tvItemDel);

        if (position % 2 == 0) {
            tvName.setBackgroundResource(R.color.white);
        } else {
            tvName.setBackgroundResource(R.color.color_f7);
        }
        tvName.setText(mDataList.get(position).getGoods_unit());

        if (listener != null) {
            tvName.setOnClickListener(v -> listener.onItemClick(v, position));
            tvEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            tvDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
