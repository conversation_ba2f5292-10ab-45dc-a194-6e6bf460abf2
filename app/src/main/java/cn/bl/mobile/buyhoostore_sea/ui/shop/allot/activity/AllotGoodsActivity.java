package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsAddDetailBean;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:调拨单-添加商品
 * Created by jingang on 2023/5/19
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AllotGoodsActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.ivImg)
    ImageView ivImg;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvSpecs)
    TextView tvSpecs;
    @BindView(R.id.tvKucun)
    TextView tvKucun;
    @BindView(R.id.tvInPrice)
    TextView tvInPrice;
    @BindView(R.id.tvSalePrice)
    TextView tvSalePrice;
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;
    @BindView(R.id.etCount)
    EditText etCount;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String code, img, name, specs, unit;
    private double inPrice, salePrice, count;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_allot_goods;
    }

    @Override
    public void initViews() {
        code = getIntent().getStringExtra("code");
    }

    @Override
    public void initData() {
        getGoodsDetail();
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //保存
                if (TextUtils.isEmpty(etCount.getText().toString().trim())) {
                    showMessage(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("transfer")+getLanguageValue("quantity"));
                    return;
                }
                count = Double.parseDouble(etCount.getText().toString().trim());
                if (count == 0) {
                    showMessage(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("transfer")+getLanguageValue("quantity"));
                    return;
                }
                AllotData.DetailInfoListBean data = new AllotData.DetailInfoListBean(name,
                        code,
                        count,
                        inPrice,
                        salePrice,
                        specs,
                        unit,
                        img);
                setResult(Constants.ADD, new Intent()
                        .putExtra("data", data)
                );
                finish();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("transfer") + getLanguageValue("commodity"));
        tvCountValue.setText(getLanguageValue("transfer")+getLanguageValue("quantity")+":");
        etCount.setHint(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("transfer")+getLanguageValue("quantity"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 更新UI
     *
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void setUI(GoodsAddDetailBean.DataBean data) {
        if (data == null) {
            return;
        }
        if (data.getListDetail().size() < 1) {
            return;
        }
        img = data.getListDetail().get(0).getGoodsPicturepath();
        name = data.getListDetail().get(0).getGoodsName();
        specs = data.getListDetail().get(0).getGoodsStandard();
        unit = data.getListDetail().get(0).getGoodsUnit();
        inPrice = data.getListDetail().get(0).getGoodsInPrice();
        salePrice = data.getListDetail().get(0).getGoodsSalePrice();

        Glide.with(this)
                .load(StringUtils.handledImgUrl(img))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(name);
        if (TextUtils.isEmpty(specs)) {
            tvSpecs.setText(getLanguageValue("spec")+" "+getLanguageValue("none"));
        } else {
            tvSpecs.setText(getLanguageValue("spec")+" "+specs);
        }
        tvKucun.setText(getLanguageValue("inventory")+":" + DFUtils.getNum2(data.getGoodsCount()));
        tvInPrice.setText(getLanguageValue("purchasePrice")+":RM" + DFUtils.getNum2(inPrice));
        tvSalePrice.setText(getLanguageValue("sellingPrice")+":RM" + DFUtils.getNum2(salePrice));
    }

    //商品详情的接口
    public void getGoodsDetail() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", code);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSelecDetail(),
                params,
                GoodsAddDetailBean.DataBean.class,
                new RequestListener<GoodsAddDetailBean.DataBean>() {
                    @Override
                    public void success(GoodsAddDetailBean.DataBean dataBean) {
                        setUI(dataBean);
                    }
                });
    }
}
