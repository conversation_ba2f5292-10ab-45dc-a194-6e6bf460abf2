package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

/**
 * Describe: 商品批发单价（实体类）
 * Created by jingang on 2025/5/19
 */
public class GoodsWholesaleData {
    private double wholesalePrice;
    private double wholesaleCount;

    public GoodsWholesaleData() {
    }

    public GoodsWholesaleData(double wholesalePrice, double wholesaleCount) {
        this.wholesalePrice = wholesalePrice;
        this.wholesaleCount = wholesaleCount;
    }

    public double getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(double wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public double getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(double wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }
}
