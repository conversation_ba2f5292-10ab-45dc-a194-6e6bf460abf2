package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Administrator on 2017/9/26 0026.
 */
public class ShopBean implements Serializable{


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"shopImagePath":"image/8302016134121/830201613412165.jpg","shopUnique":8302016134121,"shopName":"测试店铺","shopPhone":"18369679135","shopHours":"14:53-16:53"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean implements Serializable {
        /**
         * shopImagePath : image/8302016134121/830201613412165.jpg
         * shopUnique : 8302016134121
         * shopName : 测试店铺
         * shopPhone : 18369679135
         * shopHours : 14:53-16:53
         */

        private String shopImagePath;
        private String shopUnique;
        private String shopName = "";
        private String shopPhone;
        private String shopHours;
        private String shop_address_detail = "";

        public String getShopImagePath() {
            return shopImagePath;
        }

        public void setShopImagePath(String shopImagePath) {
            this.shopImagePath = shopImagePath;
        }

        public String getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(String shopUnique) {
            this.shopUnique = shopUnique;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getShopPhone() {
            return shopPhone;
        }

        public void setShopPhone(String shopPhone) {
            this.shopPhone = shopPhone;
        }

        public String getShopHours() {
            return shopHours;
        }

        public void setShopHours(String shopHours) {
            this.shopHours = shopHours;
        }

        public String getShop_address_detail() {
            return shop_address_detail;
        }
    }




}
