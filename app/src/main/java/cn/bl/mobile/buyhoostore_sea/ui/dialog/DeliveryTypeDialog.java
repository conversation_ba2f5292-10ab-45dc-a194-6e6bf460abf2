package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（选择配送方式）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class DeliveryTypeDialog extends BaseDialog {

    public static void showDialog(Context context, MyListener listener) {
        DeliveryTypeDialog.listener = listener;
        DeliveryTypeDialog dialog = new DeliveryTypeDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public DeliveryTypeDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_delivery_type);
        ButterKnife.bind(this);
    }

    @OnClick({R.id.tvDialogType0, R.id.tvDialogType1, R.id.tvDialogCancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogType0:
                //自配送
                if (listener != null) {
                    listener.onClick(view, 0);
                    dismiss();
                }
                break;
            case R.id.tvDialogType1:
                //一刻钟配送
                if (listener != null) {
                    listener.onClick(view, 2);
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.自配送 2.一刻钟配送
         */
        void onClick(View view, int type);
    }
}
