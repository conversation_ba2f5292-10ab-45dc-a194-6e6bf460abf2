package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import android.os.CountDownTimer;
import android.text.Editable;
import android.text.InputType;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.leaf.library.StatusBarUtil;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.UserInfoData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.BuildConfig;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.app.ActivityManager;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.ui.MainActivity;
import cn.bl.mobile.buyhoostore_sea.ui.SplashActivity;
import cn.bl.mobile.buyhoostore_sea.ui.TestActivity;
import cn.bl.mobile.buyhoostore_sea.ui.WebActivity;
import cn.bl.mobile.buyhoostore_sea.ui.popupwindow.LanguagePop;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 登录
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class LoginActivity extends BaseActivity {
    @BindView(R.id.linLanguage)
    LinearLayout linLanguage;
    @BindView(R.id.ivLanguage)
    ImageView ivLanguage;
    @BindView(R.id.tvLanguage)
    TextView tvLanguage;
    @BindView(R.id.ivLanguageIcon)
    ImageView ivLanguageIcon;
    @BindView(R.id.tvWelcome)
    TextView tvWelcome;
    @BindView(R.id.etAccount)
    EditText etAccount;
    @BindView(R.id.linPwd)
    LinearLayout linPwd;
    @BindView(R.id.etPwd)
    EditText etPwd;
    @BindView(R.id.ivEye)
    ImageView ivEye;
    @BindView(R.id.vPwd)
    View vPwd;
    @BindView(R.id.linCode)
    LinearLayout linCode;
    @BindView(R.id.etCode)
    EditText etCode;
    @BindView(R.id.tvCode)
    TextView tvCode;
    @BindView(R.id.vCode)
    View vCode;
    @BindView(R.id.tvLogin)
    TextView tvLogin;
    @BindView(R.id.tvType)
    TextView tvType;
    @BindView(R.id.tvForget)
    TextView tvForget;
    @BindView(R.id.tvRegister)
    TextView tvRegister;
    @BindView(R.id.ivPrivacy)
    ImageView ivPrivacy;
    @BindView(R.id.tvPrivacy)
    TextView tvPrivacy;
    @BindView(R.id.butUrl)
    Button butUrl;

    private boolean isEye;//密码可见
    private String account, pwd, code;
    private int type;//0.账号密码登录 1.验证码登录
    private String session_id;
    private boolean isPrivacy;//同意用户协议

    private SharedPreferences sp = null;
    public String power_add = "";//添加新商品
    public String power_pur = "";//采购权限
    public String power_delete = "";//删除权限
    public String power_name = "";//商品名称
    public String power_price = "";//商品价格
    public String power_count = "";//库存
    public String power_kind = "";//分类
    public String power_inprice = "";//进价
    public String power_supplier = "";
    public String managerUnique = "";//管理员编号
    public String staffPosition;//管理员等级
    public static final String CONSTANT_SHOP_NAME = "constantShopName";
    public String staffAccount;//登录账号
    private String setPowerManager;//管理其他员工的权限
    private String shopName;
    private String staffId;//管理员ID

    @Override
    protected int getLayoutId() {
        return R.layout.activity_login;
    }

    @Override
    public void initViews() {
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        StatusBarUtil.setTransparentForWindow(this);
        StatusBarUtil.setDarkMode(this);
        etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                setTextBg();
            }
        });
        etPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                pwd = s.toString().trim();
                setTextBg();
            }
        });
        etCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                code = s.toString().trim();
                setTextBg();
            }
        });
        if (BuildConfig.DEBUG) {
            butUrl.setVisibility(View.VISIBLE);
        } else {
            butUrl.setVisibility(View.GONE);
        }
        /*多语言*/
        String language = SharedPreferencesUtil.getInstantiation(this).getString("", Constants.SP_LANGUAGE);
        if (TextUtils.isEmpty(language)) {
            ivLanguage.setImageResource(R.mipmap.ic_language001);
            tvLanguage.setText("中文");
        } else {
            switch (language) {
                case "en":
                    ivLanguage.setImageResource(R.mipmap.ic_language002);
                    tvLanguage.setText("English");
                    break;
                case "th":
                    ivLanguage.setImageResource(R.mipmap.ic_language003);
                    tvLanguage.setText("แบบไทย");
                    break;
                case "ru":
                    ivLanguage.setImageResource(R.mipmap.ic_language004);
                    tvLanguage.setText("Русский");
                    break;
                case "ms":
                    ivLanguage.setImageResource(R.mipmap.ic_language005);
                    tvLanguage.setText("Melayu");
                    break;
                case "kk":
                    ivLanguage.setImageResource(R.mipmap.ic_language006);
                    tvLanguage.setText("قازاقشا");
                    break;
                default:
                    ivLanguage.setImageResource(R.mipmap.ic_language001);
                    tvLanguage.setText("中文");
                    break;
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //0.测试 1.正式 2.自定义
        int i = SharedUtils.get("test", IPreference.DataType.INTEGER);
        switch (i) {
            case 1:
                butUrl.setText("正式环境");
                break;
            case 2:
                butUrl.setText("自定义环境");
                break;
            case 3:
                butUrl.setText("开发环境");
                break;
            default:
                butUrl.setText("测试环境");
                break;
        }
    }

    @OnClick({R.id.linLanguage,
            R.id.ivEye, R.id.tvCode, R.id.tvLogin,
            R.id.tvType, R.id.tvForget, R.id.tvRegister,
            R.id.ivPrivacy, R.id.butUrl})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linLanguage:
                //选择语言
                LanguagePop.showDialog(this, ivLanguageIcon, view, linLanguage.getMeasuredWidth(), () -> {
                    AppManager.getInstance().finishAllActivity();
                    goToActivity(SplashActivity.class);
                });
                break;
            case R.id.ivEye:
                //密码可见
                isEye = !isEye;
                if (isEye) {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);//设置密码可见
                    ivEye.setImageResource(R.drawable.open_eye);
                } else {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
                    ivEye.setImageResource(R.drawable.close_eye);
                }
                break;
            case R.id.tvCode:
                //获取验证码
                account = etAccount.getText().toString().trim();
                if (TextUtils.isEmpty(account)) {
                    showMessage(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("phoneNum"));
                    return;
                }
//                if (account.length() < 11) {
//                    showMessage(getLanguageValue("enterCorrectPhone"));
//                    return;
//                }
                if (!isRun) {
                    getSendCode();
                }
                break;
            case R.id.tvLogin:
                //登录
                hideSoftInput(this);
                if (!isPrivacy) {
                    showMessage(getLanguageValue("consentClause"));
                    return;
                }
                if (type == 0) {
                    if (TextUtils.isEmpty(account) || TextUtils.isEmpty(pwd)) {
                        showMessage(getLanguageValue("accountPasswordNotEmpty"));
                        return;
                    }
                } else {
                    if (TextUtils.isEmpty(account) || TextUtils.isEmpty(code)) {
                        showMessage(getLanguageValue("phoneCaptchaNotEmpty"));
                        return;
                    }
                }
                postLogin();
                break;
            case R.id.tvType:
                //登录方式
                if (type == 0) {
                    type = 1;
                    tvType.setText(getLanguageValue("accountNumber") + getLanguageValue("password") + getLanguageValue("login"));
                    linPwd.setVisibility(View.GONE);
                    vPwd.setVisibility(View.GONE);
                    linCode.setVisibility(View.VISIBLE);
                    vCode.setVisibility(View.VISIBLE);
                } else {
                    type = 0;
                    tvType.setText(getLanguageValue("captcha") + getLanguageValue("login"));
                    linPwd.setVisibility(View.VISIBLE);
                    vPwd.setVisibility(View.VISIBLE);
                    linCode.setVisibility(View.GONE);
                    vCode.setVisibility(View.GONE);
                }
                break;
            case R.id.tvForget:
                //忘记密码
                goToActivity(ForgetPwdActivity.class);
                break;
            case R.id.tvRegister:
                //注册
                goToActivity(RegisterActivity.class);
                break;
            case R.id.ivPrivacy:
                //同意用户协议
                isPrivacy = !isPrivacy;
                if (isPrivacy) {
                    ivPrivacy.setImageResource(R.mipmap.ic_chosen001);
                } else {
                    ivPrivacy.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.butUrl:
                //设置域名（测试用）
                goToActivity(TestActivity.class);
                break;
        }
    }

    @Override
    public void setText() {
//        tvWelcome.setText(getLanguageValue("hello") + "\n" + getLanguageValue("welcome"));
        etAccount.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("phoneNum"));
        etPwd.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("login") + getLanguageValue("password"));
        etCode.setHint(getLanguageValue("please") + getLanguageValue("input") + getLanguageValue("captcha"));
        tvCode.setText(getLanguageValue("get") + getLanguageValue("captcha"));
        tvLogin.setText(getLanguageValue("login"));
        tvType.setText(getLanguageValue("captcha") + getLanguageValue("login"));
        tvForget.setText(getLanguageValue("forget") + getLanguageValue("password"));
        tvRegister.setText(getLanguageValue("quick") + getLanguageValue("regist"));
//        tvPrivacy.setText(getLanguageValue("readAgree"));

        privacy_str0 = getRstr(R.string.agree_privacy_tips1);
        privacy_str1 = getRstr(R.string.agree_privacy_tips2);
        privacy_str2 = getRstr(R.string.agree_privacy_tips3);
        privacy_str3 = getRstr(R.string.agree_privacy_tips4);
        tvPrivacy.setText(privacy_str0 + privacy_str1 + privacy_str2 + privacy_str3);
        setSpannableText();
    }

    private String privacy_str0,
            privacy_str1,
            privacy_str2,
            privacy_str3;

    /**
     * TextView设置多种颜色及部分点击事件
     */
    public void setSpannableText() {
//        SpannableString spannableString = new SpannableString(tvPrivacy.getText().toString().trim());
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(LoginActivity.this, getLanguageValue("userServiceAgreement"), ZURL.CONSTANT_shopDoc);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 6, 14, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(LoginActivity.this, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 14, 20, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        SpannableString spannableString = new SpannableString(tvPrivacy.getText().toString().trim());
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(LoginActivity.this, getLanguageValue("userServiceAgreement"), ZURL.CONSTANT_shopDoc);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, privacy_str0.length(), (privacy_str0 + privacy_str1).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(LoginActivity.this, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, (privacy_str0 + privacy_str1).length(), (privacy_str0 + privacy_str1 + privacy_str2).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvPrivacy.setMovementMethod(LinkMovementMethod.getInstance());//设置可点击状态
        tvPrivacy.setText(spannableString);
    }

    /**
     * 设置登录按钮背景色
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(account)) {
            tvLogin.setBackgroundResource(R.drawable.shape_93c0fe_22);
            vPwd.setBackgroundColor(getResources().getColor(R.color.color_e7));
            vCode.setBackgroundColor(getResources().getColor(R.color.color_e7));
            return;
        }
        if (type == 0) {
            if (TextUtils.isEmpty(pwd)) {
                tvLogin.setBackgroundResource(R.drawable.shape_93c0fe_22);
                vPwd.setBackgroundColor(getResources().getColor(R.color.color_e7));
                return;
            }
        } else {
            if (TextUtils.isEmpty(code)) {
                tvLogin.setBackgroundResource(R.drawable.shape_93c0fe_22);
                vCode.setBackgroundColor(getResources().getColor(R.color.color_e7));
                return;
            }
        }
        vPwd.setBackgroundColor(getResources().getColor(R.color.blue));
        vCode.setBackgroundColor(getResources().getColor(R.color.blue));
        tvLogin.setBackgroundResource(R.drawable.shape_blue_22);
    }

    //倒计时
    private boolean isRun = false;//倒计时线程是否运行

    /**
     * 验证码倒计时
     */
    public void time() {
        if (!isRun) {
            new CountdownThread(60000, 1000).start();// 构造CountDownTimer对象
            isRun = true;
        }
    }

    /**
     * 倒计时
     */
    class CountdownThread extends CountDownTimer {
        public CountdownThread(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @SuppressLint("SetTextI18n")
        @Override
        public void onTick(long millisUntilFinished) {
            tvCode.setText((millisUntilFinished / 1000) + "s");
        }

        @Override
        public void onFinish() {
            //倒计时结束时触发
            tvCode.setText(getLanguageValue("revalidate"));
            isRun = false;
        }
    }

    /**
     * 登录
     */
    private void postLogin() {
        String url;
        Map<String, Object> map = new HashMap<>();
        map.put("staffAccount", account);
        if (type == 1) {
            //验证码登录
            map.put("smsCode", code);
            map.put("sessionId", session_id);
            map.put("phoneType", 1);
            url = ZURL.getCheckPwdUrlTWO();
        } else {
            //账号密码登录
            map.put("staffPwd", pwd);
            url = ZURL.getLoginTwo();
        }
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                url,
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        UserInfoData data = new Gson().fromJson(s, UserInfoData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("login") + getLanguageValue("failed"));
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showMessage(data.getMsg());
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("login") + getLanguageValue("failed"));
                            return;
                        }
                        BaseApplication.getInstance().saveUserInfo(s);
                        setUI(data.getData(), "");
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    // 发送验证码
    public void getSendCode() {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSendCodeUrlTWO(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(getLanguageValue("send") + getLanguageValue("succeed"));
                        session_id = s;
                        time();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 更新接口返回结果
     *
     * @param login
     */
    private void setUI(UserInfoData.Data login, String token) {
        String shopId = login.getShopUnique();//店铺id
        String area_dict_num = login.getArea_dict_num();
        power_add = login.getPowerAdd();
        power_pur = login.getPowerPur();
        power_delete = login.getPowerDelete();
        power_name = login.getPowerName();
        power_price = login.getPowerPrice();
        power_count = login.getPowerCount();
        power_kind = login.getPowerKind();
        power_inprice = login.getPowerInprice();
        power_supplier = login.getPowerSupplier();
        staffAccount = login.getStaffAccount();
        managerUnique = login.getManagerUnique();
        staffPosition = login.getStaffPosition();
        setPowerManager = login.getPowerManager();
        shopName = login.getShopName();
        staffId = login.getStaffId();
        String staffName = login.getStaffName();
        String staffPhone = login.getStaffPhone();
        Editor editor = sp.edit();
        editor.clear();
        editor.putString("shop_longitude", login.getShopLongitude());
        editor.putString("shop_latitude", login.getShopLatitude());
        editor.putString("constantShopAddress", login.getShop_address_detail());
        editor.putString("shopId", shopId);
        editor.putString("shopName", login.getShopName());
        editor.putString("staffAccount", staffAccount);
        editor.putString("userName", account);
        editor.putString("area_dict_num", area_dict_num);
        editor.putString("shopPhone", staffAccount);
        editor.putString("power_add", power_add);
        editor.putString("power_pur", power_pur);
        editor.putString("power_delete", power_delete);
        editor.putString("power_name", power_name);
        editor.putString("power_price", power_price);
        editor.putString("power_count", power_count);
        editor.putString("power_kind", power_kind);
        editor.putString("power_inprice", power_inprice);
        editor.putString("power_supplier", power_supplier);
        editor.putString("managerUnique", managerUnique);
        editor.putString("staffPosition", staffPosition);
        editor.putString("setPowerManager", setPowerManager);
        editor.putString(CONSTANT_SHOP_NAME, shopName);
        editor.putString("staffId", staffId);
        editor.putString("staffName", staffName);
        editor.putString("staffPhone", staffPhone);
        editor.putString("staffPwd", pwd);
        editor.putString("start_denglu", "1");
        editor.putString("staffProtrait", StringUtils.handledImgUrl(login.getStaffProtrait()));
        editor.putInt("manager", login.getManager());//1、超级业务员，0、非超级业务员
        editor.putInt("shop_type", login.getShop_type());
        editor.putString("roleName", login.getRoleName());
        editor.putString("token", token);
        editor.putInt("goods_inprice_type", login.getGoodsInPriceType());//库存管理方式：0-最近入库价，1-移动加权平均，2-先进先出
        editor.putInt("is_toBound_inspect", login.getIsIoBoundInspect());//是否出入库审核：0-否、1-是
        editor.putString("token", login.getToken());
        editor.commit(); //put完毕必需要commit()否则无法保存
        showMessage(getLanguageValue("login") + getLanguageValue("succeed"));
        SharedPreferences mySharedPreferences = getSharedPreferences("test", Activity.MODE_PRIVATE);
        Editor editor2 = mySharedPreferences.edit();
        editor2.putString("exit", "");
        editor2.commit();
        goToActivity(MainActivity.class);
        finish();
    }

    /**
     * 双击退出
     */
    long exitTime = 0;

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK
                && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (System.currentTimeMillis() - exitTime > 2000) {
                showMessage(getLanguageValue("pressAgainExit"));
                exitTime = System.currentTimeMillis();
            } else {
                finish();
                ActivityManager.getInstance().popAllActivity();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
