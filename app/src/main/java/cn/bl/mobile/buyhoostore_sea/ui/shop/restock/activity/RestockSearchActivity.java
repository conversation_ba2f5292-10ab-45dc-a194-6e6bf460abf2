package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter.RestockAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPlanData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-补货单-搜索
 * Created by jingang on 2023/9/7
 */
@SuppressLint("NonConstantResourceId")
public class RestockSearchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.ivScan)
    ImageView ivScan;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keyWords;

    private RestockAdapter mAdapter;
    private List<RestockPlanData> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_search;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("search"));
        etSearch.setHint(getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("replenishment")+getLanguageValue("plan")+getLanguageValue("name"));
        ivScan.setVisibility(View.GONE);
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getRestockPlanList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getRestockPlanList();
    }

    @OnClick({R.id.ivBack, R.id.ivClear})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                page = 1;
                getRestockPlanList();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.RESTOCK_PLAN_LIST:
                //刷新列表
                page = 1;
                getRestockPlanList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new RestockAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new RestockAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(TAG, RestockEditActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getShopRestockplanId()))
                        .putExtra("status", dataList.get(position).getStatus())
                );
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("replenishment")+getLanguageValue("plan")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanDel(dataList.get(position).getShopRestockplanId(), position);
                        });
            }

            @Override
            public void onAgainClick(View view, int position) {
                //再次补货
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("replenishAgain")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanAgain(dataList.get(position).getShopRestockplanId());
                        });
            }

            @Override
            public void onCancelClick(View view, int position) {
                //取消补货
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("cancelReplenish")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postRestockPlanStatus(dataList.get(position).getShopRestockplanId(), 3, position);
                        });
            }

            @Override
            public void onPreviewClick(View view, int position) {
                //预览
                startActivity(new Intent(TAG, RestockPreviewActivity.class));
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRestockPlanList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRestockPlanList();
            }
        });
    }

    /**
     * 补货计价列表
     */
    private void getRestockPlanList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("planName", keyWords);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getRestockPlanList(),
                map,
                RestockPlanData.class,
                new RequestListListener<RestockPlanData>() {
                    @Override
                    public void onResult(List<RestockPlanData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 修改补货计划状态信息
     *
     * @param id
     * @param status   1.未生成 2.已生成 3.已取消
     * @param position
     */
    private void postRestockPlanStatus(int id, int status, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("restockPlanId", id);
        map.put("planStatus", status);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockPlanStatus(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_PLAN_LIST));
                        dataList.get(position).setStatus(status);
                        mAdapter.notifyItemChanged(position, dataList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除补货计划
     *
     * @param id
     * @param position
     */
    private void postRestockPlanDel(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopRestockplanId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockPlanDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_PLAN_LIST));
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() < 1) {
                            page = 1;
                            getRestockPlanList();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 再次补货
     *
     * @param id
     */
    private void postRestockPlanAgain(int id) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopRestockplanId", id);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getRestockPlanAgain(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE && data.getData() != null) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_PLAN_LIST));
                            startActivity(new Intent(TAG, RestockEditActivity.class)
                                    .putExtra("id", DFUtils.getNum((Double) data.getData()))
                                    .putExtra("status", 1)
                            );
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

}
