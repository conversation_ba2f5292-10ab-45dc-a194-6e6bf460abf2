package cn.bl.mobile.buyhoostore_sea.ui.farm.bean;

import java.io.Serializable;

/**
 * Describe:农批首页统计数据（实体类）
 * Created by jingang on 2023/5/28
 */
public class FarmIndexData implements Serializable {
    /**
     * monthSale : 120.04
     * monthLoan : 60.0
     * loanTotal : 60.0
     * todaySale : 0.04
     * todaySaleCount : 2
     * todayLoan : 0.0
     * todayLoanCount : 0
     * todayIncome : 0.04
     * todayIncomeCount : 2
     * todayAgentMoney : 0.0
     */

    private double monthSale;//月销售额
    private double monthLoan;//月欠款总额
    private double loanTotal;//欠款总额
    private double todaySale;//今日销售金额
    private int todaySaleCount;//今日销售订单数
    private double todayLoan;//今日欠款
    private int todayLoanCount;//今日欠款订单数
    private double todayIncome;//今日收款
    private int todayIncomeCount;//今日收款单数
    private double todayAgentMoney;//今日代卖收益

    public double getMonthSale() {
        return monthSale;
    }

    public void setMonthSale(double monthSale) {
        this.monthSale = monthSale;
    }

    public double getMonthLoan() {
        return monthLoan;
    }

    public void setMonthLoan(double monthLoan) {
        this.monthLoan = monthLoan;
    }

    public double getLoanTotal() {
        return loanTotal;
    }

    public void setLoanTotal(double loanTotal) {
        this.loanTotal = loanTotal;
    }

    public double getTodaySale() {
        return todaySale;
    }

    public void setTodaySale(double todaySale) {
        this.todaySale = todaySale;
    }

    public int getTodaySaleCount() {
        return todaySaleCount;
    }

    public void setTodaySaleCount(int todaySaleCount) {
        this.todaySaleCount = todaySaleCount;
    }

    public double getTodayLoan() {
        return todayLoan;
    }

    public void setTodayLoan(double todayLoan) {
        this.todayLoan = todayLoan;
    }

    public int getTodayLoanCount() {
        return todayLoanCount;
    }

    public void setTodayLoanCount(int todayLoanCount) {
        this.todayLoanCount = todayLoanCount;
    }

    public double getTodayIncome() {
        return todayIncome;
    }

    public void setTodayIncome(double todayIncome) {
        this.todayIncome = todayIncome;
    }

    public int getTodayIncomeCount() {
        return todayIncomeCount;
    }

    public void setTodayIncomeCount(int todayIncomeCount) {
        this.todayIncomeCount = todayIncomeCount;
    }

    public double getTodayAgentMoney() {
        return todayAgentMoney;
    }

    public void setTodayAgentMoney(double todayAgentMoney) {
        this.todayAgentMoney = todayAgentMoney;
    }
}
