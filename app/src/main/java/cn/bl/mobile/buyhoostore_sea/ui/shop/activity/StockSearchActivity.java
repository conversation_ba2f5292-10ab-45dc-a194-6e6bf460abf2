package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.jaredrummler.materialspinner.MaterialSpinner;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsKuSearchBean;
import cn.bl.mobile.buyhoostore_sea.bean.ShopBeanDa;
import cn.bl.mobile.buyhoostore_sea.bean.XiaoLeiBean;
import cn.bl.mobile.buyhoostore_sea.adapter.KuCunSearchAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-库存-库存查询
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class StockSearchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;

    @BindView(R.id.tvCount)
    TextView tvCount;
    @BindView(R.id.tvCountValue)
    TextView tvCountValue;
    @BindView(R.id.tvInCount)
    TextView tvInCount;
    @BindView(R.id.tvInValue)
    TextView tvInValue;
    @BindView(R.id.tvWarning)
    TextView tvWarning;
    @BindView(R.id.tvWarningValue)
    TextView tvWarningValue;
    @BindView(R.id.tvSaleMoney)
    TextView tvSaleMoney;
    @BindView(R.id.tvSaleMoneyValue)
    TextView tvSaleMoneyValue;

    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;

    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvStockValue)
    TextView tvStockValue;
    @BindView(R.id.tvInPriceValue)
    TextView tvInPriceValue;
    @BindView(R.id.tvSalePriceValue)
    TextView tvSalePriceValue;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private KuCunSearchAdapter mAdapter;
    private List<GoodsKuSearchBean.DataBean.GoodsListBean> dataList = new ArrayList<>();

    public List<String> list_classda = new ArrayList<>(),
            list_classxiao = new ArrayList<>();
    private MaterialSpinner shop_datype, shop_xiaotype;
    private String goods_kind_unique = "-1";
    private String goods_kind_parunique = "-1";
    private String message = "";
    private String orderType = "2"; //1升序  2降序
    private ImageView img_order;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_stock_search;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            message = etSearch.getText().toString();
            page = 1;
            getshopmessage();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                message = s.toString().trim();
                if (TextUtils.isEmpty(message)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        inintView();
        setAdapter();
    }

    @OnClick({R.id.ivBack, R.id.linWarning, R.id.ivClear, R.id.ivScan, R.id.lin_orderType})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linWarning:
                Intent intent = new Intent(this, StockYuJingActivity.class);
                intent.putExtra("message", message);
                intent.putExtra("goods_kind_unique", goods_kind_parunique);
                intent.putExtra("goods_kind_parunique", goods_kind_unique);
                startActivity(intent);
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                page = 1;
                getshopmessage();
                break;
            case R.id.ivScan:
                //扫一哈子
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.lin_orderType:
                if (orderType.equals("2")) {
                    message = etSearch.getText().toString();
                    img_order.setBackgroundResource(R.mipmap.ic_arrow005);
                    orderType = "1";
                    page = 1;
                    getshopmessage();
                } else if (orderType.equals("1")) {
                    message = etSearch.getText().toString();
                    img_order.setImageResource(R.mipmap.ic_arrow004);
                    orderType = "2";
                    page = 1;
                    getshopmessage();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("inventory")+getLanguageValue("inquire"));
        tvCountValue.setText(getLanguageValue("inventory")+getLanguageValue("quantity"));
        tvInValue.setText(getLanguageValue("inventory")+getLanguageValue("cost"));
        tvWarningValue.setText(getLanguageValue("inventory")+getLanguageValue("warn"));
        tvSaleMoneyValue.setText(getLanguageValue("inventory")+getLanguageValue("outputValue"));
        etSearch.setHint(getLanguageValue("search")+getLanguageValue("commodity")+getLanguageValue("name")+"/"+getLanguageValue("barcode"));
        tvNameValue.setText(getLanguageValue("commodity")+getLanguageValue("name"));
        tvStockValue.setText(getLanguageValue("inventory")+getLanguageValue("quantity"));
        tvInPriceValue.setText(getLanguageValue("procurement")+getLanguageValue("price"));
        tvSalePriceValue.setText(getLanguageValue("sellingPrice"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new KuCunSearchAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setEnableAutoLoadMore(true);////是否启用列表惯性滑动到底部时自动加载更多
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getshopmessage();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getshopmessage();
        });
    }

    private void inintView() {
        shop_datype = (MaterialSpinner) findViewById(R.id.shop_datype);
        shop_xiaotype = (MaterialSpinner) findViewById(R.id.shop_xiaotype);
        img_order = (ImageView) findViewById(R.id.img_order);
        getClassifyda();
        getshopmessage();
    }


    /**
     * 获取大类
     */
    public void getClassifyda() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("status", 1);//1.启用 2.禁用
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsCate(),
                params,
                ShopBeanDa.DataBean.class,
                new RequestListListener<ShopBeanDa.DataBean>() {
                    @Override
                    public void onResult(List<ShopBeanDa.DataBean> list) {
                        List<String> ShopAllName = new ArrayList<>();
                        ShopAllName.add(0, "商品大类");
                        for (int t = 0; t < list.size(); t++) {
                            ShopAllName.add(list.get(t).getGroupName());
                        }
                        list_classda.add(0, "-1");
                        for (int f = 0; f < list.size(); f++) {
                            list_classda.add(list.get(f).getGroupUnique());  //获取大分类编号
                        }
                        int t = list.size();
                        if (t > 0) {
                            String[] classifyNameArr = (String[]) ShopAllName.toArray(new String[t]);
                            shop_datype.setItems(classifyNameArr);
                        }
                        shop_datype.setOnItemSelectedListener((view, position, id, item) -> {
                            goods_kind_unique = list_classda.get(position);
                            getClassifyxiao();
                        });
                        getClassifyxiao();
                    }
                });
    }

    /**
     * 获取小类
     */
    public void getClassifyxiao() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("groupUnique", goods_kind_unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getsmalltype(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "获取小类 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        int status4 = 2;
                        JSONObject object4;
                        try {
                            object4 = new JSONObject(s);
                            status4 = object4.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status4 == 1) {
                            XiaoLeiBean xiaoLeiBean = new Gson().fromJson(s, XiaoLeiBean.class);
                            List<String> ShopAllName = new ArrayList<>();
                            ShopAllName.add(0, getLanguageValue("commodity")+getLanguageValue("subclass"));
                            for (int t = 0; t < xiaoLeiBean.getData().size(); t++) {
                                ShopAllName.add(xiaoLeiBean.getData().get(t).getKindName());
                            }
                            list_classxiao.clear();
                            list_classxiao.add(0, "-1");
                            for (int f = 0; f < xiaoLeiBean.getData().size(); f++) {
                                list_classxiao.add(xiaoLeiBean.getData().get(f).getKindUnique() + "");  //获取xiao分类编号
                            }
                            int t = xiaoLeiBean.getData().size();

                            if (t > 0) {
                                String[] classifyNameArr = (String[]) ShopAllName.toArray(new String[t]);
                                shop_xiaotype.setItems(classifyNameArr);
                            } else if (t == 0) {
                                String[] classifyNameArr = (String[]) ShopAllName.toArray(new String[t]);
                                shop_xiaotype.setItems(classifyNameArr);
                            }
                            goods_kind_parunique = list_classxiao.get(0);
                            shop_xiaotype.setOnItemSelectedListener((view, position, id, item) -> {
                                goods_kind_parunique = list_classxiao.get(position);
                                page = 1;
                                getshopmessage();
                            });
                            page = 1;
                            getshopmessage();
                        }
                    }
                });
    }

    /**
     * 库存
     */
    public void getshopmessage() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsMessage", message);
        params.put("goods_kind_unique", goods_kind_parunique);
        params.put("goods_kind_parunique", goods_kind_unique);
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        params.put("orderType", orderType);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getkucun(),
                params,
                GoodsKuSearchBean.DataBean.class,
                new RequestListener<GoodsKuSearchBean.DataBean>() {
                    @Override
                    public void success(GoodsKuSearchBean.DataBean data) {
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (data.getGoods_list().size() == 0) {
                            return;
                        }
                        tvCount.setText(data.getGoods_count_sum());
                        tvInCount.setText(data.getGoods_in_price_sum());
                        tvWarning.setText(data.getStock_warning_num());
                        tvSaleMoney.setText(data.getGoods_sale_price_sum());
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(data.getGoods_list());
                        mAdapter.setDataList(dataList);
                    }

                    @Override
                    public void onError(String msg) {
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                            tvCount.setText("0");
                            tvInCount.setText("0");
                            tvWarning.setText("0");
                            tvSaleMoney.setText("0");
                        }
                    }
                });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    etSearch.setText(data.getStringExtra("result"));
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("keywords"));
                    message = data.getStringExtra("result");
                    etSearch.setText(message);
                    page = 1;
                    getshopmessage();
                    break;
            }
        }
    }

}