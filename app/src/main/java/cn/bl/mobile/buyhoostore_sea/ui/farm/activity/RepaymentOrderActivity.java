package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.RepaymentOrderAdapter;

/**
 * Describe:农批-快速还款-按单还款
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class RepaymentOrderActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvCountTotal)
    TextView tvCountTotal;
    @BindView(R.id.tvPriceTotal)
    TextView tvPriceTotal;
    @BindView(R.id.tvChoose)
    TextView tvChoose;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.ivAll)
    ImageView ivAll;

    private RepaymentOrderAdapter mAdapter;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_repayment_order;
    }

    @Override
    public void initViews() {
        tvTitle.setText("按单还款");
        setAdapter();
    }

    @OnClick({R.id.ivBack, R.id.ivAll, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivAll:
                //全选
                break;
            case R.id.tvConfirm:
                //还款
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new RepaymentOrderAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
    }
}
