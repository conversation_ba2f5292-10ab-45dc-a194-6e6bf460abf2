package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:补货单-预览（实体类）
 * Created by jingang on 2023/9/8
 */
public class RestockPreviewData implements Serializable {
    /**
     * shopRestockplanPresentId : 12
     * shopUnique : 1536215939565
     * shopRestockplanId : 19
     * supplierUnique : 4042326458752
     * remark : null
     * createUser : 3586
     * createTime : 2023-09-08 15:39:40
     * supplierName : 东方红
     * supplierPhone : 11011011011
     * goodsTotal : 326.25
     * goodsCounts : 1
     * goodsList : [{"shopRestockplanGoodsId":12,"goodsPicturepath":"","goodsName":"长城干红葡萄酒","goodsCount":3,"goodsInPrice":null,"goodsUnit":null,"goodsTotal":326.25,"goodsBarcode":"16901009906076"}]
     */

    private int shopRestockplanPresentId;
    private String shopUnique;
    private int shopRestockplanId;
    private String supplierUnique;
    private String remark;
    private String createUser;
    private String createTime;
    private String supplierName;
    private String supplierPhone;
    private double goodsTotal;
    private int goodsCounts;
    private int purchaseType;//采购类型:1-购销2自采
    private List<GoodsListBean> goodsList;

    public int getShopRestockplanPresentId() {
        return shopRestockplanPresentId;
    }

    public void setShopRestockplanPresentId(int shopRestockplanPresentId) {
        this.shopRestockplanPresentId = shopRestockplanPresentId;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public int getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(int shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public double getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(double goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public int getGoodsCounts() {
        return goodsCounts;
    }

    public void setGoodsCounts(int goodsCounts) {
        this.goodsCounts = goodsCounts;
    }

    public int getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(int purchaseType) {
        this.purchaseType = purchaseType;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public static class GoodsListBean {
        /**
         * shopRestockplanGoodsId : 12
         * goodsPicturepath :
         * goodsName : 长城干红葡萄酒
         * goodsCount : 3.0
         * goodsInPrice : null
         * goodsUnit : null
         * goodsTotal : 326.25
         * goodsBarcode : 16901009906076
         */

        private int shopRestockplanGoodsId;
        private String goodsPicturepath;
        private String goodsName;
        private double goodsCount;
        private double goodsInPrice;
        private String goodsUnit;
        private double goodsTotal;
        private String goodsBarcode;

        public int getShopRestockplanGoodsId() {
            return shopRestockplanGoodsId;
        }

        public void setShopRestockplanGoodsId(int shopRestockplanGoodsId) {
            this.shopRestockplanGoodsId = shopRestockplanGoodsId;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public double getGoodsInPrice() {
            return goodsInPrice;
        }

        public void setGoodsInPrice(double goodsInPrice) {
            this.goodsInPrice = goodsInPrice;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public double getGoodsTotal() {
            return goodsTotal;
        }

        public void setGoodsTotal(double goodsTotal) {
            this.goodsTotal = goodsTotal;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }
    }
}
