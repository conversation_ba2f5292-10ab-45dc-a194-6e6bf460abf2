package cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity;

import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.content.Intent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.NameEditDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.adapter.PanLocationAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.bean.PanLocationData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:盘点-货位列表
 * Created by jingang on 2023/6/21
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class PanLocationActivity extends BaseActivity {
    private PanLocationActivity TAG = PanLocationActivity.this;
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvAdd)
    TextView tvAdd;

    private PanLocationAdapter mAdapter;
    private List<PanLocationData.GoodsLocationListBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_pan_location;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("cargoSpace")+getLanguageValue("list"));
        tvAdd.setText(getLanguageValue("add")+getLanguageValue("cargoSpace"));
        setAdapter();
    }

    @Override
    public void initData() {
        getPanLocationList();
    }

    @OnClick({R.id.ivBack, R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvAdd:
                //新增
                NameEditDialog.showDialog(this,
                        getLanguageValue("cargoSpace")+getLanguageValue("name"),
                        "",
                        getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("cargoSpace")+getLanguageValue("name"),
                        (view1, content) -> {
                            postLocationAdd(content);
                        });
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new PanLocationAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getPanLocationList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getPanLocationList();
            }
        });
        mAdapter.setListener(new PanLocationAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //选择
                setResult(Constants.CHOOSE_LOCATION, new Intent()
                        .putExtra("id", dataList.get(position).getLocationId())
                        .putExtra("name", dataList.get(position).getLocationName())
                );
                TAG.finish();
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                NameEditDialog.showDialog(TAG,
                        getLanguageValue("cargoSpace")+getLanguageValue("name"),
                        dataList.get(position).getLocationName(),
                        getLanguageValue("please")+getLanguageValue("input")+getLanguageValue("cargoSpace")+getLanguageValue("name"),
                        (view1, content) -> {
                            postLocationEdit(dataList.get(position).getLocationId(), content, position);
                        });
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(TAG,
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("cargoSpace")+"?",
                        getLanguageValue("confirm"),
                        (DialogInterface.OnClickListener) (dialog, which) -> {
                            postLocationDel(dataList.get(position).getLocationId(), position);
                        });
            }
        });
    }

    /**
     * 货位列表
     */
    private void getPanLocationList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskGoodsLocationList(),
                map,
                PanLocationData.class,
                new RequestListener<PanLocationData>() {
                    @Override
                    public void success(PanLocationData panLocationData) {
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(panLocationData.getGoodsLocationList());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 货位新增
     */
    private void postLocationAdd(String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("locationName", name);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskGoodsLocationAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        page = 1;
                        getPanLocationList();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 修改货位
     */
    private void postLocationEdit(String id, String name, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("locationId", id);
        map.put("locationName", name);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskGoodsLocationUpdate(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        dataList.get(position).setLocationName(name);
                        mAdapter.notifyItemChanged(position, dataList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 删除货位
     */
    private void postLocationDel(String id, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("staffId", getStaff_id());
        map.put("locationId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getTaskGoodsLocationDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }
}
