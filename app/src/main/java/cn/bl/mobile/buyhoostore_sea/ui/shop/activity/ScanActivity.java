package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.camera.view.PreviewView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.zxing.DecodeHintType;
import com.google.zxing.Result;
import com.king.camera.scan.AnalyzeResult;
import com.king.camera.scan.BaseCameraScan;
import com.king.camera.scan.CameraScan;
import com.king.view.viewfinderview.ViewfinderView;
import com.king.zxing.DecodeConfig;
import com.king.zxing.DecodeFormatManager;
import com.king.zxing.analyze.MultiFormatAnalyzer;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.SearchUniqueBean;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsQuickAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.GoodsListScanAdapter;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:扫码
 * Created by jingang on 2023/2/1
 */
@SuppressLint("NonConstantResourceId")
public final class ScanActivity extends BaseActivity implements CameraScan.OnScanResultCallback<Result> {
    @BindView(R.id.tvHistory)
    TextView tvHistory;
    @BindView(R.id.previewView)
    PreviewView previewView;
    @BindView(R.id.viewfinderView)
    ViewfinderView viewfinderView;
    @BindView(R.id.tvTips)
    TextView tvTips;
    @BindView(R.id.ivLight)
    ImageView ivLight;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.rvGoods)
    RecyclerView rvGoods;

    //商品列表
    private GoodsListScanAdapter mAdapter;
    private List<SearchUniqueBean.DataBean.ListDetailBean> dataList = new ArrayList<>();

    private int type,//类型 0.扫码返回 1.出入库 2.速录
            scanType;//0.条形码 1.二维码 2.全部

    /**
     * CameraScan
     */
    private CameraScan<Result> mCameraScan;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_scan;
    }

    @Override
    public void initViews() {
        setStatusBar(false);
        Window window = getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        //取消设置透明状态栏,使 ContentView 内容不再覆盖状态栏
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        //需要设置这个 flag 才能调用 setStatusBarColor 来设置状态栏颜色
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        //设置状态栏颜色
        window.setStatusBarColor(getResources().getColor(R.color.color_333));
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        etSearch.addTextChangedListener(new TextWatcher() {

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString())) {
                    ivClear.setVisibility(View.GONE);
                    dataList.clear();
                    mAdapter.clear();
                    rvGoods.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            //此方法会走两次 不知道啥原因
            if (!isQuicklyClick()) {
                String code = etSearch.getText().toString();
                if (TextUtils.isEmpty(code)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("commodity")+getLanguageValue("name")+"/"+getLanguageValue("barcode"));
                    dataList.clear();
                    mAdapter.clear();
                    rvGoods.setVisibility(View.GONE);
                } else {
                    page = 1;
                    getUpdate(code);
                }
            }
            return false;
        });

        if (PermissionUtils.checkPermissionsGroup(this, 0)) {
            initScan();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 0, type -> {
                if (type != 0) {
                    finish();
                }
            });
        }
//        initScan();

        type = getIntent().getIntExtra("type", 0);
        scanType = getIntent().getIntExtra("scanType", 0);
        if (type == 1) {
            tvHistory.setVisibility(View.VISIBLE);
        } else {
            tvHistory.setVisibility(View.GONE);
        }

        setGoodsAdapter();
    }

    @OnClick({R.id.ivBack, R.id.tvHistory, R.id.ivClear, R.id.ivLight})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvHistory:
                //出入库历史
                goToActivity(ChuRuHistoryActivity.class);
                break;
            case R.id.ivClear:
                //清除搜索内容
                etSearch.setText("");
                break;
            case R.id.ivLight:
                //闪光灯
                if (mCameraScan != null) {
                    boolean isTorch = mCameraScan.isTorchEnabled();
                    mCameraScan.enableTorch(!isTorch);
                    if (ivLight != null) {
                        ivLight.setSelected(!isTorch);
                    }
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvHistory.setText(getLanguageValue("inAndOutOfTheWarehouse") + getLanguageValue("history"));
        tvTips.setText(getLanguageValue("barcodeScan"));
        etSearch.setHint(getLanguageValue("input") + getLanguageValue("commodity") + getLanguageValue("barcode") + "/" + getLanguageValue("commodity") + getLanguageValue("name"));
    }

    /**
     * 设置适配器
     */
    private void setGoodsAdapter() {
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new GoodsListScanAdapter(this);
        rvGoods.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            setResult(dataList.get(position).getGoodsBarcode());
        });
    }

    /**
     * 扫码结果回调
     *
     * @param result
     */
    @Override
    public void onScanResultCallback(@NonNull AnalyzeResult<Result> result) {
        if (mCameraScan != null) {
            mCameraScan.setAnalyzeImage(false);
        }
        setResult(result.getResult().getText());
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.e(tag, "mCameraScan = " + mCameraScan);
        if (mCameraScan != null) {
            mCameraScan.setAnalyzeImage(true);
        }
    }

    @Override
    protected void onDestroy() {
        if (mCameraScan != null) {
            mCameraScan.release();
        }
        super.onDestroy();
    }

    /**
     * 初始化扫码
     */
    public void initScan() {
        viewfinderView.setFrameWidth(DensityUtils.getScreenWidth(this) - 100);//扫描框宽度
        viewfinderView.setFrameHeight(DensityUtils.dip2px(this, 200));//扫描框高度
        viewfinderView.setFrameColor(getResources().getColor(R.color.transparent));//边框颜色
        viewfinderView.setFrameCornerColor(getResources().getColor(R.color.red));//边角颜色
        viewfinderView.setFrameCornerStrokeWidth(3);//边角宽度
        viewfinderView.setLaserColor(getResources().getColor(R.color.red));
        mCameraScan = new BaseCameraScan<>(this, previewView);
        // 初始化解码配置
        DecodeConfig decodeConfig = new DecodeConfig();
        Map<DecodeHintType, Object> hints;
        //0.条形码 1.二维码 2.全部
        switch (scanType) {
            case 1:
                hints = DecodeFormatManager.TWO_DIMENSIONAL_HINTS;
                break;
            case 2:
                hints = DecodeFormatManager.DEFAULT_HINTS;
                break;
            default:
                hints = DecodeFormatManager.ONE_DIMENSIONAL_HINTS;
                break;
        }
        decodeConfig
                .setHints(hints)//解码方式
                .setSupportVerticalCode(true)//设置是否支持扫垂直的条码
                .setSupportLuminanceInvert(true) // 设置是否支持识别反色码，黑白颜色反转
                .setFullAreaScan(true);//设置是否全区域识别，默认false
        mCameraScan.setAnalyzer(new MultiFormatAnalyzer(decodeConfig))
//                .bindFlashlightView(ivLight)//绑定手电筒
                .setOnScanResultCallback(this)//扫描结果回调
                .setPlayBeep(true);//提示音
        mCameraScan.startCamera();
    }

    /**
     * 继续分析
     */
    private void resumeScan() {
        new Handler().postDelayed(() -> {
            if (mCameraScan != null) {
                mCameraScan.setAnalyzeImage(true);
            }
        }, 1000);
    }

    /**
     * 得到商品条码（扫码或搜索）
     *
     * @param code
     */
    private void setResult(String code) {
        switch (type) {
            case 0:
                setResult(Constants.SCAN, new Intent()
                        .putExtra("result", code)
                );
                this.finish();
                break;
            case 1:
                startActivity(new Intent(this, ChuRuSelectActivity.class)
                        .putExtra("result", code)
                );
                break;
            case 2:
                //速录
                startActivity(new Intent(this, GoodsQuickAddActivity.class)
                        .putExtra("goodsBarcode", code)
                );
                break;
        }
    }

    /**
     * 商品列表（出入库）
     *
     * @param code
     */
    public void getUpdate(String code) {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", code);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getgoodsname(),
                params,
                SearchUniqueBean.DataBean.class,
                new RequestListListener<SearchUniqueBean.DataBean>() {
                    @Override
                    public void onResult(List<SearchUniqueBean.DataBean> data) {
                        hideDialog();
                        if (data == null) {
                            return;
                        }
                        dataList.clear();
                        for (int i = 0; i < data.size(); i++) {
                            dataList.addAll(data.get(i).getListDetail());
                        }
                        if (dataList.size() > 0) {
                            rvGoods.setVisibility(View.VISIBLE);
                            mAdapter.setDataList(dataList);
                        } else {
                            rvGoods.setVisibility(View.GONE);
                            showMessage(getLanguageValue("noSearchResult"));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                    finish();
                } else {
                    initScan();
                }
                break;
        }
    }

}