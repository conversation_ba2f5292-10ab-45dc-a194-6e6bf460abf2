package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.adapter.RepaymentAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.FarmRepaymentTypeDialog;

/**
 * Describe:农批-快速还款
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class RepaymentActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keywords;

    private RepaymentAdapter mAdapter;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_repayment;
    }

    @Override
    public void initViews() {
        tvTitle.setText("快速还款");
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            keywords = v.getText().toString().trim();
            return true;
        });
        setAdapter();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new RepaymentAdapter(this);
        recyclerView.setAdapter(mAdapter);
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishLoadMore();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                smartRefreshLayout.finishRefresh();
            }
        });
        mAdapter.setOnItemClickListener((view, position) -> {
            //还款
            FarmRepaymentTypeDialog.showDialog(this, (view1, type) -> {
                Log.e(tag, "还款方式 = " + type);
                startActivity(new Intent(this, RepaymentOrderActivity.class));
            });
        });
    }
}
