package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean;

import java.io.Serializable;

/**
 * Describe:补货单-商品列表（实体类）
 * Created by jingang on 2023/9/7
 */
public class RestockGoodsData implements Serializable {
    /**
     * shopRestockplanGoodsId : 7
     * goodsPicturepath : null
     * goodsName : 猫王抽纸
     * goodsCount : 40.0
     * goodsInPrice : 7.14
     * goodsUnit : 包
     * goodsTotal : 285.6
     * goodsBarcode : 6923083028089
     */

    private int shopRestockplanGoodsId;
    private String goodsPicturepath;
    private String goodsName;
    private double goodsCount;//数量
    private double goodsInPrice;//单价
    private String goodsUnit;
    private double goodsTotal;//总价
    private String goodsBarcode;

    public int getShopRestockplanGoodsId() {
        return shopRestockplanGoodsId;
    }

    public void setShopRestockplanGoodsId(int shopRestockplanGoodsId) {
        this.shopRestockplanGoodsId = shopRestockplanGoodsId;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(double goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public double getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(double goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }
}
