package cn.bl.mobile.buyhoostore_sea.printer;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.bean.BluetoothParameter;

/**
 * Describe:蓝牙列表（适配器）
 * Created by jingang on 2023/3/21
 */
public class BluetoothDeviceAdapter extends BaseAdapter<BluetoothParameter> {
    private String mac;

    @SuppressLint("NotifyDataSetChanged")
    public void setMac(String mac) {
        this.mac = mac;
        notifyDataSetChanged();
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public BluetoothDeviceAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_bluetooth_device;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName, tvStatus, tvMac;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvMac = holder.getView(R.id.tvItemMac);
        if (TextUtils.equals(mac, mDataList.get(position).getBluetoothMac())) {
            tvStatus.setVisibility(View.VISIBLE);
            tvName.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvMac.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvStatus.setVisibility(View.GONE);
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvMac.setTextColor(mContext.getResources().getColor(R.color.color_333));
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> {
                onItemClickListener.onItemClick(holder.itemView, position);
            });
        }
        TextView tvName, tvStatus, tvMac;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvMac = holder.getView(R.id.tvItemMac);

        tvName.setText(mDataList.get(position).getBluetoothName());
        tvMac.setText(mDataList.get(position).getBluetoothMac());
        if (TextUtils.equals(mac, mDataList.get(position).getBluetoothMac())) {
            tvStatus.setVisibility(View.VISIBLE);
            tvName.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvMac.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvStatus.setVisibility(View.GONE);
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvMac.setTextColor(mContext.getResources().getColor(R.color.color_333));
        }
    }
}
