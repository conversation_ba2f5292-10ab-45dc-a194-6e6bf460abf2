package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.ShopInfoResponseModel;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.TimeDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-设置-营业时间
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SetShopTimeActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvDate)
    TextView tvDate;

    public static String startTime, endTime;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_set_shop_time;
    }

    @Override
    public void initViews() {
        tvTitle.setText("营业时间设置");
    }

    @Override
    public void initData() {
        getShopInfo();
    }

    @OnClick({R.id.ivBack, R.id.tvDate})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvDate:
                //选择时间
                TimeDialog.showDialog(this, startTime, endTime, (startDate, endDate) -> {
                    startTime = startDate;
                    endTime = endDate;
                    postShopInfo();
                });
                break;
        }
    }

    //获取店铺信息
    public void getShopInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getShopInfoUrlTWO(),
                map,
                ShopInfoResponseModel.DataBean.class,
                new RequestListener<ShopInfoResponseModel.DataBean>() {
                    @Override
                    public void success(ShopInfoResponseModel.DataBean data) {
                        if (!TextUtils.isEmpty(data.getShopHours())) {
                            tvDate.setText(data.getShopHours());
                            String[] times = data.getShopHours().split("-");
                            if (times.length == 2) {
                                startTime = times[0];
                                endTime = times[1];
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 店铺信息修改
     */
    private void postShopInfo() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("shopHours", startTime + "-" + endTime);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateInfoUrlTWO(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        tvDate.setText(startTime + "-" + endTime);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });

    }

}

