package cn.bl.mobile.buyhoostore_sea.ui.shop.bean;

import java.io.Serializable;

/**
 * Describe:店铺配送设置（实体类）
 * Created by jingang on 2023/11/1
 */
public class ShopDeliveryData implements Serializable {
    /**
     * shop_unique : 1536215939565
     * distribution_scope : 10.0
     * take_estimate_time : 0
     * is_order_taking : 1
     * delivery_type : 2
     * take_fee : 5.0
     * subsidy_delivery_price : 0.0
     * take_free_price : -60.0
     * shop_take_price : 0.0
     */

    private String shop_unique;
    private double distribution_scope;//配送范围，单位KM
    private double take_estimate_time;//预计店铺配送时常：单位分钟
    private int is_order_taking;
    private int delivery_type;//配送方式选择：0、自；1、美团；2、一刻钟配送
    private double take_fee;//起送费，超过这个金额才配送
    private double subsidy_delivery_price;
    private double take_free_price;//免配送费，订单超过这个金额，配送费店铺承担
    private double shop_take_price;//商家自配送，收取客户的配送费

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public double getDistribution_scope() {
        return distribution_scope;
    }

    public void setDistribution_scope(double distribution_scope) {
        this.distribution_scope = distribution_scope;
    }

    public double getTake_estimate_time() {
        return take_estimate_time;
    }

    public void setTake_estimate_time(double take_estimate_time) {
        this.take_estimate_time = take_estimate_time;
    }

    public int getIs_order_taking() {
        return is_order_taking;
    }

    public void setIs_order_taking(int is_order_taking) {
        this.is_order_taking = is_order_taking;
    }

    public int getDelivery_type() {
        return delivery_type;
    }

    public void setDelivery_type(int delivery_type) {
        this.delivery_type = delivery_type;
    }

    public double getTake_fee() {
        return take_fee;
    }

    public void setTake_fee(double take_fee) {
        this.take_fee = take_fee;
    }

    public double getSubsidy_delivery_price() {
        return subsidy_delivery_price;
    }

    public void setSubsidy_delivery_price(double subsidy_delivery_price) {
        this.subsidy_delivery_price = subsidy_delivery_price;
    }

    public double getTake_free_price() {
        return take_free_price;
    }

    public void setTake_free_price(double take_free_price) {
        this.take_free_price = take_free_price;
    }

    public double getShop_take_price() {
        return shop_take_price;
    }

    public void setShop_take_price(double shop_take_price) {
        this.shop_take_price = shop_take_price;
    }
}
