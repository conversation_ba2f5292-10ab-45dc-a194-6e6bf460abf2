package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.ShopSwitchAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.ShopData;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-切换店铺
 * Created by jingang on 2023/3/3
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class ShopSwitchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String keyWords, managerUnique, staffPosition;
    private ArrayList<ShopData> dataList = new ArrayList<>();
    private ShopSwitchAdapter mAdapter;
    private SharedPreferences sp;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_shop_switch;
    }

    @Override
    public void initViews() {
        tvTitle.setText(getLanguageValue("shop")+getLanguageValue("choose"));
        etSearch.setHint(getLanguageValue("enterStoreNamePhone"));
        sp = getSharedPreferences(Constants.SP_SHOP, MODE_PRIVATE);
        managerUnique = sp.getString("managerUnique", "");
        staffPosition = sp.getString("staffPosition", "");

        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getShopList();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });

        setAdapter();
    }

    @Override
    public void initData() {
        getShopList();
    }

    @OnClick({R.id.ivBack, R.id.ivClear})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入框输入
                etSearch.setText("");
                page = 1;
                getShopList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new ShopSwitchAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            SharedPreferences.Editor editor = sp.edit();
            if (dataList.get(position).getStaffList() != null && dataList.get(position).getStaffList().size() > 0) {
                editor.putString("staffName", dataList.get(position).getStaffList().get(0).getStaffName());
                editor.putString("shopPhone", dataList.get(position).getStaffList().get(0).getStaffAccount());
                editor.putString("staffAccount", dataList.get(position).getStaffList().get(0).getStaffAccount());
            }
            editor.putString("shopId", dataList.get(position).getShopUnique());
            editor.putString("shopName", dataList.get(position).getShopName());
            editor.putString("area_dict_num", dataList.get(position).getArea_dict_num());
            editor.putString(LoginActivity.CONSTANT_SHOP_NAME, dataList.get(position).getShopName());
            editor.putString(Constants.CONSTANT_SHOP_ADDRESS, dataList.get(position).getShopAddress());
            editor.commit();
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.CHOOSE_SHOP));
            finish();
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getShopList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getShopList();
            }
        });
    }


    /**
     * 管理员获取店铺列表
     */
    private void getShopList() {
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("managerUnique", managerUnique);
        map.put("shopUnique", getShop_id());
        map.put("staffPosition", staffPosition);
        map.put("shop_name", keyWords);
        map.put("pageNum", page);
        map.put("pageSize", Constants.limit);
//        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
//                ZURL.getshoplistTWO(),
//                map,
//                new RequestListener<String>() {
//                    @Override
//                    public void success(String s) {
//                        LogLongUtils.e(tag,"s = "+s);
//                    }
//                });
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getshoplistTWO(),
                map,
                ShopData.class,
                new RequestListListener<ShopData>() {
                    @Override
                    public void onResult(List<ShopData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            mAdapter.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}