package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.StringData;

/**
 * Describe:string-适用于条件筛选（适配器）
 * Created by jingang on 2023/5/24
 */
public class StringAdapter extends BaseAdapter<StringData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public StringAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_screen_string;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getName());
        if (mDataList.get(position).isCheck()) {
            tvName.setBackgroundResource(R.drawable.shape_blue_tm16_4);
            tvName.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvName.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_f2_4);
            tvName.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
    }
}
