package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierPaymentInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierPaymentRecordAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierPaymentData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商详情-付款记录
 * Created by jingang on 2023/9/4
 */
@SuppressLint("NonConstantResourceId")
public class SupplierPaymentFragment extends LazyBaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String supplierUnique;

    private SupplierPaymentRecordAdapter mAdapter;
    private List<SupplierPaymentData> dataList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SupplierPaymentFragment newInstance(String unique) {
        SupplierPaymentFragment fragment = new SupplierPaymentFragment();
        Bundle bundle = new Bundle();
        bundle.putString("unique", unique);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout_top16;
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        assert getArguments() != null;
        supplierUnique = getArguments().getString("unique");
        getPaymentList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_LIST:
                page = 1;
                getPaymentList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new SupplierPaymentRecordAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            startActivity(new Intent(getActivity(), SupplierPaymentInfoActivity.class)
                    .putExtra("unique", supplierUnique)
                    .putExtra("id", String.valueOf(dataList.get(position).getPaymentId()))
            );
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getPaymentList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getPaymentList();
            }
        });
    }

    /**
     * 付款记录
     */
    private void getPaymentList() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getSupplierInfoPayment(),
                map,
                SupplierPaymentData.class,
                new RequestListListener<SupplierPaymentData>() {
                    @Override
                    public void onResult(List<SupplierPaymentData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
