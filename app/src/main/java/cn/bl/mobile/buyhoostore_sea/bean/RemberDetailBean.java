package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Created by Administrator on 2017/10/16 0016.
 */
public class RemberDetailBean {


    /**
     * status : 1
     * msg :
     * totals : 0
     * perPageNum : 0
     * data : {"cusWeixin":"","cusName":"big","cusRegeditDate":"2017-1-1","cusBirthday":"2001-01-01","cusUnique":"1002","cusQQ":"","cusPhone":"13791054485","cusHeadPath":"upload/no_goodsB.jpg","cusEmail":"","cusSex":"1","cusUsePoints":0,"cusId":6,"shopUnique":8302016134121,"totalPoints":0,"cusPoints":0,"cusTotal":0}
     * supData : null
     * shopMembership : null
     * addMembership : null
     * heji : null
     */

    private int status;
    private String msg;
    private int totals;
    private int perPageNum;
    private DataBean data;
    private Object supData;
    private Object shopMembership;
    private Object addMembership;
    private Object heji;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getTotals() {
        return totals;
    }

    public void setTotals(int totals) {
        this.totals = totals;
    }

    public int getPerPageNum() {
        return perPageNum;
    }

    public void setPerPageNum(int perPageNum) {
        this.perPageNum = perPageNum;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getSupData() {
        return supData;
    }

    public void setSupData(Object supData) {
        this.supData = supData;
    }

    public Object getShopMembership() {
        return shopMembership;
    }

    public void setShopMembership(Object shopMembership) {
        this.shopMembership = shopMembership;
    }

    public Object getAddMembership() {
        return addMembership;
    }

    public void setAddMembership(Object addMembership) {
        this.addMembership = addMembership;
    }

    public Object getHeji() {
        return heji;
    }

    public void setHeji(Object heji) {
        this.heji = heji;
    }

    public static class DataBean {
        /**
         * cusWeixin :
         * cusName : big
         * cusRegeditDate : 2017-1-1
         * cusBirthday : 2001-01-01
         * cusUnique : 1002
         * cusQQ :
         * cusPhone : 13791054485
         * cusHeadPath : upload/no_goodsB.jpg
         * cusEmail :
         * cusSex : 1
         * cusUsePoints : 0.0
         * cusId : 6
         * shopUnique : 8302016134121
         * totalPoints : 0.0
         * cusPoints : 0.0
         * cusTotal : 0.0
         */

        private String cusWeixin;
        private String cusName;
        private String cusRegeditDate;
        private String cusBirthday;
        private String cusUnique;
        private String cusQQ;
        private String cusPhone;
        private String cusHeadPath;
        private String cusEmail;
        private String cusSex;
        private String cusUsePoints;
        private String cusId;
        private String shopUnique;
        private String totalPoints;
        private String cusPoints;
        private String cusTotal;
        private String cusType;
        private String cusPassword;
        private String cus_level_id;
        private String cus_level_name;
        private String cus_status;
        private String cus_remark;
        private String cus_balance;

        public String getCus_balance() {
            return cus_balance;
        }

        public void setCus_balance(String cus_balance) {
            this.cus_balance = cus_balance;
        }

        public String getCus_status() {
            return cus_status;
        }

        public void setCus_status(String cus_status) {
            this.cus_status = cus_status;
        }

        public String getCus_remark() {
            return cus_remark;
        }

        public void setCus_remark(String cus_remark) {
            this.cus_remark = cus_remark;
        }

        public String getCusType() {
            return cusType;
        }

        public void setCusType(String cusType) {
            this.cusType = cusType;
        }

        public String getCusPassword() {
            return cusPassword;
        }

        public void setCusPassword(String cusPassword) {
            this.cusPassword = cusPassword;
        }

        public String getCus_level_id() {
            return cus_level_id;
        }

        public void setCus_level_id(String cus_level_id) {
            this.cus_level_id = cus_level_id;
        }

        public String getCus_level_name() {
            return cus_level_name;
        }

        public void setCus_level_name(String cus_level_name) {
            this.cus_level_name = cus_level_name;
        }

        public String getCusWeixin() {
            return cusWeixin;
        }

        public void setCusWeixin(String cusWeixin) {
            this.cusWeixin = cusWeixin;
        }

        public String getCusName() {
            return cusName;
        }

        public void setCusName(String cusName) {
            this.cusName = cusName;
        }

        public String getCusRegeditDate() {
            return cusRegeditDate;
        }

        public void setCusRegeditDate(String cusRegeditDate) {
            this.cusRegeditDate = cusRegeditDate;
        }

        public String getCusBirthday() {
            return cusBirthday;
        }

        public void setCusBirthday(String cusBirthday) {
            this.cusBirthday = cusBirthday;
        }

        public String getCusUnique() {
            return cusUnique;
        }

        public void setCusUnique(String cusUnique) {
            this.cusUnique = cusUnique;
        }

        public String getCusQQ() {
            return cusQQ;
        }

        public void setCusQQ(String cusQQ) {
            this.cusQQ = cusQQ;
        }

        public String getCusPhone() {
            return cusPhone;
        }

        public void setCusPhone(String cusPhone) {
            this.cusPhone = cusPhone;
        }

        public String getCusHeadPath() {
            return cusHeadPath;
        }

        public void setCusHeadPath(String cusHeadPath) {
            this.cusHeadPath = cusHeadPath;
        }

        public String getCusEmail() {
            return cusEmail;
        }

        public void setCusEmail(String cusEmail) {
            this.cusEmail = cusEmail;
        }

        public String getCusSex() {
            return cusSex;
        }

        public void setCusSex(String cusSex) {
            this.cusSex = cusSex;
        }

        public String getCusUsePoints() {
            return cusUsePoints;
        }

        public void setCusUsePoints(String cusUsePoints) {
            this.cusUsePoints = cusUsePoints;
        }

        public String getCusId() {
            return cusId;
        }

        public void setCusId(String cusId) {
            this.cusId = cusId;
        }

        public String getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(String shopUnique) {
            this.shopUnique = shopUnique;
        }

        public String getTotalPoints() {
            return totalPoints;
        }

        public void setTotalPoints(String totalPoints) {
            this.totalPoints = totalPoints;
        }

        public String getCusPoints() {
            return cusPoints;
        }

        public void setCusPoints(String cusPoints) {
            this.cusPoints = cusPoints;
        }

        public String getCusTotal() {
            return cusTotal;
        }

        public void setCusTotal(String cusTotal) {
            this.cusTotal = cusTotal;
        }
    }
}
