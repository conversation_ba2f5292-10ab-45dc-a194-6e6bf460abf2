package cn.bl.mobile.buyhoostore_sea.wxapi;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;

import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.ui.MainActivity;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;

/**
 * 微信客户端回调activity
 * Created by zqr on 2016/11/24.
 */
public class WXEntryActivity extends Activity implements IWXAPIEventHandler {
    private IWXAPI api;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = WXAPIFactory.createWXAPI(this, Constants.WX_AppId, false);
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
        //获取开放标签传递的extinfo数据逻辑
        if (req.getType() == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX && req instanceof ShowMessageFromWX.Req) {
            ShowMessageFromWX.Req showReq = (ShowMessageFromWX.Req) req;
            WXMediaMessage mediaMsg = showReq.message;
            String extInfo_goodid = mediaMsg.messageExt;
            if (null != extInfo_goodid && !extInfo_goodid.isEmpty()) {
                if (extInfo_goodid.contains("orderId")) {
                    int phoneIndex = extInfo_goodid.indexOf("phone");
                    int shopUniqueIndex = extInfo_goodid.indexOf("shop_unique");
                    int statusIndex = extInfo_goodid.indexOf("status");
                    String orderId = extInfo_goodid.substring(8, phoneIndex);
                    String phone = extInfo_goodid.substring(phoneIndex + 5, shopUniqueIndex);

                    String shopUnique = extInfo_goodid.substring(shopUniqueIndex + 11, statusIndex);
                    String status = "";
                    if (statusIndex != -1) {
                        status = extInfo_goodid.substring(statusIndex + 6);

                    }
                    Intent intent = new Intent(this, MainActivity.class);
                    intent.putExtra("orderId", orderId);
                    intent.putExtra("phone", phone);
                    intent.putExtra("shopUnique", shopUnique);
                    intent.putExtra("status", status);
                    intent.putExtra("shareFlag", "0");

                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                    finish();
                } else {
                    SharedPreferences sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
                    String area_dict_num = sp.getString("area_dict_num", "371302");
                    String shopId = sp.getString("shopId", "0");
                    if ("0".equals(shopId)) {
                        Intent intent = new Intent(this, LoginActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        startActivity(intent);
                    } else {
                        Intent intent = new Intent(WXEntryActivity.this, MainActivity.class);
                        intent.putExtra("goodsId", extInfo_goodid);
                        intent.putExtra("shareFlag", "1");
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        startActivity(intent);
                        finish();
                    }
                }
            }
        }
    }

    @Override
    public void onResp(BaseResp resp) {
        Log.e("111111", "type = " + resp.getType() + " code = " + resp.errCode);
        if (resp.getType() == ConstantsAPI.COMMAND_SENDAUTH) {
            switch (resp.errCode) {
                case BaseResp.ErrCode.ERR_OK:
                    //登陆成功，获取code值
                    String code = ((SendAuth.Resp) resp).code;
                    //code：081qtd000OooAP1vI9200Z8tX53qtd0i
                    Log.e("111111", "code = " + code);
//                //通过EventBus将获取到的code值，传递到登录页面
//                WXEventBean wxEventBean = new WXEventBean();
//                wxEventBean.setCode(code);
//                EventBus.getDefault().postSticky(wxEventBean);
                    break;
                case BaseResp.ErrCode.ERR_AUTH_DENIED:
                    ToastUtil.showToast(this, "授权出错");
                    break;
                case BaseResp.ErrCode.ERR_USER_CANCEL:
                    ToastUtil.showToast(this, "取消授权");
                    break;
            }
        } else {
            //分享
            switch (resp.errCode) {
                case BaseResp.ErrCode.ERR_OK:
                    ToastUtil.showToast(this, "已完成");
                    break;
                case BaseResp.ErrCode.ERR_USER_CANCEL:
                    ToastUtil.showToast(this, "分享取消");
                    //分享取消
                    break;
                case BaseResp.ErrCode.ERR_AUTH_DENIED:
                    //分享拒绝
                    ToastUtil.showToast(this, "分享拒绝");
                    break;
            }
        }
        finish();
    }

}