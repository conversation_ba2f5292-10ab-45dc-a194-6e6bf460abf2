package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdate;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.MyLocationStyle;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiSearch;
import com.yxl.commonlibrary.base.BaseActivity;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;

/**
 * Describe:地图选址
 * Created by jingang on 2023/11/17
 */
@SuppressLint("NonConstantResourceId")
public class PoiActivity extends BaseActivity implements
        AMapLocationListener {
    @BindView(R.id.mapView)
    MapView mMapView;
    @BindView(R.id.etSearch)
    EditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;

    private AMap aMap;
    private AMapLocationClient mlocationClient;
    private AMapLocationClientOption mLocationOption;
    private Marker marker;

    private PoiAdapter poiAdapter;

    private String city, keyword;
    private double lat, lng;
    public boolean alwaysLocation = true; //只定位一次

    @Override
    protected int getLayoutId() {
        return R.layout.activity_poi;
    }

    @Override
    public void initViews() {
        etSearch.setHint(getLanguageValue("enterSearchKeyword"));
        etSearch.setOnEditorActionListener((v, actionId, event) -> {
            doSearchQuery();
            return true;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyword = s.toString().trim();
                if (TextUtils.isEmpty(keyword)) {
                    ivClear.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
    }

    @Override
    public void initData() {
        getLocation();
    }

    @OnClick({R.id.ivBack, R.id.ivClear})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivClear:
                //清除输入
                etSearch.setText("");
                doSearchQuery();
                break;
        }
    }

    @SuppressLint("SimpleDateFormat")
    @Override
    public void onLocationChanged(AMapLocation location) {
        if (null != location) {
            StringBuffer sb = new StringBuffer();
            //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
            if (location.getErrorCode() == 0) {
                sb.append("定位成功" + "\n");
                sb.append("定位类型: " + location.getLocationType() + "\n");
                sb.append("经度: " + location.getLongitude() + "\n");
                sb.append("纬度: " + location.getLatitude() + "\n");
                sb.append("精度: " + location.getAccuracy() + "米" + "\n");
                sb.append("速度: " + location.getSpeed() + "米/秒" + "\n");
                sb.append("角度: " + location.getBearing() + "\n");
                sb.append("城市编码: " + location.getCityCode() + "\n");
                sb.append("区域编码: " + location.getAdCode() + "\n");

                city = location.getCityCode();
                lat = location.getLatitude();
                lng = location.getLongitude();
                //获取定位信息进行周边搜索
//                doSearchQuery();
                cameraMove();
                Log.e("111111", "定位成功  = " + sb);
            } else {
                //定位失败
                sb.append("定位失败" + "\n");
                sb.append("错误码:" + location.getErrorCode() + "\n");
                sb.append("错误信息:" + location.getErrorInfo() + "\n");
                sb.append("错误描述:" + location.getLocationDetail() + "\n");
                Log.e("111111", sb.toString());
                showMessage(getLanguageValue("locateFailed") + location.getErrorCode());
            }
            if (alwaysLocation) {
                stopLocation();
            }
        } else {
            showMessage(getLanguageValue("checkNetwork"));
        }
    }

    /**
     * 设置适配器
     */
    public void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        poiAdapter = new PoiAdapter(this);
        recyclerView.setAdapter(poiAdapter);
        poiAdapter.setOnItemClickListener((view, position) -> {
            PoiItem item = poiAdapter.getDataList().get(position);
//            Log.e(tag, "省：" + item.getProvinceName()
//                    + "\n市：" + item.getCityName()
//                    + "\n区：" + item.getAdName()
//                    + "\n区域编码：" + item.getAdCode()
//                    + "\nlat：" + item.getLatLonPoint().getLatitude() + " lng：" + item.getLatLonPoint().getLongitude()
//                    + "\n地址：" + item.toString()
//                    + "\n详细地址：" + item.getSnippet());
            setResult(Constants.CHOOSE_LOCATION, new Intent()
                    .putExtra("address", item.getProvinceName()
                            + item.getCityName()
                            + item.getAdName()
                            + item.getSnippet()
                            + item)
                    .putExtra("lat", String.valueOf(item.getLatLonPoint().getLatitude()))
                    .putExtra("lng",String.valueOf(item.getLatLonPoint().getLongitude()))
                    .putExtra("province", item.getProvinceName())
                    .putExtra("city", item.getCityName())
                    .putExtra("district", item.getAdName())
                    .putExtra("area", item.getAdCode())
            );
            finish();
        });
    }

    /**
     * 关键字搜索
     */
    private void doSearchQuery() {
        hideSoftInput(this);
        PoiSearch.Query query = new PoiSearch.Query(keyword, "", city);
        query.setPageSize(30);
        query.setPageNum(0);
        query.setExtensions(PoiSearch.EXTENSIONS_ALL);//解决不返回adCode问题
        PoiSearch poiSearch;
        try {
            poiSearch = new PoiSearch(this, query);
            if (TextUtils.isEmpty(keyword)) {
                poiSearch.setBound(new PoiSearch.SearchBound(new LatLonPoint(lat,
                        lng), 1000));
            }
            poiSearch.setOnPoiSearchListener(new PoiSearch.OnPoiSearchListener() {
                @Override
                public void onPoiSearched(PoiResult poiResult, int i) {
                    if (i == AMapException.CODE_AMAP_SUCCESS) {
                        ArrayList<PoiItem> pois = poiResult.getPois();
                        Log.e(tag, "地区2=" + poiResult.getPois());
                        poiAdapter.clear();
                        poiAdapter.addAll(pois);
                        cameraMarkers();
                    }
                }

                @Override
                public void onPoiItemSearched(PoiItem poiItem, int i) {

                }
            });
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            e.printStackTrace();
            Log.e(tag, "e = " + e.getMessage());
        }
    }

    /**
     * 动态申请定位权限
     */
    public void getLocation() {
        if (PermissionUtils.checkPermissionsGroup(this, 1)) {
            startLocation();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION, 1);
        }
    }

    /**
     * 开始定位
     */
    public void startLocation() {
        if (mlocationClient == null) {
            //初始化client
            try {
                mlocationClient = new AMapLocationClient(this);
                mLocationOption = defaultOption();
                //设置定位参数
                mlocationClient.setLocationOption(mLocationOption);
                // 设置定位监听
                mlocationClient.setLocationListener(this);
                mlocationClient.startLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 停止定位
     */
    public void stopLocation() {
        // 停止定位
        if (mlocationClient != null) {
            mlocationClient.stopLocation();
        }
    }

    /**
     * 默认的定位参数
     */
    //可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
    //可选，设置是否gps优先，只在高精度模式下有效。默认关闭
    //可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
    //可选，设置定位间隔。默认为2秒
    //可选，设置是否返回逆地理地址信息。默认是true
    //可选，设置是否单次定位。默认是false
    //可选，设置是否等待wifi刷新，默认为false.如果设置为true,会自动变为单次定位，持续定位时不要使用
    //可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
    //可选，设置是否使用传感器。默认是false
    //可选，设置是否开启wifi扫描。默认为true，如果设置为false会同时停止主动刷新，停止以后完全依赖于系统刷新，定位位置可能存在误差
    //可选，设置是否使用缓存定位，默认为true
    //可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
    private AMapLocationClientOption defaultOption() {
        AMapLocationClientOption mOption = new AMapLocationClientOption();
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        mOption.setGpsFirst(false);
        mOption.setHttpTimeOut(30000);
        mOption.setInterval(10000);//定位间隔为10s，实时向socket发送
        mOption.setNeedAddress(true);
        mOption.setOnceLocation(false);
        mOption.setOnceLocationLatest(false);
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);
        mOption.setSensorEnable(false);
        mOption.setWifiScan(true);
        mOption.setLocationCacheEnable(true);
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);
        mOption.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.Transport);
        return mOption;
    }

    /**
     * 移动地图
     */
    private void cameraMove() {
        LatLng latlng = new LatLng(lat, lng);
        CameraUpdate camera = CameraUpdateFactory.newCameraPosition(new CameraPosition(latlng, 18, 0, 0));
        aMap.moveCamera(camera);
    }

    /**
     * 创建定位点marker
     */
    private void cameraMarkers() {
        if (marker != null) {
            marker.remove();
        }
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.position(new LatLng(lat, lng));
        markerOptions.title(getLanguageValue("atPresent")+getLanguageValue("location"));
        markerOptions.visible(true);
        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_address001));
        markerOptions.icon(bitmapDescriptor);
        marker = aMap.addMarker(markerOptions);
    }

    @Override
    public void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        //在activity执行onPause时执行mMapView.onPause ()，暂停地图的绘制
        mMapView.onPause();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
        mMapView.onSaveInstanceState(outState);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
        if (null != mlocationClient) {
            mlocationClient.onDestroy();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //在activity执行onCreate时执行mMapView.onCreate(savedInstanceState)，创建地图
        mMapView.onCreate(savedInstanceState);
        //初始化地图控制器对象
        if (aMap == null) {
            aMap = mMapView.getMap();
        }
        MyLocationStyle locationStyle = new MyLocationStyle();
        locationStyle.strokeColor(Color.argb(0, 0, 0, 0));// 设置圆形的边框颜色
        locationStyle.radiusFillColor(Color.argb(0, 0, 0, 0));// 设置圆形的填充颜色
        aMap.setMyLocationStyle(locationStyle);
        // 设置定位监听
//        aMap.setLocationSource(this);
        // 设置为true表示显示定位层并可触发定位，false表示隐藏定位层并不可触发定位，默认是false
        aMap.setMyLocationEnabled(true);
        // 设置定位的类型为定位模式，有定位、跟随或地图根据面向方向旋转几种
        aMap.setMyLocationType(AMap.LOCATION_TYPE_LOCATE);
        //监听地图发生变化之后
        aMap.setOnCameraChangeListener(new AMap.OnCameraChangeListener() {
            @Override
            public void onCameraChange(CameraPosition cameraPosition) {

            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                Log.e(tag, "地图移动");
                lat = cameraPosition.target.latitude;
                lng = cameraPosition.target.longitude;
                doSearchQuery();
            }
        });
        UiSettings uiSettings = aMap.getUiSettings();
        uiSettings.setZoomControlsEnabled(true);
        // 设置为true表示显示定位层并可触发定位，false表示隐藏定位层并不可触发定位，默认是false
        uiSettings.setMyLocationButtonEnabled(true);// 设置默认定位按钮是否显示
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    startLocation();
                }
                break;
        }
    }
}