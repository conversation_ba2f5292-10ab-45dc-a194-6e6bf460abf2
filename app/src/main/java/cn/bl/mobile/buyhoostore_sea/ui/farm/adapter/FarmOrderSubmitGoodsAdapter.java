package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmOrderSubmitGoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:农批-选择货品（适配器）
 * Created by jingang on 2023/5/26
 */
public class FarmOrderSubmitGoodsAdapter extends BaseAdapter<FarmOrderSubmitGoodsData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public FarmOrderSubmitGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_farm_order_submit;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvCount, tvPrice, tvTotal, tvChengType, tvSaleType;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvChengType = holder.getView(R.id.tvItemChengType);
        tvSaleType = holder.getView(R.id.tvItemSaleType);
        ImageView ivDel = holder.getView(R.id.ivItemDel);

        tvName.setText(mDataList.get(position).getGoodsName());
        tvCount.setText(DFUtils.getNum2(mDataList.get(position).getGoodsCount()) + mDataList.get(position).getUnit());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsPrice()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getGoodsCount() * mDataList.get(position).getGoodsPrice()));
        tvChengType.setText(mDataList.get(position).getChengType());
        tvSaleType.setText(mDataList.get(position).getSaleType());
        if (onItemClickListener != null) {
            ivDel.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
