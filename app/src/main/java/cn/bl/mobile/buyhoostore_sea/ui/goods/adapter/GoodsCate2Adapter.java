package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateChildData;

/**
 * Describe: 商品分类-三级（适配器）
 * Created by jingang on 2025/5/22
 */
public class GoodsCate2Adapter extends BaseAdapter<CateChildData> {
    public GoodsCate2Adapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_cate2;
    }


    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        TextView tvEnable = holder.getView(R.id.tvItemEnable);
        ImageView ivMore = holder.getView(R.id.ivItemMore);
//        ImageView ivEdit, ivDel;
//        ivEdit = holder.getView(R.id.ivItemEdit);
//        ivDel = holder.getView(R.id.ivItemDel);
        tvEnable.setText(getLanguageValue("deactivated"));

        tvName.setText(mDataList.get(position).getKindName());
        tvEnable.setVisibility(mDataList.get(position).getValid_type() == 2 ? View.VISIBLE : View.GONE);
//        //是否可编辑，1、不可编辑；2、可编辑
//        if (TextUtils.equals(mDataList.get(position).getEditType(), "2")) {
//            ivEdit.setVisibility(View.VISIBLE);
//            ivDel.setVisibility(View.VISIBLE);
//        } else {
//            ivEdit.setVisibility(View.GONE);
//            ivDel.setVisibility(View.GONE);
//        }
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
//            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
//            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

//        void onEditClick(View view, int position);
//
//        void onDelClick(View view, int position);

        void onMoreClick(View view, int position);
    }
}
