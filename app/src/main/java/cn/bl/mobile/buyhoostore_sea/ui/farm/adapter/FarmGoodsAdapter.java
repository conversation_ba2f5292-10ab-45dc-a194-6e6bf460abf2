package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmGoodsData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:农批-商品（适配器）
 * Created by jingang on 2023/5/24
 */
public class FarmGoodsAdapter extends BaseAdapter<FarmGoodsData.GoodsListBean> {
    public FarmGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_farm;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvChengType, tvSaleType, tvKucun;
        tvName = holder.getView(R.id.tvItemName);
        tvChengType = holder.getView(R.id.tvItemChengType);
        tvSaleType = holder.getView(R.id.tvItemSaleType);
        tvKucun = holder.getView(R.id.tvItemKucun);

        tvName.setText(mDataList.get(position).getGoodsName());
        tvChengType.setText(mDataList.get(position).getGoodsChengType());
        tvSaleType.setText(mDataList.get(position).getGoodsSaleType());
        tvKucun.setText("库存:" + DFUtils.getNum2(mDataList.get(position).getGoodsCount()) + mDataList.get(position).getGoodsUnit());

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            lin.setOnLongClickListener(v -> {
                listener.onItemLongClick(v, position);
                return true;
            });
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onItemLongClick(View view, int position);
    }
}
