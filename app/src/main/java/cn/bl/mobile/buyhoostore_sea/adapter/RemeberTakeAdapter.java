package cn.bl.mobile.buyhoostore_sea.adapter;

/**
 * Created by Administrator on 2017/10/13 0013.
 */

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseApplication;

import org.json.JSONException;

import java.util.List;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.RemeberTakeBean;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.MemberTakeActivity;

/**
 * Created by Administrator on 2017/7/24 0024.
 */
public class RemeberTakeAdapter extends BaseAdapter {
    private ViewHolder holder;
    private List<RemeberTakeBean.DataBean> list;
    private Context context;
    private RemeberTakeBean.DataBean categoryBean;
    private LayoutInflater mInflater;
    int bg_color;

    public RemeberTakeAdapter(Context context, List<RemeberTakeBean.DataBean> list) {
        mInflater = LayoutInflater.from(context);
        this.list = list;
        this.context = context;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(
                    R.layout.item_remebertake_tab, null);
            holder.text_time = (TextView) convertView.findViewById(R.id.text_time);
            holder.text_type = (TextView) convertView.findViewById(R.id.text_type);
            holder.text_takemoney = (TextView) convertView.findViewById(R.id.text_takemoney);
            holder.text_jifen = (TextView) convertView.findViewById(R.id.text_jifen);
            holder.text_name = (TextView) convertView.findViewById(R.id.text_name);
            holder.lin_color = (LinearLayout) convertView.findViewById(R.id.lin_color);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        categoryBean = list.get(position);
        holder.text_time.setText(categoryBean.getRecDate() + "");
        String recharge_method = categoryBean.getRecharge_method();
        String consumptionType = categoryBean.getConsumptionType();
        if (MemberTakeActivity.cus_type.equals("1")) {
            if (recharge_method.equals("1")) {
                holder.text_type.setText(getLanguageValue("cash"));
            } else if (recharge_method.equals("2")) {
                holder.text_type.setText(getLanguageValue("wechat"));
            } else if (recharge_method.equals("3")) {
                holder.text_type.setText(getLanguageValue("alipay"));

            } else if (recharge_method.equals("4")) {
                holder.text_type.setText(getLanguageValue("saveZero"));
            } else if (recharge_method.equals("8")) {
                holder.text_type.setText(getLanguageValue("blend") + getLanguageValue("payment"));
            }
        } else {
            holder.text_type.setText(consumptionType);
        }
        String take_money = categoryBean.getRecMoney();
        String ji_fen = categoryBean.getSalePoints();
        if (categoryBean.getCus_type().equals("1")) {
            holder.text_takemoney.setText("+" + take_money + "");
            holder.text_jifen.setText(ji_fen + "");
            holder.text_takemoney.setTextColor(context.getResources().getColor(R.color.blue));
            holder.text_jifen.setTextColor(context.getResources().getColor(R.color.blue));
        } else if (categoryBean.getCus_type().equals("3")) {

            holder.text_takemoney.setText("-" + take_money + "");
            holder.text_jifen.setText(ji_fen + "");
            holder.text_takemoney.setTextColor(context.getResources().getColor(R.color.red));
            holder.text_jifen.setTextColor(context.getResources().getColor(R.color.blue));
        }
        if (ji_fen.equals("") || ji_fen.equals("0")) {

            holder.text_jifen.setText("-");
            holder.text_jifen.setTextColor(context.getResources().getColor(R.color.blue));
        }

        holder.text_name.setText(categoryBean.getStaffName() + "");

        bg_color = position % 2;
        if (bg_color == 0) {
            holder.lin_color.setBackgroundResource(R.color.white);

        } else {

            holder.lin_color.setBackgroundResource(R.color.bg_rember);
        }


        return convertView;
    }

    private class ViewHolder {
        TextView text_time;//消费时间
        TextView text_type;//消费方式
        TextView text_takemoney;//消费额
        TextView text_jifen;//积分
        TextView text_name;//操作人
        LinearLayout lin_color;//背景颜色
    }

    /**
     * 获取多语言值
     *
     * @param key
     * @return
     */
    public String getLanguageValue(String key) {
        String value;
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        if (BaseApplication.getLanguageValueJson() == null) {
            value = "";
        } else {
            try {
                value = BaseApplication.getLanguageValueJson().getString(key);
            } catch (JSONException e) {
                e.printStackTrace();
                value = "";
            }
        }
        return value;
    }


}

