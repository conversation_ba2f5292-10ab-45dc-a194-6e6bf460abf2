package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierApplyAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商申请列表
 * Created by jingang on 2023/10/11
 */
@SuppressLint("NonConstantResourceId")
public class SupplierApplyFragment extends LazyBaseFragment {
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private boolean isVisible;

    private SupplierApplyAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SupplierApplyFragment newInstance() {
        SupplierApplyFragment fragment = new SupplierApplyFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.layout_smartrefreshlayout_top16;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isVisible) {
            page = 1;
            getSupplierList();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_LIST:
                //刷新列表
                if (isVisible) {
                    page = 1;
                    getSupplierList();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new SupplierApplyAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            IAlertDialog.showDialog(getActivity(),
                    getLanguageValue("confirm")+getLanguageValue("through")+getLanguageValue("apply")+"?",
                    getLanguageValue("confirm"),
                    (dialog, which) -> {
                        postSupplierConfirm(dataList.get(position).getId(), position);
                    });
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getSupplierList();
        });
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        showDialog();
        hideSoftInput(getActivity());
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("queryMeg", SupplierActivity.keyWords);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getSupplierApplyList(),
                map,
                SupplierData.class,
                new RequestListListener<SupplierData>() {
                    @Override
                    public void onResult(List<SupplierData> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 供货商绑定（）确认通过
     *
     * @param id
     */
    private void postSupplierConfirm(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierBind(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        isVisible = false;
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) {
            return;
        }
        switch (requestCode) {
            case Constants.ADD:
                getSupplierList();
                break;
        }
    }
}
