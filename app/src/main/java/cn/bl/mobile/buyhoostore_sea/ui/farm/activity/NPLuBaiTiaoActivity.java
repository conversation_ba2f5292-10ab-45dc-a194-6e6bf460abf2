package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.app.AlertDialog;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.yanzhenjie.permission.AndPermission;
import com.yanzhenjie.permission.runtime.Permission;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;

import com.yxl.commonlibrary.utils.DateUtils;
import com.yxl.commonlibrary.utils.EventBusManager;
import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCropEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.NPCustomerDialog;
import cn.bl.mobile.buyhoostore_sea.utils.ToastUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * author 刘清营
 * Created on 2023/5/26.
 * Describe:
 */
public class NPLuBaiTiaoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRight)
    TextView tvRight;
    @BindView(R.id.tv_select_customer)
    TextView selectCustomerTV;
    @BindView(R.id.tv_qian_kuan_date)
    TextView qianKuanDateTV;
    @BindView(R.id.et_qiankuan)
    EditText qianKuanET;
    @BindView(R.id.iv_photo)
    ImageView photoIV;
    @BindView(R.id.et_remark)
    EditText remarkET;
    private MemberBean.DataBean mMemberBean;

    private File mFile;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_np_lu_bai_tiao;
    }

    @Override
    public void initViews() {
        tvTitle.setText("录入白条");
        tvRight.setText("赊欠记录");
        tvRight.setTextColor(ContextCompat.getColor(this, R.color.green));

        qianKuanDateTV.setText(DateUtils.getCurrentDate());
    }

    private void submit() {

        if (TextUtils.isEmpty(qianKuanET.getText())) {
            ToastUtil.showToast(this, "请输入欠款金额");
            return;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("cusName", qianKuanET.getText());
        map.put("cusPhone", remarkET.getText());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getAddCusNP(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        ToastUtil.showToast(NPLuBaiTiaoActivity.this, "添加成功");
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFRESH_MEMBER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @OnClick({R.id.saveTV, R.id.ivBack, R.id.iv_photo, R.id.tvRight,R.id.tv_qian_kuan_date,R.id.tv_select_customer})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvRight:
                goToActivity(NPBaiTiaoRecordActivity.class);
                break;
            case R.id.saveTV:
                submit();

                break;
            case R.id.tv_select_customer: {
                NPCustomerDialog dialog = new NPCustomerDialog(this);
                if (mMemberBean != null) {
                    dialog.setCusUnique(mMemberBean.getCus_unique());
                }
                dialog.setonDialogClickListener(memberBean -> {
                    mMemberBean = memberBean;
                    selectCustomerTV.setText(memberBean.getCusName());
                });
                new XPopup.Builder(this)
                        .popupAnimation(PopupAnimation.ScrollAlphaFromBottom)
                        .offsetY(200)
                        .isDestroyOnDismiss(true)
                        .asCustom(dialog)
                        .show();
                break;
            }

            case R.id.iv_photo:
                AlertDialog.Builder photo_builder = new AlertDialog.Builder(this);
                photo_builder.setTitle("选择凭证");
                photo_builder.setPositiveButton("相机",
                        (dialog, which) -> {
                            takePhoto();//相机拍照
                        });
                photo_builder.setNegativeButton("相册",
                        (dialog, which) ->
                                AndPermission.with(this)
                                        .runtime()
                                        .permission(Permission.Group.CAMERA, Permission.Group.STORAGE)
                                        .onGranted(permissions -> {
                                            pickPhoto();
                                        }).onDenied(permissions -> {
                                            showMessage("请开启Klip相机使用权限");
                                        }).start());
                photo_builder.create().show();
                break;

        }
    }

    private void takePhoto() {
        PictureSelector.create(this)
                .openCamera(SelectMimeType.ofImage())
                .setCropEngine(new ImageFileCropEngine(this))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        LocalMedia media = result.get(0);
                        String path = media.getAvailablePath();
                        mFile = new File(path);
                        Glide.with(getApplicationContext())
                                .load(PictureMimeType.isContent(path) && !media.isCut() && !media.isCompressed() ? Uri.parse(path)
                                        : path)
                                .centerCrop()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .into(photoIV);
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setCropEngine(new ImageFileCropEngine(this))
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        LocalMedia media = result.get(0);
                        String path = media.getAvailablePath();
                        mFile = new File(path);
                        Glide.with(getApplicationContext())
                                .load(PictureMimeType.isContent(path) && !media.isCut() && !media.isCompressed() ? Uri.parse(path)
                                        : path)
                                .centerCrop()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .into(photoIV);
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }


}
