package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPlanData;

/**
 * Describe:补货单-商品列表（适配器）
 * Created by jingang on 2023/9/7
 */
public class RestockGoodsAdapter extends BaseAdapter<RestockPlanData.GoodsListBean> {

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RestockGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName = holder.getView(R.id.tvItemName);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
    }
}
