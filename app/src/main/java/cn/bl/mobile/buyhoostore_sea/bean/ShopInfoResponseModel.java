package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;

public class ShopInfoResponseModel {


	/**
	 * status : 1
	 * msg : 查询成功！
	 * data : {"shopImagePath":"image/8302016134121/83020161341217.png","shopUnique":8302016134121,"shopName":"测试店铺","shopPhone":"18369679135","shopHours":"02:08-23:58"}
	 * pageIndex : null
	 * pageSize : null
	 * pageCount : null
	 * redundant : null
	 */

	private int status;
	private String msg;
	private DataBean data;
	private Object pageIndex;
	private Object pageSize;
	private Object pageCount;
	private Object redundant;

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public DataBean getData() {
		return data;
	}

	public void setData(DataBean data) {
		this.data = data;
	}

	public Object getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(Object pageIndex) {
		this.pageIndex = pageIndex;
	}

	public Object getPageSize() {
		return pageSize;
	}

	public void setPageSize(Object pageSize) {
		this.pageSize = pageSize;
	}

	public Object getPageCount() {
		return pageCount;
	}

	public void setPageCount(Object pageCount) {
		this.pageCount = pageCount;
	}

	public Object getRedundant() {
		return redundant;
	}

	public void setRedundant(Object redundant) {
		this.redundant = redundant;
	}

	public static class DataBean implements Serializable {
		/**
		 * shopImagePath : image/8302016134121/83020161341217.png
		 * shopUnique : 8302016134121
		 * shopName : 测试店铺
		 * shopPhone : 18369679135
		 * shopHours : 02:08-23:58
		 */

		private String shopImagePath;
		private long shopUnique;
		private String shopName;
		private String shopPhone;
		private String shopHours;
		public String image_path;
		private String shopAddress;
		public String getShopAddress() {
			return shopAddress;
		}

		public void setShopAddress(String shopAddress) {
			this.shopAddress = shopAddress;
		}

		public String getShopImagePath() {
			return shopImagePath;
		}

		public void setShopImagePath(String shopImagePath) {
			this.shopImagePath = shopImagePath;
		}

		public long getShopUnique() {
			return shopUnique;
		}

		public void setShopUnique(long shopUnique) {
			this.shopUnique = shopUnique;
		}

		public String getShopName() {
			return shopName;
		}

		public void setShopName(String shopName) {
			this.shopName = shopName;
		}

		public String getShopPhone() {
			return shopPhone;
		}

		public void setShopPhone(String shopPhone) {
			this.shopPhone = shopPhone;
		}

		public String getShopHours() {
			return shopHours;
		}

		public void setShopHours(String shopHours) {
			this.shopHours = shopHours;
		}
	}



}
