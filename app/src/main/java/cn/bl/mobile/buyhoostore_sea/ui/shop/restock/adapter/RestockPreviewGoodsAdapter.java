package cn.bl.mobile.buyhoostore_sea.ui.shop.restock.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.restock.bean.RestockPreviewData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:
 * Created by jingang on 2023/9/8
 */
public class RestockPreviewGoodsAdapter extends BaseAdapter<RestockPreviewData.GoodsListBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RestockPreviewGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName = holder.getView(R.id.tvItemName);
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText("x" + DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
    }
}
