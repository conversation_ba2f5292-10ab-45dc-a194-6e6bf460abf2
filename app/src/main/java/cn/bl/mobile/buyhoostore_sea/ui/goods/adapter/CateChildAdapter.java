package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.bean.CateChildData;

/**
 * Describe:商品管理-二级分类（适配器）
 * Created by jingang on 2022/12/1
 */
public class CateChildAdapter extends BaseAdapter<CateChildData> {

    public CateChildAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_child;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvCate = holder.getView(R.id.tvItemCate);
        RecyclerView recyclerView = holder.getView(R.id.rvItemCate);

        tvCate.setText(mDataList.get(position).getKindName());
        if (mDataList.get(position).isCheck()) {
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tvCate.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            tvCate.setTextColor(mContext.getResources().getColor(R.color.color_666));
        }

        if (mDataList.get(position).isShow()) {
            recyclerView.setVisibility(View.VISIBLE);
            Cate2Adapter adapter = new Cate2Adapter(mContext);
            recyclerView.setAdapter(adapter);
            if (mDataList.get(position).getKindDetail() != null) {
                adapter.setDataList(mDataList.get(position).getKindDetail());
            }
            adapter.setOnItemClickListener((view, position1) -> {
                if (listener != null) {
                    listener.onChildItemClick(position, position1);
                }
            });
        } else {
            recyclerView.setVisibility(View.GONE);
        }

//        if (mDataList.get(position).getKindDetail() == null) {
//            recyclerView.setVisibility(View.GONE);
//        } else {
//            if (mDataList.get(position).getKindDetail().isEmpty()) {
//                recyclerView.setVisibility(View.GONE);
//            } else {
//                recyclerView.setVisibility(View.VISIBLE);
//                Cate2Adapter adapter = new Cate2Adapter(mContext);
//                recyclerView.setAdapter(adapter);
//                adapter.setDataList(mDataList.get(position).getKindDetail());
//                adapter.setOnItemClickListener((view, position1) -> {
//                    if (listener != null) {
//                        listener.onChildItemClick(position, position1);
//                    }
//                });
//            }
//        }

        if (listener != null) {
            tvCate.setOnClickListener(v -> listener.onItemClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(int position);

        void onChildItemClick(int position, int position1);
    }
}
