package cn.bl.mobile.buyhoostore_sea.bean;

public class ShopZiZhiBean {


    /**
     * status : 1
     * msg : null
     * data : {"shop_unique":*************,"yingyezhao_img":"image/*************/f028026f-3a71-42e5-8048-b279e33e4496.png","card_zheng_img":"image/*************/e2b12143-58f2-4d9a-ab50-bf8cfd63345d.png","shop_qualification_id":1,"create_time":*************,"register_shop_name":"海尔","legal_name":"博林","shop_phone":"***********","bank_card_img":"image/*************/b6db9d91-fdd7-427e-9d68-b2b9a1f915df.png","card_fan_img":"image/*************/8da52bcc-62c5-4f90-b04a-ddbbc1e49e4e.png","use_shop_name":"滨河阳光超市"}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private Object msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * shop_unique : *************
         * yingyezhao_img : image/*************/f028026f-3a71-42e5-8048-b279e33e4496.png
         * card_zheng_img : image/*************/e2b12143-58f2-4d9a-ab50-bf8cfd63345d.png
         * shop_qualification_id : 1
         * create_time : *************
         * register_shop_name : 海尔
         * legal_name : 博林
         * shop_phone : ***********
         * bank_card_img : image/*************/b6db9d91-fdd7-427e-9d68-b2b9a1f915df.png
         * card_fan_img : image/*************/8da52bcc-62c5-4f90-b04a-ddbbc1e49e4e.png
         * use_shop_name : 滨河阳光超市
         */

        private long shop_unique;
        private String yingyezhao_img;
        private String card_zheng_img;
        private int shop_qualification_id;
//        private long create_time;
        private String register_shop_name;
        private String legal_name;
        private String shop_phone;
        private String bank_card_img;
        private String card_fan_img;
        private String use_shop_name;

        public long getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(long shop_unique) {
            this.shop_unique = shop_unique;
        }

        public String getYingyezhao_img() {
            return yingyezhao_img;
        }

        public void setYingyezhao_img(String yingyezhao_img) {
            this.yingyezhao_img = yingyezhao_img;
        }

        public String getCard_zheng_img() {
            return card_zheng_img;
        }

        public void setCard_zheng_img(String card_zheng_img) {
            this.card_zheng_img = card_zheng_img;
        }

        public int getShop_qualification_id() {
            return shop_qualification_id;
        }

        public void setShop_qualification_id(int shop_qualification_id) {
            this.shop_qualification_id = shop_qualification_id;
        }

//        public long getCreate_time() {
//            return create_time;
//        }
//
//        public void setCreate_time(long create_time) {
//            this.create_time = create_time;
//        }

        public String getRegister_shop_name() {
            return register_shop_name;
        }

        public void setRegister_shop_name(String register_shop_name) {
            this.register_shop_name = register_shop_name;
        }

        public String getLegal_name() {
            return legal_name;
        }

        public void setLegal_name(String legal_name) {
            this.legal_name = legal_name;
        }

        public String getShop_phone() {
            return shop_phone;
        }

        public void setShop_phone(String shop_phone) {
            this.shop_phone = shop_phone;
        }

        public String getBank_card_img() {
            return bank_card_img;
        }

        public void setBank_card_img(String bank_card_img) {
            this.bank_card_img = bank_card_img;
        }

        public String getCard_fan_img() {
            return card_fan_img;
        }

        public void setCard_fan_img(String card_fan_img) {
            this.card_fan_img = card_fan_img;
        }

        public String getUse_shop_name() {
            return use_shop_name;
        }

        public void setUse_shop_name(String use_shop_name) {
            this.use_shop_name = use_shop_name;
        }
    }
}