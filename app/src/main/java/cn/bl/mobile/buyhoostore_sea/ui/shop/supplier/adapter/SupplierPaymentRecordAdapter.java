package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierPaymentData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:供货商详情-付款记录（适配器）
 * Created by jingang on 2023/9/4
 */
public class SupplierPaymentRecordAdapter extends BaseAdapter<SupplierPaymentData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public SupplierPaymentRecordAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_payment_record;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvUser, tvStatus, tvMoney, tvRemarks, tvMoneyValue, tvRemarksValue;
        tvUser = holder.getView(R.id.tvItemUser);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvMoney = holder.getView(R.id.tvItemMoney);
        tvRemarks = holder.getView(R.id.tvItemRemarks);
        tvMoneyValue = holder.getView(R.id.tvMoneyValue);
        tvRemarksValue = holder.getView(R.id.tvRemarksValue);
        tvMoneyValue.setText(getLanguageValue("repayment") + getLanguageValue("amount"));
        tvRemarksValue.setText(getLanguageValue("repayment") + getLanguageValue("remark"));

        //状态1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废，6-异常
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getLanguageValue("toBeReceived"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            case 2:
                tvStatus.setText(getLanguageValue("toBePaid"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            case 3:
                tvStatus.setText(getLanguageValue("toBeConfirmed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
                break;
            case 4:
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            case 5:
                tvStatus.setText(getLanguageValue("voided"));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_999));
                break;
            default:
                tvStatus.setText("");
                break;
        }
        tvUser.setText(mDataList.get(position).getCreateBy() + " " + mDataList.get(position).getCreateTime());
        tvMoney.setText("RM" + DFUtils.getNum2(mDataList.get(position).getPaymentMoney()));
        if (TextUtils.isEmpty(mDataList.get(position).getRemark())) {
            tvRemarks.setText(getLanguageValue("none"));
        } else {
            tvRemarks.setText(mDataList.get(position).getRemark());
        }
    }
}
