package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseFragment;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:赊销申请（银行卡）
 * Created by jingang on 2023/2/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyFragment02 extends BaseFragment {
    @BindView(R.id.linBg0)
    LinearLayout linBg0;
    @BindView(R.id.ivBg0)
    ImageView ivBg0;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.ivCamera0)
    ImageView ivCamera0;
    @BindView(R.id.tvImg0Value)
    TextView tvImg0Value;
    @BindView(R.id.linBg1)
    LinearLayout linBg1;
    @BindView(R.id.ivBg1)
    ImageView ivBg1;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.ivCamera1)
    ImageView ivCamera1;
    @BindView(R.id.tvImg1Value)
    TextView tvImg1Value;
    @BindView(R.id.tvBank_codeValue)
    TextView tvBank_codeValue;
    @BindView(R.id.etBank_code)
    EditText etBank_code;
    @BindView(R.id.tvBank_nameValue)
    TextView tvBank_nameValue;
    @BindView(R.id.etBank_name)
    EditText etBank_name;
    @BindView(R.id.tvExample)
    TextView tvExample;
    @BindView(R.id.tvPrevious)
    TextView tvPrevious;
    @BindView(R.id.tvNext)
    TextView tvNext;

    private int type,//类型：0.正面 1.反面
            status;
    private String bank0, bank1,
            bank_code,//银行卡号
            bank_name;//开户行

    public AggregationApplyFragment02(int status,String bank0, String bank1, String bank_code, String bank_name) {
        this.status = status;
        this.bank0 = bank0;
        this.bank1 = bank1;
        this.bank_code = bank_code;
        this.bank_name = bank_name;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_aggregation_apply02;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        etBank_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                bank_code = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank_code", bank_code));
                setTextBg();
            }
        });
        etBank_name.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                bank_name = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank_name", bank_name));
                setTextBg();
            }
        });
        if(status == 1){
            linBg0.setEnabled(false);
            ivCamera0.setVisibility(View.GONE);
            linBg1.setEnabled(false);
            ivCamera1.setVisibility(View.GONE);
            etBank_code.setFocusable(false);
            etBank_name.setFocusable(false);
        }else{
            linBg0.setEnabled(true);
            ivCamera0.setVisibility(View.VISIBLE);
            linBg1.setEnabled(true);
            ivCamera1.setVisibility(View.VISIBLE);
            etBank_code.setFocusable(true);
            etBank_name.setFocusable(true);
        }
        setUI();
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.linBg0, R.id.linBg1, R.id.tvPrevious, R.id.tvNext})
    public void onViewClicked(View view) {
//        if(status == 1){
//            //审核中不可编辑
//            return;
//        }
        switch (view.getId()) {
            case R.id.linBg0:
                //正面
                type = 0;
                getImg();
                break;
            case R.id.linBg1:
                //反面
                type = 1;
                getImg();
                break;
            case R.id.tvPrevious:
                //上一步
                if (listener != null) {
                    listener.onPreviousClick();
                }
                break;
            case R.id.tvNext:
                //下一步
                if (TextUtils.isEmpty(bank0)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("bankCardFrontPhoto"));
                    return;
                }
                if (TextUtils.isEmpty(bank1)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("bankCardPhoto"));
                    return;
                }
                if(TextUtils.isEmpty(bank_code)){
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("bankDeposit"));
                    return;
                }
                if(TextUtils.isEmpty(bank_name)){
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("bankDeposit"));
                    return;
                }
                if (listener != null) {
                    listener.onNextClick();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvImg0Value.setText(getLanguageValue("shootUpload")+getLanguageValue("bankCardFrontPhoto"));
        tvImg1Value.setText(getLanguageValue("shootUpload")+getLanguageValue("bankCardPhoto"));
        tvBank_codeValue.setText(getLanguageValue("bankCard")+getLanguageValue("number"));
        etBank_code.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("bankCard")+getLanguageValue("number"));
        tvBank_nameValue.setText(getLanguageValue("bankDeposit"));
        etBank_code.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("bankDeposit"));
//        tvExample.setText(getLanguageValue("example")+":"+);
        tvPrevious.setText(getLanguageValue("previousStep"));
        tvNext.setText(getLanguageValue("nextStep"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(AggregationApplyFragment02.this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvCamera.setText(getLanguageValue("photograph"));
        tvAlbum.setText(getLanguageValue("phoneSelect"));
        tvCancel.setText(getLanguageValue("cancel"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(getActivity())
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        if (type == 0) {
//                            bank0 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank0", bank0));
//                        } else {
//                            bank1 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank1", bank1));
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(getActivity())
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        if (type == 0) {
//                            bank0 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank0", bank0));
//                        } else {
//                            bank1 = path;
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank1", bank1));
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 上传图片
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                getActivity().runOnUiThread(() -> {
                        showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                getActivity().runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            if (type == 0) {
                                bank0 = data.getData().getUrl();
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank0", bank0));
                            } else {
                                bank1 = data.getData().getUrl();
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("bank1", bank1));
                            }
                            setUI();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //正面
        if (TextUtils.isEmpty(bank0)) {
            ivBg0.setImageResource(R.mipmap.ic_bank_bg001);
            ivImg0.setVisibility(View.GONE);
        } else {
            ivBg0.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg0.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(bank0)
                    .into(ivImg0);
        }
        //反面
        if (TextUtils.isEmpty(bank1)) {
            ivBg1.setImageResource(R.mipmap.ic_bank_bg002);
            ivImg1.setVisibility(View.GONE);
        } else {
            ivBg1.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg1.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(bank1)
                    .into(ivImg1);
        }
        etBank_code.setText(bank_code);
        etBank_name.setText(bank_name);
        setTextBg();
    }

    /**
     * 判断按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(bank0)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(bank1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(bank_code)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(bank_name)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        tvNext.setBackgroundResource(R.drawable.shape_blue_22);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onPreviousClick();

        void onNextClick();
    }
}
