package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.SupplierData;

/**
 * Describe:农批-分类：代卖（适配器）
 * Created by jingang on 2023/5/26
 */
public class FarmCate1Adapter extends BaseAdapter<SupplierData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public FarmCate1Adapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_farm;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin = holder.getView(R.id.linItem);
        View v = holder.getView(R.id.vDialog);
        TextView tvCate = holder.getView(R.id.tvItemCate);
        tvCate.setText(mDataList.get(position).getSupplier_name());
        if (mDataList.get(position).isCheck()) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            v.setVisibility(View.VISIBLE);
            tvCate.setTextColor(mContext.getResources().getColor(R.color.blue));
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            v.setVisibility(View.INVISIBLE);
            tvCate.setTextColor(mContext.getResources().getColor(R.color.color_333));
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
    }
}
