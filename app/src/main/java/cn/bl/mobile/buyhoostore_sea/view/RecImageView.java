package cn.bl.mobile.buyhoostore_sea.view;

import android.content.Context;
import android.util.AttributeSet;

import com.qmuiteam.qmui.widget.QMUIRadiusImageView;

/**
 * Describe:矩形图片
 * Created by jingang on 2020/3/27
 */
@SuppressWarnings("ALL")
public class RecImageView extends QMUIRadiusImageView {
    public RecImageView(Context context) {
        super(context);
    }

    public RecImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public RecImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, widthMeasureSpec);
    }
}
