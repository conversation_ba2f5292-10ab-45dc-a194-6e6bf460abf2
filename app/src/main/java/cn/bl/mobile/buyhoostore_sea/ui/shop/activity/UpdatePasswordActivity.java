package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.jpush.JPushUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 店铺-店铺设置-密码修改
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class UpdatePasswordActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.etPwd)
    EditText etPwd;
    @BindView(R.id.etPwd1)
    EditText etPwd1;
    @BindView(R.id.etPwd2)
    EditText etPwd2;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private SharedPreferences sp = null;
    private String staffAccount = "";

    private String pwd, pwd1, pwd2;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_update_password;
    }

    @Override
    public void initViews() {
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
        staffAccount = sp.getString("staffAccount", "");
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //确认
                pwd = etPwd.getText().toString().trim();
                pwd1 = etPwd1.getText().toString().trim();
                pwd2 = etPwd2.getText().toString().trim();
                if (TextUtils.isEmpty(pwd)) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("original")+getLanguageValue("password"));
                    return;
                }
                if (TextUtils.isEmpty(pwd1)) {
                    showMessage(getLanguageValue("passwordNew"));
                    return;
                }
                if (pwd1.length() < 6) {
                    showMessage(getLanguageValue("passwordLengthSix"));
                    return;
                }
                if (TextUtils.isEmpty(pwd2)) {
                    showMessage(getLanguageValue("passwordNewAgain"));
                    return;
                }
                if (pwd2.length() < 6) {
                    showMessage(getLanguageValue("passwordLengthSixAgain"));
                    return;
                }
                if (!TextUtils.equals(pwd1, pwd2)) {
                    showMessage(getLanguageValue("newPasswordInconsistent"));
                    return;
                }
                postPwd();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("password")+getLanguageValue("modification"));
        etPwd.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("original")+getLanguageValue("password"));
        etPwd1.setHint(getLanguageValue("passwordNew"));
        etPwd2.setHint(getLanguageValue("passwordNewAgain"));
        tvConfirm.setText(getLanguageValue("confirm"));
    }

    /**
     * 修改密码
     */
    private void postPwd() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("staffAccount", staffAccount);
        map.put("staffPwd", pwd);
        map.put("staffNewPwd", pwd1);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.setnewpassword(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(getLanguageValue("modification")+getLanguageValue("succeed"));
                        postUpdateJPushId();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 更新推送注册id
     */
    private void postUpdateJPushId() {
        Map<String, Object> map = new HashMap<>();
        map.put("registration_phone_type", 2);
        map.put("registration_id", "");
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateRegistrationId(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        loginOut();
                    }

                    @Override
                    public void onError(String msg) {
                        loginOut();
                    }
                });
    }

    private void loginOut() {
        BaseApplication.getInstance().saveUserInfo("");
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("colose"));
        SharedPreferences mySharedPreferences = getSharedPreferences("test", Activity.MODE_PRIVATE);
        SharedPreferences.Editor editor = mySharedPreferences.edit();
        editor.putString("exit", "已退出");
        editor.commit();
        JPushUtil.setTagAndAlias("", this);
        if (sp != null) {
            SharedPreferences.Editor editor1 = sp.edit();
            editor1.clear();
            editor1.commit();
        }
        AppManager.getInstance().finishAllActivity();
        goToActivity(LoginActivity.class);
        finish();
    }

}
