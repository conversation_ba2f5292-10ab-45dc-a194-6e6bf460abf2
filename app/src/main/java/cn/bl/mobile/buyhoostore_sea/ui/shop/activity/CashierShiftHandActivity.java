package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.RemoteException;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.pos.sdk.printer.POIPrinterManager;
import com.pos.sdk.printer.models.PrintLine;
import com.pos.sdk.printer.models.TextPrintLine;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.sharedpreference.IPreference;
import com.yxl.commonlibrary.sharedpreference.SharedUtils;
import com.yxl.commonlibrary.utils.DateUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;
import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CartDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.CashierStatusDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.GoodsListDialog;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsInfoData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.CashierQuickCateAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.GoodsDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.CashierStatusData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.bean.SaleListUniqueData;
import cn.bl.mobile.buyhoostore_sea.utils.SystemTTS;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.view.ScannerEditText;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import vendor.kozen.hardware.scan.V1_0.IScan;
import vendor.kozen.hardware.scan.V1_0.IScanCallback;

/**
 * Describe:店铺-移动收银（手持设备）
 * Created by jingang on 2024/4/11
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class CashierShiftHandActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvPrint)
    TextView tvPrint;
    @BindView(R.id.ivPrint)
    ImageView ivPrint;
    @BindView(R.id.etSearch)
    ScannerEditText etSearch;
    @BindView(R.id.ivClear)
    ImageView ivClear;
    @BindView(R.id.rvCate)
    RecyclerView rvCate;//商品分类
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvCartNum)
    TextView tvCartNum;//购物车数量
    @BindView(R.id.tvTotal)
    TextView tvTotal;//总价
    @BindView(R.id.tvCount)
    TextView tvCount;//总数量
    @BindView(R.id.tvCash)
    TextView tvCash;
    @BindView(R.id.tvCustom)
    TextView tvCustom;

    private boolean isPrint;//是否打印小票
    private String keyWords,
            saleListUnique,//订单号（可通过接口创建）
            scanCode;//扫码
    private double total;//总价
    private int shopType;//7.宁宇

    //商品分类
    private List<CateData> cateList = new ArrayList<>();
    private CashierQuickCateAdapter cateAdapter;

    //购物车
    private List<GoodsData> cartList = new ArrayList<>();

    //商品列表
    private GoodsDialogAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();

    //语音播报
    private SystemTTS systemTTS;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_cashier_shift_hand;
    }

    @Override
    public void initViews() {
        shopType = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getInt("shop_type", 0);
        systemTTS = SystemTTS.getInstance(this);
        isPrint = !TextUtils.isEmpty(SharedUtils.get(Constants.CASHIER_QUICK_IS_PRINT, IPreference.DataType.STRING));
        if (isPrint) {
            ivPrint.setImageResource(R.mipmap.ic_chosen001);
        } else {
            ivPrint.setImageResource(R.mipmap.ic_chose001);
        }
        etSearch.setRawInputType(Configuration.KEYBOARD_QWERTY);//键盘默认为数字，但可以切换到中英文输入
        requestFocus();
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    ivClear.setVisibility(View.GONE);
                    rvCate.setVisibility(View.VISIBLE);
                    smartRefreshLayout.setVisibility(View.GONE);
                } else {
                    ivClear.setVisibility(View.VISIBLE);
                    rvCate.setVisibility(View.GONE);
                    smartRefreshLayout.setVisibility(View.VISIBLE);
                    page = 1;
                    getGoodsList();
                }
//                String text = s.toString().trim();
//                cancelTimer();
//                if (TextUtils.isEmpty(text)) {
//                    keyWords = text;
//                    ivClear.setVisibility(View.GONE);
//                    rvCate.setVisibility(View.VISIBLE);
//                    smartRefreshLayout.setVisibility(View.GONE);
//                } else {
//                    startTiming(500, text);
//                }
            }
        });
        etSearch.setScanResultListener(this::setResult);
        initIScan();
        initIPrint();
        setAdapter();
    }

//    private CountDownTimer timer;
//    private String text;
//
//    /**
//     * 计时器开始（定时关闭）
//     */
//    private void startTiming(long time, String msg) {
//        text = msg;
//        if (time == 0) {
//            return;
//        }
//        timer = new CountDownTimer(time, 1000L) {
//            @Override
//            public void onTick(long millisUntilFinished) {
//            }
//
//            @Override
//            public void onFinish() {
//                Log.e(tag, "text = " + text);
//                requestFocus();
//                if (text.length() > 17) {
//                    //付款码
//                    Log.e(tag, "keyWords = " + keyWords + " text = " + text);
//                    if (!TextUtils.isEmpty(text)) {
//                        if (!TextUtils.isEmpty(keyWords)) {
//                            text = text.substring(keyWords.length(), text.length());
//                        }
//                        setResultSymcode(text);
//                    }
//                    etSearch.setText("");
//                } else {
//                    keyWords = text;
//                    if (TextUtils.isEmpty(keyWords)) {
//                        ivClear.setVisibility(View.GONE);
//                        rvCate.setVisibility(View.VISIBLE);
//                        smartRefreshLayout.setVisibility(View.GONE);
//                    } else {
//                        ivClear.setVisibility(View.VISIBLE);
//                        rvCate.setVisibility(View.GONE);
//                        smartRefreshLayout.setVisibility(View.VISIBLE);
//                        page = 1;
//                        getGoodsList();
//                    }
//                }
//            }
//        };
//        timer.start();
//    }
//
//    /**
//     * 计时器清除（定时关闭）
//     */
//    private void cancelTimer() {
//        if (timer != null) {
//            timer.cancel();
//            timer = null;
//        }
//    }

    /**
     * 重新获取焦点
     */
    private void requestFocus() {
        etSearch.requestFocus();
        etSearch.setFocusable(true);
        etSearch.setFocusableInTouchMode(true);
    }

    @Override
    public void initData() {
        getCate();
        getSaleListUnique();
    }

    @OnClick({R.id.ivBack, R.id.ivPrint, R.id.ivClear, R.id.relCart, R.id.tvCash, R.id.tvCustom})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                if (!TextUtils.isEmpty(keyWords)) {
                    etSearch.setText("");
                    hideSoftInput(this);
                } else {
                    onBackPressed();
                }
                break;
            case R.id.ivPrint:
                //是否打印小票
                isPrint = !isPrint;
                if (isPrint) {
                    ivPrint.setImageResource(R.mipmap.ic_chosen001);
                    SharedUtils.put(Constants.CASHIER_QUICK_IS_PRINT, Constants.CASHIER_QUICK_IS_PRINT);
                } else {
                    ivPrint.setImageResource(R.mipmap.ic_chose001);
                    SharedUtils.put(Constants.CASHIER_QUICK_IS_PRINT, "");
                }
                break;
            case R.id.ivClear:
                //清除搜索内容
                etSearch.setText("");
                hideSoftInput(this);
                break;
            case R.id.relCart:
                //购物车
                if (cartList.size() < 1) {
                    showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                    return;
                }
                CartDialog.showDialog(this, cartList, new CartDialog.MyListener() {
                    @Override
                    public void onCartList(List<GoodsData> list) {
                        cartList = list;
                        getTotal();
                        if (!TextUtils.isEmpty(keyWords)) {
                            page = 1;
                            getGoodsList();
                        }
                    }

                    @Override
                    public void onCashClick() {
                        if (cartList.size() < 1) {
                            showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                            return;
                        }
                        postCashPay();
                    }

                    @Override
                    public void onCustomClick() {
                        if (cartList.size() < 1) {
                            showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                            return;
                        }
                        startActivityForResult(new Intent(TAG, MemberQuickActivity.class)
                                        .putExtra("money", total)
                                        .putExtra("saleListUnique", saleListUnique)
                                        .putExtra("cartList", (Serializable) cartList)
                                , Constants.PAY);
                    }

                    @Override
                    public void onScanResult(String result) {
//                        setResultSymcode(result);
                        setResult(result);
                    }
                });
                break;
            case R.id.tvCash:
                //现金
                if (isQuicklyClick()) {
                    return;
                }
                if (cartList.size() < 1) {
                    showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                    return;
                }
                postCashPay();
                break;
            case R.id.tvCustom:
                //会员
                if (isQuicklyClick()) {
                    return;
                }
                if (cartList.size() < 1) {
                    showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                    return;
                }
                startActivityForResult(new Intent(TAG, MemberQuickActivity.class)
                                .putExtra("money", total)
                                .putExtra("saleListUnique", saleListUnique)
                                .putExtra("cartList", (Serializable) cartList)
                        , Constants.PAY);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (printerManager != null) {
            printerManager.close();
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("mobileCashRegister"));
        tvPrint.setText(getLanguageValue("whetherOrNot") + getLanguageValue("print") + "");
        etSearch.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("commodity") + getLanguageValue("name") + "/" + getLanguageValue("barcode"));
        tvCash.setText(getLanguageValue("cash"));
        tvCustom.setText(getLanguageValue("member"));
    }

    /**
     * 得到扫码结果
     *
     * @param code
     */
    private void setResult(String code) {
        Log.e(tag, "扫码结果 = " + code);
        scanCode = code;
        if (TextUtils.isEmpty(scanCode)) {
            return;
        }
        if (scanCode.length() < 2) {
            return;
        }
        clearDialog();
        String chooseCode = scanCode.substring(0, 2);
        if (scanCode.length() == 11) {
            //会员码
            if (cartList.size() < 1) {
                showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                return;
            }
            getCusList();
        } else {
            switch (chooseCode) {
                case "62":
                case "28":
                case "13":
                    if (scanCode.length() == 18 || ("62".equals(chooseCode) && scanCode.length() == 19)) {
                        //支付码
                        if (cartList.size() < 1) {
                            showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                            return;
                        }
                        postScanPay();
                    }
                    break;
                case "36":
                    //会员码
                    showMessage(getLanguageValue("notSupport"));
                    break;
                default:
                    //商品码
                    getGoodsInfo();
                    break;
            }
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //商品分类
        cateAdapter = new CashierQuickCateAdapter(this);
        rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (isQuicklyClick()) {
                return;
            }
            for (int i = 0; i < cateList.size(); i++) {
                if (cateList.get(i).isCheck()) {
                    cateList.get(i).setCheck(false);
                    cateAdapter.notifyItemChanged(i);
                }
            }
            cateList.get(position).setCheck(true);
            cateAdapter.notifyItemChanged(position);
            getGoodsListCate(cateList.get(position).getGroupUnique());
//            GoodsListDialog.showDialog(this, cateList.get(position).getGroupUnique(), cartList, total, data -> {
//                int pos = isAdd(data.getGoodsBarcode());
//                if (pos == -1) {
//                    cartList.add(0, data);
//                } else {
//                    cartList.set(pos, data);
//                }
//                getTotal();
//            });
        });

        //商品列表
        mAdapter = new GoodsDialogAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if ((total + dataList.get(position).getGoodsSalePrice()) > Constants.CASHIER_MAX_MONEY) {
                showMessage(getLanguageValue("cartTotal") + Constants.CASHIER_MAX_MONEY);
                return;
            }
            dataList.get(position).setCartNum(dataList.get(position).getCartNum() + 1);
            mAdapter.notifyItemChanged(position);
            int pos = isAdd(dataList.get(position).getGoodsBarcode());
            if (pos == -1) {
                cartList.add(0, dataList.get(position));
            } else {
                cartList.set(pos, dataList.get(position));
            }
            getTotal();
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsList();
            }
        });
    }

    /**
     * 判断是否已添加
     *
     * @return -1.未添加 其他.已添加
     */
    private int isAdd(String barcode) {
        int pos = -1;
        if (TextUtils.isEmpty(barcode)) {
            return -1;
        }
        if (cartList.size() < 1) {
            return -1;
        }
        for (int i = 0; i < cartList.size(); i++) {
            if (cartList.get(i).getGoodsBarcode().equals(barcode)) {
                pos = i;
            }
        }
        return pos;
    }

    /**
     * 小计
     */
    private void getTotal() {
        int num = 0;
        total = 0;
        for (int i = 0; i < cartList.size(); i++) {
            num = num + cartList.get(i).getCartNum();
            total = total + cartList.get(i).getCartNum() * cartList.get(i).getGoodsSalePrice();
        }
        if (num > 0) {
            tvCartNum.setVisibility(View.VISIBLE);
            if (num > 99) {
                tvCartNum.setText("99+");
            } else {
                tvCartNum.setText(String.valueOf(num));
            }
        } else {
            tvCartNum.setVisibility(View.GONE);
        }
        tvTotal.setText("RM" + DFUtils.getNum2(total));
        tvCount.setText(cartList.size() + getLanguageValue("types") + getLanguageValue("commodity"));
    }

    /**
     * 清除弹窗、清除搜索
     */
    private void clearDialog() {
        if (CartDialog.isShow()) {
            CartDialog.dismissDialog();
        }
        if (GoodsListDialog.isShow()) {
            GoodsListDialog.dismissDialog();
        }
        if (!TextUtils.isEmpty(keyWords)) {
            etSearch.setText("");
            hideSoftInput(this);
        }
    }

    /**
     * 获取分类
     */
    private void getCate() {
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getdapaixu(),
                map,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        cateList.clear();
                        cateList.addAll(list);
                        cateAdapter.setDataList(cateList);
                    }
                });
    }

    /**
     * 判断分类是否含有商品
     */
    private void getGoodsListCate(String cateUnique) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("groupUnique", cateUnique);
        map.put("pageIndex", page);
        map.put("pageSize", 20);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSelectGoods(),
                map,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> list) {
                        hideDialog();
                        if (list.size() < 1) {
                            showMessage(getLanguageValue("noProductCategory"));
                            return;
                        }
                        GoodsListDialog.showDialog(TAG, cateUnique, cartList, total, new GoodsListDialog.MyListener() {
                            @Override
                            public void onClick(GoodsData data) {
                                int pos = isAdd(data.getGoodsBarcode());
                                if (pos == -1) {
                                    cartList.add(0, data);
                                } else {
                                    cartList.set(pos, data);
                                }
                                getTotal();
                            }

                            @Override
                            public void onScanResult(String result) {
                                Log.e(tag, "result = " + result);
//                                setResultSymcode(result);
                                setResult(result);
                            }
                        });
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    /**
     * 得到搜索的内容
     */
    public void getGoodsList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        params.put("orderType", 1);
        params.put("goodsMessage", keyWords);
        params.put("groupUnique", "-1");
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSelectGoods(),
                params,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> goodsData) {
                        if (page == 1) {
                            dataList.clear();
                            smartRefreshLayout.finishRefresh();
                            double price = isNumeric(keyWords);
                            if (price < 1000 && price > 0) {
                                dataList.add(new GoodsData("0",
                                        "999999999" + keyWords,
                                        "RM" + DFUtils.getNum4(price) + getLanguageValue("tempProduct"),
                                        price,
                                        price,
                                        0));
                            }
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(goodsData);
                        for (int i = 0; i < cartList.size(); i++) {
                            for (int j = 0; j < dataList.size(); j++) {
                                if (!TextUtils.isEmpty(cartList.get(i).getGoodsBarcode()) && !TextUtils.isEmpty(dataList.get(j).getGoodsBarcode())) {
                                    if (cartList.get(i).getGoodsBarcode().equals(dataList.get(j).getGoodsBarcode())) {
                                        dataList.get(j).setCartNum(cartList.get(i).getCartNum());
                                    }
                                }
                            }
                        }
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        super.onError(msg);
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            mAdapter.clear();
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                    }
                });
    }

    /**
     * 判断字符串是否为纯数字
     *
     * @param str
     * @return
     */
    public double isNumeric(String str) {
        try {
            // 如果字符串是整数
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 商品详情
     */
    private void getGoodsInfo() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", scanCode);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSelecDetail(),
                map,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData infoData) {
                        hideDialog();
                        if (infoData.getListDetail().size() > 0) {
                            GoodsInfoData.ListDetailBean data = infoData.getListDetail().get(0);
                            int pos = isAdd(data.getGoodsBarcode());
                            if (pos == -1) {
                                cartList.add(0, new GoodsData(String.valueOf(data.getGoodsId()),
                                        data.getGoodsBarcode(), data.getGoodsName(),
                                        data.getGoodsInPrice(),
                                        data.getGoodsSalePrice(),
                                        1));
                            } else {
                                for (int i = 0; i < cartList.size(); i++) {
                                    if (data.getGoodsBarcode().equals(cartList.get(i).getGoodsBarcode())) {
                                        cartList.set(pos, new GoodsData(String.valueOf(data.getGoodsId()),
                                                data.getGoodsBarcode(),
                                                data.getGoodsName(),
                                                data.getGoodsInPrice(),
                                                data.getGoodsSalePrice(),
                                                cartList.get(i).getCartNum() + 1));
                                    }
                                }
                            }
                            getTotal();
                            showMessage(data.getGoodsName() + getLanguageValue("addTo") + getLanguageValue("succeed"));
                        } else {
                            showMessage(getLanguageValue("noSuchProduct"));
                            systemTTS.playText(getLanguageValue("noSuchProduct"));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        systemTTS.playText(msg);
                    }
                });
    }

    /**
     * 创建订单编号
     */
    public void getSaleListUnique() {
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getcreateSaleListUnique(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        saleListUnique = data.getSale_list_unique();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                    }
                });
    }

    /**
     * 扫码支付（非会员）
     */
    private void postScanPay() {
        showDialog();
        List array = new ArrayList();
        try {
            Map object = new HashMap();
            for (int i = 0; i < cartList.size(); i++) {
                object.put("goodsBarcode", cartList.get(i).getGoodsBarcode());
                object.put("goodsName", cartList.get(i).getGoodsName());
                object.put("saleListDetailCount", cartList.get(i).getCartNum());
                object.put("saleListDetailPrice", cartList.get(i).getGoodsSalePrice());
                object.put("goodsId", cartList.get(i).getGoodsId());
                object.put("goodsInPrice", cartList.get(i).getGoodsInPrice());
                array.add(object);
            }
        } catch (Exception ignored) {
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("saleListUnique", saleListUnique);
        map.put("saleListTotal", (int) (total * 100));//必须为整数
        map.put("saleListCashier", getStaff_id());
        map.put("machine_num", 1);
        map.put("saleListActuallyReceived", (int) (total * 100));//必须为整数
        map.put("sale_list_paysment", 13);
        map.put("auth_code", scanCode);
        map.put("pointsRatio", getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString(MemberActivity.CONSATNT_INTEGRAL_RATIO, "1"));
        map.put("sale_list_detail", array.toString());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getYiTongPay(),
                map,
                CashierStatusData.class,
                new RequestListener<CashierStatusData>() {
                    @Override
                    public void success(CashierStatusData data) {
                        hideDialog();
                        getSaleListUnique();
                        if (data == null) {
                            showMessage(getLanguageValue("cashierFail"));
                            systemTTS.playText(getLanguageValue("cashierFail"));
                            return;
                        }
                        if (TextUtils.isEmpty(data.getTrade_state())) {
                            showMessage(getLanguageValue("cashierFail"));
                            systemTTS.playText(getLanguageValue("cashierFail"));
                            return;
                        }
                        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
                        switch (data.getTrade_state()) {
                            case "SUCCESS":
                                showMessage(getLanguageValue("cashierSuccess") + ":RM" + DFUtils.getNum4(total));
                                systemTTS.playText(getLanguageValue("cashierSuccess") + ":RM" + DFUtils.getNum4(total));
//                                if (isPrint) {
//                                    //打印小票
//                                    goodsNames = "无码商品";
//                                    goodsPrice = DFUtils.getNum4(total);
//                                    memberName = "";
//                                    memberPhone = "";
//                                    no = saleListUnique;
//                                    addPrint();
//                                }
                                cartList.clear();
                                etSearch.setText("");
                                hideSoftInput(TAG);
                                getTotal();
                                break;
                            case "DOING":
                            case "USERPAYING":
                                CashierStatusDialog.showDialog(TAG, data.getOut_trade_no(), total, (msg, status) -> {
                                    if (status == 0) {
                                        cartList.clear();
                                        etSearch.setText("");
                                        hideSoftInput(TAG);
                                        getTotal();
                                    }
                                    systemTTS.playText(msg);
                                });
                                getTotal();
                                break;
                            default:
                                showMessage(getLanguageValue("cashierFail"));
                                systemTTS.playText(getLanguageValue("cashierFail"));
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(getLanguageValue("cashierFail") + ":" + msg);
                        systemTTS.playText(getLanguageValue("cashierFail") + ":" + msg);
                        getSaleListUnique();
                    }
                });
    }

    /**
     * 现金收银
     */
    private void postCashPay() {
        showDialog();
        String goodsPurprice = "",//进价
                saleListDetailPrice = "",//售价
                saleListDetailCount = "",
                goodsName = "",
                goodsBarcode = "",
                goodsId = "",
                goods_old_price = "";//售价
        Map<String, Object> map = new HashMap<>();
        map.put("saleListState", 3);
        map.put("saleListPayDetail", "[{pay_method:1,pay_money:" + total + "}]");//支付详情
        map.put("shopUnique", getShop_id());
        map.put("saleListUnique", saleListUnique);//订单编号（接口创建）

        for (int i = 0; i < cartList.size(); i++) {
            if (TextUtils.isEmpty(goodsPurprice)) {
                goodsPurprice = String.valueOf(cartList.get(i).getGoodsInPrice());
            } else {
                goodsPurprice = goodsPurprice + ";" + cartList.get(i).getGoodsInPrice();
            }
            if (TextUtils.isEmpty(saleListDetailPrice)) {
                saleListDetailPrice = String.valueOf(cartList.get(i).getGoodsSalePrice());
            } else {
                saleListDetailPrice = saleListDetailPrice + ";" + cartList.get(i).getGoodsSalePrice();
            }
            if (TextUtils.isEmpty(saleListDetailCount)) {
                saleListDetailCount = String.valueOf(cartList.get(i).getCartNum());
            } else {
                saleListDetailCount = saleListDetailCount + ";" + cartList.get(i).getCartNum();
            }
            if (TextUtils.isEmpty(goodsName)) {
                goodsName = cartList.get(i).getGoodsName();
            } else {
                goodsName = goodsName + ";" + cartList.get(i).getGoodsName();
            }
            if (TextUtils.isEmpty(goodsBarcode)) {
                goodsBarcode = cartList.get(i).getGoodsBarcode();
            } else {
                goodsBarcode = goodsBarcode + ";" + cartList.get(i).getGoodsBarcode();
            }
            if (TextUtils.isEmpty(goodsId)) {
                goodsId = cartList.get(i).getGoodsId();
            } else {
                goodsId = goodsId + ";" + cartList.get(i).getGoodsId();
            }
            if (TextUtils.isEmpty(goods_old_price)) {
                goods_old_price = String.valueOf(cartList.get(i).getGoodsSalePrice());
            } else {
                goods_old_price = goods_old_price + ";" + cartList.get(i).getGoodsSalePrice();
            }
        }
        map.put("goodsPurprice", goodsPurprice);//商品进货价格
        map.put("saleListDetailPrice", saleListDetailPrice);//商品详情价格
        map.put("saleListDetailCount", saleListDetailCount);//商品详情数量
        map.put("goodsName", goodsName);//商品名称
        map.put("goodsBarcode", goodsBarcode);//商品条码
        map.put("goodsId", goodsId);//商品id
        map.put("goods_old_price", goods_old_price);//商品原价

        map.put("saleListTotal", total);//订单总额
        map.put("saleListCashier", getStaff_id());//员工id
        map.put("saleListActuallyReceived", total);//实收金额
        map.put("machine_num", 1);
        map.put("sale_list_payment", 1);//收银方式 1.现金 5.会员余额
        map.put("pointsRatio", getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString(MemberActivity.CONSATNT_INTEGRAL_RATIO, "1"));//积分比例
        map.put("type", 2);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getNingYuPay(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(getLanguageValue("cash") + getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(total));
                        systemTTS.playText(getLanguageValue("cash") + getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(total));
//                        if (isPrint) {
//                            //打印小票
//                            goodsNames = "无码商品";
//                            goodsPrice = DFUtils.getNum4(total);
//                            memberName = "";
//                            memberPhone = "";
//                            no = saleListUnique;
//                            addPrint();
//                        }
                        getSaleListUnique();
                        cartList.clear();
                        etSearch.setText("");
                        hideSoftInput(TAG);
                        getTotal();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(getLanguageValue("cashierFail") + ":" + msg);
                        systemTTS.playText(getLanguageValue("cashierFail") + msg);
                        getSaleListUnique();
                    }
                });
    }

    /**
     * 会员接口
     */
    public void getCusList() {
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        if (shopType == 7) {
            url = ZURL.getNingYuCusList();
            map.put("cusMsg", scanCode);
        } else {
            url = ZURL.getRemember();
            map.put("searchKey", scanCode);
            map.put("pages", page);
            map.put("perpage", Constants.limit);
        }
        RXHttpUtil.requestByFormPostAsResponseList(this,
                url,
                map,
                MemberBean.DataBean.class,
                new RequestListListener<MemberBean.DataBean>() {
                    @Override
                    public void onResult(List<MemberBean.DataBean> list) {
                        hideDialog();
                        if (list == null) {
                            showMessage(getLanguageValue("member") + getLanguageValue("information") + getLanguageValue("get") + getLanguageValue("failed"));
                            systemTTS.playText(getLanguageValue("member") + getLanguageValue("information") + getLanguageValue("get") + getLanguageValue("failed"));
                            return;
                        }
                        if (list.size() < 1) {
                            showMessage(getLanguageValue("member") + getLanguageValue("information") + getLanguageValue("get") + getLanguageValue("failed"));
                            systemTTS.playText(getLanguageValue("member") + getLanguageValue("information") + getLanguageValue("get") + getLanguageValue("failed"));
                            return;
                        }
                        MemberBean.DataBean data = list.get(0);
                        String unique;
                        double balance;
                        if (shopType == 7) {
                            unique = data.getCusUnique();
                            balance = data.getCusBalance() + data.getCus_rebate();
                        } else {
                            unique = data.getCus_unique();
                            if (TextUtils.isEmpty(data.getCus_balance())) {
                                balance = 0;
                            } else {
                                balance = Double.parseDouble(data.getCus_balance());
                            }
                        }
                        if (balance < total) {
                            showMessage(getLanguageValue("balance") + getLanguageValue("insufficient"));
                            systemTTS.playText(getLanguageValue("balance") + getLanguageValue("insufficient"));
                            return;
                        }
                        postMemberPay(unique);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        systemTTS.playText(msg);
                    }
                });
    }

    /**
     * 会员余额收银（会员）
     */
    private void postMemberPay(String unique) {
        showDialog();
        String url,
                goodsPurprice = "",//进价
                saleListDetailPrice = "",//售价
                saleListDetailCount = "",
                goodsName = "",
                goodsBarcode = "",
                goodsId = "",
                goods_old_price = "";//售价
        Map<String, Object> map = new HashMap<>();
        if (shopType == 7) {
            url = ZURL.getNingYuMemberPay();
            map.put("memberCard", unique);//会员unique
            map.put("storedCard", unique);//会员卡号（会员unique）
            map.put("storedCardMoney", total);//储值卡消费金额
            map.put("member_card", total);//储值卡支付的金额
        } else {
            url = ZURL.getNingYuPay();
        }
        map.put("shopUnique", getShop_id());
        map.put("saleListUnique", saleListUnique);//订单编号（接口创建）

        for (int i = 0; i < cartList.size(); i++) {
            if (TextUtils.isEmpty(goodsPurprice)) {
                goodsPurprice = String.valueOf(cartList.get(i).getGoodsInPrice());
            } else {
                goodsPurprice = goodsPurprice + ";" + cartList.get(i).getGoodsInPrice();
            }
            if (TextUtils.isEmpty(saleListDetailPrice)) {
                saleListDetailPrice = String.valueOf(cartList.get(i).getGoodsSalePrice());
            } else {
                saleListDetailPrice = saleListDetailPrice + ";" + cartList.get(i).getGoodsSalePrice();
            }
            if (TextUtils.isEmpty(saleListDetailCount)) {
                saleListDetailCount = String.valueOf(cartList.get(i).getCartNum());
            } else {
                saleListDetailCount = saleListDetailCount + ";" + cartList.get(i).getCartNum();
            }
            if (TextUtils.isEmpty(goodsName)) {
                goodsName = cartList.get(i).getGoodsName();
            } else {
                goodsName = goodsName + ";" + cartList.get(i).getGoodsName();
            }
            if (TextUtils.isEmpty(goodsBarcode)) {
                goodsBarcode = cartList.get(i).getGoodsBarcode();
            } else {
                goodsBarcode = goodsBarcode + ";" + cartList.get(i).getGoodsBarcode();
            }
            if (TextUtils.isEmpty(goodsId)) {
                goodsId = cartList.get(i).getGoodsId();
            } else {
                goodsId = goodsId + ";" + cartList.get(i).getGoodsId();
            }
            if (TextUtils.isEmpty(goods_old_price)) {
                goods_old_price = String.valueOf(cartList.get(i).getGoodsSalePrice());
            } else {
                goods_old_price = goods_old_price + ";" + cartList.get(i).getGoodsSalePrice();
            }
        }
        map.put("goodsPurprice", goodsPurprice);//商品进货价格
        map.put("saleListDetailPrice", saleListDetailPrice);//商品详情价格
        map.put("saleListDetailCount", saleListDetailCount);//商品详情数量
        map.put("goodsName", goodsName);//商品名称
        map.put("goodsBarcode", goodsBarcode);//商品条码
        map.put("goodsId", goodsId);//商品id
        map.put("goods_old_price", goods_old_price);//商品原价

        map.put("saleListTotal", total);//订单总额
        map.put("saleListCashier", getStaff_id());//员工id
        map.put("saleListActuallyReceived", total);//实收金额
        map.put("machine_num", 1);
        map.put("sale_list_payment", 5);//收银方式 1.现金 5.会员余额
        map.put("pointsRatio", getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE).getString(MemberActivity.CONSATNT_INTEGRAL_RATIO, "1"));//积分比例
        map.put("type", 2);
        RXHttpUtil.requestByFormPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(getLanguageValue("member") + getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(total));
                        systemTTS.playText(getLanguageValue("member") + getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(total));
                        cartList.clear();
                        etSearch.setText("");
                        hideSoftInput(TAG);
                        getTotal();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(getLanguageValue("member") + getLanguageValue("cashierFail") + ":" + msg);
                        systemTTS.playText(getLanguageValue("member") + getLanguageValue("cashierFail") + ":" + msg);
                    }
                });
    }

    /********扫码start********/
    //手持设备
    private IScan mIScan;

    /**
     * 扫码监听
     */
    public IScanCallback mScanCallback = new IScanCallback.Stub() {
        @Override
        public void onScanResult(String barcode, String result) {
            //扫码结果
            runOnUiThread(() -> {
                Log.e(tag, "barcode = " + barcode + " result = " + result);
                setResult(result);
            });
        }

        @Override
        public void onTimeOut() {
            //扫码超时
            runOnUiThread(() -> {
                showMessage(getLanguageValue("scanTimeout"));
            });
        }
    };

    /**
     * 启动扫码
     */
    private void initIScan() {
        try {
            mIScan = IScan.getService();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        try {
            mIScan.setCallback(mScanCallback);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        try {
            mIScan.setTimeout(60 * 1000);
        } catch (RemoteException e) {
            Log.e(tag, "doDecode, Exception = " + e);
        }
    }

    /********扫码end********/

    /********打印start********/
    private POIPrinterManager printerManager;
    /*小票信息*/
    private String goodsNames,//品名
            goodsPrice, //单价
            goodsCount = "1",//数量
            memberName,//客户名称
            memberPhone,//客户电话
            no;//订单号

    /**
     * 实例化打印机
     */
    private void initIPrint() {
        printerManager = new POIPrinterManager(this);
    }

    /**
     * 打印
     */
    private void addPrint() {
        runOnUiThread(() -> {
            try {
                printerManager.open();
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(tag, "打印机模块打开异常：" + e.getLocalizedMessage());
                showMessage("打印机模块打开异常:" + e.getLocalizedMessage());
            }
            Log.e(tag, "打印机模块打开成功");
            //判断打印机状态
            int printStatus = printerManager.getPrinterState();
            if (printStatus == POIPrinterManager.STATUS_IDLE) {
                Log.e(tag, "打印机状态正常");
                //第一行
                printerManager.addPrintLine(new TextPrintLine("欢迎光临" + getShop_name() + "\n\n", PrintLine.CENTER));

                //第二行
                printerManager.addPrintLine(printList("品名", "单价", "数量", 24, false));

                //第三行
                printerManager.addPrintLine(printList(goodsNames, goodsPrice, goodsCount, 24, false));

                //第四行
                printerManager.addPrintLine(new TextPrintLine("*************************************"));

                //第五行
                printerManager.addPrintLine(new TextPrintLine("应付金额：RM" + goodsPrice
                        + "\n交易件数：" + goodsCount
                        + "\n下单时间：" + DateUtils.getCurrentDate("yyyy-MM-dd HH:mm")
                        + "\n客户姓名：" + StringUtils.formattedName(memberName)
                        + "\n客户电话：" + StringUtils.getStarMobile(memberPhone)
                        + "\n支付状态：已付款"
                ));

                //第六行
                printerManager.addPrintLine(new TextPrintLine("\n送货地址："
                        + "\n备注："
                ));

                //第七行
                printerManager.addPrintLine(new TextPrintLine("订单号：" + no
                        + "\n\n\n\n\n"));

                printerManager.beginPrint(new POIPrinterManager.IPrinterListener() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {
                        //打印后需要手动清空打印机缓存
                        printerManager.cleanCache();
                        Log.e(tag, "打印完成");
                        showMessage("打印完成");
                    }

                    @Override
                    public void onError(int code, String msg) {
                        //打印后需要手动清空打印机缓存
                        printerManager.cleanCache();
                        Log.e(tag, "打印失败：" + String.format("%d%s", code, msg));
                        showMessage("打印失败：" + String.format("%d%s", code, msg));
                    }
                });
            } else {
                Log.e(tag, "打印机状态异常 = " + printStatus);
                showMessage("打印机状态异常 = " + printStatus);
            }
        });
    }

    private List<TextPrintLine> printList(
            String leftStr,
            String centerStr,
            String rightStr,
            int size,
            boolean bold) {
        TextPrintLine textPrintLine1 = new TextPrintLine(leftStr, PrintLine.LEFT, size, bold);
        TextPrintLine textPrintLine2 = new TextPrintLine(centerStr, PrintLine.CENTER, size, bold);
        TextPrintLine textPrintLine3 = new TextPrintLine(rightStr, PrintLine.RIGHT, size, bold);
        List<TextPrintLine> list = new ArrayList();
        list.add(textPrintLine1);
        list.add(textPrintLine2);
        list.add(textPrintLine3);
        return list;
    }

    /********打印end********/

    /**
     * 得到扫码结果
     *
     * @param code
     */
    private void setResultSymcode(String code) {
        Log.e(tag, "扫码结果 = " + code);
        scanCode = code;
        if (TextUtils.isEmpty(scanCode)) {
            return;
        }
        if (scanCode.length() < 2) {
            return;
        }
        clearDialog();
        String chooseCode = scanCode.substring(0, 2);
        switch (chooseCode) {
            case "62":
            case "28":
            case "13":
                if (scanCode.length() == 18 || ("62".equals(chooseCode) && scanCode.length() == 19)) {
                    //支付码
                    if (cartList.size() < 1) {
                        showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("commodity"));
                        return;
                    }
                    postScanPay();
                } else {
                    showMessage(getLanguageValue("showPaymentCodeCorrect"));
                    systemTTS.playText(getLanguageValue("showPaymentCodeCorrect"));
                }
                break;
            default:
                showMessage(getLanguageValue("showPaymentCodeCorrect"));
                systemTTS.playText(getLanguageValue("showPaymentCodeCorrect"));
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.PAY:
                    //余额支付
                    int type = data.getIntExtra("type", 0);
                    String msg = data.getStringExtra("msg");
                    if (type == 1) {
                        systemTTS.playText(getLanguageValue("member") + getLanguageValue("cashierSuccess") + "RM" + DFUtils.getNum4(total));
                        cartList.clear();
                        etSearch.setText("");
                        hideSoftInput(this);
                        getTotal();
                    } else {
                        systemTTS.playText(getLanguageValue("cashierFail") + msg);
                    }
                    getSaleListUnique();
                    break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (!TextUtils.isEmpty(keyWords)) {
            etSearch.setText("");
            hideSoftInput(this);
        } else {
            return super.onKeyDown(keyCode, event);
        }
        return false;
    }
}
