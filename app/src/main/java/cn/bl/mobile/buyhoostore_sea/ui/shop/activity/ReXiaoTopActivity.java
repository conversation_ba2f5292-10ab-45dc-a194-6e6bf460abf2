package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.adapter.GoodHotCakeAdapter;
import cn.bl.mobile.buyhoostore_sea.bean.ReXiaoBean;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 销售排行
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class ReXiaoTopActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvRank)
            TextView tvRank;
    @BindView(R.id.tvName)
            TextView tvName;
    @BindView(R.id.tvSale)
            TextView tvSale;
    @BindView(R.id.tvRate)
            TextView tvRate;
    @BindView(R.id.tvStatus)
            TextView tvStatus;

    ListView list_top;
    GoodHotCakeAdapter goodHotCakeAdapter;
    ReXiaoBean reXiaoBean;
    List<ReXiaoBean.DataBean> dataBeen = new ArrayList<>();
    String startDate;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_re_xiao_top;
    }

    @Override
    public void initViews() {
        startDate = getIntent().getStringExtra("startDate");
        list_top = findViewById(R.id.list_top);
    }

    @Override
    public void initData() {
        getrexiao();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("sale")+getLanguageValue("ranking"));
        tvRank.setText(getLanguageValue("ranking"));
        tvName.setText(getLanguageValue("commodity")+getLanguageValue("name"));
        tvSale.setText(getLanguageValue("day")+getLanguageValue("salesVolume"));
        tvRate.setText(getLanguageValue("ringRatio")+getLanguageValue("growth"));
        tvStatus.setText(getLanguageValue("status"));
    }

    /**
     * 热销排行接口
     * shopUnique
     */
    public void getrexiao() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("startDate", startDate);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getrexiaodetail(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "热销排行 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        JSONObject object;
                        int status = 2;
                        try {
                            object = new JSONObject(s);
                            status = object.getInt("status");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (status == 1) {
                            Gson gson1 = new Gson();
                            reXiaoBean = gson1.fromJson(s, ReXiaoBean.class);
                            if (reXiaoBean.getData() != null) {
                                for (int i = 0; i < reXiaoBean.getData().size(); i++) {
                                    dataBeen.add(reXiaoBean.getData().get(i));
                                }
                                goodHotCakeAdapter = new GoodHotCakeAdapter(getApplicationContext(), dataBeen);
                                list_top.setAdapter(goodHotCakeAdapter);
                            }
                        }
                    }
                });
    }
}
