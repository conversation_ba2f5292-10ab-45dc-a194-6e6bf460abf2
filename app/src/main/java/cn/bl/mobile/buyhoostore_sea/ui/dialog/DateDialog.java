package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.pickerview.DateTimeEntity;
import cn.bl.mobile.buyhoostore_sea.utils.pickerview.DateTimeWheelLayout;

/**
 * Describe:日期时间选择弹窗
 * Created by jingang on 2022/12/6
 */
@SuppressLint({"NonConstantResourceId", "SimpleDateFormat"})
public class DateDialog extends BaseDialog {
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.wheelLayout)
    DateTimeWheelLayout wheelLayout;

    private static int type;
    private static String date;

    public static void showDialog(Context mContext, String date, MyListener listener) {
        DateDialog.type = 0;
        DateDialog.date = date;
        DateDialog dialog = new DateDialog(mContext);
        dialog.setRange("", "", date);
        dialog.setListener(listener);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public static void showDialog(Context mContext, int type, String date, MyListener listener) {
        DateDialog.type = type;
        DateDialog.date = date;
        DateDialog dialog = new DateDialog(mContext);
        dialog.setRange("", "", date);
        dialog.setListener(listener);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public DateDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_date);
        ButterKnife.bind(this);
        tvCancel.setText(getLanguageValue("cancel"));
        tvTitle.setText(getLanguageValue("choose")+getLanguageValue("date"));
        tvConfirm.setText(getLanguageValue("confirm"));

        wheelLayout.setDefaultValue(DateTimeEntity.today()); //当前日期
        wheelLayout.setSelectedTextColor(getContext().getResources().getColor(R.color.black));//选中字体颜色
        wheelLayout.setSelectedTextSize(16 * getContext().getResources().getDisplayMetrics().scaledDensity);//选中字体大小
        wheelLayout.setTextColor(getContext().getResources().getColor(R.color.color_999));//字体颜色
        wheelLayout.setTextSize(14 * getContext().getResources().getDisplayMetrics().scaledDensity);//字体大小
        wheelLayout.setIndicatorColor(getContext().getResources().getColor(R.color.transparent)); //横线颜色
        wheelLayout.setOnDateTimeSelectedListener((year, month, day) -> {
            Log.e(tag, "year = " + year + "-" + month + "-" + day);
            String month_str, day_str;
            if (month < 10) {
                month_str = "0" + month;
            } else {
                month_str = String.valueOf(month);
            }
            if (day < 10) {
                day_str = "0" + day;
            } else {
                day_str = String.valueOf(day);
            }
            date = year + "-" + month_str + "-" + day_str;
        });
    }

    @OnClick({R.id.tvDialogCancel, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogCancel:
                //取消
                dismiss();
//                if (listener != null) {
//                    listener.onClick("");
//                    dismiss();
//                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (listener != null) {
                    listener.onClick(date);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置日期时间范围
     * <p>
     * 0.查询：开始 1.查询：结束 2.活动：开始 3.活动：结束
     *
     * @param startTime
     * @param endTime
     * @param nowTime
     */
    private void setRange(String startTime, String endTime, String nowTime) {
        DateTimeEntity entityStart, entityEnd, entityNow;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date dateStart, dateEnd, dateNow;

        /*****************开始日期时间*****************/
        try {
            dateStart = dateFormat.parse(startTime);
            if (dateStart != null) {
                entityStart = new DateTimeEntity();
                entityStart.setYear(dateStart.getYear() + 1900);
                if (dateStart.getMonth() == 0) {
                    entityStart.setMonth(1);
                } else {
                    entityStart.setMonth(dateStart.getMonth() + 1);
                }
                entityStart.setDay(dateStart.getDate());
            } else {
                entityStart = DateTimeEntity.yearOnFuture(-10);
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityStart = DateTimeEntity.yearOnFuture(-10);
        }

        /*****************结束日期时间*****************/
        try {
            dateEnd = dateFormat.parse(endTime);
            if (dateEnd != null) {
                entityEnd = new DateTimeEntity();
                entityEnd.setYear(dateEnd.getYear() + 1900);
                if (dateEnd.getMonth() == 0) {
                    entityEnd.setMonth(1);
                } else {
                    entityEnd.setMonth(dateEnd.getMonth() + 1);
                }
                entityEnd.setDay(dateEnd.getDate());
            } else {
                if (type == 1) {
                    entityEnd = DateTimeEntity.yearOnFuture(10);
                } else {
                    entityEnd = DateTimeEntity.today();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (type == 1) {
                entityEnd = DateTimeEntity.yearOnFuture(10);
            } else {
                entityEnd = DateTimeEntity.today();
            }
        }

        /*****************现在日期时间*****************/
        try {
            dateNow = dateFormat.parse(nowTime);
            if (dateNow != null) {
                entityNow = new DateTimeEntity();
                entityNow.setYear(dateNow.getYear() + 1900);
                if (dateNow.getMonth() == 0) {
                    entityNow.setMonth(1);
                } else {
                    entityNow.setMonth(dateNow.getMonth() + 1);
                }
                entityNow.setDay(dateNow.getDate());
            } else {
                entityNow = DateTimeEntity.today();
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityNow = DateTimeEntity.today();
        }
        if (wheelLayout != null) {
            wheelLayout.setRange(entityStart, entityEnd, entityNow); //日期范围
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String date);
    }
}
