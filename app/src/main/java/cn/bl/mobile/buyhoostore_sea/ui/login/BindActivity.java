package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.leaf.library.StatusBarUtil;
import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.WebActivity;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:登录（微信）-绑定手机号
 * Created by jingang on 2023/3/6
 */
@SuppressLint("NonConstantResourceId")
public class BindActivity extends BaseActivity {
    @BindView(R.id.etAccount)
    EditText etAccount;
    @BindView(R.id.etCode)
    EditText etCode;
    @BindView(R.id.vCode)
    View vCode;
    @BindView(R.id.tvBind)
    TextView tvBind;
    @BindView(R.id.ivPrivacy)
    ImageView ivPrivacy;
    @BindView(R.id.tvPrivacy)
    TextView tvPrivacy;

    private String account, code;
    private boolean isPrivacy;//同意用户协议

    private String privacy_str0,
            privacy_str1,
            privacy_str2,
            privacy_str3;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_bind;
    }

    @Override
    public void initViews() {
        StatusBarUtil.setTransparentForWindow(this);
        StatusBarUtil.setDarkMode(this);
        etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                setTextBg();
            }
        });
        etCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                code = s.toString().trim();
                setTextBg();
            }
        });

        privacy_str0 = getRstr(R.string.agree_privacy_tips1);
        privacy_str1 = getRstr(R.string.agree_privacy_tips2);
        privacy_str2 = getRstr(R.string.agree_privacy_tips3);
        privacy_str3 = getRstr(R.string.agree_privacy_tips4);
        tvPrivacy.setText(privacy_str0 + privacy_str1 + privacy_str2 + privacy_str3);
        setSpannableText();
    }

    @OnClick({R.id.ivBack, R.id.tvCode, R.id.tvRegister, R.id.tvBind, R.id.ivPrivacy})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCode:
                //获取验证码
                break;
            case R.id.tvRegister:
                //立即注册
                goToActivity(RegisterActivity.class);
                this.finish();
                break;
            case R.id.tvBind:
                //立即绑定
                hideSoftInput(this);
                if (!isPrivacy) {
                    showMessage("请您同意用户条款");
                    return;
                }
                IAlertDialog.showDialog(this,
                        "手机号未注册\n绑定的手机号未注册百货商家账号",
                        "立即注册",
                        (dialog, which) -> {

                        });
                break;
            case R.id.ivPrivacy:
                //同意用户协议
                isPrivacy = !isPrivacy;
                if (isPrivacy) {
                    ivPrivacy.setImageResource(R.mipmap.ic_chosen001);
                } else {
                    ivPrivacy.setImageResource(R.mipmap.ic_chose001);
                }
                break;
        }
    }

    /**
     * TextView设置多种颜色及部分点击事件
     */
    public void setSpannableText() {
        SpannableString spannableString = new SpannableString(tvPrivacy.getText().toString().trim());
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(BindActivity.this, "用户服务协议", ZURL.CONSTANT_shopDoc);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 6, 14, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//        spannableString.setSpan(new ClickableSpan() {
//            @Override
//            public void onClick(View view) {
//                WebActivity.toWebActivity(BindActivity.this, "隐私政策", ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
//            }
//
//            @Override
//            public void updateDrawState(TextPaint ds) {
//                super.updateDrawState(ds);
//                ds.setColor(getResources().getColor(R.color.blue));
//                ds.setUnderlineText(false);
//            }
//        }, 14, 20, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(BindActivity.this, getLanguageValue("userServiceAgreement"), ZURL.CONSTANT_shopDoc);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, privacy_str0.length(), (privacy_str0 + privacy_str1).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View view) {
                WebActivity.toWebActivity(BindActivity.this, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.blue));
                ds.setUnderlineText(false);
            }
        }, (privacy_str0 + privacy_str1).length(), (privacy_str0 + privacy_str1 + privacy_str2).length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvPrivacy.setMovementMethod(LinkMovementMethod.getInstance());//设置可点击状态
        tvPrivacy.setText(spannableString);
    }

    /**
     *
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(account)) {
            tvBind.setBackgroundResource(R.drawable.shape_93c0fe_22);
            vCode.setBackgroundColor(getResources().getColor(R.color.color_e7));
            return;
        }
        if (TextUtils.isEmpty(code)) {
            tvBind.setBackgroundResource(R.drawable.shape_93c0fe_22);
            vCode.setBackgroundColor(getResources().getColor(R.color.color_e7));
            return;
        }
        vCode.setBackgroundColor(getResources().getColor(R.color.blue));
        tvBind.setBackgroundResource(R.drawable.shape_blue_22);
    }
}
