package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:调拨单-新增-商品（适配器）
 * Created by jingang on 2023/5/19
 */
public class AllotAddGoodsAdapter extends BaseAdapter<AllotData.DetailInfoListBean> {

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    private int type;//0.详情（无删除） 1.添加（有删除）

    public void setType(int type) {
        this.type = type;
    }

    public AllotAddGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_allot_add_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivDel;
        ivImg = holder.getView(R.id.ivItemImg);
        ivDel = holder.getView(R.id.ivItemDel);
        TextView tvName, tvSpecs, tvInPrice, tvSalePrice, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvSpecs = holder.getView(R.id.tvItemSpecs);
        tvInPrice = holder.getView(R.id.tvItemInPrice);
        tvSalePrice = holder.getView(R.id.tvItemSalePrice);
        tvCount = holder.getView(R.id.tvItemCount);

        if (type == 1) {
            ivDel.setVisibility(View.VISIBLE);
        } else {
            ivDel.setVisibility(View.GONE);
        }

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsSpec())) {
            tvSpecs.setText(getLanguageValue("spec") + " " + getLanguageValue("none"));
        } else {
            tvSpecs.setText(getLanguageValue("spec") + " " + mDataList.get(position).getGoodsSpec());
        }
        tvInPrice.setText(getLanguageValue("purchasePrice") + ":RM" + DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        tvSalePrice.setText(getLanguageValue("sellingPrice") + ":RM" + DFUtils.getNum2(mDataList.get(position).getGoodsSalePrice()));
        tvCount.setText(getLanguageValue("transfer") + getLanguageValue("quantity") + ":" + mDataList.get(position).getPurchaseListDetailCount() + mDataList.get(position).getGoodsUnit());
        if (onItemClickListener != null) {
            ivDel.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
