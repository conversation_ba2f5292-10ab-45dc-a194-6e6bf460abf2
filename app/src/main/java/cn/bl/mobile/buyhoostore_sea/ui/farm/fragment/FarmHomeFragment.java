package cn.bl.mobile.buyhoostore_sea.ui.farm.fragment;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.beiing.leafchart.OutsideLineChart;
import com.beiing.leafchart.bean.Axis;
import com.beiing.leafchart.bean.AxisValue;
import com.beiing.leafchart.bean.Line;
import com.beiing.leafchart.bean.PointValue;
import com.yxl.commonlibrary.base.BaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.SellBean;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.NPLuBaiTiaoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.NPLuZhiChuActivity;
import cn.bl.mobile.buyhoostore_sea.ui.farm.bean.FarmIndexData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.activity.FarmActivity;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-首页
 * Created by jingang on 2023/5/28
 */
@SuppressLint("NonConstantResourceId")
public class FarmHomeFragment extends BaseFragment {
    @BindView(R.id.tvMonthSale)
    TextView tvMonthSale;//月销售额
    @BindView(R.id.tvMonthLoan)
    TextView tvMonthLoan;//余欠款额
    @BindView(R.id.tvLoanTotal)
    TextView tvLoanTotal;//欠款总额
    @BindView(R.id.tvTodaySale)
    TextView tvTodaySale;//今日销额
    @BindView(R.id.tvTodaySaleCount)
    TextView tvTodaySaleCount;
    @BindView(R.id.tvTodayLoan)
    TextView tvTodayLoan;//今日欠款
    @BindView(R.id.tvTodayLoanCount)
    TextView tvTodayLoanCount;
    @BindView(R.id.tvTodayIncome)
    TextView tvTodayIncome;//今日实收
    @BindView(R.id.tvTodayIncomeCount)
    TextView tvTodayIncomeCount;
    @BindView(R.id.tvTodayAgent)
    TextView tvTodayAgent;//今日代卖
    @BindView(R.id.lineChart)
    OutsideLineChart lineChart;

    //折线图
    private List<Double> list_sell = new ArrayList<>();
    private List<String> weekend = new ArrayList<>();
    private Double best_num = 0.0;//最大值

    /**
     * 初始化fragment
     *
     * @return
     */
    public static FarmHomeFragment newInstance() {
        FarmHomeFragment fragment = new FarmHomeFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fm_home_farm;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onResume() {
        super.onResume();
        getFarm_index();
        getSaleSumTrend();
    }

    @OnClick({R.id.tvConfirm, R.id.tvRecordCredit, R.id.tvRecordPay, R.id.tvRepayment})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvConfirm:
                //开单
                goToActivity(FarmActivity.class);
                break;
            case R.id.tvRecordCredit:
                //录白条
                goToActivity(NPLuBaiTiaoActivity.class);
                break;
            case R.id.tvRecordPay:
                //录支付
                goToActivity(NPLuZhiChuActivity.class);
                break;
            case R.id.tvRepayment:
                //快速还款
                break;
        }
    }

    /**
     * 更新UI
     *
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void setUI(FarmIndexData data) {
        if (data == null) {
            return;
        }
        tvMonthSale.setText(DFUtils.getNum3(data.getMonthSale()));
        tvMonthLoan.setText(DFUtils.getNum3(data.getMonthLoan()));
        tvLoanTotal.setText(DFUtils.getNum3(data.getLoanTotal()));
        tvTodaySale.setText(DFUtils.getNum3(data.getTodaySale()));
        tvTodaySaleCount.setText("销售金额(" + data.getTodaySaleCount() + "单)");
        tvTodayLoan.setText(DFUtils.getNum3(data.getTodayLoan()));
        tvTodayLoanCount.setText("销售欠款(" + data.getTodayLoanCount() + "单)");
        tvTodayIncome.setText(DFUtils.getNum3(data.getTodayIncome()));
        tvTodayIncomeCount.setText("实收汇总(" + data.getTodayIncomeCount() + "单)");
        tvTodayAgent.setText(DFUtils.getNum3(data.getTodayAgentMoney()));
    }

    //曲线阴影
    private void initLineChart(Double best_num) {
        Axis axisX = new Axis(getAxisValuesX());
        axisX.setAxisColor(Color.parseColor("#002DB7F5")).setTextColor(Color.DKGRAY).setHasLines(true);
        Axis axisY = new Axis(getAxisValuesY());
        axisY.setAxisColor(Color.parseColor("#797979")).setTextColor(Color.DKGRAY).setHasLines(false).setShowText(true);
        lineChart.setAxisX(axisX);
        lineChart.setAxisY(axisY);
        lineChart.setChartData(getFoldLine(best_num));
        lineChart.showWithAnimation(1000);
    }

    private List<AxisValue> getAxisValuesX() {
        List<AxisValue> axisValues = new ArrayList<>();
        if (list_sell.size() > 7) {
            for (int i = 1; i <= list_sell.size(); i++) {
                AxisValue value = new AxisValue();
                value.setLabel(weekend.get(i - 1));     //月的
                axisValues.add(value);
            }
        } else {
            for (int i = 1; i <= list_sell.size(); i++) {
                AxisValue value = new AxisValue();
                value.setLabel(weekend.get(i - 1));    //周的
                axisValues.add(value);
            }
        }
        return axisValues;
    }


    /**
     * 设置间隔大小
     *
     * @return
     */
    private List<AxisValue> getAxisValuesY() {
        List<AxisValue> axisValues = new ArrayList<>();
        if (list_sell.size() < 8) {
            for (int i = 0; i < list_sell.size(); i++) {
                AxisValue value = new AxisValue();
                if (i == 0) {
                    value.setLabel(String.valueOf(0));
                } else {
                    value.setLabel(String.valueOf((int) (best_num / (list_sell.size() - 1) * i)));
                }
                axisValues.add(value);
            }
        } else {
            list_sell.size();
            for (int i = 0; i < list_sell.size() / 3; i++) {
                AxisValue value = new AxisValue();
                if (i == 0) {
                    value.setLabel(String.valueOf(0));
                } else {
                    value.setLabel(String.valueOf((int) (best_num / (list_sell.size() / 3 - 1) * i)));
                }
                axisValues.add(value);
            }
        }
        return axisValues;
    }

    /**
     * 设置对应的数据额
     *
     * @return
     */
    private Line getFoldLine(Double best_num) {
        List<PointValue> pointValues = new ArrayList<>();
        for (int i = 0; i < list_sell.size(); i++) {
            PointValue pointValue = new PointValue();
            pointValue.setX((i) / (float) (list_sell.size() - 1));
            Double var = list_sell.get(i);
            pointValue.setLabel(String.valueOf(list_sell.get(i)));
            pointValue.setY((float) (var / best_num));
            pointValues.add(pointValue);
        }
        Line line = new Line(pointValues);
        line.setLineColor(Color.parseColor("#9CA4A7"))
                .setLineWidth(1)
                .setPointColor(Color.YELLOW)
                .setPointRadius(3)
                .setHasPoints(true)
                .setFill(true)
                .setFillColor(Color.parseColor("#1CA4FC"))
                .setHasLabels(true)
                .setLabelColor(Color.parseColor("#1CA4FC"));
        return line;
    }

    /**
     * 农批首页统计
     */
    private void getFarm_index() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getFarm_index(),
                map,
                FarmIndexData.class,
                new RequestListener<FarmIndexData>() {
                    @Override
                    public void success(FarmIndexData farmIndexData) {
                        setUI(farmIndexData);
                    }
                });
    }

    /**
     * 经营统计-销售额走势
     */
    public void getSaleSumTrend() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("checkType", 2);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getShopSellzoushi(),
                params,
                SellBean.class,
                new RequestListListener<SellBean>() {
                    @Override
                    public void onResult(List<SellBean> list) {
                        list_sell.clear();
                        weekend.clear();
                        best_num = 0.0;
                        for (int i = 0; i <list.size(); i++) {
                            list_sell.add(list.get(i).getSaleTotal());
                            weekend.add(list.get(i).getTime());
                            double st_money = list.get(i).getSaleTotal();
                            if (st_money > best_num) {
                                best_num = st_money;
                            }
                        }
                        initLineChart(best_num);
                    }
                });
    }

}
