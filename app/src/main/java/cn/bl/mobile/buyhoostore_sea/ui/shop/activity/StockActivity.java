package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * 店铺-库存
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class StockActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvStock0)
    TextView tvStock0;
    @BindView(R.id.tvStock1)
    TextView tvStock1;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_stock;
    }

    @Override
    public void initViews() {
    }

    @OnClick({R.id.ivBack, R.id.linStock0, R.id.linStock1})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linStock0:
                //库存查询
                goToActivity(StockSearchActivity.class);
                break;
            case R.id.linStock1:
                //库存预警
                goToActivity(StockYuJingActivity.class);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("inventory"));
        tvStock0.setText(getLanguageValue("inventory")+getLanguageValue("inquire"));
        tvStock1.setText(getLanguageValue("inventory")+getLanguageValue("warn"));
    }
}