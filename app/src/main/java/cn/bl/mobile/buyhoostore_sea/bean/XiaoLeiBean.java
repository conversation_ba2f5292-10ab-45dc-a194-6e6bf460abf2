package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Created by Administrator on 2017/10/26 0026.
 */
public class XiaoLeiBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"kindName":"瓜果类","kindUnique":120001},{"kindName":"柑橘类","kindUnique":120002},{"kindName":"仁果类","kindUnique":120003},{"kindName":"浆果类","kindUnique":120004},{"kindName":"核果类","kindUnique":120005},{"kindName":"热带亚热带水果","kindUnique":120006}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * kindName : 瓜果类
         * kindUnique : 120001
         */

        private String kindName;
        private int kindUnique;

        public String getKindName() {
            return kindName;
        }

        public void setKindName(String kindName) {
            this.kindName = kindName;
        }

        public int getKindUnique() {
            return kindUnique;
        }

        public void setKindUnique(int kindUnique) {
            this.kindUnique = kindUnique;
        }
    }
}
