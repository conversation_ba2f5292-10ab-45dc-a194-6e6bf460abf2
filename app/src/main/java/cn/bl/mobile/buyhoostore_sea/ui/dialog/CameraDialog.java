package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（拍照、相册选择图片）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class CameraDialog extends BaseDialog {
    @BindView(R.id.tvDialogCamera)
    TextView tvDialogCamera;
    @BindView(R.id.tvDialogAlbum)
    TextView tvDialogAlbum;
    @BindView(R.id.tvDialogCancel)
    TextView tvDialogCancel;

    public static void showDialog(Context context, MyListener listener) {
        CameraDialog.listener = listener;
        CameraDialog dialog = new CameraDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CameraDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_camera);
        ButterKnife.bind(this);
        tvDialogCamera.setText(getLanguageValue("photograph"));
        tvDialogAlbum.setText(getLanguageValue("phoneSelect"));
        tvDialogCancel.setText(getLanguageValue("cancel"));
    }

    @OnClick({R.id.tvDialogCamera, R.id.tvDialogAlbum, R.id.tvDialogCancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogCamera:
                //拍照
                if (listener != null) {
                    listener.onClick(view, 0);
                    dismiss();
                }
                break;
            case R.id.tvDialogAlbum:
                //从手机相册选择
                if (listener != null) {
                    listener.onClick(view, 1);
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.拍照 1.从手机相册选择
         */
        void onClick(View view, int type);
    }
}
