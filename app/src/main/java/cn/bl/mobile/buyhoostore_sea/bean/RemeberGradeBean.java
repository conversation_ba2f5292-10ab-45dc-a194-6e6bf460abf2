package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

public class RemeberGradeBean {


    /**
     * status : 1
     * msg : null
     * totals : 0
     * data : [{"cusLevelId":"cusLevelId","cusLevelPoints":"cusLevelPoints","cusLevelName":"cusLevelName","cusLevelDiscount":"cusLevelDiscount"}]
     */

    private int status;
    private Object msg;
    private int totals;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public int getTotals() {
        return totals;
    }

    public void setTotals(int totals) {
        this.totals = totals;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * cusLevelId : cusLevelId
         * cusLevelPoints : cusLevelPoints
         * cusLevelName : cusLevelName
         * cusLevelDiscount : cusLevelDiscount
         */

        private String cusLevelId;
        private String cusLevelPoints;
        private String cusLevelName;
        private String cusLevelDiscount;

        public String getCusLevelId() {
            return cusLevelId;
        }

        public void setCusLevelId(String cusLevelId) {
            this.cusLevelId = cusLevelId;
        }

        public String getCusLevelPoints() {
            return cusLevelPoints;
        }

        public void setCusLevelPoints(String cusLevelPoints) {
            this.cusLevelPoints = cusLevelPoints;
        }

        public String getCusLevelName() {
            return cusLevelName;
        }

        public void setCusLevelName(String cusLevelName) {
            this.cusLevelName = cusLevelName;
        }

        public String getCusLevelDiscount() {
            return cusLevelDiscount;
        }

        public void setCusLevelDiscount(String cusLevelDiscount) {
            this.cusLevelDiscount = cusLevelDiscount;
        }
    }
}