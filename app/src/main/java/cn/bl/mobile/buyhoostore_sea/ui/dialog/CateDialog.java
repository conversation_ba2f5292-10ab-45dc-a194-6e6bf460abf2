package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CateChildData;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.Cate2DialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.CateChildDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.CateDialogAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（选择商品分类）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class CateDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.rvDialogCate)
    RecyclerView rvCate;
    @BindView(R.id.rvDialogCate1)
    RecyclerView rvCate1;
    @BindView(R.id.rvDialogCate2)
    RecyclerView rvCate2;

    private static String cateId,
            cateId1,
            cateId2,
            cateName,
            cateName1,
            cateName2;

    //一级分类
    private List<CateData> cateList = new ArrayList<>();
    private CateDialogAdapter cateAdapter;

    //二级分类
    private List<CateChildData> cateList1 = new ArrayList<>();
    private CateChildDialogAdapter cateAdapter1;

    //三级分类
    private List<CateChildData> cateList2 = new ArrayList<>();
    private Cate2DialogAdapter cateAdapter2;

    public static void showDialog(Context context, String cateId, String cateId1, String cateId2, MyListener listener) {
        CateDialog.cateId = cateId;
        CateDialog.cateId1 = cateId1;
        CateDialog.cateId2 = cateId2;
        CateDialog.listener = listener;
        CateDialog dialog = new CateDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public CateDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_cate);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("choose") + getLanguageValue("classification"));
        tvConfirm.setText(getLanguageValue("confirm"));
        setAdapter();
        getCate();
    }

    @OnClick({R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogConfirm:
                if (TextUtils.isEmpty(cateId)) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("majorCategories"));
                    return;
                }
                if (TextUtils.isEmpty(cateId1)) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("subclass"));
                    return;
                }
                if (listener != null) {
                    listener.onClick(cateId, cateName, cateId1, cateName1, cateId2, cateName2);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //一级分类
        cateAdapter = new CateDialogAdapter(getContext());
        rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < cateList.size(); i++) {
                    cateList.get(i).setCheck(false);
                }
                cateList.get(position).setCheck(true);
                cateId = cateList.get(position).getGroupUnique();
                cateName = cateList.get(position).getGroupName();
                cateAdapter.setDataList(cateList);
                //二级分类
                cateList1.clear();
                cateList1.addAll(cateList.get(position).getKindDetail());
                for (int i = 0; i < cateList1.size(); i++) {
                    if (cateList1.get(i).isCheck()) {
                        cateList1.get(i).setCheck(false);
                    }
                }
                cateList2.clear();
                if (cateList1.size() > 0) {
                    cateList1.get(0).setCheck(true);
                    cateId1 = cateList1.get(0).getKindUnique();
                    cateName1 = cateList1.get(0).getKindName();
                    if (cateList1.get(0).getKindDetail() != null) {
                        cateList2.addAll(cateList1.get(0).getKindDetail());
                    }
                } else {
                    cateId1 = "";
                    cateName1 = "";
                }
                cateAdapter1.setDataList(cateList1);
                //三级分类
                cateId2 = "";
                cateName2 = "";
                for (int i = 0; i < cateList2.size(); i++) {
                    if (cateList2.get(i).isCheck()) {
                        cateList2.get(i).setCheck(false);
                    }
                }
                cateAdapter2.setDataList(cateList2);
            }
        });
        //二级分类
        cateAdapter1 = new CateChildDialogAdapter(getContext());
        rvCate1.setAdapter(cateAdapter1);
        cateAdapter1.setOnItemClickListener((view, position) -> {
            if (!cateList1.get(position).isCheck()) {
                for (int i = 0; i < cateList1.size(); i++) {
                    cateList1.get(i).setCheck(false);
                }
                cateList1.get(position).setCheck(true);
                cateId1 = cateList1.get(position).getKindUnique();
                cateName1 = cateList1.get(position).getKindName();
                cateAdapter1.setDataList(cateList1);
                //三级分类
                cateId2 = "";
                cateName2 = "";
                cateList2.clear();
                if (cateList1.get(position).getKindDetail() != null) {
                    cateList2.addAll(cateList1.get(position).getKindDetail());
                }
                for (int i = 0; i < cateList2.size(); i++) {
                    if (cateList2.get(i).isCheck()) {
                        cateList2.get(i).setCheck(false);
                    }
                }
                cateAdapter2.setDataList(cateList2);
            }
        });
        //三级分类
        cateAdapter2 = new Cate2DialogAdapter(getContext());
        rvCate2.setAdapter(cateAdapter2);
        cateAdapter2.setOnItemClickListener((view, position) -> {
            if (cateList2.get(position).isCheck()) {
                cateList2.get(position).setCheck(false);
                cateId2 = "";
                cateName2 = "";
            } else {
                for (int i = 0; i < cateList2.size(); i++) {
                    cateList2.get(i).setCheck(false);
                }
                cateList2.get(position).setCheck(true);
                cateId2 = cateList2.get(position).getKindUnique();
                cateName2 = cateList2.get(position).getKindName();
            }
            cateAdapter2.setDataList(cateList2);
        });
    }

    /**
     * 自定义商品分类查询
     */
    private void getCate() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("status", 1);//1.启用 2.禁用
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsCate(),
                map,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        hideDialog();
                        //一级分类
                        cateList.clear();
                        cateList.addAll(list);
                        if (TextUtils.isEmpty(cateId)) {
                            if (cateList.size() > 0) {
                                cateList.get(0).setCheck(true);
                                cateId = cateList.get(0).getGroupUnique();
                                cateName = cateList.get(0).getGroupName();
                                //二级分类
                                cateList1.clear();
                                cateList1.addAll(cateList.get(0).getKindDetail());
                            }
                        } else {
                            for (int i = 0; i < cateList.size(); i++) {
                                if (cateList.get(i).getGroupUnique().equals(cateId)) {
                                    cateList.get(i).setCheck(true);
                                    cateName = cateList.get(i).getGroupName();
                                    //二级分类
                                    cateList1.clear();
                                    cateList1.addAll(cateList.get(i).getKindDetail());
                                }
                            }
                        }
                        cateAdapter.setDataList(cateList);
                        //二级分类
                        if (TextUtils.isEmpty(cateId1)) {
                            if (cateList1.size() > 0) {
                                cateList1.get(0).setCheck(true);
                                cateId1 = cateList1.get(0).getKindUnique();
                                cateName1 = cateList1.get(0).getKindName();
                                //三级分类
                                cateList2.clear();
                                cateList2.addAll(cateList1.get(0).getKindDetail());
                            } else {
                                cateId1 = "";
                                cateName1 = "";
                            }
                        } else {
                            for (int i = 0; i < cateList1.size(); i++) {
                                if (cateList1.get(i).getKindUnique().equals(cateId1)) {
                                    cateList1.get(i).setCheck(true);
                                    cateName1 = cateList1.get(i).getKindName();
                                    //三级分类
                                    cateList2.clear();
                                    cateList2.addAll(cateList1.get(i).getKindDetail());
                                }
                            }
                        }
                        cateAdapter1.setDataList(cateList1);

                        //三级分类
                        if (!TextUtils.isEmpty(cateId2)) {
                            for (int i = 0; i < cateList2.size(); i++) {
                                if (cateList2.get(i).getKindUnique().equals(cateId2)) {
                                    cateList2.get(i).setCheck(true);
                                    cateName2 = cateList2.get(i).getKindName();
                                }
                            }
                        }
                        cateAdapter2.setDataList(cateList2);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(String cateId, String cateName,
                     String cateId1, String cateName1,
                     String cateId2, String cateName2);
    }
}
