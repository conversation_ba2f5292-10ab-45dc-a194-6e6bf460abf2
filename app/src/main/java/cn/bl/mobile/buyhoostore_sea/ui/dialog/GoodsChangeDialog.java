package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（兑换商品）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class GoodsChangeDialog extends BaseDialog {
    @BindView(R.id.ivDialogStatus)
    ImageView ivStatus;
    @BindView(R.id.tvDialogStatus)
    TextView tvStatus;
    @BindView(R.id.tvDialogTips)
    TextView tvTips;

    private static boolean isSuccess;
    private static String reason;

    public static void showDialog(Context context, boolean isSuccess, String reason) {
        GoodsChangeDialog.isSuccess = isSuccess;
        GoodsChangeDialog.reason = reason;
        GoodsChangeDialog dialog = new GoodsChangeDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public GoodsChangeDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_goods_change);
        ButterKnife.bind(this);
        if (isSuccess) {
            ivStatus.setImageResource(R.mipmap.ic_credit_state001);
            tvStatus.setText("兑换成功");
            tvTips.setVisibility(View.GONE);
        } else {
            ivStatus.setImageResource(R.mipmap.ic_credit_state003);
            tvStatus.setText("兑换失败");
            tvTips.setVisibility(View.VISIBLE);
            tvTips.setText(reason);
        }
    }

    @OnClick({R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogConfirm:
                //知道了
                dismiss();
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

}
