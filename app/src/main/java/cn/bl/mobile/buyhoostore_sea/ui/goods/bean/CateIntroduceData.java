package cn.bl.mobile.buyhoostore_sea.ui.goods.bean;

import java.io.Serializable;

/**
 * Describe:商品分类说明（实体类）
 * Created by jingang on 2023/8/23
 */
public class CateIntroduceData implements Serializable {
    /**
     * title : 分类管理说明
     * content : 开启自定义分类后，所有商品分类会更新为默认自定义分类；可先创建自定义分类，再对商品分类进行修改；
     */

    private String title;
    private String content;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
