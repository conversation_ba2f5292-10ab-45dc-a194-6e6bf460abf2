package cn.bl.mobile.buyhoostore_sea.ui;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.graphics.Color;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.ui.login.GuideActivity;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PrivacyAgainAgainDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PrivacyAgainDialog;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.PrivacyDialog;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.utils.LogLongUtils;
import cn.bl.mobile.buyhoostore_sea.utils.statusbar.StatusBarUtil;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * 启动页
 */
@SuppressLint("CustomSplashScreen")
public class SplashActivity extends BaseActivity {
    private SharedPreferences preferences;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_splash;
    }

    @Override
    public void initViews() {
        //设置状态栏
        StatusBarUtil.setLightStatusBar(this, true, true);
        getWindow().setNavigationBarColor(Color.parseColor("#ffffff"));

        preferences = getSharedPreferences("count", MODE_PRIVATE);
        new Handler().postDelayed(() -> {
//            jumpHome();
            getLanguageValue();
        }, 1000);
    }

    /**
     * 隐私政策
     */
    private void showDialogPrivacy() {
        PrivacyDialog.showDialog(TAG, new PrivacyDialog.MyListener() {
            @Override
            public void onPrivacyClick() {
                WebActivity.toWebActivity(TAG, getLanguageValue("privacyPolicy"), ZURL.CONSTANT_QUERY_PRIVACY_POLICY);
            }

            @Override
            public void onClick(int type) {
                //type：0.不同意 1.同意
                if (type == 1) {
                    gotoGuideActivity();
                } else {
                    shoDialogPrivacy1();
                }
            }
        });
    }

    /**
     * 隐私政策-二次确认
     */
    private void shoDialogPrivacy1() {
        PrivacyAgainDialog.showDialog(TAG, type1 -> {
            //type1:0.仍不同意 1.同意
            if (type1 == 1) {
                gotoGuideActivity();
            } else {
                shoDialogPrivacy2();
            }
        });
    }

    /**
     * 隐私政策-三次确认
     */
    private void shoDialogPrivacy2() {
        PrivacyAgainAgainDialog.showDialog(TAG, type2 -> {
            //type2：0.退出应用 1.再次查看
            if (type2 == 1) {
                showDialogPrivacy();
            } else {
                finish();
            }
        });
    }

    /**
     * 跳转页面判断
     */
    public void jumpHome() {
        // 读取SharedPreferences中需要的数据
        int count = preferences.getInt("count", 0);
        // 判断程序与第几次运行，如果是第一次运行则跳转到引导页面
        if (count == 0) {
            showDialogPrivacy();
        } else {
            // 跳转到登录界面
            if (TextUtils.isEmpty(getShop_id())) {
                goToActivity(LoginActivity.class);
            } else {
                goToActivity(MainActivity.class);
            }
            finish();
        }
    }

    /**
     * 跳转到guide页
     */
    private void gotoGuideActivity() {
        goToActivity(GuideActivity.class);
        finish();
        Editor editor = preferences.edit();
        int count = preferences.getInt("count", 0);
        // 存入数据
        if (count > 100) {
            count = 100;
        }
        editor.putInt("count", ++count);
        // 提交修改
        editor.commit();
    }

    /**
     * 根据语言查询多语言值
     */
    private void getLanguageValue() {
        String language = SharedPreferencesUtil.getInstantiation(this).getString("", Constants.SP_LANGUAGE);
        String mLanguage;
        if (TextUtils.isEmpty(language)) {
            mLanguage = "chinese";
        } else {
            switch (language) {
                case "en":
                    mLanguage = "english";
                    break;
                case "th":
                    mLanguage = "hai";
                    break;
                case "ru":
                    mLanguage = "russian";
                    break;
                case "ms":
                    mLanguage = "malay";
                    break;
                case "kk":
                    mLanguage = "kazakh";
                    break;
                case "vi":
                    mLanguage = "vie_VN";
                    break;
                default:
                    mLanguage = "chinese";
                    break;
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("type", 3);//端类型：安卓收银机（1），windows收银机（2），安卓APP（3），苹果APP（4），后台（5），小程序（6），H5（7）
        map.put("language", mLanguage);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getLanguageValue(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        LogLongUtils.e(tag, "多语言值 = " + s);
                        try {
                            JSONObject jsonObject = new JSONObject(s);
                            int status = jsonObject.getInt("status");
                            if (status == Constants.SUCCESS_CODE) {
                                BaseApplication.mLanguageValueJson = jsonObject.getJSONObject("data");
                                jumpHome();
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
//                            finish();
                            jumpHome();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
//                        finish();
                        jumpHome();
                    }
                });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

}
