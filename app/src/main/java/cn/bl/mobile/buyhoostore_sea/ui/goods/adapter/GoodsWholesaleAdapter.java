package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.GoodsWholesaleData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.utils.DecimalDigitsInputFilter;

/**
 * Describe: 商品批发单价（适配器）
 * Created by jingang on 2025/5/19
 */
public class GoodsWholesaleAdapter extends BaseAdapter<GoodsWholesaleData> {
    public GoodsWholesaleAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_wholesale;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvDel, tvCountValue, tvPriceValue;
        EditText etCount, etPrice;
        tvName = holder.getView(R.id.tvItemName);
        tvDel = holder.getView(R.id.tvItemDel);
        tvCountValue = holder.getView(R.id.tvItemCountValue);
        etCount = holder.getView(R.id.etItemCount);
        tvPriceValue = holder.getView(R.id.tvItemPriceValue);
        etPrice = holder.getView(R.id.etItemPrice);
        tvDel.setText(getLanguageValue("delete"));
        tvCountValue.setText(getLanguageValue("startingQuantity"));
        etCount.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("startingQuantity"));
        tvPriceValue.setText(getLanguageValue("wholesaleUnitPrice"));
        etPrice.setHint(getLanguageValue("pleaseEnter") + getLanguageValue("wholesaleUnitPrice"));

        etPrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});

        tvName.setText(getLanguageValue("pricingRules") + (position + 1));
        etCount.setText(mDataList.get(position).getWholesaleCount() > 0 ? DFUtils.getNum4(mDataList.get(position).getWholesaleCount()) : "");
        etPrice.setText(mDataList.get(position).getWholesalePrice() > 0 ? DFUtils.getNum4(mDataList.get(position).getWholesalePrice()) : "");

        tvDel.setVisibility(position == 0 ? View.GONE : View.VISIBLE);
        TextWatcher watcherCount = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                double count = TextUtils.isEmpty(str) ? 0 : Double.parseDouble(str);
                mDataList.get(position).setWholesaleCount(count);
                if (listener != null) {
                    listener.onCount(count, position);
                }
            }
        };
        etCount.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcherCount);
            } else {
                mV.removeTextChangedListener(watcherCount);
            }
        });

        TextWatcher watcherPrice = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                double price = TextUtils.isEmpty(str) ? 0 : Double.parseDouble(str);
                mDataList.get(position).setWholesalePrice(price);
                if (listener != null) {
                    listener.onPrice(price, position);
                }
            }
        };
        etPrice.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcherPrice);
            } else {
                mV.removeTextChangedListener(watcherPrice);
            }
        });

        if (listener != null) {
            tvDel.setOnClickListener(v -> listener.onDelClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onDelClick(int position);

        void onCount(double count, int position);

        void onPrice(double price, int position);
    }
}
