package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.adapter.SupplierAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierEditActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.activity.SupplierInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.bean.SupplierListData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商列表
 * Created by jingang on 2023/10/11
 */
@SuppressLint("NonConstantResourceId")
public class SupplierFragment extends LazyBaseFragment {
    @BindView(R.id.tvTotalValue)
    TextView tvTotalValue;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;
    @BindView(R.id.tvAdd)
    TextView tvAdd;

    private SupplierAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    private boolean isVisible, isRefresh;

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SupplierFragment newInstance() {
        SupplierFragment fragment = new SupplierFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_supplier;
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        getSupplierList();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isRefresh) {
            page = 1;
            getSupplierList();
        }
    }

    @OnClick({R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvAdd:
                //添加供货商
                goToActivity(SupplierEditActivity.class);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_LIST:
                //刷新列表
                if (isVisible) {
                    page = 1;
                    getSupplierList();
                } else {
                    isRefresh = true;
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTotalValue.setText(getLanguageValue("totalArrears"));
        tvAdd.setText(getLanguageValue("addTo")+getLanguageValue("supplier"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new SupplierAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (TextUtils.isEmpty(dataList.get(position).getSupplierUnique())) {
                    showMessage(getLanguageValue("notYet")+getLanguageValue("supplier")+getLanguageValue("information"));
                    return;
                }
                startActivity(new Intent(getActivity(), SupplierInfoActivity.class)
                        .putExtra("unique", dataList.get(position).getSupplierUnique())
                        .putExtra("name", dataList.get(position).getSupplierName())
                );
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(getActivity(),
                        getLanguageValue("confirm")+getLanguageValue("delete")+getLanguageValue("supplier")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postSupplierDel(dataList.get(position).getId(), position);
                        });
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getSupplierList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getSupplierList();
            }
        });
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        showDialog();
        hideSoftInput(getActivity());
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("queryMeg", SupplierActivity.keyWords);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierList(),
                map,
                SupplierListData.class,
                new RequestListener<SupplierListData>() {
                    @Override
                    public void success(SupplierListData data) {
                        hideDialog();
                        isRefresh = false;
                        tvTotal.setText(DFUtils.getNum2(data.getTotalDebts()));
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(data.getList());
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 供货商删除
     *
     * @param id
     * @param position
     */
    private void postSupplierDel(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("createId", getStaff_id());
        map.put("createBy", getStaff_name());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}
