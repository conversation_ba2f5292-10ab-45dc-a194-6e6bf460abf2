package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:配送设置-添加配送员
 * Created by jingang on 2023/11/1
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class ShopRiderAddActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.etName)
    EditText etName;
    @BindView(R.id.tvMobileValue)
    TextView tvMobileValue;
    @BindView(R.id.etMobile)
    EditText etMobile;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String id;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_rider_add;
    }

    @Override
    public void initViews() {
        id = getIntent().getStringExtra("id");
        if (!TextUtils.isEmpty(id)) {
            etName.setText(getIntent().getStringExtra("name"));
            etMobile.setText(getIntent().getStringExtra("phone"));
        }
    }

    @OnClick({R.id.ivBack, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvConfirm:
                //保存
                save();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("addTo")+getLanguageValue("distributor"));
        tvNameValue.setText(getLanguageValue("personnel")+getLanguageValue("fullName"));
        etName.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("personnel")+getLanguageValue("fullName"));
        tvMobileValue.setText(getLanguageValue("phoneNum"));
        etMobile.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("phoneNum"));
        tvConfirm.setText(getLanguageValue("preservation"));
    }

    /**
     * 保存设置
     */
    private void save() {
        if (TextUtils.isEmpty(etName.getText().toString().trim())) {
            showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("personnel")+getLanguageValue("fullName"));
            return;
        }
        if (TextUtils.isEmpty(etMobile.getText().toString().trim())) {
            showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("phoneNum"));
            return;
        }
//        if (etMobile.getText().toString().trim().length() < 11) {
//            showMessage(getLanguageValue("enterCorrectPhone"));
//            return;
//        }
        if (!NetworkUtils.isConnectInternet(this)) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        hideSoftInput(this);
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShop_id());
        if (!TextUtils.isEmpty(id)) {
            map.put("id", id);
        }
        map.put("courier_name", etName.getText().toString().trim());
        map.put("courier_phone", etMobile.getText().toString().trim());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateDistributionPerson(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        setResult(Constants.RIDER, new Intent());
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                    }
                });
    }
}
