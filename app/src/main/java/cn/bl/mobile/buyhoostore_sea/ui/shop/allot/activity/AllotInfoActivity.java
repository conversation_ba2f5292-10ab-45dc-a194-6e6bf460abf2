package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.BaseData;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotAddGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-调拨单-详情
 * Created by jingang on 2023/5/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class AllotInfoActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvOutTips)
    TextView tvOutTips;
    @BindView(R.id.tvInTips)
    TextView tvInTips;
    @BindView(R.id.tvBg)
    TextView tvBg;
    @BindView(R.id.tvOutName)
    TextView tvOutName;
    @BindView(R.id.tvStatus)
    TextView tvStatus;
    @BindView(R.id.tvInName)
    TextView tvInName;
    @BindView(R.id.tvNameValue)
    TextView tvNameValue;
    @BindView(R.id.tvName)
    TextView tvName;
    @BindView(R.id.tvTimeValue)
    TextView tvTimeValue;
    @BindView(R.id.tvTime)
    TextView tvTime;
    @BindView(R.id.linRecipients)
    LinearLayout linRecipients;
    @BindView(R.id.tvRecipientsNameValue)
    TextView tvRecipientsNameValue;
    @BindView(R.id.tvRecipientsName)
    TextView tvRecipientsName;
    @BindView(R.id.tvRecipientsTimeValue)
    TextView tvRecipientsTimeValue;
    @BindView(R.id.tvRecipientsTime)
    TextView tvRecipientsTime;
    @BindView(R.id.tvGoodsValue)
    TextView tvGoodsValue;
    @BindView(R.id.rvGoods)
    RecyclerView rvGoods;
    @BindView(R.id.tvOperation)
    TextView tvOperation;

    private AllotData data;
    private int id, status;

    private AllotAddGoodsAdapter mAdapter;
    private List<AllotData.DetailInfoListBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_allot_info;
    }

    @Override
    public void initViews() {
        data = (AllotData) getIntent().getSerializableExtra("data");
        setAdapter();
    }

    @OnClick({R.id.ivBack, R.id.tvOperation})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvOperation:
                //操作
                if (status == 2) {
                    //撤销调拨
                    IAlertDialog.showDialog(this,
                            getLanguageValue("confirm") + getLanguageValue("revoke") + getLanguageValue("transfer") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postAllotQuash();
                            });
                } else if (status == 3) {
                    //确认收货
                    IAlertDialog.showDialog(this,
                            getLanguageValue("confirm") + getLanguageValue("receipt") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postAllotFinish();
                            });
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("transfer") + getLanguageValue("details"));
        tvBg.setText(getLanguageValue("transfer") + getLanguageValue("to"));
        tvNameValue.setText(getLanguageValue("apply") + getLanguageValue("people") + ":");
        tvTimeValue.setText(getLanguageValue("apply") + getLanguageValue("time") + ":");
        tvRecipientsNameValue.setText(getLanguageValue("receipt") + getLanguageValue("people") + ":");
        tvRecipientsTimeValue.setText(getLanguageValue("receipt") + getLanguageValue("time") + ":");
        tvGoodsValue.setText(getLanguageValue("transfer") + getLanguageValue("commodity"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new AllotAddGoodsAdapter(this);
        rvGoods.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            IAlertDialog.showDialog(this,
                    getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("commodity") + "?",
                    getLanguageValue("confirm"),
                    (dialog, which) -> {

                    });
        });
        setUI();
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        id = data.getPurchaseListId();
        //2.待发货（待调出） 3.待收货（待调入） 4.已完成
        status = data.getAllocationStatus();
        setUIStatus();
        //本店判断
        String outId = String.valueOf(data.getStorehouseOutId()),
                inId = String.valueOf(data.getStorehouseInId());
        if (TextUtils.isEmpty(getShop_id()) || TextUtils.isEmpty(outId)) {
            tvOutTips.setText(getLanguageValue("transferOutStore"));
        } else {
            if (getShop_id().equals(outId)) {
                tvOutTips.setText(getLanguageValue("transferOutStore") + "(" + getLanguageValue("ourStore") + ")");
            } else {
                tvOutTips.setText(getLanguageValue("transferOutStore"));
            }
        }
        if (TextUtils.isEmpty(getShop_id()) || TextUtils.isEmpty(outId)) {
            tvInTips.setText(getLanguageValue("transferIntoStore"));
        } else {
            if (getShop_id().equals(inId)) {
                tvInTips.setText(getLanguageValue("transferIntoStore") + "(" + getLanguageValue("ourStore") + ")");
            } else {
                tvInTips.setText(getLanguageValue("transferIntoStore"));
            }
        }
        tvOutName.setText(data.getStorehouseOutName());
        tvInName.setText(data.getStorehouseInName());
        tvName.setText(data.getUserName());
        tvTime.setText(DateUtils.getDateToString(data.getPurchaseListDate(), DateUtils.PATTERN_SECOND));
        //收货人
        if (data.getRecipientsUserId() == 0) {
            linRecipients.setVisibility(View.GONE);
        } else {
            linRecipients.setVisibility(View.VISIBLE);
            tvRecipientsName.setText(data.getRecipientsUserIdName());
            tvRecipientsTime.setText(DateUtils.getDateToString(data.getRecipientsTime(), DateUtils.PATTERN_SECOND));
        }

        //商品
        if (data.getDetailInfoList() != null) {
            rvGoods.setVisibility(View.VISIBLE);
            dataList.clear();
            dataList.addAll(data.getDetailInfoList());
            mAdapter.setDataList(dataList);
        } else {
            rvGoods.setVisibility(View.GONE);
        }
    }

    /**
     * 状态
     */
    private void setUIStatus() {
        switch (status) {
            case 2:
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvStatus.setText(getLanguageValue("beTransferOut"));
                tvStatus.setTextColor(getResources().getColor(R.color.blue));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("revoke") + getLanguageValue("transfer"));
                tvOperation.setBackgroundResource(R.drawable.shape_ce_kuang_22);
                tvOperation.setTextColor(getResources().getColor(R.color.color_999));
                break;
            case 3:
                tvBg.setBackgroundResource(R.mipmap.ic_arrow016);
                tvStatus.setText(getLanguageValue("beTransferEnter"));
                tvStatus.setTextColor(getResources().getColor(R.color.color_f7931e));
                tvOperation.setVisibility(View.VISIBLE);
                tvOperation.setText(getLanguageValue("confirm") + getLanguageValue("receipt"));
                tvOperation.setBackgroundResource(R.drawable.shape_jb_448df6_22);
                tvOperation.setTextColor(getResources().getColor(R.color.white));
                break;
            case 4:
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvStatus.setText(getLanguageValue("completed"));
                tvStatus.setTextColor(getResources().getColor(R.color.color_999));
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
            default:
                tvStatus.setText("");
                tvBg.setBackgroundResource(R.mipmap.ic_arrow015);
                tvOperation.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 调拨-撤销
     */
    private void postAllotQuash() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getAllot_quash(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                            finish();
                        }
                    }
                });
    }

    /**
     * 调拨-确认收货
     */
    private void postAllotFinish() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("recipientsUserId", getStaff_id());
        map.put("recipientsUserIdName", getStaff_name());
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getAllot_finish(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                            status = 4;
                            setUIStatus();
                        }
                    }
                });
    }
}
