package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.utils.DensityUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（商品-批量操作）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class GoodsPlatformDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogPrinter)
    TextView tvDialogPrinter;
    @BindView(R.id.ivDialogPrinterUp)
    ImageView ivPrinterUp;
    @BindView(R.id.tvDialogPrinterUp)
    TextView tvDialogPrinterUp;
    @BindView(R.id.ivDialogPrinterDown)
    ImageView ivPrinterDown;
    @BindView(R.id.tvDialogPrinterDown)
    TextView tvDialogPrinterDown;
    @BindView(R.id.tvDialogApplet)
    TextView tvDialogApplet;
    @BindView(R.id.ivDialogAppletUp)
    ImageView ivAppletUp;
    @BindView(R.id.tvDialogAppletUp)
    TextView tvDialogAppletUp;
    @BindView(R.id.ivDialogAppletDown)
    ImageView ivAppletDown;
    @BindView(R.id.tvDialogAppletDown)
    TextView tvDialogAppletDown;
    @BindView(R.id.tvDialogConfirm)
    TextView tvDialogConfirm;

    private static int printerStatus,//收银机上架状态 1.上架 2.下架
            appletStatus;//小程序上架状态 1.上架 2.下架

    public static void showDialog(Context context, int printerStatus, int appletStatus, MyListener listener) {
        GoodsPlatformDialog.printerStatus = printerStatus;
        GoodsPlatformDialog.appletStatus = appletStatus;
        GoodsPlatformDialog.listener = listener;
        GoodsPlatformDialog dialog = new GoodsPlatformDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public GoodsPlatformDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_platform);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("commodity")+getLanguageValue("operation"));
        tvDialogPrinter.setText(getLanguageValue("cashRegister"));
        tvDialogPrinterUp.setText(getLanguageValue("onTheShelf"));
        tvDialogPrinterDown.setText(getLanguageValue("offTheShelf"));
        tvDialogApplet.setText(getLanguageValue("miniProgram"));
        tvDialogAppletUp.setText(getLanguageValue("onTheShelf"));
        tvDialogAppletDown.setText(getLanguageValue("offTheShelf"));
        tvDialogConfirm.setText(getLanguageValue("confirm"));

        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
        switch (printerStatus) {
            case 1:
                ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }
        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
        switch (appletStatus) {
            case 1:
                ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }
    }

    @OnClick({R.id.ivDialogPrinterUp, R.id.ivDialogPrinterDown,
            R.id.ivDialogAppletUp, R.id.ivDialogAppletDown,
            R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogPrinterUp:
                //收银机上架
                if (printerStatus == 1) {
                    printerStatus = 0;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                } else {
                    printerStatus = 1;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivDialogPrinterDown:
                //收银机下架
                if (printerStatus == 2) {
                    printerStatus = 0;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                } else {
                    printerStatus = 2;
                    ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                    ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                }
                break;
            case R.id.ivDialogAppletUp:
                //小程序上架
                if (appletStatus == 1) {
                    appletStatus = 0;
                    ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                } else {
                    appletStatus = 1;
                    ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                }
                break;
            case R.id.ivDialogAppletDown:
                //小程序下架
                if (appletStatus == 2) {
                    appletStatus = 0;
                    ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                } else {
                    appletStatus = 2;
                    ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                    ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (printerStatus == 0) {
                    showMessage(getLanguageValue("cashRegisterStatus"));
                    return;
                }
                if (appletStatus == 0) {
                    showMessage(getLanguageValue("appletStatus"));
                    return;
                }
                if (listener != null) {
                    listener.onPlatform(printerStatus, appletStatus);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        void onPlatform(int printerStatus, int appletStatus);
    }
}
