package cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.commonlibrary.base.LazyBaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.activity.GouXInfoActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.goux.bean.GouXOrderListData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.supplier.adapter.SupplierGouXAdapter;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:供货商详情-购销订单
 * Created by jingang on 2023/9/4
 */
@SuppressLint("NonConstantResourceId")
public class SupplierOrderFragment extends LazyBaseFragment {
    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.tvType2)
    TextView tvType2;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private String supplierUnique;
    private int type;//0.全部 1.赊欠 2.结清

    private SupplierGouXAdapter mAdapter;
    private List<GouXOrderListData> dataList = new ArrayList<>();

    /**
     * 初始化fragment
     *
     * @return
     */
    public static SupplierOrderFragment newInstance(String unique) {
        SupplierOrderFragment fragment = new SupplierOrderFragment();
        Bundle bundle = new Bundle();
        bundle.putString("unique", unique);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_supplier_order;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        setAdapter();
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        assert getArguments() != null;
        supplierUnique = getArguments().getString("unique");
        getOrderList();
    }

    @OnClick({R.id.tvType0, R.id.tvType1, R.id.tvType2})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvType0:
                //全部
                if (type != 0) {
                    type = 0;
                    clearTextBg();
                    tvType0.setBackgroundResource(R.drawable.shape_blue_tm_kuang_8);
                    tvType0.setTextColor(getResources().getColor(R.color.blue));
                }
                break;
            case R.id.tvType1:
                //赊欠
                if (type != 1) {
                    type = 1;
                    clearTextBg();
                    tvType1.setBackgroundResource(R.drawable.shape_blue_tm_kuang_8);
                    tvType1.setTextColor(getResources().getColor(R.color.blue));
                }
                break;
            case R.id.tvType2:
                //结清
                if (type != 2) {
                    type = 2;
                    clearTextBg();
                    tvType2.setBackgroundResource(R.drawable.shape_blue_tm_kuang_8);
                    tvType2.setTextColor(getResources().getColor(R.color.blue));
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOUX_LIST:
                //刷新列表
            case Constants.SUPPLIER_LIST:
                page = 1;
                getOrderList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new SupplierGouXAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierGouXAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(getActivity(), GouXInfoActivity.class)
                        .putExtra("id", String.valueOf(dataList.get(position).getId()))
                );
            }

            @Override
            public void onCheckClick(View view, int position) {

            }

            @Override
            public void onKefuClick(View view, int position) {
                //打电话
                mobile = dataList.get(position).getSupplierPhone();
                if (PermissionUtils.checkPermissionsGroup(getActivity(), 4)) {
                    callPhone(mobile);
                } else {
                    PermissionUtils.requestPermissions(SupplierOrderFragment.this, Constants.PERMISSION, 4);
                }
            }
        });
        smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getOrderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getOrderList();
            }
        });
    }

    /**
     * 清除按钮背景样式
     */
    private void clearTextBg() {
        tvType0.setBackgroundResource(R.drawable.shape_white_8);
        tvType0.setTextColor(getResources().getColor(R.color.color_333));
        tvType1.setBackgroundResource(R.drawable.shape_white_8);
        tvType1.setTextColor(getResources().getColor(R.color.color_333));
        tvType2.setBackgroundResource(R.drawable.shape_white_8);
        tvType2.setTextColor(getResources().getColor(R.color.color_333));
    }

    /**
     * 拨打电话
     *
     * @param phoneNum
     */
    public void callPhone(String phoneNum) {
        if (TextUtils.isEmpty(phoneNum)) {
            showMessage(getLanguageValue("noPhone"));
            return;
        }
        IAlertDialog.showDialog(getActivity(),
                getLanguageValue("confirmCall")+":" + phoneNum,
                getLanguageValue("confirm"),
                (dialog, which) -> {
                    startActivity(new Intent(Intent.ACTION_CALL)
                            .setData(Uri.parse("tel:" + phoneNum)));
                });
    }

    /**
     * 购销单列表
     */
    private void getOrderList() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("supplierUnique", supplierUnique);
//        map.put("settleStatus", type);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getSupplierInfoGouXList(),
                map,
                GouXOrderListData.class,
                new RequestListListener<GouXOrderListData>() {
                    @Override
                    public void onResult(List<GouXOrderListData> list) {
                        hideDialog();
                        if (page == 1) {
                            smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showMessage(msg);
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    callPhone(mobile);
                }
                break;
        }
    }

}
