package cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.BaseData;

import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.dialog.ShopDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.adapter.AllotAddGoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.bean.AllotData;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:店铺-调拨单-申请调拨
 * Created by jingang on 2023/5/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class AllotAddActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvOutValue)
    TextView tvOutValue;
    @BindView(R.id.tvAllotValue)
    TextView tvAllotValue;
    @BindView(R.id.tvInValue)
    TextView tvInValue;
    @BindView(R.id.tvInName)
    TextView tvInName;
    @BindView(R.id.tvOutName)
    TextView tvOutName;
    @BindView(R.id.tvGoodsValue)
    TextView tvGoodsValue;
    @BindView(R.id.tvGoods)
    TextView tvGoods;
    @BindView(R.id.rvGoods)
    RecyclerView rvGoods;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    private String inId;//调入方id

    private AllotAddGoodsAdapter mAdapter;
    private List<AllotData.DetailInfoListBean> dataList = new ArrayList<>();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_allot_add;
    }

    @Override
    public void initViews() {
        tvOutName.setText(getShop_name());
        setAdapter();
    }

    @OnClick({R.id.ivBack, R.id.tvInName, R.id.tvGoods, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvInName:
                //选择店铺
                ShopDialog.showDialog(this, (view1, unique, name) -> {
                    inId = unique;
                    tvInName.setText(name);
                });
                break;
            case R.id.tvGoods:
                //添加商品
                startActivityForResult(new Intent(this, ScanActivity.class), Constants.SCAN);
                break;
            case R.id.tvConfirm:
                //提交
                if (TextUtils.isEmpty(inId)) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("transferIntoStore"));
                    return;
                }
                if (dataList.size() < 1) {
                    showMessage(getLanguageValue("please") + getLanguageValue("addTo") + getLanguageValue("transfer") + getLanguageValue("commodity"));
                    return;
                }

                List array = new ArrayList();
                try {
                    for (int i = 0; i < dataList.size(); i++) {
                        Map object = new HashMap();
                        object.put("goodsName", dataList.get(i).getGoodsName());
                        object.put("goodsBarcode", dataList.get(i).getGoodsBarcode());
                        object.put("purchaseListDetailCount", dataList.get(i).getPurchaseListDetailCount());
                        object.put("purchaseListDetailPrice", dataList.get(i).getGoodsInPrice());
                        array.add(object);
                    }
                } catch (Exception ignored) {
                }
                postAllotAdd(array);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("apply") + getLanguageValue("transfer"));
        tvOutValue.setText(getLanguageValue("transferOutStore"));
        tvAllotValue.setText(getLanguageValue("transfer") + getLanguageValue("to"));
        tvInValue.setText(getLanguageValue("transferIntoStore"));
        tvInName.setText(getLanguageValue("pleaseSelect") + getLanguageValue("shop"));
        tvGoodsValue.setText(getLanguageValue("transfer") + getLanguageValue("commodity"));
        tvGoods.setText(getLanguageValue("transfer") + getLanguageValue("commodity"));
        tvConfirm.setText(getLanguageValue("submit") + getLanguageValue("transfer") + getLanguageValue("order"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        rvGoods.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new AllotAddGoodsAdapter(this);
        rvGoods.setAdapter(mAdapter);
        mAdapter.setType(1);
        mAdapter.setOnItemClickListener((view, position) -> {
            IAlertDialog.showDialog(this,
                    getLanguageValue("confirm") + getLanguageValue("delete") + getLanguageValue("commodity") + "?",
                    getLanguageValue("confirm"),
                    (dialog, which) -> {
                        dataList.remove(position);
                        mAdapter.remove(position);
                    });
        });
    }

    /**
     * 新增调拨
     *
     * @param array
     */
    private void postAllotAdd(List array) {
        Map<String, Object> map = new HashMap<>();
        map.put("inboundStoreOfId", inId);//调入方id
        map.put("pullStoreOfId", getShop_id());//调出方id
        map.put("userId", getStaff_id());//申请用户（员工id）
        map.put("userName", getStaff_name());
        map.put("detailList", array);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getAllot_add(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "s = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        showMessage(data.getMsg());
                        if (data.getStatus() == Constants.SUCCESS_CODE) {
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                            finish();
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.SCAN:
                    //扫码
                    startActivityForResult(new Intent(this, AllotGoodsActivity.class)
                                    .putExtra("code", data.getStringExtra("result"))
                            , Constants.ADD);
                    break;
                case Constants.ADD:
                    //添加商品
                    AllotData.DetailInfoListBean datas = (AllotData.DetailInfoListBean) data.getSerializableExtra("data");
                    dataList.add(datas);
                    mAdapter.setDataList(dataList);
                    break;
            }
        }
    }
}
