package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.OrderInfoData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:销售订单-详情-商品（适配器）
 * Created by jingang on 2023/5/20
 */
public class OrderInfoGoodsAdapter extends BaseAdapter<OrderInfoData.DataBean.ListDetailBean> {

    public OrderInfoGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_info_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice, tvSpecs, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvSpecs = holder.getView(R.id.tvItemSpecs);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(R.mipmap.ic_nothing))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvPrice.setText("RM" + DFUtils.getNum2(mDataList.get(position).getSaleListDetailPrice()));
//        tvSpecs.setText("不知道");
        tvCount.setText("x" + mDataList.get(position).getSaleListDetailCount());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSubTotal()));
    }
}
