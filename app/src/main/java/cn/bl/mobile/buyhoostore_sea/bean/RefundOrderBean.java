package cn.bl.mobile.buyhoostore_sea.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RefundOrderBean extends BaseBean<RefundOrderBean> {
    /**
     * 订单号
     */
    private String saleListUnique;
    /**
     * 昵称
     */
    private String cusName;
    private String cusProtrait;
    private String retListDatetime;
    private double retListTotal;
    private double retListCount;
    private int retListHandlestate = -1;
    private List<Detail> listDetail = new ArrayList<>();
    private List<Detail> goodsList = new ArrayList<>();

    private int machineNum;
    private List<Object> payMethods = new ArrayList<>();
    private Object peisongMoney = new Object();
    private String receiptDateTime;
    private String saleListAddress;
    private int saleListCashier;
    private double saleListDelfee;
    private int saleListId;
    private String saleListName;
    private int saleListNumber;
    private String saleListPayment;
    private int saleListPaymentCode;
    private String saleListPhone;
    private double saleListPur;
    private String saleListRemarks;
    private String saleListState;
    private int saleListStateCode;
    private double saleListTotal;
    private String saleType;
    private String sendDateTime;
    private int shippingMethod;
    private long shopUnique;
    private double totalCount;
    private String retListUnique;
    // 拒绝退款理由
    private String retListRemarks;
    // 退款原因
    private String retListReason;

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusProtrait() {
        return cusProtrait;
    }

    public void setCusProtrait(String cusProtrait) {
        this.cusProtrait = cusProtrait;
    }

    public String getRetListDatetime() {
        return retListDatetime;
    }

    public void setRetListDatetime(String retListDatetime) {
        this.retListDatetime = retListDatetime;
    }

    public double getRetListTotal() {
        return retListTotal;
    }

    public void setRetListTotal(double retListTotal) {
        this.retListTotal = retListTotal;
    }

    public double getRetListCount() {
        return retListCount;
    }

    public void setRetListCount(double retListCount) {
        this.retListCount = retListCount;
    }

    public int getRetListHandlestate() {
        return retListHandlestate;
    }

    public void setRetListHandlestate(int retListHandlestate) {
        this.retListHandlestate = retListHandlestate;
    }

    public List<Detail> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<Detail> listDetail) {
        this.listDetail = listDetail;
    }

    public List<Detail> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<Detail> goodsList) {
        this.goodsList = goodsList;
    }

    public int getMachineNum() {
        return machineNum;
    }

    public void setMachineNum(int machineNum) {
        this.machineNum = machineNum;
    }

    public List<Object> getPayMethods() {
        return payMethods;
    }

    public void setPayMethods(List<Object> payMethods) {
        this.payMethods = payMethods;
    }

    public Object getPeisongMoney() {
        return peisongMoney;
    }

    public void setPeisongMoney(Object peisongMoney) {
        this.peisongMoney = peisongMoney;
    }

    public String getReceiptDateTime() {
        return receiptDateTime;
    }

    public void setReceiptDateTime(String receiptDateTime) {
        this.receiptDateTime = receiptDateTime;
    }

    public String getSaleListAddress() {
        return saleListAddress;
    }

    public void setSaleListAddress(String saleListAddress) {
        this.saleListAddress = saleListAddress;
    }

    public int getSaleListCashier() {
        return saleListCashier;
    }

    public void setSaleListCashier(int saleListCashier) {
        this.saleListCashier = saleListCashier;
    }

    public double getSaleListDelfee() {
        return saleListDelfee;
    }

    public void setSaleListDelfee(double saleListDelfee) {
        this.saleListDelfee = saleListDelfee;
    }

    public int getSaleListId() {
        return saleListId;
    }

    public void setSaleListId(int saleListId) {
        this.saleListId = saleListId;
    }

    public String getSaleListName() {
        return saleListName;
    }

    public void setSaleListName(String saleListName) {
        this.saleListName = saleListName;
    }

    public int getSaleListNumber() {
        return saleListNumber;
    }

    public void setSaleListNumber(int saleListNumber) {
        this.saleListNumber = saleListNumber;
    }

    public String getSaleListPayment() {
        return saleListPayment;
    }

    public void setSaleListPayment(String saleListPayment) {
        this.saleListPayment = saleListPayment;
    }

    public int getSaleListPaymentCode() {
        return saleListPaymentCode;
    }

    public void setSaleListPaymentCode(int saleListPaymentCode) {
        this.saleListPaymentCode = saleListPaymentCode;
    }

    public String getSaleListPhone() {
        return saleListPhone;
    }

    public void setSaleListPhone(String saleListPhone) {
        this.saleListPhone = saleListPhone;
    }

    public double getSaleListPur() {
        return saleListPur;
    }

    public void setSaleListPur(double saleListPur) {
        this.saleListPur = saleListPur;
    }

    public String getSaleListRemarks() {
        return saleListRemarks;
    }

    public void setSaleListRemarks(String saleListRemarks) {
        this.saleListRemarks = saleListRemarks;
    }

    public String getSaleListState() {
        return saleListState;
    }

    public void setSaleListState(String saleListState) {
        this.saleListState = saleListState;
    }

    public int getSaleListStateCode() {
        return saleListStateCode;
    }

    public void setSaleListStateCode(int saleListStateCode) {
        this.saleListStateCode = saleListStateCode;
    }

    public double getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(double saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getSendDateTime() {
        return sendDateTime;
    }

    public void setSendDateTime(String sendDateTime) {
        this.sendDateTime = sendDateTime;
    }

    public int getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(int shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public double getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(double totalCount) {
        this.totalCount = totalCount;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public String getRetListRemarks() {
        return retListRemarks;
    }

    public void setRetListRemarks(String retListRemarks) {
        this.retListRemarks = retListRemarks;
    }

    public String getRetListReason() {
        return retListReason;
    }

    public void setRetListReason(String retListReason) {
        this.retListReason = retListReason;
    }

    public static class Detail implements Serializable {
        @SerializedName("goodsBarcode")
        private String goodsBarcode;
        @SerializedName("goodsName")
        private String goodsName;
        @SerializedName("goodsPicturepath")
        private String goodsPicturepath;
        @SerializedName("retListDetailPrice")
        private String retListDetailPrice;
        @SerializedName("retListDetailCount")
        private double retListDetailCount;
        @SerializedName("saleListDetailId")
        private int saleListDetailId;
        @SerializedName("saleListDetailPrice")
        private double saleListDetailPrice;
        @SerializedName("subTotal")
        private double subTotal;

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getRetListDetailPrice() {
            return retListDetailPrice;
        }

        public void setRetListDetailPrice(String retListDetailPrice) {
            this.retListDetailPrice = retListDetailPrice;
        }

        public double getRetListDetailCount() {
            return retListDetailCount;
        }

        public void setRetListDetailCount(double retListDetailCount) {
            this.retListDetailCount = retListDetailCount;
        }

        public int getSaleListDetailId() {
            return saleListDetailId;
        }

        public void setSaleListDetailId(int saleListDetailId) {
            this.saleListDetailId = saleListDetailId;
        }

        public double getSaleListDetailPrice() {
            return saleListDetailPrice;
        }

        public void setSaleListDetailPrice(double saleListDetailPrice) {
            this.saleListDetailPrice = saleListDetailPrice;
        }

        public double getSubTotal() {
            return subTotal;
        }

        public void setSubTotal(double subTotal) {
            this.subTotal = subTotal;
        }
    }
}
