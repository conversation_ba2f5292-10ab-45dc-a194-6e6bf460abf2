package cn.bl.mobile.buyhoostore_sea.ui.farm.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:农批-首页-快速还款-选择还款类型
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class FarmRepaymentTypeDialog extends Dialog {

    public static void showDialog(Context mContext, MyListener listener) {
        FarmRepaymentTypeDialog.listener = listener;
        FarmRepaymentTypeDialog dialog = new FarmRepaymentTypeDialog(mContext);
        dialog.show();
    }

    public FarmRepaymentTypeDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_farm_repayment_type);
        ButterKnife.bind(this);
    }

    @OnClick({R.id.ivDialogClose, R.id.linDialogType0, R.id.linDialogType1})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogType0:
                if (listener != null) {
                    listener.onTypeClick(view, 0);
                    dismiss();
                }
                break;
            case R.id.linDialogType1:
                if (listener != null) {
                    listener.onTypeClick(view, 1);
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
    }

    private static MyListener listener;

    public interface MyListener {
        void onTypeClick(View view, int type);
    }
}
