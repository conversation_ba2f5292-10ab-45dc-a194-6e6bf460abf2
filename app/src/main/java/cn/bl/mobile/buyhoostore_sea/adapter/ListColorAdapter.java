package cn.bl.mobile.buyhoostore_sea.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;

import java.math.BigDecimal;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.StatisticsSaleSumTrendData;

/**
 * 经营统计-分类销售占比（适配器）
 */
public class ListColorAdapter extends BaseAdapter<StatisticsSaleSumTrendData> {
    private double sum;
    String[] colors = {"#2ECC71", "#0d9347", "#d2ea57", "#87cd43", "#80ae9c", "#aecdc3", "#b4bba5", "#3d8a6f", "#ffcd5d", "#c74a66", "#08a86c", "#42a4b8", "#8b736e", "#b7d62d"};

    public void setSum(double sum) {
        this.sum = sum;
    }

    public ListColorAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_list_color;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(com.yxl.commonlibrary.base.ViewHolder holder, int position) {
        TextView tvColor, tvName, tvCount;
        tvColor = holder.getView(R.id.tvItemColor);
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);

        tvName.setText(mDataList.get(position).getKindName());
        tvColor.setTextColor(Color.parseColor(colors[position]));
        //分类占比
        double d_sum = mDataList.get(position).getSum();
        BigDecimal bd = new BigDecimal(((d_sum / sum) * 100));
        double percent = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        tvCount.setText(percent + "%");
    }

}

