package cn.bl.mobile.buyhoostore_sea.ui.shop.allot;

import android.annotation.SuppressLint;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.google.android.material.tabs.TabLayout;
import com.yxl.commonlibrary.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.ui.SimpleFragmentPagerAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.AllotScreenDialog;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity.AllotAddActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.allot.activity.AllotSearchActivity;
import cn.bl.mobile.buyhoostore_sea.view.NoScrollViewPager;

/**
 * Describe:店铺-调拨
 * Created by jingang on 2023/5/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class AllotActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvScreening)
    TextView tvScreening;
    @BindView(R.id.tabLayout)
    TabLayout tabLayout;
    @BindView(R.id.viewPager)
    NoScrollViewPager viewPager;
    @BindView(R.id.tvAdd)
    TextView tvAdd;

    private String[] titles = new String[]{getLanguageValue("whole"),
            getLanguageValue("beTransferOut"),
            getLanguageValue("beTransferEnter"),
            getLanguageValue("completed")};
    private SimpleFragmentPagerAdapter sfpAdapter;
    private List<Fragment> fragmentList = new ArrayList<>();

    private int day = -1;//0.近1个月 1.近3个月 2.近6个月
    public static String startTime, endTime, shopUnique;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_allot;
    }

    @Override
    public void initViews() {
        setFragment();
    }

    @OnClick({R.id.ivBack, R.id.ivSearch, R.id.flScreen, R.id.tvAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.ivSearch:
                //搜索
                goToActivity(AllotSearchActivity.class);
                break;
            case R.id.flScreen:
                //筛选
                AllotScreenDialog.showDialog(this, day, shopUnique, (type, day, startTime, endTime, shopUnique) -> {
                    this.day = day;
                    if (type == 1) {
                        tvScreening.setVisibility(View.VISIBLE);
                    } else {
                        tvScreening.setVisibility(View.GONE);
                    }
                    AllotActivity.startTime = startTime;
                    AllotActivity.endTime = endTime;
                    AllotActivity.shopUnique = shopUnique;
                    Log.e(tag, "day = " + day + " startTime = " + startTime + " endTime = " + endTime + " shopUnique = " + shopUnique);
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("state"));
                });
                break;
            case R.id.tvAdd:
                //新增调拨单
                goToActivity(AllotAddActivity.class);
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("transfer"));
        tvScreening.setText(getLanguageValue("filtering"));
        tvAdd.setText(getLanguageValue("add") + getLanguageValue("transfer") + getLanguageValue("order"));
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        startTime = "";
        endTime = "";
        shopUnique = "";
        fragmentList.clear();
        for (int i = 0; i < titles.length; i++) {
            fragmentList.add(AllotFragment.newInstance(i + 1));
        }
        sfpAdapter = new SimpleFragmentPagerAdapter(getSupportFragmentManager(), fragmentList, titles);
        viewPager.setAdapter(sfpAdapter);
        viewPager.setCurrentItem(0);
        viewPager.setOffscreenPageLimit(titles.length);
        tabLayout.setupWithViewPager(viewPager);
    }

}
