package cn.bl.mobile.buyhoostore_sea.bean;

/**
 * Describe:
 * Created by jingang on 2023/2/4
 */
public class BaseData {
    /**
     * status : 1
     * msg : 删除成功！
     * data : null
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * count : null
     * total : null
     * rows : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
