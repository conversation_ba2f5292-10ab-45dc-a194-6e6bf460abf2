package cn.bl.mobile.buyhoostore_sea.ui.login;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.GestureDetector;
import android.view.GestureDetector.OnGestureListener;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.ViewFlipper;

import com.yxl.commonlibrary.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * 导航页
 */
@SuppressLint("NonConstantResourceId")
public class GuideActivity extends BaseActivity implements OnGestureListener {
    @BindView(R.id.viewFlipper)
    ViewFlipper flipper;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.ivImg2)
    ImageView ivImg2;

    private int[] imgID = {R.drawable.guide_bg11, R.drawable.guide_bg22, R.drawable.guide_bg33};
    private List<ImageView> ivs = new ArrayList<>();
    private GestureDetector detector;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_guide;
    }

    @Override
    public void initViews() {
        //设置状态栏
        cn.bl.mobile.buyhoostore_sea.utils.statusbar.StatusBarUtil.setLightStatusBar(this, true, true);
        getWindow().setNavigationBarColor(Color.parseColor("#ffffff"));

        ivs.add(ivImg0);
        ivs.add(ivImg1);
        ivs.add(ivImg2);
        detector = new GestureDetector(this);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT);
        for (int j : imgID) {
            RelativeLayout relativeLayout = new RelativeLayout(this);
            ImageView imageView = new ImageView(this);
            imageView.setImageResource(j);
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            relativeLayout.addView(imageView, lp);
            flipper.addView(relativeLayout, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        }
        ivs.get(0).setEnabled(false);

    }

    //改变
    private void dotChange(int index) {
        for (int i = 0; i < ivs.size(); i++) {
            ivs.get(i).setEnabled(i != index);
        }
    }

    @Override
    public boolean onDown(MotionEvent e) {
        return false;
    }

    @Override
    public void onShowPress(MotionEvent e) {

    }

    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        return false;
    }

    @Override
    public void onLongPress(MotionEvent e) {

    }

    @Override
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        if (e1.getX() - e2.getX() > 120) {
            // 添加动画
            this.flipper.setInAnimation(AnimationUtils.loadAnimation(this, R.anim.push_left_in));
            this.flipper.setOutAnimation(AnimationUtils.loadAnimation(this, R.anim.push_left_out));
            View view = flipper.getChildAt(imgID.length - 1);
            View view1 = flipper.getCurrentView();
            view.setOnClickListener(v -> {
                goToActivity(LoginActivity.class);
                finish();
            });

            if (view == view1) {
                //最后一张可以点击可跳转
                //跳转到登录界面
                goToActivity(LoginActivity.class);
                finish();
            } else {
                this.flipper.showNext();
                dotChange(flipper.getDisplayedChild());
            }
            return true;
        }// 从右向左滑动
        else if (e1.getX() - e2.getX() < -120) {
            this.flipper.setInAnimation(AnimationUtils.loadAnimation(this, R.anim.push_right_in));
            this.flipper.setOutAnimation(AnimationUtils.loadAnimation(this, R.anim.push_right_out));
            if (flipper.getChildAt(0) == flipper.getCurrentView()) {
//                showMessage("第一张");
            } else {
                this.flipper.showPrevious();
                dotChange(flipper.getDisplayedChild());
            }

            return true;
        }
        return true;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return this.detector.onTouchEvent(event);
    }


}
