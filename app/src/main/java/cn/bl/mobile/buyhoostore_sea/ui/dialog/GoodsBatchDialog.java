package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.yxl.commonlibrary.utils.DensityUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（商品-批量操作）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class GoodsBatchDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvDialogTitle;
    @BindView(R.id.tvDialogBatchStatus)
    TextView tvDialogBatchStatus;
    @BindView(R.id.tvDialogBatchCate)
    TextView tvDialogBatchCate;
    @BindView(R.id.tvDialogBatchPrint)
    TextView tvDialogBatchPrint;
    @BindView(R.id.tvDialogBatchDel)
    TextView tvDialogBatchDel;

    public static void showDialog(Context context, MyListener listener) {
        GoodsBatchDialog.listener = listener;
        GoodsBatchDialog dialog = new GoodsBatchDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public GoodsBatchDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_batch);
        ButterKnife.bind(this);
        tvDialogTitle.setText(getLanguageValue("batch")+getLanguageValue("operation"));
        tvDialogBatchStatus.setText(getLanguageValue("batch")+getLanguageValue("onOffShelf"));
        tvDialogBatchCate.setText(getLanguageValue("batch")+getLanguageValue("transferClass"));
        tvDialogBatchPrint.setText(getLanguageValue("batch")+getLanguageValue("printTag"));
        tvDialogBatchDel.setText(getLanguageValue("delete")+getLanguageValue("commodity"));
    }

    @OnClick({R.id.linDialogBatchStatus, R.id.linDialogBatchCate,
            R.id.linDialogBatchPrint, R.id.linDialogBatchDel})
    public void onViewClicked(View view) {
        if (listener == null) {
            return;
        }
        switch (view.getId()) {
            case R.id.linDialogBatchStatus:
                //批量商品上下架
                listener.onStatus();
                break;
            case R.id.linDialogBatchCate:
                //批量转移分类
                listener.onCate();
                break;
            case R.id.linDialogBatchPrint:
                //批量打印价签
                listener.onPrint();
                break;
            case R.id.linDialogBatchDel:
                //删除选中商品
                listener.onDel();
                break;
        }
        dismiss();
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        void onStatus();

        void onCate();

        void onPrint();

        void onDel();
    }
}
