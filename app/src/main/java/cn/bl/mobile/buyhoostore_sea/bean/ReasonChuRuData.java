package cn.bl.mobile.buyhoostore_sea.bean;

import java.util.List;

/**
 * Describe:出入库原因
 * Created by jingang on 2023/2/6
 */
public class ReasonChuRuData {
    /**
     * msg : 查询成功！
     * data : [{"list":[{"dictLabel":"采购","dictSort":1},{"dictLabel":"退货","dictSort":2},{"dictLabel":"其他","dictSort":3}],"dictType":"sys_inbound_reason"},{"list":[{"dictLabel":"临期","dictSort":1},{"dictLabel":"破损","dictSort":2},{"dictLabel":"其他","dictSort":3}],"dictType":"sys_outbound_reason"}]
     * status : 1
     */

    private String msg;
    private int status;
    private List<DataBean> data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * list : [{"dictLabel":"采购","dictSort":1},{"dictLabel":"退货","dictSort":2},{"dictLabel":"其他","dictSort":3}]
         * dictType : sys_inbound_reason
         */

        private String dictType;
        private List<ListBean> list;

        public String getDictType() {
            return dictType;
        }

        public void setDictType(String dictType) {
            this.dictType = dictType;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        public static class ListBean {
            /**
             * dictLabel : 采购
             * dictSort : 1
             */

            private String dictLabel;
            private int dictSort;

            private boolean check;

            public String getDictLabel() {
                return dictLabel;
            }

            public void setDictLabel(String dictLabel) {
                this.dictLabel = dictLabel;
            }

            public int getDictSort() {
                return dictSort;
            }

            public void setDictSort(int dictSort) {
                this.dictSort = dictSort;
            }

            public boolean isCheck() {
                return check;
            }

            public void setCheck(boolean check) {
                this.check = check;
            }
        }
    }
}
