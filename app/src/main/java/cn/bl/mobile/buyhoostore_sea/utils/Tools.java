package cn.bl.mobile.buyhoostore_sea.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.os.Build;
import android.os.Environment;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.switfpass.pay.utils.MD5;
import com.switfpass.pay.utils.SignUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * @classname: Tools.java
 * @description: TODO
 * @author: wzh
 * @version: V1.0
 * @createdate: 2015-9-7 上午11:40:23
 * @copyright: 北京车联天下信息技术有限公司
 */
public class Tools {
    public static boolean hasSDCard() {
        return Environment.MEDIA_MOUNTED.equals(Environment
                .getExternalStorageState());
    }

    /**
     * @Title: convertUTFToISO
     * @Description: 字符串编码转换 【utf-8 to iso8859-1】
     * @param:            @param temp
     * @param:            @return
     * @return: String
     * @throws:
     * @author: wzh
     * @Date: 2015-9-7 上午11:40:27
     */
    public static String convertUTFToISO(String temp) {
        try {
            if(!TextUtils.isEmpty(temp))
                return new String(temp.getBytes("UTF-8"), "iso-8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }


    // 两次点击按钮之间的点击间隔不能少于1000毫秒
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime = 0;

    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();

        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }

        lastClickTime = curClickTime;
        return flag;
    }

    private static String mActivityJumpTag; //activity跳转tag
    private static long mClickTime;         //activity跳转时间

    public static boolean checkDoubleClick(Intent intent) {
        //默认检查通过
        boolean result = true;
        //标记对象
        String tag;
        if (intent.getComponent() != null) {
            //显示跳转
            tag = intent.getComponent().getClassName();
        } else if (intent.getAction() != null) {
            //隐式跳转
            tag = intent.getAction();
        } else {
            return true;
        }
        if (tag.equals(mActivityJumpTag) && mClickTime >= SystemClock.uptimeMillis() - 500) {
            //检查不通过
            result = false;
        }
        //记录启动标记时间
        mActivityJumpTag = tag;
        mClickTime = SystemClock.uptimeMillis();
        return result;
    }


    public static void setNavigationBar(Activity context, View toolLine, WindowManager windowManager) {
        int sysVersion = Build.VERSION.SDK_INT;
        if (/*sysVersion > Build.VERSION_CODES.KITKAT*/checkDeviceHasNavigationBar(context)) {
            int result = 0;
            result = getNavigationBarHeight(context);
            //LinearLayout.LayoutParams para = new LinearLayout.LayoutParams(windowManager.getDefaultDisplay().getWidth(), result);
            RelativeLayout.LayoutParams para = new RelativeLayout.LayoutParams(windowManager.getDefaultDisplay().getWidth(), result);
            //设置修改后的布局。
            para.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            toolLine.setLayoutParams(para);
        }
    }


    private static boolean checkDeviceHasNavigationBar(Activity context) {

        boolean hasNavigationBar = false;
        Resources rs = context.getResources();
        int id = rs.getIdentifier("config_showNavigationBar", "bool", "android");
        //if (id > 0) {
        hasNavigationBar = rs.getBoolean(id);
        //}
        try {
            Class systemPropertiesClass = Class.forName("android.os.SystemProperties");
            Method m = systemPropertiesClass.getMethod("get", String.class);
            String navBarOverride = (String) m.invoke(systemPropertiesClass, "qemu.hw.mainkeys");
            if ("1".equals(navBarOverride)) {
                hasNavigationBar = false;
            } else if ("0".equals(navBarOverride)) {
                hasNavigationBar = true;
                //手动设置控件的margin
                //linebutton是一个linearlayout,里面包含了两个Button
                /* if (null != linbutton){
                 *//*LinearLayout.LayoutParams layout = (LinearLayout.LayoutParams) linbutton.getLayoutParams();
                    //setMargins：顺序是左、上、右、下
                    layout.setMargins(0,0,0,getNavigationBarHeight(context)+10);*//*

                    LinearLayout.LayoutParams para = new LinearLayout.LayoutParams(windowManager.getDefaultDisplay().getWidth(), getNavigationBarHeight(context));
                    //设置修改后的布局。
                    linbutton.setLayoutParams(para);
                }*/


            }
        } catch (Exception e) {
            Log.w("Tools", e);
        }

        return hasNavigationBar;
    }

    /**
     * 测量底部导航栏的高度
     *
     * @param mActivity:上下文环境
     * @return：返回测量出的底部导航栏高度
     */
    private static int getNavigationBarHeight(Activity mActivity) {
        Resources resources = mActivity.getResources();
        //Resources resources = context.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        int height = 0;
        if (resourceId > 0) {
            height = resources.getDimensionPixelSize(resourceId);
        }
        Log.d("NavigationBarHeight", "height= " + height);
        return height;
    }


    public static void showErrMsg(JSONObject object, Context context) {
        boolean msgIsNull = object.isNull("msg");
        if (!msgIsNull) {
            String msg1 = null;
            try {
                msg1 = object.getString("msg");
                if (null != msg1 && !msg1.isEmpty()) {
                    Toast.makeText(context, msg1, Toast.LENGTH_SHORT).show();
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }

        }
    }


    /**
     * 显示这个图片,解决了列表问题. * 列表问题：滑动过程中，getView的imageView会重复利用，导致图片会串位 * @param imageView 显得的View * @param url the url * @return
     */
    /*public void display(final ImageView imageView, String url) {
        if (AbStrUtil.isEmpty(url)) {
            if (noImage != null) {
                if (loadingView != null) {
                    loadingView.setVisibility(View.INVISIBLE);
                    imageView.setVisibility(View.VISIBLE);
                }
                imageView.setImageDrawable(noImage);
            }
            return;
        }
        //设置下载项
        final AbImageDownloadItem item = new AbImageDownloadItem();
        //设置显示的大小
        item.width = width;
        item.height = height;
        //设置为缩放
        item.type = type;
        item.imageUrl = url;
        final String cacheKey = AbImageCache.getCacheKey(item.imageUrl, item.width, item.height, item.type);
        item.bitmap = AbImageCache.getBitmapFromCache(cacheKey);
        if (D) Log.d(TAG, "缓存中获取的" + cacheKey + ":" + item.bitmap);
        //设置标记
        imageView.setTag(url);
        if (item.bitmap == null) {
            //先显示加载中
            if (loadingView != null) {
                loadingView.setVisibility(View.VISIBLE);
                imageView.setVisibility(View.INVISIBLE);
            } else if (loadingImage != null) {
                imageView.setImageDrawable(loadingImage);
            }
            //下载完成后更新界面
            item.setListener(new AbImageDownloadListener() {
                @Override
                public void update(Bitmap bitmap, String imageUrl) {
                    //未设置加载中的图片，并且设置了隐藏的View
                    if (loadingView != null && imageUrl.equals(imageView.getTag())) {
                        loadingView.setVisibility(View.INVISIBLE);
                        imageView.setVisibility(View.VISIBLE);
                    }
                    //要判断这个imageView的url有变化，如果没有变化才set， //有变化就取消，解决列表的重复利用View的问题
                    if (bitmap != null && imageUrl.equals(imageView.getTag())) {
                        if (D) Log.d(TAG, "图片下载，设置:" + imageUrl);
                        imageView.setImageBitmap(bitmap);
                    } else {
                        if (errorImage != null && imageUrl.equals(imageView.getTag())) {
                            imageView.setImageDrawable(errorImage);
                        }
                    }
                }
            });
            if (D) Log.d(TAG, "图片下载，执行:" + url);
            mAbImageDownloadPool.execute(item);
        } else {
            if (loadingView != null) {
                loadingView.setVisibility(View.INVISIBLE);
                imageView.setVisibility(View.VISIBLE);
            }
            imageView.setImageBitmap(item.bitmap);
        }
    }*/
    public static void dialogDimss(Dialog newLoadDialog) {
        if (null != newLoadDialog) {
            WeiboDialogUtils.closeDialog(newLoadDialog);
        }
    }

    public static boolean isApplicationAvilible(Context context, String packageName) {
        PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> installedPackages = packageManager.getInstalledPackages(0);
        if (installedPackages != null) {
            for (PackageInfo packageInfo : installedPackages) {
                if (packageInfo.packageName.equals(packageName)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 创建自定义输出目录
     *
     * @return
     */
    public static String getSandboxPath(Context context) {
        File externalFilesDir = context.getExternalFilesDir("");
        File customFile = new File(externalFilesDir.getAbsolutePath(), "Sandbox");
        if (!customFile.exists()) {
            customFile.mkdirs();
        }
        return customFile.getAbsolutePath() + File.separator;
    }

    public static String createSign(String signKey, Map<String, String> params) {
        StringBuilder buf = new StringBuilder((params.size() + 1) * 10);
        SignUtils.buildPayParams(buf, params, false);
        buf.append("&key=").append(signKey);
        String preStr = buf.toString();
        String sign = "";
        // 获得签名验证结果
        try {
            sign = MD5.md5s(preStr).toUpperCase();
        } catch (Exception e) {
            sign = MD5.md5s(preStr).toUpperCase();
        }
        return sign;
    }
}
