package cn.bl.mobile.buyhoostore_sea.ui.goods.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.Interface.OnItemChildClickListener;
import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.bean.CateData;

/**
 * Describe:商品管理-分类（适配器）
 * Created by jingang on 2022/11/30
 */
public class CateAdapter extends BaseAdapter<CateData> {
    public CateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        View vCate = holder.getView(R.id.vItemCate);
        TextView tvCate = holder.getView(R.id.tvItemCate);
        RecyclerView rvCate = holder.getView(R.id.rvItemCate);

        tvCate.setText(mDataList.get(position).getGroupName());
        if (mDataList.get(position).isCheck()) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            vCate.setVisibility(View.VISIBLE);
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            vCate.setVisibility(View.INVISIBLE);
            tvCate.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        }
        if (mDataList.get(position).isShow()) {
            rvCate.setVisibility(View.VISIBLE);
            rvCate.setLayoutManager(new LinearLayoutManager(mContext));
            CateChildAdapter adapter = new CateChildAdapter(mContext);
            rvCate.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getKindDetail());
            adapter.setListener(new CateChildAdapter.MyListener() {
                @Override
                public void onItemClick(int position1) {
                    if (listener != null) {
                        listener.onChildItemClick(position, position1);
                    }
                }

                @Override
                public void onChildItemClick(int position1, int position2) {
                    if (listener != null) {
                        listener.onChildChildItemClick(position, position1, position2);
                    }
                }
            });
        } else {
            rvCate.setVisibility(View.GONE);
        }
        if (listener != null) {
            tvCate.setOnClickListener(v -> listener.onItemClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(int position);

        void onChildItemClick(int position, int position1);

        void onChildChildItemClick(int position, int position1, int position2);
    }
}
