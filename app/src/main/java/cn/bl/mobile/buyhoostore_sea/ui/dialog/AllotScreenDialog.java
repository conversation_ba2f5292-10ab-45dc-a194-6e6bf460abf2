package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.yxl.commonlibrary.utils.DateUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（调拨单-订单筛选）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class AllotScreenDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvDialogTitle;
    @BindView(R.id.tvDialogTimeValue)
    TextView tvDialogTimeValue;
    @BindView(R.id.tvDialogTime0)
    TextView tvTime0;
    @BindView(R.id.tvDialogTime1)
    TextView tvTime1;
    @BindView(R.id.tvDialogTime2)
    TextView tvTime2;
    @BindView(R.id.tvDialogStartTime)
    TextView tvStartTime;
    @BindView(R.id.tvDialogEndTime)
    TextView tvEndTime;
    @BindView(R.id.tvDialogResetting)
    TextView tvDialogResetting;
    @BindView(R.id.tvDialogConfirm)
    TextView tvDialogConfirm;

    private static int day = -1;//0.今天 1.昨天 2.前天
    private static String startTime, endTime, shopUnique;

    public static void showDialog(Context context, int day, String shopUnique, MyListener listener) {
        AllotScreenDialog.listener = listener;
        AllotScreenDialog.day = day;
        AllotScreenDialog.shopUnique = shopUnique;
        AllotScreenDialog dialog = new AllotScreenDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public AllotScreenDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_screen_allot);
        ButterKnife.bind(this);
        tvDialogTitle.setText(getLanguageValue("order")+getLanguageValue("filter"));
        tvDialogTimeValue.setText(getLanguageValue("placeAnOrder")+getLanguageValue("time"));
        tvTime0.setText(getLanguageValue("today"));
        tvTime1.setText(getLanguageValue("yesterday"));
        tvTime2.setText(getLanguageValue("dayBeforeYesterday"));
        tvStartTime.setHint(getLanguageValue("start")+getLanguageValue("time"));
        tvEndTime.setHint(getLanguageValue("end")+getLanguageValue("time"));
        tvDialogResetting.setText(getLanguageValue("reset"));
        tvDialogConfirm.setText(getLanguageValue("confirm"));
        setDate();
    }

    @OnClick({R.id.ivDialogClose,
            R.id.tvDialogTime0, R.id.tvDialogTime1, R.id.tvDialogTime2,
            R.id.tvDialogStartTime, R.id.tvDialogEndTime,
            R.id.tvDialogResetting, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogTime0:
                //近1月
                if (day != 0) {
                    day = 0;
                    setDate();
                }
                break;
            case R.id.tvDialogTime1:
                if (day != 1) {
                    day = 1;
                    setDate();
                }
                break;
            case R.id.tvDialogTime2:
                if (day != 2) {
                    day = 2;
                    setDate();
                }
                break;
            case R.id.tvDialogStartTime:
                //开始时间
            case R.id.tvDialogEndTime:
                //结束时间
                DateStartEndDialog.showDialog(getContext(),
                        startTime,
                        endTime,
                        startTime,
                        (startDate, endDate) -> {
                            day = -1;
                            startTime = startDate;
                            endTime = endDate;
                            setDate();
                        });
                break;
            case R.id.tvDialogResetting:
                //重置
                startTime = "";
                endTime = "";
                day = -1;
                shopUnique = "";
                setDate();
                if (listener != null) {
                    listener.onClick(0, day, startTime, endTime, shopUnique);
                    dismiss();
                }
                break;
            case R.id.tvDialogConfirm:
                //确认
                setDate();
                if (listener != null) {
                    listener.onClick(1, day, startTime, endTime, shopUnique);
                    dismiss();
                }
                break;
        }
    }

    private void setDate() {
        clearTextBg();
        switch (day) {
            case -1:
                break;
            case 0:
                tvTime0.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                tvTime0.setTextColor(getContext().getResources().getColor(R.color.blue));
                startTime = DateUtils.getOldDate(0);
                endTime = DateUtils.getOldDate(0);
                break;
            case 1:
                tvTime1.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                tvTime1.setTextColor(getContext().getResources().getColor(R.color.blue));
                startTime = DateUtils.getOldDate(-1);
                endTime = DateUtils.getOldDate(-1);
                break;
            case 2:
                tvTime2.setBackgroundResource(R.drawable.shape_blue_tm16_4);
                tvTime2.setTextColor(getContext().getResources().getColor(R.color.blue));
                startTime = DateUtils.getOldDate(-2);
                endTime = DateUtils.getOldDate(-2);
                break;
            default:
                startTime = "";
                endTime = "";
                break;
        }
        if (day == -1) {
            tvStartTime.setText(startTime);
            tvEndTime.setText(endTime);
        } else {
            tvStartTime.setText("");
            tvEndTime.setText("");
        }
    }

    /**
     * 清除选中样式
     */
    private void clearTextBg() {
        tvTime0.setBackgroundResource(R.drawable.shape_f2_4);
        tvTime0.setTextColor(getContext().getResources().getColor(R.color.color_333));
        tvTime1.setBackgroundResource(R.drawable.shape_f2_4);
        tvTime1.setTextColor(getContext().getResources().getColor(R.color.color_333));
        tvTime2.setBackgroundResource(R.drawable.shape_f2_4);
        tvTime2.setTextColor(getContext().getResources().getColor(R.color.color_333));
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(int type, int day, String startTime, String endTime, String shopUnique);
    }
}
