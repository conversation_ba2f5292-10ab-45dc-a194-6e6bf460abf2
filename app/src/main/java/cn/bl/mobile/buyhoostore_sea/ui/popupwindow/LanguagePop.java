package cn.bl.mobile.buyhoostore_sea.ui.popupwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.PopupWindow;
import android.widget.TextView;

import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.utils.MultiLanguageUtils;

/**
 * Describe:popupWindow（条件选择）
 * Created by jingang on 2024/05/20
 */
@SuppressLint({"StaticFieldLeak", "NonConstantResourceId"})
public class LanguagePop extends PopupWindow implements View.OnClickListener {
    private static Context mContext;
    private static View viewIcon;
    private final Animation openAnim, closeAnim;

    private TextView tvLanguage0, tvLanguage1, tvLanguage2, tvLanguage3, tvLanguage4, tvLanguage5;
    private String language, area;

    public static void showDialog(Context context, View viewIcon, View viewShow, int width, MyListener listener) {
        LanguagePop.mContext = context;
        LanguagePop.viewIcon = viewIcon;
        LanguagePop popupWindow = new LanguagePop(context);
        popupWindow.setListener(listener);
        popupWindow.setWidth(width);
        popupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        //new ColorDrawable(0)即为透明背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(0));
        // 设置动画效果
//        popupWindow.setAnimationStyle(R.style.dialog_anim);
        //设置可以获取焦点
        popupWindow.setFocusable(true);//设置为true isShowing才会有值
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    public LanguagePop(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_language, null);
        setContentView(view);
        tvLanguage0 = view.findViewById(R.id.tvPopLanguage0);
        tvLanguage1 = view.findViewById(R.id.tvPopLanguage1);
        tvLanguage2 = view.findViewById(R.id.tvPopLanguage2);
        tvLanguage3 = view.findViewById(R.id.tvPopLanguage3);
        tvLanguage4 = view.findViewById(R.id.tvPopLanguage4);
        tvLanguage5 = view.findViewById(R.id.tvPopLanguage5);
        tvLanguage0.setOnClickListener(this);
        tvLanguage1.setOnClickListener(this);
        tvLanguage2.setOnClickListener(this);
        tvLanguage3.setOnClickListener(this);
        tvLanguage4.setOnClickListener(this);
        tvLanguage5.setOnClickListener(this);

        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);

        language = SharedPreferencesUtil.getInstantiation(context).getString("", Constants.SP_LANGUAGE);
        Log.e("111111", "language = " + language + " country = " + SharedPreferencesUtil.getInstantiation(context).getString("", Constants.SP_COUNTRY));
        if (TextUtils.isEmpty(language)) {
            clearSelect();
            tvLanguage0.setTextColor(mContext.getResources().getColor(R.color.blue));
        } else {
            switch (language) {
                case "en":
                    clearSelect();
                    tvLanguage1.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
                case "th":
                    clearSelect();
                    tvLanguage2.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
                case "ko":
                    clearSelect();
                    tvLanguage3.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
                case "ms":
                    clearSelect();
                    tvLanguage4.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
                case "kk":
                    clearSelect();
                    tvLanguage5.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
                default:
                    clearSelect();
                    tvLanguage0.setTextColor(mContext.getResources().getColor(R.color.blue));
                    break;
            }
        }
    }

    @Override
    public void showAsDropDown(View anchor) {
        super.showAsDropDown(anchor);
        viewIcon.startAnimation(openAnim);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        viewIcon.startAnimation(closeAnim);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvPopLanguage0:
                //中文
                if (!language.equals("zh")) {
                    language = "zh";
                    area = "ZH";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage1:
                //英文
                if (!language.equals("en")) {
                    language = "en";
                    area = "US";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage2:
                //泰文
                if (!language.equals("th")) {
                    language = "th";
                    area = "TH";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage3:
                //俄文
                if (!language.equals("ru")) {
                    language = "ru";
                    area = "RU";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage4:
                //马来文
                if (!language.equals("ms")) {
                    language = "ms";
                    area = "MY";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage5:
                //哈萨克文
                if (!language.equals("kk")) {
                    language = "kk";
                    area = "KZ";
                    setLanguage();
                }
                break;
        }
    }

    /**
     * 清除选择
     */
    private void clearSelect() {
        tvLanguage0.setTextColor(mContext.getResources().getColor(R.color.black));
        tvLanguage1.setTextColor(mContext.getResources().getColor(R.color.black));
        tvLanguage2.setTextColor(mContext.getResources().getColor(R.color.black));
        tvLanguage3.setTextColor(mContext.getResources().getColor(R.color.black));
        tvLanguage4.setTextColor(mContext.getResources().getColor(R.color.black));
        tvLanguage5.setTextColor(mContext.getResources().getColor(R.color.black));
    }

    /**
     * 设置语言
     */
    private void setLanguage() {
        if (listener != null) {
            MultiLanguageUtils.changeLanguage(mContext, language, area);
            listener.onConfirm();
            dismiss();
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onConfirm();
    }
}
