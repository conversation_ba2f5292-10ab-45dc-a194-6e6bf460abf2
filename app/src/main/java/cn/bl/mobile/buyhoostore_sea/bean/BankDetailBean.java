package cn.bl.mobile.buyhoostore_sea.bean;

public class BankDetailBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : {"cardId":8,"shopUnique":null,"bank":1,"bankCard":"622325123545654254","bankName":"工商银行","bankPhone":"***********","creatTime":"2018-10-31","defaultType":2,"bankImg":"1","validType":1}
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public static class DataBean {
        /**
         * cardId : 8
         * shopUnique : null
         * bank : 1
         * bankCard : 622325123545654254
         * bankName : 工商银行
         * bankPhone : ***********
         * creatTime : 2018-10-31
         * defaultType : 2
         * bankImg : 1
         * validType : 1
         */

        private int cardId;
        private Object shopUnique;
        private int bank;
        private String bankCard;
        private String bankName;
        private String bankPhone;
        private String creatTime;
        private int defaultType;
        private String bankImg;
        private int validType;

        public int getCardId() {
            return cardId;
        }

        public void setCardId(int cardId) {
            this.cardId = cardId;
        }

        public Object getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(Object shopUnique) {
            this.shopUnique = shopUnique;
        }

        public int getBank() {
            return bank;
        }

        public void setBank(int bank) {
            this.bank = bank;
        }

        public String getBankCard() {
            return bankCard;
        }

        public void setBankCard(String bankCard) {
            this.bankCard = bankCard;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getBankPhone() {
            return bankPhone;
        }

        public void setBankPhone(String bankPhone) {
            this.bankPhone = bankPhone;
        }

        public String getCreatTime() {
            return creatTime;
        }

        public void setCreatTime(String creatTime) {
            this.creatTime = creatTime;
        }

        public int getDefaultType() {
            return defaultType;
        }

        public void setDefaultType(int defaultType) {
            this.defaultType = defaultType;
        }

        public String getBankImg() {
            return bankImg;
        }

        public void setBankImg(String bankImg) {
            this.bankImg = bankImg;
        }

        public int getValidType() {
            return validType;
        }

        public void setValidType(int validType) {
            this.validType = validType;
        }
    }
}
