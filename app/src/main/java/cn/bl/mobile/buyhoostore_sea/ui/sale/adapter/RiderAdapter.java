package cn.bl.mobile.buyhoostore_sea.ui.sale.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.Interface.OnItemClickListener;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.RiderData;

/**
 * Describe:选择骑手（适配器）
 * Created by jingang on 2023/5/24
 */
public class RiderAdapter extends BaseAdapter<RiderData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RiderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_rider;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvName, tvMobile;
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        ImageView ivCheck = holder.getView(R.id.ivItemCheck);

        tvName.setText(mDataList.get(position).getCourier_name());
        tvMobile.setText(mDataList.get(position).getCourier_phone());
        if (mDataList.get(position).isCheck()) {
            ivCheck.setImageResource(R.mipmap.ic_chosen001);
        } else {
            ivCheck.setImageResource(R.mipmap.ic_chose001);
        }
    }
}
