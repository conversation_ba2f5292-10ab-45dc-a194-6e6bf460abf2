package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（编辑名称）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class NameEditDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.etDialogContent)
    EditText etContent;
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;

    private static String title, content, hint;

    public static void showDialog(Context context, String title, String content, String hint, MyListener listener) {
        NameEditDialog.title = title;
        NameEditDialog.content = content;
        NameEditDialog.hint = hint;
        NameEditDialog.listener = listener;
        NameEditDialog dialog = new NameEditDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public NameEditDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_name);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("editor")+getLanguageValue("name"));
        etContent.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("name"));
        tvCancel.setText(getLanguageValue("cancel"));
        tvConfirm.setText(getLanguageValue("confirm"));
//        if (!TextUtils.isEmpty(hint)) {
//            if (hint.equals("请输入金额")) {
//                etContent.setInputType(InputType.TYPE_NUMBER_FLAG_DECIMAL);
//            }
//        }
        tvTitle.setText(title);
        etContent.setText(content);
        etContent.setHint(hint);
    }

    @OnClick({R.id.tvDialogCancel, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogCancel:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(etContent.getText().toString().trim())) {
                    showMessage(hint);
                    return;
                }
                if (listener != null) {
                    listener.onClick(view, etContent.getText().toString().trim());
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(View view, String content);
    }
}
