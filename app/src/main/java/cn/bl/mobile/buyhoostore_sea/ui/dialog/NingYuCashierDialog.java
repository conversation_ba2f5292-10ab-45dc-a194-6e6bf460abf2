package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:dialog（宁宇会员收银）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class NingYuCashierDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogNameValue)
    TextView tvDialogNameValue;
    @BindView(R.id.tvDialogName)
    TextView tvName;
    @BindView(R.id.tvDialogMobileValue)
    TextView tvDialogMobileValue;
    @BindView(R.id.tvDialogMobile)
    TextView tvMobile;
    @BindView(R.id.tvDialogMoneyValue)
    TextView tvDialogMoneyValue;
    @BindView(R.id.tvDialogMoney)
    TextView tvMoney;
    @BindView(R.id.tvDialogConfirm)
    TextView tvDialogConfirm;

    private static MemberBean.DataBean data;
    private static double money;

    public static void showDialog(Context context, MemberBean.DataBean data, double money, MyListener listener) {
        NingYuCashierDialog.data = data;
        NingYuCashierDialog.money = money;
        NingYuCashierDialog.listener = listener;
        NingYuCashierDialog dialog = new NingYuCashierDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    @SuppressLint("SetTextI18n")
    public NingYuCashierDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_ningyu_cashier);
        ButterKnife.bind(this);
        tvTitle.setText(getLanguageValue("confirm") + getLanguageValue("member") + getLanguageValue("collection"));
        tvDialogNameValue.setText(getLanguageValue("member") + getLanguageValue("name"));
        tvDialogMobileValue.setText(getLanguageValue("member") + getLanguageValue("phoneNum"));
        tvDialogMoneyValue.setText(getLanguageValue("pay") + getLanguageValue("amount"));
        tvDialogConfirm.setText(getLanguageValue("confirm") + getLanguageValue("collection"));

        tvName.setText(data.getCusName());
        tvMobile.setText(data.getCusPhone());
        tvMoney.setText("RM" + DFUtils.getNum2(money));
    }

    @OnClick({R.id.ivDialogClose, R.id.tvDialogConfirm})
    public void onViewClicked(View view) {
        if (isQuicklyClick()) {
            return;
        }
        switch (view.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                if (listener != null) {
                    listener.onConfirmClick();
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }


    private static MyListener listener;

    public interface MyListener {
        void onConfirmClick();
    }
}
