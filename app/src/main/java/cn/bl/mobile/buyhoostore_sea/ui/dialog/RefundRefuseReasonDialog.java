package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.CommonMessageBean;
import com.yxl.commonlibrary.bean.EventData;
import cn.bl.mobile.buyhoostore_sea.ui.sale.AddCommonMessageActivity;
import cn.bl.mobile.buyhoostore_sea.ui.sale.adapter.RefundRefuseReasonAdapter;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:dialog（退款订单-拒绝-拒绝原因）
 * Created by jingang on 2024/11/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class RefundRefuseReasonDialog extends BaseDialog {
    @BindView(R.id.tvDialogCancel)
    TextView tvCancel;
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogConfirm)
    TextView tvConfirm;
    @BindView(R.id.etDialogSearch)
    EditText etSearch;
    @BindView(R.id.tvDialogAddValue)
    TextView tvAddValue;
    @BindView(R.id.tvDialogAdd)
    TextView tvAdd;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private List<CommonMessageBean> dataList = new ArrayList<>();
    private RefundRefuseReasonAdapter mAdapter;

    public static void showDialog(Context context, MyListener listener) {
        RefundRefuseReasonDialog.listener = listener;
        RefundRefuseReasonDialog dialog = new RefundRefuseReasonDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtils.getScreenHeight(dialog.getContext()) / 4 * 3);
        dialog.show();
    }

    public RefundRefuseReasonDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_refund_refuse_reason);
        ButterKnife.bind(this);
        tvCancel.setText(getLanguageValue("cancel"));
        tvTitle.setText(getLanguageValue("denied")+getLanguageValue("reason"));
        tvConfirm.setText(getLanguageValue("confirm"));
        etSearch.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("denied")+getLanguageValue("reason"));
        tvAddValue.setText(getLanguageValue("commonSkill"));
        tvAdd.setText(getLanguageValue("add"));
        setAdapter();
        getRefundReasonList();
    }

    @OnClick({R.id.tvDialogCancel, R.id.tvDialogConfirm, R.id.tvDialogAdd})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvDialogCancel:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(etSearch.getText().toString().trim())) {
                    showMessage(getLanguageValue("pleaseEnter")+getLanguageValue("denied")+getLanguageValue("reason"));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(etSearch.getText().toString().trim());
                    dismiss();
                }
                break;
            case R.id.tvDialogAdd:
                //新增
                getContext().startActivity(new Intent(getContext(), AddCommonMessageActivity.class));
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.REFUND_REFUSE_REASON:
                getRefundReasonList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new RefundRefuseReasonAdapter(getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new RefundRefuseReasonAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (listener != null) {
                    listener.onConfirm(dataList.get(position).getMsg());
                    dismiss();
                }
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                Intent intent = new Intent(getContext(), AddCommonMessageActivity.class);
                EventBus.getDefault().postSticky(dataList.get(position));
                getContext().startActivity(intent);
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                postRefundReasonDel(dataList.get(position).getId(), position);
            }
        });
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> getRefundReasonList());
        smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 查询常用话术
     */
    private void getRefundReasonList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getRefundCommonMsgList(),
                map,
                CommonMessageBean.class,
                new RequestListListener<CommonMessageBean>() {
                    @Override
                    public void onResult(List<CommonMessageBean> list) {
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            recyclerView.setVisibility(View.VISIBLE);
                            linEmpty.setVisibility(View.GONE);
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 删除常用话术
     *
     * @param id
     */
    private void postRefundReasonDel(int id, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("delFlag", 2);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getHandelRefundMsgUrL(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        hideDialog();
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String msg);
    }
}
