package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Administrator on 2018/1/17 0017.
 */
public class ChuRuBean {


    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"goodsCount":1,"stockTime":"2018-01-17","goodsName":"密缘酱菜","stockCount":99,"stockResource":"订单"},{"goodsCount":1,"stockTime":"2018-01-17","goodsName":"农夫山泉","stockCount":31,"stockResource":"订单"},{"goodsCount":1,"stockTime":"2018-01-13","goodsName":"可比克薯片原味","stockCount":0,"stockResource":"订单"},{"goodsCount":1,"stockTime":"2018-01-13","goodsName":"可比克薯片原味","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":1,"stockTime":"2018-01-13","goodsName":"达利法式软面包香奶味","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":3,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":1,"stockTime":"2018-01-13","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":5,"stockTime":"2018-01-12","goodsName":"芋头","stockCount":0,"stockResource":"手动"},{"goodsCount":1,"stockTime":"2018-01-12","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-12","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":10,"stockTime":"2018-01-12","goodsName":"香烟盒","stockCount":0,"stockResource":"订单"},{"goodsCount":3,"stockTime":"2018-01-11","goodsName":"上好佳鲜虾条","stockCount":0,"stockResource":"订单"},{"goodsCount":5,"stockTime":"2018-01-11","goodsName":"125ml未来星儿童成长牛奶佳智型四联包","stockCount":0,"stockResource":"订单"},{"goodsCount":3,"stockTime":"2018-01-11","goodsName":"上好佳鲜虾条","stockCount":0,"stockResource":"订单"},{"goodsCount":5,"stockTime":"2018-01-11","goodsName":"125ml未来星儿童成长牛奶佳智型四联包","stockCount":0,"stockResource":"订单"}]
     * pageIndex : null
     * pageSize : null
     * pageCount : null
     * object : null
     * redundant : null
     */

    private int status;
    private String msg;
    private Object pageIndex;
    private Object pageSize;
    private Object pageCount;
    private Object object;
    private Object redundant;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Object pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Object getPageSize() {
        return pageSize;
    }

    public void setPageSize(Object pageSize) {
        this.pageSize = pageSize;
    }

    public Object getPageCount() {
        return pageCount;
    }

    public void setPageCount(Object pageCount) {
        this.pageCount = pageCount;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public Object getRedundant() {
        return redundant;
    }

    public void setRedundant(Object redundant) {
        this.redundant = redundant;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean implements Serializable {
        /**
         * goodsCount : 1.0
         * stockTime : 2018-01-17
         * goodsName : 密缘酱菜
         * stockCount : 99.0
         * stockResource : 订单
         */

        private double goodsCount;
        private String stockTime;
        private String goodsName;
        private double stockCount;
        private String stockResource;
        private String stockId;
        private String staffName;//操作人员
        private String goodsPicturePath;
        private String listUnique;
        private String goodsBarcode;
        private String reason;
        private double stockPrice;
        private String goodsUnit;//单位
        private int auditStatus;//0.待审核 1.审核通过 2.审核不通过
        private String stockRemarks;//出库备注
        private List<String> stockPicture;//出库照片

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public String getStockTime() {
            return stockTime;
        }

        public void setStockTime(String stockTime) {
            this.stockTime = stockTime;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public double getStockCount() {
            return stockCount;
        }

        public void setStockCount(double stockCount) {
            this.stockCount = stockCount;
        }

        public String getStockResource() {
            return stockResource;
        }

        public void setStockResource(String stockResource) {
            this.stockResource = stockResource;
        }

        public String getStockId() {
            return stockId;
        }

        public void setStockId(String stockId) {
            this.stockId = stockId;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getGoodsPicturePath() {
            return goodsPicturePath;
        }

        public void setGoodsPicturePath(String goodsPicturePath) {
            this.goodsPicturePath = goodsPicturePath;
        }

        public String getListUnique() {
            return listUnique;
        }

        public void setListUnique(String listUnique) {
            this.listUnique = listUnique;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public double getStockPrice() {
            return stockPrice;
        }

        public void setStockPrice(double stockPrice) {
            this.stockPrice = stockPrice;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public int getAuditStatus() {
            return auditStatus;
        }

        public void setAuditStatus(int auditStatus) {
            this.auditStatus = auditStatus;
        }

        public String getStockRemarks() {
            return stockRemarks;
        }

        public void setStockRemarks(String stockRemarks) {
            this.stockRemarks = stockRemarks;
        }

        public List<String> getStockPicture() {
            return stockPicture;
        }

        public void setStockPicture(List<String> stockPicture) {
            this.stockPicture = stockPicture;
        }
    }
}
