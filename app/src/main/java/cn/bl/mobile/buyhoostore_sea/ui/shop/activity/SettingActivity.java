package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.TextView;

import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.jpush.JPushUtil;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.ui.login.LoginActivity;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * @Author: Great Han
 * @Description: 优化后——》店铺设置
 * @Date: 8:45 2019/7/2
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SettingActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvInfoPersonal)
    TextView tvInfoPersonal;
    @BindView(R.id.tvInfoShop)
    TextView tvInfoShop;
    @BindView(R.id.tvAuthShop)
    TextView tvAuthShop;
    @BindView(R.id.tvLoginOut)
    TextView tvLoginOut;

    private SharedPreferences sp = null;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_setting;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void initViews() {
        sp = getSharedPreferences(Constants.SP_SHOP, Context.MODE_PRIVATE);
    }

    @Override
    public void initData() {
    }

    @OnClick({R.id.ivBack,
            R.id.tvInfoPersonal, R.id.tvInfoShop, R.id.tvAuthShop,
            R.id.tvLoginOut})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvInfoPersonal:
                //个人信息
                goToActivity(PersonUserInfoActivity.class);
                break;
            case R.id.tvInfoShop:
                //店铺信息
                goToActivity(ShopInfoActivity.class);
                break;
            case R.id.tvAuthShop:
                //店铺资质信息
                goToActivity(QualifyActivity.class);
                break;
            case R.id.tvLoginOut:
                //退出登录
                IAlertDialog.showDialog(this,
                        getLanguageValue("confirm")+getLanguageValue("signOut")+getLanguageValue("accountNumber")+"?",
                        getLanguageValue("confirm"),
                        (dialog, which) -> {
                            postUpdateJPushId();
                        });
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("shop") + getLanguageValue("setUp"));
        tvInfoPersonal.setText(getLanguageValue("individual") + getLanguageValue("information"));
        tvInfoShop.setText(getLanguageValue("shop") + getLanguageValue("information"));
        tvAuthShop.setText(getLanguageValue("shop")+getLanguageValue("credential")+getLanguageValue("information"));
        tvLoginOut.setText(getLanguageValue("signOut")+getLanguageValue("login"));
    }

    private void loginOut() {
        BaseApplication.getInstance().saveUserInfo("");
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("colose"));
        SharedPreferences mySharedPreferences = getSharedPreferences("test", Activity.MODE_PRIVATE);
        SharedPreferences.Editor editor = mySharedPreferences.edit();
        editor.putString("exit", "已退出");
        editor.commit();
        JPushUtil.setTagAndAlias("", this);
        if (sp != null) {
            SharedPreferences.Editor editor1 = sp.edit();
            editor1.clear();
            editor1.commit();
        }
        AppManager.getInstance().finishAllActivity();
        goToActivity(LoginActivity.class);
        finish();
    }


    /**
     * 更新推送注册id
     */
    private void postUpdateJPushId() {
        Map<String, Object> map = new HashMap<>();
        map.put("registration_phone_type", 2);
        map.put("registration_id", "");
        map.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdateRegistrationId(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        loginOut();
                    }

                    @Override
                    public void onError(String msg) {
                        loginOut();
                    }
                });
    }
}