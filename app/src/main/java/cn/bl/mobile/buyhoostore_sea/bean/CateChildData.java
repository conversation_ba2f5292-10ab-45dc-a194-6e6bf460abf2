package cn.bl.mobile.buyhoostore_sea.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:二级分类（实体类）
 * Created by jingang on 2022/11/30
 */
public class CateChildData implements Serializable {
    /**
     * kindName : 常用
     * kindUnique : 99991
     */

    private int kindId;
    private String kindName;
    private String kindUnique;
    private boolean check;
    private boolean show;
    private String editType;//1.不可编辑 2.可编辑
    private int valid_type;//1.启用 2.禁用
    private List<CateChildData> kindDetail;

    public int getKindId() {
        return kindId;
    }

    public void setKindId(int kindId) {
        this.kindId = kindId;
    }

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }

    public String getKindUnique() {
        return kindUnique;
    }

    public void setKindUnique(String kindUnique) {
        this.kindUnique = kindUnique;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public String getEditType() {
        return editType;
    }

    public void setEditType(String editType) {
        this.editType = editType;
    }

    public int getValid_type() {
        return valid_type;
    }

    public void setValid_type(int valid_type) {
        this.valid_type = valid_type;
    }

    public List<CateChildData> getKindDetail() {
        return kindDetail;
    }

    public void setKindDetail(List<CateChildData> kindDetail) {
        this.kindDetail = kindDetail;
    }
}
