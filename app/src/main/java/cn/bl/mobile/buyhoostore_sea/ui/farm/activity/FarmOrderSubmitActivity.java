package cn.bl.mobile.buyhoostore_sea.ui.farm.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.MemberBean;
import cn.bl.mobile.buyhoostore_sea.bean.BaseData;
import cn.bl.mobile.buyhoostore_sea.ui.farm.dialog.NPCustomerDialog;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:农批-收银（提交订单）
 * Created by jingang on 2023/5/25
 */
@SuppressLint("NonConstantResourceId")
public class FarmOrderSubmitActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvTotal)
    TextView tvTotal;
    @BindView(R.id.tvCus)
    TextView tvCus;
    @BindView(R.id.linPayment0)
    LinearLayout linPayment0;
    @BindView(R.id.ivPayment0)
    ImageView ivPayment0;
    @BindView(R.id.tvPayment0)
    TextView tvPayment0;
    @BindView(R.id.linPayment1)
    LinearLayout linPayment1;
    @BindView(R.id.ivPayment1)
    ImageView ivPayment1;
    @BindView(R.id.tvPayment1)
    TextView tvPayment1;
    @BindView(R.id.linPayment2)
    LinearLayout linPayment2;
    @BindView(R.id.ivPayment2)
    ImageView ivPayment2;
    @BindView(R.id.tvPayment2)
    TextView tvPayment2;
    @BindView(R.id.linPayment3)
    LinearLayout linPayment3;
    @BindView(R.id.ivPayment3)
    ImageView ivPayment3;
    @BindView(R.id.tvPayment3)
    TextView tvPayment3;

    private double total;
    private String goodsMsg, cusUnique, cusName;
    private int type = 6;//1、现金；2、支付宝；3、微信;4、银行卡;6、聚合支付'
    private boolean isErase;//是否抹零
    private MemberBean.DataBean mMemberBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_farm_order_submit;
    }

    @Override
    public void initViews() {
        tvTitle.setText("收银");
        total = getIntent().getDoubleExtra("total", 0);
        goodsMsg = getIntent().getStringExtra("json");
        cusUnique = getIntent().getStringExtra("cusUnique");
        cusName = getIntent().getStringExtra("cusName");
        tvTotal.setText(DFUtils.getNum2(total));
        tvCus.setText(cusName);
    }

    @OnClick({R.id.ivBack, R.id.tvErase, R.id.tvCus, R.id.linPayment0, R.id.linPayment1, R.id.linPayment2, R.id.linPayment3, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvErase:
                //抹零
                isErase = !isErase;
                if (isErase) {
                    tvTotal.setText(DFUtils.getNum(total));
                } else {
                    tvTotal.setText(DFUtils.getNum2(total));
                }
                break;
            case R.id.tvCus:
                //选择客户
                NPCustomerDialog dialog = new NPCustomerDialog(this);
                if (mMemberBean != null) {
                    dialog.setCusUnique(mMemberBean.getCus_unique());
                }
                dialog.setonDialogClickListener(memberBean -> {
                    tvCus.setText(memberBean.getCusName());
                    cusUnique = memberBean.getCus_unique();
                    mMemberBean = memberBean;
                });
                new XPopup.Builder(this)
                        .popupAnimation(PopupAnimation.ScrollAlphaFromBottom)
                        .offsetY(200)
                        .isDestroyOnDismiss(true)
                        .asCustom(dialog)
                        .show();
                break;
            case R.id.linPayment0:
                //现金
                if (type != 1) {
                    type = 1;
                    clearTextBg();
                    linPayment0.setBackgroundResource(R.drawable.shape_33cc67_8);
                    ivPayment0.setImageResource(R.mipmap.ic_payment00);
                    tvPayment0.setTextColor(getResources().getColor(R.color.white));
                }
                break;
            case R.id.linPayment1:
                //聚合码
                if (type != 6) {
                    type = 6;
                    clearTextBg();
                    linPayment1.setBackgroundResource(R.drawable.shape_33cc67_8);
                    ivPayment1.setImageResource(R.mipmap.ic_payment10);
                    tvPayment1.setTextColor(getResources().getColor(R.color.white));
                }
                break;
            case R.id.linPayment2:
                //银行卡收款
                if (type != 4) {
                    type = 4;
                    clearTextBg();
                    linPayment2.setBackgroundResource(R.drawable.shape_33cc67_8);
                    ivPayment2.setImageResource(R.mipmap.ic_payment20);
                    tvPayment2.setTextColor(getResources().getColor(R.color.white));
                }
                break;
            case R.id.linPayment3:
                //其他（不知道）
                break;
            case R.id.tvConfirm:
                //提交
                postFarmOrder();
                break;
        }
    }

    /**
     * 清除选择样式（收款方式）
     */
    private void clearTextBg() {
        linPayment0.setBackgroundResource(R.drawable.shape_f2_8);
        ivPayment0.setImageResource(R.mipmap.ic_payment01);
        tvPayment0.setTextColor(getResources().getColor(R.color.color_666));
        linPayment1.setBackgroundResource(R.drawable.shape_f2_8);
        ivPayment1.setImageResource(R.mipmap.ic_payment11);
        tvPayment1.setTextColor(getResources().getColor(R.color.color_666));
        linPayment2.setBackgroundResource(R.drawable.shape_f2_8);
        ivPayment2.setImageResource(R.mipmap.ic_payment21);
        tvPayment2.setTextColor(getResources().getColor(R.color.color_666));
        linPayment3.setBackgroundResource(R.drawable.shape_f2_8);
        ivPayment3.setImageResource(R.mipmap.ic_payment31);
        tvPayment3.setTextColor(getResources().getColor(R.color.color_666));
    }

    /**
     * 农批产品订单保存
     */
    private void postFarmOrder() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("cusUnique", cusUnique);
        map.put("staffId", getStaff_id());
        map.put("saleListPayment", type);
        map.put("goodsMsg", goodsMsg);
        if (isErase) {
            map.put("saleListActuallyReceived", DFUtils.getNum(total));
        } else {
            map.put("saleListActuallyReceived", DFUtils.getNum2(total));
        }
        RXHttpUtil.requestByFormPostAsOriginalResponse(this, ZURL.getFarmOrderSave(), map, new RequestListener<String>() {
            @Override
            public void success(String s) {
                BaseData data = new Gson().fromJson(s, BaseData.class);
                showMessage(data.getMsg());
                if (data.getStatus() == Constants.SUCCESS_CODE) {
                    finish();
                }
            }
        });
    }
}
