package cn.bl.mobile.buyhoostore_sea.ui.shop.activity;

import java.util.HashMap;
import java.util.Map;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.bean.SwitchResponseModel;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

import com.yxl.commonlibrary.base.BaseActivity;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

/**
 * 店铺-店铺设置-模块信息
 */
@SuppressLint("NonConstantResourceId")
public class SetSwitchActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;

    @BindView(R.id.cb_songshui)
    CheckBox cb_songshui;
    @BindView(R.id.cb_kuaidi)
    CheckBox cb_kuaidi;
    @BindView(R.id.cb_jiazheng)
    CheckBox cb_jiazheng;
    @BindView(R.id.cb_guoshu)
    CheckBox cb_guoshu;
    @BindView(R.id.cb_xianhua)
    CheckBox cb_xianhua;
    @BindView(R.id.cb_dangao)
    CheckBox cb_dangao;
    @BindView(R.id.cb_xiyi)
    CheckBox cb_xiyi;
    @BindView(R.id.look_suplice)
    CheckBox look_suplice;

    private String songshui = "0";
    private String kuaidi = "0";
    private String jiazheng = "0";
    private String guoshu = "0";
    private String xianhua = "0";
    private String dangao = "0";
    private String xiyi = "0";
    private String shoppur = "0";

    @Override
    protected int getLayoutId() {
        return R.layout.activity_set_switch;
    }

    @Override
    public void initViews() {
        tvTitle.setText("模块信息");
    }

    @Override
    public void initData() {
        getSetSwitch();
    }

    @OnClick({R.id.ivBack, R.id.cb_songshui, R.id.cb_kuaidi, R.id.cb_jiazheng, R.id.cb_guoshu, R.id.cb_xianhua, R.id.cb_dangao, R.id.cb_xiyi, R.id.look_suplice})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.cb_songshui:
                if (cb_songshui.isChecked()) {
                    songshui = "1";
                } else {
                    songshui = "2";
                }
                setpower();
                break;
            case R.id.cb_kuaidi:
                if (cb_kuaidi.isChecked()) {
                    kuaidi = "1";
                } else {
                    kuaidi = "2";
                }
                setpower();
                break;
            case R.id.cb_jiazheng:
                if (cb_jiazheng.isChecked()) {
                    jiazheng = "1";
                } else {
                    jiazheng = "2";
                }
                setpower();
                break;
            case R.id.cb_guoshu:
                if (cb_guoshu.isChecked()) {
                    guoshu = "1";
                } else {
                    guoshu = "2";
                }
                setpower();
                break;
            case R.id.cb_xianhua:
                if (cb_xianhua.isChecked()) {
                    xianhua = "1";
                } else {
                    xianhua = "2";
                }
                setpower();
                break;
            case R.id.cb_dangao:
                if (cb_dangao.isChecked()) {
                    dangao = "1";
                } else {
                    dangao = "2";
                }
                setpower();
                break;
            case R.id.cb_xiyi:
                if (cb_xiyi.isChecked()) {
                    xiyi = "1";
                } else {
                    xiyi = "2";
                }
                setpower();
                break;
            case R.id.look_suplice:
                if (look_suplice.isChecked()) {
                    shoppur = "1";
                } else {
                    shoppur = "2";
                }
                setpower();
                break;
            case R.id.base_title_back:
                finish();
                break;
        }
    }

    //获取模块信息
    public void getSetSwitch() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSetSwitchUrlTWO(),
                params,
                SwitchResponseModel.DataBean.class,
                new RequestListener<SwitchResponseModel.DataBean>() {
                    @Override
                    public void success(SwitchResponseModel.DataBean data) {
                        if (data != null) {
                            songshui = data.getShopDeliverwater();
                            kuaidi = data.getShopExpress();
                            jiazheng = data.getShopHomemarking();
                            guoshu = data.getShopFruit();
                            xianhua = data.getShopFlower();
                            dangao = data.getShopCake();
                            xiyi = data.getShopLaundry();
                            shoppur = data.getShopPur();
                            if (songshui.equals("1")) {
                                cb_songshui.setChecked(true);
                            } else if (songshui.equals("2")) {
                                cb_songshui.setChecked(false);
                            }

                            if (kuaidi.equals("1")) {
                                cb_kuaidi.setChecked(true);
                            } else if (kuaidi.equals("2")) {
                                cb_kuaidi.setChecked(false);
                            }

                            if (jiazheng.equals("1")) {
                                cb_jiazheng.setChecked(true);
                            } else if (jiazheng.equals("2")) {
                                cb_jiazheng.setChecked(false);
                            }

                            if (guoshu.equals("1")) {
                                cb_guoshu.setChecked(true);
                            } else if (guoshu.equals("2")) {
                                cb_guoshu.setChecked(false);
                            }

                            if (xianhua.equals("1")) {
                                cb_xianhua.setChecked(true);
                            } else if (xianhua.equals("2")) {
                                cb_xianhua.setChecked(false);
                            }

                            if (dangao.equals("1")) {
                                cb_dangao.setChecked(true);
                            } else if (dangao.equals("2")) {
                                cb_dangao.setChecked(false);
                            }

                            if (xiyi.equals("1")) {
                                cb_xiyi.setChecked(true);
                            } else if (xiyi.equals("2")) {
                                cb_xiyi.setChecked(false);
                            }
                            if (shoppur.equals("1")) {
                                look_suplice.setChecked(true);
                            } else if (shoppur.equals("2")) {
                                look_suplice.setChecked(false);
                            }

                        }
                    }
                });
    }

    /**
     * 修改店铺功能模块
     * private String songshui="0";
     * private String kuaidi="0";
     * private String jiazheng="0";
     * private String guoshu="0";
     * private String xianhua="0";
     * private String dangao="0";
     * private String xiyi="0";
     */
    public void setpower() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("shopFlower", xianhua);
        params.put("shopLaundry", xiyi);
        params.put("shopPur", shoppur);
        params.put("shopFruit", guoshu);
        params.put("shopDeliverwater", songshui);
        params.put("shopHomemarking", jiazheng);
        params.put("shopCake", dangao);
        params.put("shopExpress", kuaidi);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.setshoppower(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage("修改成功");
                    }
                });
    }
}
