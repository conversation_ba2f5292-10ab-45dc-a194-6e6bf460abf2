package cn.bl.mobile.buyhoostore_sea.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import cn.bl.mobile.buyhoostore_sea.R;

/**
 * Describe:dialog（介绍：分类说明）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class IntroduceDialog extends BaseDialog {
    @BindView(R.id.tvDialogTitle)
    TextView tvTitle;
    @BindView(R.id.tvDialogContent)
    TextView tvContent;

    private static String title, content;

    public static void showDialog(Context context, String title, String content) {
        IntroduceDialog.title = title;
        IntroduceDialog.content = content;
        IntroduceDialog dialog = new IntroduceDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public IntroduceDialog(Context context) {
        super(context, R.style.dialog_style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_introduce);
        ButterKnife.bind(this);
        tvTitle.setText(title);
        tvContent.setText(content);
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }
}
