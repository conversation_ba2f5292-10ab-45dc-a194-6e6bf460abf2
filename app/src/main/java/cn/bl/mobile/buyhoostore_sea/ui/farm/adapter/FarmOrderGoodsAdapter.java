package cn.bl.mobile.buyhoostore_sea.ui.farm.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.sale.bean.OrderInfoData;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;

/**
 * Describe:农批-订单-商品（适配器）
 * Created by jingang on 2023/5/25
 */
public class FarmOrderGoodsAdapter extends BaseAdapter<OrderInfoData.DataBean.ListDetailBean> {

    public FarmOrderGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_farm_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvChengType, tvSaleType, tvCount, tvPrice, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvChengType = holder.getView(R.id.tvItemChengType);
        tvSaleType = holder.getView(R.id.tvItemSaleType);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTotal = holder.getView(R.id.tvItemTotal);

        tvName.setText(mDataList.get(position).getGoodsName());
        tvChengType.setText(mDataList.get(position).getGoodsChengType());
        tvSaleType.setText(mDataList.get(position).getGoodsType());
        tvCount.setText(DFUtils.getNum2(mDataList.get(position).getSaleListDetailCount()));
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getSaleListDetailPrice()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSubTotal()));
    }
}
