package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.activity;

import android.annotation.SuppressLint;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.adapter.MaterialAdapter;

/**
 * Describe:店铺-聚合码-选择物料
 * Created by jingang on 2023/5/9
 */
@SuppressLint("NonConstantResourceId")
public class MaterialActivity extends BaseActivity {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    private MaterialAdapter mAdapter;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_material;
    }

    @Override
    public void initViews() {
//        tvTitle.setText(getLanguageValue("pleaseSelect")+getLanguageValue(""));
        setAdapter();
    }

    @OnClick({R.id.ivBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new MaterialAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            goToActivity(MaterialInfoActivity.class);
        });
    }
}
