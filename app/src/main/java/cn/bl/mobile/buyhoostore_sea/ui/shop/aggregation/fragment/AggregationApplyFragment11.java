package cn.bl.mobile.buyhoostore_sea.ui.shop.aggregation.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.commonlibrary.base.BaseFragment;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Locale;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import com.yxl.commonlibrary.bean.EventData;
import com.yxl.commonlibrary.utils.EventBusManager;

import cn.bl.mobile.buyhoostore_sea.utils.GlideEngine;
import cn.bl.mobile.buyhoostore_sea.utils.ImageFileCompressEngine;
import cn.bl.mobile.buyhoostore_sea.utils.MeSandboxFileEngine;
import cn.bl.mobile.buyhoostore_sea.bean.UrlData;
import cn.bl.mobile.buyhoostore_sea.utils.PermissionUtils;
import cn.bl.mobile.buyhoostore_sea.utils.RegularUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe:赊销审核（身份证）
 * Created by jingang on 2023/2/14
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class AggregationApplyFragment11 extends BaseFragment {
    @BindView(R.id.tvInfo0Value)
    TextView tvInfo0Value;
    @BindView(R.id.linBg0)
    LinearLayout linBg0;
    @BindView(R.id.ivBg0)
    ImageView ivBg0;
    @BindView(R.id.ivImg0)
    ImageView ivImg0;
    @BindView(R.id.ivCamera0)
    ImageView ivCamera0;
    @BindView(R.id.tvImg0Value)
    TextView tvImg0Value;
    @BindView(R.id.linBg1)
    LinearLayout linBg1;
    @BindView(R.id.ivBg1)
    ImageView ivBg1;
    @BindView(R.id.ivImg1)
    ImageView ivImg1;
    @BindView(R.id.ivCamera1)
    ImageView ivCamera1;
    @BindView(R.id.tvImg1Value)
    TextView tvImg1Value;
    @BindView(R.id.linBg2)
    LinearLayout linBg2;
    @BindView(R.id.ivBg2)
    ImageView ivBg2;
    @BindView(R.id.ivImg2)
    ImageView ivImg2;
    @BindView(R.id.ivCamera2)
    ImageView ivCamera2;
    @BindView(R.id.tvImg2Value)
    TextView tvImg2Value;

    @BindView(R.id.tvInfo1Value)
    TextView tvInfo1Value;
    @BindView(R.id.linBg3)
    LinearLayout linBg3;
    @BindView(R.id.ivBg3)
    ImageView ivBg3;
    @BindView(R.id.ivImg3)
    ImageView ivImg3;
    @BindView(R.id.ivCamera3)
    ImageView ivCamera3;
    @BindView(R.id.tvImg3Value)
    TextView tvImg3Value;
    @BindView(R.id.linBg4)
    LinearLayout linBg4;
    @BindView(R.id.ivBg4)
    ImageView ivBg4;
    @BindView(R.id.ivImg4)
    ImageView ivImg4;
    @BindView(R.id.ivCamera4)
    ImageView ivCamera4;
    @BindView(R.id.tvImg4Value)
    TextView tvImg4Value;
    @BindView(R.id.tvIdcard_nameValue)
    TextView tvIdcard_nameValue;
    @BindView(R.id.etIdcard_name)
    EditText etIdcard_name;
    @BindView(R.id.tvIdcard_codeValue)
    TextView tvIdcard_codeValue;
    @BindView(R.id.etIdcard_code)
    EditText etIdcard_code;
    @BindView(R.id.tvIdcard_name1Value)
    TextView tvIdcard_name1Value;
    @BindView(R.id.etIdcard_name1)
    EditText etIdcard_name1;
    @BindView(R.id.tvIdcard_code1Value)
    TextView tvIdcard_code1Value;
    @BindView(R.id.etIdcard_code1)
    EditText etIdcard_code1;
    @BindView(R.id.tvPrevious)
    TextView tvPrevious;
    @BindView(R.id.tvNext)
    TextView tvNext;

    private int type,//类型：0.身份证正面 1.身份证反面 2.手持身份证
            status;
    private String idcard0, idcard1, idcard2, idcard3, idcard4,
            idcard_name,//真实姓名（法人）
            idcard_code,//身份证（法人）
            idcard_name1,//真实姓名（持卡人）
            idcard_code1;//身份证（持卡人）

    public AggregationApplyFragment11(int status, String idcard0, String idcard1, String idcard2, String idcard3, String idcard4,
                                      String idcard_name, String idcard_code, String idcard_name1, String idcard_code1) {
        this.status = status;
        this.idcard0 = idcard0;
        this.idcard1 = idcard1;
        this.idcard2 = idcard2;
        this.idcard3 = idcard3;
        this.idcard4 = idcard4;
        this.idcard_name = idcard_name;
        this.idcard_code = idcard_code;
        this.idcard_name1 = idcard_name1;
        this.idcard_code1 = idcard_code1;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fm_aggregation_apply11;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        etIdcard_name.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                idcard_name = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard_name", idcard_name));
                setTextBg();
            }
        });
        etIdcard_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                idcard_code = s.toString().toLowerCase(Locale.ROOT);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard_code", idcard_code));
                setTextBg();
            }
        });
        etIdcard_name1.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                idcard_name1 = s.toString().trim();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard_name1", idcard_name1));
                setTextBg();
            }
        });
        etIdcard_code1.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                idcard_code1 = s.toString().toLowerCase(Locale.ROOT);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard_code1", idcard_code1));
                setTextBg();
            }
        });
        if (status == 1) {
            linBg0.setEnabled(false);
            ivCamera0.setVisibility(View.GONE);
            linBg1.setEnabled(false);
            ivCamera1.setVisibility(View.GONE);
            linBg2.setEnabled(false);
            ivCamera2.setVisibility(View.GONE);
            linBg3.setEnabled(false);
            ivCamera3.setVisibility(View.GONE);
            linBg4.setEnabled(false);
            ivCamera4.setVisibility(View.GONE);
            etIdcard_name.setFocusable(false);
            etIdcard_code.setFocusable(false);
            etIdcard_name1.setFocusable(false);
            etIdcard_code1.setFocusable(false);
        } else {
            linBg0.setEnabled(true);
            ivCamera0.setVisibility(View.VISIBLE);
            linBg1.setEnabled(true);
            ivCamera1.setVisibility(View.VISIBLE);
            linBg2.setEnabled(true);
            ivCamera2.setVisibility(View.VISIBLE);
            linBg3.setEnabled(true);
            ivCamera3.setVisibility(View.VISIBLE);
            linBg4.setEnabled(true);
            ivCamera4.setVisibility(View.VISIBLE);
            etIdcard_name.setFocusable(true);
            etIdcard_code.setFocusable(true);
            etIdcard_name1.setFocusable(true);
            etIdcard_code1.setFocusable(true);
        }
        setUI();
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.linBg0, R.id.linBg1, R.id.linBg2, R.id.linBg3, R.id.linBg4, R.id.tvPrevious, R.id.tvNext})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.linBg0:
                //身份证正面（法人）
                type = 0;
                getImg();
                break;
            case R.id.linBg1:
                //身份证反面（法人）
                type = 1;
                getImg();
                break;
            case R.id.linBg2:
                //手持身份证正面（法人）
                type = 2;
                getImg();
                break;
            case R.id.linBg3:
                type = 3;
                getImg();
                break;
            case R.id.linBg4:
                type = 4;
                getImg();
                break;
            case R.id.tvPrevious:
                //上一步
                if (listener != null) {
                    listener.onPreviousClick();
                }
                break;
            case R.id.tvNext:
                //下一步
                if (TextUtils.isEmpty(idcard0)) {
                    showMessage(getLanguageValue("uploadLegalPersonFace"));
                    return;
                }
                if (TextUtils.isEmpty(idcard1)) {
                    showMessage(getLanguageValue("uploadLegalPersonEmblem"));
                    return;
                }
                if (TextUtils.isEmpty(idcard2)) {
                    showMessage(getLanguageValue("uploadLegalPersonHeld"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_name)) {
                    showMessage(getLanguageValue("uploadLegalPersonRealname"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_code)) {
                    showMessage(getLanguageValue("enterLegalPersonId"));
                    return;
                }
                if(!RegularUtils.isValidatedAllIdcard(idcard_code)){
                    showMessage(getLanguageValue("enterLegalPersonIdCorrect"));
                    return;
                }
                if (TextUtils.isEmpty(idcard3)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("identityWitnessFace"));
                    return;
                }
                if (TextUtils.isEmpty(idcard4)) {
                    showMessage(getLanguageValue("please")+getLanguageValue("upload")+getLanguageValue("indentityEmblem"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_name1)) {
                    showMessage(getLanguageValue("enterApplicantRealname"));
                    return;
                }
                if (TextUtils.isEmpty(idcard_code1)) {
                    showMessage(getLanguageValue("enterApplicantId"));
                    return;
                }
                if(!RegularUtils.isValidatedAllIdcard(idcard_code)){
                    showMessage(getLanguageValue("enterApplicantIdCorrect"));
                    return;
                }
                if (listener != null) {
                    listener.onNextClick();
                }
                break;
        }
    }

    @Override
    public void setText() {
        tvInfo0Value.setText(getLanguageValue("legalPersonInfo"));
        tvImg0Value.setText(getLanguageValue("shoot")+getLanguageValue("indentityEmblem"));
        tvImg1Value.setText(getLanguageValue("shoot")+getLanguageValue("identityWitnessFace"));
        tvImg2Value.setText(getLanguageValue("uploadLegalPersonHeld"));
        tvIdcard_nameValue.setText(getLanguageValue("realname"));
        etIdcard_name.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("realname"));
        tvIdcard_codeValue.setText(getLanguageValue("idNumber"));
        etIdcard_code.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("idNumber"));
        tvInfo1Value.setText(getLanguageValue("cardholderInfo"));
        tvImg3Value.setText(getLanguageValue("shoot")+getLanguageValue("indentityEmblem"));
        tvImg4Value.setText(getLanguageValue("shoot")+getLanguageValue("indentityEmblem"));
        tvIdcard_name1Value.setText(getLanguageValue("realname"));
        etIdcard_name1.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("realname"));
        tvIdcard_code1Value.setText(getLanguageValue("realname"));
        etIdcard_code1.setHint(getLanguageValue("pleaseEnter")+getLanguageValue("realname"));
        tvPrevious.setText(getLanguageValue("previousStep"));
        tvNext.setText(getLanguageValue("nextStep"));
    }

    /**
     * 动态申请权限
     */
    public void getImg() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 0)) {
            showDialogCamera();
        } else {
            PermissionUtils.requestPermissions(AggregationApplyFragment11.this, Constants.PERMISSION, 0);
        }
    }

    /**
     * dialog（拍照、相册选择图片）
     */
    private void showDialogCamera() {
        TextView tvCamera, tvAlbum, tvCancel;
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_camera, null);
        dialog.setContentView(view);

        tvCamera = view.findViewById(R.id.tvDialogCamera);
        tvAlbum = view.findViewById(R.id.tvDialogAlbum);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvCamera.setText(getLanguageValue("photograph"));
        tvAlbum.setText(getLanguageValue("phoneSelect"));
        tvCancel.setText(getLanguageValue("cancel"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        tvCamera.setOnClickListener(v -> {
            takePhoto();
            dialog.dismiss();
        });
        tvAlbum.setOnClickListener(v -> {
            pickPhoto();
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(v -> dialog.dismiss());
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(getActivity())
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())

                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        switch (type) {
//                            case 0:
//                                idcard0 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard0", idcard0));
//                                break;
//                            case 1:
//                                idcard1 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard1", idcard1));
//                                break;
//                            case 2:
//                                idcard2 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard2", idcard2));
//                                break;
//                            case 3:
//                                idcard3 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard3", idcard3));
//                                break;
//                            case 4:
//                                idcard4 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard4", idcard4));
//                                break;
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(getActivity())
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(1)
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        showDialog();
                        String path = result.get(0).getAvailablePath();
                        setUpdateFile(new File(path));
//                        switch (type) {
//                            case 0:
//                                idcard0 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard0", idcard0));
//                                break;
//                            case 1:
//                                idcard1 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard1", idcard1));
//                                break;
//                            case 2:
//                                idcard2 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard2", idcard2));
//                                break;
//                            case 3:
//                                idcard3 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard3", idcard3));
//                                break;
//                            case 4:
//                                idcard4 = path;
//                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard4", idcard4));
//                                break;
//                        }
//                        setUI();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    /***
     * 资质认证提交
     */
    public void setUpdateFile(File file) {
        OkHttpClient mOkHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .header("Authorization", "Client-ID " + "...")
                .url(ZURL.getUploadFile())
                .post(new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("image/jpg"), file))
                        .build())
                .build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("111111", "请求失败" + e);
                getActivity().runOnUiThread(() -> {
                    showMessage(getLanguageValue("uploadFailTry"));
                    hideDialog();
                });
            }

            @Override
            public void onResponse(Call call, Response response) {
                getActivity().runOnUiThread(() -> {
                    hideDialog();
                    try {
                        UrlData data = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), UrlData.class);
                        if (data == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getData() == null) {
                            showMessage(getLanguageValue("uploadFailTry"));
                            return;
                        }
                        if (data.getStatus() == 1 && !TextUtils.isEmpty(data.getData().getUrl())) {
                            switch (type) {
                                case 0:
                                    idcard0 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard0", idcard0));
                                    break;
                                case 1:
                                    idcard1 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard1", idcard1));
                                    break;
                                case 2:
                                    idcard2 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard2", idcard2));
                                    break;
                                case 3:
                                    idcard3 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard3", idcard3));
                                    break;
                                case 4:
                                    idcard4 = data.getData().getUrl();
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData("idcard4", idcard4));
                                    break;
                            }
                            setUI();
                            return;
                        }
                        showMessage(getLanguageValue("uploadFailTry"));
                    } catch (Exception e) {
                        showMessage(getLanguageValue("uploadFailTry"));
                    }
                });
            }
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //正面
        if (TextUtils.isEmpty(idcard0)) {
            ivBg0.setImageResource(R.mipmap.ic_idcard_bg001);
            ivImg0.setVisibility(View.GONE);
        } else {
            ivBg0.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg0.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(idcard0)
                    .into(ivImg0);
        }
        //反面
        if (TextUtils.isEmpty(idcard1)) {
            ivBg1.setImageResource(R.mipmap.ic_idcard_bg002);
            ivImg1.setVisibility(View.GONE);
        } else {
            ivBg1.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg1.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(idcard1)
                    .into(ivImg1);
        }
        //手持
        if (TextUtils.isEmpty(idcard2)) {
            ivBg2.setImageResource(R.mipmap.ic_idcard_bg003);
            ivImg2.setVisibility(View.GONE);
        } else {
            ivBg2.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg2.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(idcard2)
                    .into(ivImg2);
        }
        //正面
        if (TextUtils.isEmpty(idcard3)) {
            ivBg3.setImageResource(R.mipmap.ic_idcard_bg001);
            ivImg3.setVisibility(View.GONE);
        } else {
            ivBg3.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg3.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(idcard3)
                    .into(ivImg3);
        }
        //反面
        if (TextUtils.isEmpty(idcard4)) {
            ivBg4.setImageResource(R.mipmap.ic_idcard_bg002);
            ivImg4.setVisibility(View.GONE);
        } else {
            ivBg4.setImageResource(R.mipmap.ic_credit_apply_bg001);
            ivImg4.setVisibility(View.VISIBLE);
            Glide.with(getActivity())
                    .load(idcard4)
                    .into(ivImg4);
        }
        etIdcard_name.setText(idcard_name);
        etIdcard_code.setText(idcard_code);
        etIdcard_name1.setText(idcard_name1);
        etIdcard_code1.setText(idcard_code1);
        setTextBg();
    }

    /**
     * 判断按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(idcard0)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard2)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard3)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard4)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard_name)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard_code)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard_name1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        if (TextUtils.isEmpty(idcard_code1)) {
            tvNext.setBackgroundResource(R.drawable.shape_b9d6fe_22);
            return;
        }
        tvNext.setBackgroundResource(R.drawable.shape_blue_22);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showMessage(getLanguageValue("funNotSet"));
                } else {
                    showDialogCamera();
                }
                break;
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onPreviousClick();

        void onNextClick();
    }
}
