package cn.bl.mobile.buyhoostore_sea.ui.goods;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gengcon.www.jcprintersdk.callback.PrintCallback;
import com.gprinter.command.LabelCommand;
import com.gprinter.utils.LogUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.commonlibrary.base.BaseFragment;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import butterknife.BindView;
import butterknife.OnClick;
import cn.bl.mobile.buyhoostore_sea.Constants;
import cn.bl.mobile.buyhoostore_sea.R;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.CateAdapter;
import cn.bl.mobile.buyhoostore_sea.adapter.GoodsAdapter;
import cn.bl.mobile.buyhoostore_sea.bean.CateData;

import com.yxl.commonlibrary.bean.EventData;

import cn.bl.mobile.buyhoostore_sea.bean.GoodsData;
import cn.bl.mobile.buyhoostore_sea.printer.PrinterSettingActivity;
import cn.bl.mobile.buyhoostore_sea.printer.jc.PrintUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.Printer;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.SharedPreferencesUtil;
import cn.bl.mobile.buyhoostore_sea.printer.jiabo.ThreadPoolManager;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsCateActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsChangeRecordActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsEditActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.activity.GoodsSearchActivity;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.CateSortAdapter;
import cn.bl.mobile.buyhoostore_sea.ui.goods.adapter.MyItemTouchHelper;
import cn.bl.mobile.buyhoostore_sea.ui.goods.bean.CateMoreData;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ChuRuSelectActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.activity.ScanActivity;
import cn.bl.mobile.buyhoostore_sea.ui.shop.pan.activity.PanGoodsRecordActivity;
import cn.bl.mobile.buyhoostore_sea.utils.NetworkUtils;
import cn.bl.mobile.buyhoostore_sea.ui.dialog.IAlertDialog;
import cn.bl.mobile.buyhoostore_sea.utils.DFUtils;
import cn.bl.mobile.buyhoostore_sea.zhttp.ZURL;

/**
 * Describe:商品管理
 * Created by jingang on 2022/11/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsFragment extends BaseFragment {
    @BindView(R.id.relParent)
    RelativeLayout relParent;
    @BindView(R.id.relTop0)
    RelativeLayout relTop0;
    @BindView(R.id.relTop1)
    RelativeLayout relTop1;
    @BindView(R.id.tvOperate)
    TextView tvOperate;
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvCancel)
    TextView tvCancel;
    @BindView(R.id.tvTitleOperate)
    TextView tvTitleOperate;

    @BindView(R.id.rvCate)
    RecyclerView rvCate;
    @BindView(R.id.tvCateSort)
    TextView tvCateSort;

    @BindView(R.id.tvSort)
    TextView tvSort;
    @BindView(R.id.ivSort)
    ImageView ivSort;
    @BindView(R.id.tvKucun)
    TextView tvKucun;
    @BindView(R.id.ivKucun)
    ImageView ivKucun;
    @BindView(R.id.tvPrice)
    TextView tvPrice;
    @BindView(R.id.ivPrice)
    ImageView ivPrice;
    @BindView(R.id.tvSale)
    TextView tvSale;
    @BindView(R.id.ivSale)
    ImageView ivSale;

    @BindView(R.id.tvType0)
    TextView tvType0;
    @BindView(R.id.vType0)
    View vType0;
    @BindView(R.id.tvType1)
    TextView tvType1;
    @BindView(R.id.vType1)
    View vType1;
    @BindView(R.id.tvType2)
    TextView tvType2;
    @BindView(R.id.vType2)
    View vType2;

    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.linEmpty)
    LinearLayout linEmpty;

    @BindView(R.id.ivMore)
    ImageView ivMore;
    @BindView(R.id.tvAdd)
    TextView tvAdd;
    @BindView(R.id.tvSulu)
    TextView tvSulu;

    // 子按钮列表
    private List<TextView> buttonItems = new ArrayList<>();
    // 标识当前按钮弹出与否，1代表已经未弹出，-1代表已弹出
    private int flag = 1;
    private int wParent, hParent, wView, hView;//父布局、子view宽高

    //分类
    private CateAdapter cateAdapter;
    private List<CateData> cateList = new ArrayList<>(),
            cateSortList = new ArrayList<>();//分类排序列表
    private String groupUnique,//一级分类id
            kindUnique,//二级分类id -1.全部
            kindUnique2;//三级分类
    private int orderType,// 1.升序 2.降序
            order;//排序方式 1或null.默认排序 2.销量 3.价格 4.库存
    private int printerStatus,//收银机上架状态 1.上架 2.下架
            appletStatus;//小程序上架状态 1.上架 2.下架
    private boolean isBatch;//当前是否为批量操作

    //商品列表
    private int status = 0;//商品状态 0.全部 1.已上架 2.已下架
    private List<GoodsData> dataList = new ArrayList<>(),//当前
            dataList1 = new ArrayList<>(),//全部
            dataList2 = new ArrayList<>(),//已上架
            dataList3 = new ArrayList<>(),//已下架
            batchList = new ArrayList<>();//批量操作
    private GoodsAdapter mAdapter;

    private String goodsBarcode,//商品编码
            goodsName,//商品名称
            goodsPrice,//商品价格
            goodsUnit,//商品单位
            goodsImg;//商品图片

    //打印价签
    private SharedPreferencesUtil sharedPreferencesUtil;

    //本地缓存
    private SharedPreferences sp;
    public static String powerAdd;

    /**
     * 初始化fragment
     *
     * @return
     */
    public static GoodsFragment newInstance() {
        GoodsFragment fragment = new GoodsFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fm_goods;
    }

    @Override
    protected void initView(@Nullable Bundle savedInstanceState) {
        sp = getActivity().getSharedPreferences("shop", Context.MODE_PRIVATE);
        powerAdd = sp.getString("power_add", "0");
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(getActivity());
        printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);

        setAdapter();

        // 将子按钮们加入列表中
//        buttonItems.add(ivAdd);
//        buttonItems.add(ivSule);
        buttonItems.add(tvAdd);
        buttonItems.add(tvSulu);
        relParent.post(() -> {
            wParent = relParent.getMeasuredWidth();
            hParent = relParent.getMeasuredHeight();
        });
        ivMore.post(() -> {
            wView = ivMore.getMeasuredWidth();
            hView = ivMore.getMeasuredHeight();
        });
    }

    /**
     * 按钮移动动画
     *
     * @params 子按钮列表
     * @params 弹出时圆形半径radius
     */
    public void buttonAnimation(int radius) {
        for (int i = 0; i < buttonItems.size(); i++) {
            ObjectAnimator objAnimatorX;
            ObjectAnimator objAnimatorY;
            ObjectAnimator objAnimatorRotate;
            // 将按钮设为可见
            buttonItems.get(i).setVisibility(View.VISIBLE);
            // 按钮在X、Y方向的移动距离
            float distanceX = (float) (flag * radius * (Math.cos(Math.toRadians(60 / (buttonItems.size() - 1) * i + 148))));
            float distanceY = -(float) (flag * radius * (Math.sin(Math.toRadians(60 / (buttonItems.size() - 1) * i + 148))));
            // X方向移动
            objAnimatorX = ObjectAnimator.ofFloat(buttonItems.get(i),
                    "x",
                    wParent - wView - DensityUtils.dip2px(getActivity(), 15),
                    wParent - wView - DensityUtils.dip2px(getActivity(), 15) - distanceX);
            objAnimatorX.setDuration(200);
            objAnimatorX.start();
            // Y方向移动
            objAnimatorY = ObjectAnimator.ofFloat(buttonItems.get(i),
                    "y",
                    hParent - hView - DensityUtils.dip2px(getActivity(), 100),
                    hParent - hView - DensityUtils.dip2px(getActivity(), 100) - distanceY);
            objAnimatorY.setDuration(200);
            objAnimatorY.start();
            // 按钮旋转
            objAnimatorRotate = ObjectAnimator.ofFloat(buttonItems.get(i), "rotation", 0, 360);
            objAnimatorRotate.setDuration(200);
            objAnimatorRotate.start();
            if (flag == -1) {
                objAnimatorX.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {
                    }

                    public void onAnimationEnd(Animator animation) {
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                    }
                });
            } else {
                // 将按钮设为隐藏
                for (int j = 0; j < buttonItems.size(); j++) {
                    buttonItems.get(j).setVisibility(View.GONE);
                }
            }
        }
    }

    @Override
    public void initData() {
        getCate();
    }

    @OnClick({R.id.ivSearch, R.id.ivBatch, R.id.tvCancel, R.id.tvOperate,
            R.id.linSort, R.id.linKucun, R.id.linPrice, R.id.linSale,
            R.id.tvCateSort,
            R.id.linType0, R.id.linType1, R.id.linType2,
            R.id.ivMore, R.id.tvAdd, R.id.tvSulu})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ivSearch:
                //搜索
                goToActivity(GoodsSearchActivity.class);
                break;
            case R.id.ivBatch:
                //批量
                isBatch = true;
                relTop0.setVisibility(View.GONE);
                relTop1.setVisibility(View.VISIBLE);
                mAdapter.setShow(isBatch);
                getBatchList();
                break;
            case R.id.tvCancel:
                //批量取消
                isBatch = false;
                relTop0.setVisibility(View.VISIBLE);
                relTop1.setVisibility(View.GONE);
                mAdapter.setShow(isBatch);
                break;
            case R.id.tvOperate:
                //批量操作
                if (batchList.size() < 1) {
                    showMessage(getLanguageValue("pleaseSelect") + getLanguageValue("commodity"));
                    return;
                }
                showDialogBatch();
                break;
            case R.id.linSort:
                //排序
                clearOrder();
                tvSort.setTextColor(getResources().getColor(R.color.blue));
                if (order == 1) {
                    if (orderType == 1) {
                        orderType = 2;
                        ivSort.setImageResource(R.mipmap.ic_arrow007);
                    } else {
                        orderType = 1;
                        ivSort.setImageResource(R.mipmap.ic_arrow006);
                    }
                } else {
                    order = 1;
                    orderType = 1;
                    ivSort.setImageResource(R.mipmap.ic_arrow006);
                }
                page = 1;
                getGoods();
                break;
            case R.id.linKucun:
                //库存
                clearOrder();
                tvKucun.setTextColor(getResources().getColor(R.color.blue));
                if (order == 4) {
                    if (orderType == 1) {
                        orderType = 2;
                        ivKucun.setImageResource(R.mipmap.ic_arrow007);
                    } else {
                        orderType = 1;
                        ivKucun.setImageResource(R.mipmap.ic_arrow006);
                    }
                } else {
                    order = 4;
                    orderType = 2;
                    ivKucun.setImageResource(R.mipmap.ic_arrow007);
                }
                page = 1;
                getGoods();
                break;
            case R.id.linPrice:
                //价格
                clearOrder();
                tvPrice.setTextColor(getResources().getColor(R.color.blue));
                if (order == 3) {
                    if (orderType == 1) {
                        orderType = 2;
                        ivPrice.setImageResource(R.mipmap.ic_arrow007);
                    } else {
                        orderType = 1;
                        ivPrice.setImageResource(R.mipmap.ic_arrow006);
                    }
                } else {
                    order = 3;
                    orderType = 1;
                    ivPrice.setImageResource(R.mipmap.ic_arrow006);
                }
                page = 1;
                getGoods();
                break;
            case R.id.linSale:
                //销量
                clearOrder();
                tvSale.setTextColor(getResources().getColor(R.color.blue));
                if (order == 2) {
                    if (orderType == 1) {
                        orderType = 2;
                        ivSale.setImageResource(R.mipmap.ic_arrow007);
                    } else {
                        orderType = 1;
                        ivSale.setImageResource(R.mipmap.ic_arrow006);
                    }
                } else {
                    order = 2;
                    orderType = 2;
                    ivSale.setImageResource(R.mipmap.ic_arrow007);
                }
                page = 1;
                getGoods();
                break;
            case R.id.ivMore:
                if (flag == 1) {
                    flag = -1;
                    ivMore.setImageResource(R.mipmap.ic_close003);
                } else {
                    flag = 1;
                    ivMore.setImageResource(R.mipmap.ic_add004);
                }
                buttonAnimation(DensityUtils.dip2px(getActivity(), 84));
                break;
            case R.id.tvSulu:
                //速录
                SharedPreferences.Editor editor1 = sp.edit();
                editor1.putString("pd_type", "");
                editor1.commit();
                if ("1".equals(powerAdd)) {
                    //速录
                    startActivity(new Intent(getActivity(), ScanActivity.class)
                            .putExtra("type", 2));
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
                break;

            case R.id.tvCateSort:
                //分类排序
                showDialogCate();
                break;
            case R.id.linType0:
                //全部
                if (status != 0) {
                    status = 0;
                    clearType();
                    tvType0.setTextColor(getResources().getColor(R.color.blue));
                    vType0.setVisibility(View.VISIBLE);
                    dataList = dataList1;
                    if (mAdapter != null) {
                        mAdapter.setDataList(dataList);
                    }
                    setEmpty(dataList);
                }
                break;
            case R.id.linType1:
                //已上架
                if (status != 1) {
                    status = 1;
                    clearType();
                    tvType1.setTextColor(getResources().getColor(R.color.blue));
                    vType1.setVisibility(View.VISIBLE);
                    dataList = dataList2;
                    if (mAdapter != null) {
                        mAdapter.setDataList(dataList);
                    }
                    setEmpty(dataList);
                }
                break;
            case R.id.linType2:
                //已下架
                if (status != 2) {
                    status = 2;
                    clearType();
                    tvType2.setTextColor(getResources().getColor(R.color.blue));
                    vType2.setVisibility(View.VISIBLE);
                    dataList = dataList3;
                    if (mAdapter != null) {
                        mAdapter.setDataList(dataList);
                    }
                    setEmpty(dataList);
                }
                break;
            case R.id.tvAdd:
                //添加商品
                if ("1".equals(powerAdd)) {
                    startActivityForResult(new Intent(getActivity(), GoodsEditActivity.class), Constants.KU_INOUT);
                } else {
                    showMessage(getLanguageValue("noPermission"));
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
//        if (flag == 1) {
//            flag = -1;
//            ivMore.setImageResource(R.mipmap.ic_close003);
//            buttonAnimation(DensityUtils.dip2px(getActivity(), 84));
//        }
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_CATE:
                getCate();
                break;
            case Constants.GOODS_LIST:
                page = 1;
                getGoods();
                break;
        }
    }

    @Override
    public void setText() {
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("administration"));
        tvCancel.setText(getLanguageValue("cancel"));
        tvTitleOperate.setText(getLanguageValue("choose") + getLanguageValue("commodity"));
        tvOperate.setText(getLanguageValue("operation") + "(0)");
        tvCateSort.setText(getLanguageValue("sort"));
        tvSort.setText(getLanguageValue("sort"));
        tvKucun.setText(getLanguageValue("inventory"));
        tvPrice.setText(getLanguageValue("price"));
        tvSale.setText(getLanguageValue("salesVolume"));
        tvType0.setText(getLanguageValue("whole"));
        tvType1.setText(getLanguageValue("already") + getLanguageValue("onTheShelf"));
        tvType2.setText(getLanguageValue("already") + getLanguageValue("offTheShelf"));
        tvAdd.setText(getLanguageValue("addTo"));
        tvSulu.setText(getLanguageValue("quick") + getLanguageValue("addTo"));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //分类
        rvCate.setLayoutManager(new LinearLayoutManager(getActivity()));
        cateAdapter = new CateAdapter(getActivity());
        rvCate.setAdapter(cateAdapter);
        cateAdapter.setListener(new CateAdapter.MyListener() {
            @Override
            public void onItemClick(int position) {
                //一级
                if (!cateList.get(position).isShow()) {
                    for (int i = 0; i < cateList.size(); i++) {
                        cateList.get(i).setShow(false);
                        for (int j = 0; j < cateList.get(i).getKindDetail().size(); j++) {
                            cateList.get(i).getKindDetail().get(j).setShow(false);
                        }
                    }
                    cateList.get(position).setShow(true);
                } else {
                    cateList.get(position).setShow(false);
                }
                for (int i = 0; i < cateList.size(); i++) {
                    cateList.get(i).setCheck(false);
                    for (int j = 0; j < cateList.get(i).getKindDetail().size(); j++) {
                        cateList.get(i).getKindDetail().get(j).setCheck(false);
                        for (int k = 0; k < cateList.get(i).getKindDetail().get(j).getKindDetail().size(); k++) {
                            cateList.get(i).getKindDetail().get(j).getKindDetail().get(k).setCheck(false);
                        }
                    }
                }
                cateList.get(position).setCheck(true);
                cateAdapter.setDataList(cateList);
                groupUnique = cateList.get(position).getGroupUnique();
                kindUnique = "-1";//全部
                kindUnique2 = "";
                page = 1;
                getGoods();
            }

            @Override
            public void onChildItemClick(int position, int position1) {
                //二级
                if (!cateList.get(position).getKindDetail().get(position1).isShow()) {
                    for (int i = 0; i < cateList.get(position).getKindDetail().size(); i++) {
                        cateList.get(position).getKindDetail().get(i).setShow(false);
                    }
                    cateList.get(position).getKindDetail().get(position1).setShow(true);
                } else {
                    cateList.get(position).getKindDetail().get(position1).setShow(false);
                }
                for (int i = 0; i < cateList.get(position).getKindDetail().size(); i++) {
                    cateList.get(position).getKindDetail().get(i).setCheck(false);
                    for (int j = 0; j < cateList.get(position).getKindDetail().get(i).getKindDetail().size(); j++) {
                        cateList.get(position).getKindDetail().get(i).getKindDetail().get(j).setCheck(false);
                    }
                }
                cateList.get(position).getKindDetail().get(position1).setCheck(true);
                cateAdapter.setDataList(cateList);
                kindUnique = cateList.get(position).getKindDetail().get(position1).getKindUnique();
                kindUnique2 = "";
                page = 1;
                getGoods();
            }

            @Override
            public void onChildChildItemClick(int position, int position1, int position2) {
                //三级
                if (!cateList.get(position).getKindDetail().get(position1).getKindDetail().get(position2).isCheck()) {
                    for (int i = 0; i < cateList.get(position).getKindDetail().get(position1).getKindDetail().size(); i++) {
                        cateList.get(position).getKindDetail().get(position1).getKindDetail().get(i).setCheck(false);
                    }
                    cateList.get(position).getKindDetail().get(position1).getKindDetail().get(position2).setCheck(true);
                    cateAdapter.setDataList(cateList);
                    kindUnique2 = cateList.get(position).getKindDetail().get(position1).getKindDetail().get(position2).getKindUnique();
                    page = 1;
                    getGoods();
                }
            }
        });

        //商品
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mAdapter = new GoodsAdapter(getActivity());
        recyclerView.setAdapter(mAdapter);

        smartRefreshLayout.setEnableAutoLoadMore(true);////是否启用列表惯性滑动到底部时自动加载更多
        smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            page = 1;
            getGoods();
        });
        smartRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            page++;
            getGoods();
        });
        mAdapter.setListener(new GoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                startActivity(new Intent(getActivity(), GoodsEditActivity.class)
                        .putExtra("goodsBarcode", mAdapter.getDataList().get(position).getGoodsBarcode())
                        .putExtra("type", 1)
                );
            }

            @Override
            public void onMoreClick(View view, int position) {
                //更多（商品操作）
                goodsBarcode = dataList.get(position).getGoodsBarcode();
                goodsName = dataList.get(position).getGoodsName();
                goodsImg = dataList.get(position).getGoodsPicturePath();
                goodsPrice = String.valueOf(dataList.get(position).getGoodsSalePrice());
                goodsUnit = dataList.get(position).getGoodsUnit();
                printerStatus = dataList.get(position).getPcShelfState();
                appletStatus = dataList.get(position).getShelfState();
                showDialogMore();
            }

            @SuppressLint("SetTextI18n")
            @Override
            public void onCheckClick(View view, int position) {
                //选择（批量操作）
                printerStatus = 0;
                appletStatus = 0;
                dataList.get(position).setCheck(!dataList.get(position).isCheck());
                mAdapter.notifyItemChanged(position, dataList);
                getBatchList();
            }
        });
    }

    /**
     * 获取批量操作列表
     */
    @SuppressLint("SetTextI18n")
    private void getBatchList() {
        if (!isBatch) {
            return;
        }
        batchList.clear();
        for (int i = 0; i < dataList.size(); i++) {
            if (dataList.get(i).isCheck()) {
                batchList.add(dataList.get(i));
            }
        }
        goodsBarcode = "";
        for (int i = 0; i < batchList.size(); i++) {
            if (TextUtils.isEmpty(goodsBarcode)) {
                goodsBarcode = batchList.get(i).getGoodsBarcode();
            } else {
                goodsBarcode = goodsBarcode + "," + batchList.get(i).getGoodsBarcode();
            }
        }
        tvOperate.setText(getLanguageValue("operation") + "(" + batchList.size() + ")");
    }

    /******************************dialog*********************************/

    //单个操作
    private Dialog dialogMore;

    //批量操作
    private Dialog dialogBatch;

    //上下架平台
    private Dialog dialogPlatform;
    private ImageView ivPrinterUp, ivPrinterDown, ivAppletUp, ivAppletDown;

    /**
     * dialog（分类排序）
     */
    private void showDialogCate() {
        Dialog dialog = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_cate_sort, null);
        dialog.setContentView(view);

        View vTop = view.findViewById(R.id.vDialog);
        TextView tvCancel = view.findViewById(R.id.tvDialogCancel);
        TextView tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        RecyclerView rvCate = view.findViewById(R.id.rvDialogCate);
        TextView tvTitle = view.findViewById(R.id.tvDialogTitle);
        TextView tvMyCate = view.findViewById(R.id.tvDialogMyCate);

        tvCancel.setText(getLanguageValue("cancel"));
        tvTitle.setText(getLanguageValue("classification") + getLanguageValue("sort"));
        tvConfirm.setText(getLanguageValue("finish"));
        tvMyCate.setText(getLanguageValue("mine") + getLanguageValue("classification"));

        Window window = dialog.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();

        vTop.setOnClickListener(v -> dialog.dismiss());
        tvCancel.setOnClickListener(v -> dialog.dismiss());
        //完成
        tvConfirm.setOnClickListener(v -> {
            JSONArray array = new JSONArray();
            for (int i = 0; i < cateSortList.size(); i++) {
//                if (cateSortList.get(i).getGroupName().equals("更多分类")) {
//                    break;
//                }
                if (cateSortList.get(i).getGroupName().equals(getLanguageValue("more") + getLanguageValue("classification"))) {
                    break;
                }
                JSONObject object = new JSONObject();
                try {
                    object.put("goods_kind_name", cateSortList.get(i).getGroupName());
                    object.put("goods_kind_unique", cateSortList.get(i).getGroupUnique());
                    array.put(object);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            Log.e(tag, "array = " + array);
            postCateSort(array.toString());
            dialog.dismiss();
        });
        //我的分类
        rvCate.setLayoutManager(new LinearLayoutManager(getActivity()));
        CateSortAdapter adapter = new CateSortAdapter(getActivity());
        rvCate.setAdapter(adapter);
        adapter.setDataList(cateSortList);
        new ItemTouchHelper(new MyItemTouchHelper(getActivity(), cateSortList, adapter))
                .attachToRecyclerView(rvCate);
    }

    /**
     * dialog（商品操作）
     */
    private void showDialogMore() {
        dialogMore = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_more, null);
        dialogMore.setContentView(view);

        TextView tvTitle, tvStatus, tvPrint, tvChu, tvRu, tvCate, tvPan, tvChange, tvDel;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvStatus = view.findViewById(R.id.tvDialogStatus);
        tvPrint = view.findViewById(R.id.tvDialogPrint);
        tvChu = view.findViewById(R.id.tvDialogChu);
        tvRu = view.findViewById(R.id.tvDialogRu);
        tvCate = view.findViewById(R.id.tvDialogCate);
        tvPan = view.findViewById(R.id.tvDialogPan);
        tvChange = view.findViewById(R.id.tvDialogChange);
        tvDel = view.findViewById(R.id.tvDialogDel);
        view.findViewById(R.id.linDialogCate).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogPrint).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogChu).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogRu).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogStatus).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogPan).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogChange).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogDel).setOnClickListener(new MyClickListener());
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvStatus.setText(getLanguageValue("commodity") + getLanguageValue("onOffShelf"));
        tvPrint.setText(getLanguageValue("printTag"));
        tvChu.setText(getLanguageValue("commodity") + getLanguageValue("outOfTheWarehouse"));
        tvRu.setText(getLanguageValue("commodity") + getLanguageValue("warehousing"));
        tvCate.setText(getLanguageValue("transferClass"));
        tvPan.setText(getLanguageValue("diskLibrary") + getLanguageValue("record"));
        tvChange.setText(getLanguageValue("change") + getLanguageValue("record"));
        tvDel.setText(getLanguageValue("delete") + getLanguageValue("commodity"));

        Window window = dialogMore.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialogMore.show();
    }

    /**
     * dialog（批量操作）
     */
    private void showDialogBatch() {
        dialogBatch = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_batch, null);
        dialogBatch.setContentView(view);
        TextView tvTitle, tvStatus, tvCate, tvPrint, tvDel;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvStatus = view.findViewById(R.id.tvDialogBatchStatus);
        tvCate = view.findViewById(R.id.tvDialogBatchCate);
        tvPrint = view.findViewById(R.id.tvDialogBatchPrint);
        tvDel = view.findViewById(R.id.tvDialogBatchDel);
        view.findViewById(R.id.linDialogBatchCate).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogBatchPrint).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogBatchDel).setOnClickListener(new MyClickListener());
        view.findViewById(R.id.linDialogBatchStatus).setOnClickListener(new MyClickListener());
        tvTitle.setText(getLanguageValue("batch") + getLanguageValue("operation"));
        tvStatus.setText(getLanguageValue("batch") + getLanguageValue("onOffShelf"));
        tvCate.setText(getLanguageValue("batch") + getLanguageValue("transferClass"));
        tvPrint.setText(getLanguageValue("batch") + getLanguageValue("printTag"));
        tvDel.setText(getLanguageValue("batch") + getLanguageValue("delete") + getLanguageValue("commodity"));

        Window window = dialogBatch.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialogBatch.show();
    }

    /**
     * dialog（选择上下架平台）
     */
    private void showDialogPlatform() {
        dialogPlatform = new Dialog(getActivity(), R.style.dialog_bottom);
        View view = View.inflate(getActivity(), R.layout.dialog_platform, null);
        dialogPlatform.setContentView(view);

        TextView tvTitle, tvPrinter, tvPrinterUp, tvPrinterDown, tvApplet, tvAppletUp, tvAppletDown, tvConfirm;
        tvTitle = view.findViewById(R.id.tvDialogTitle);
        tvPrinter = view.findViewById(R.id.tvDialogPrinter);
        tvPrinterUp = view.findViewById(R.id.tvDialogPrinterUp);
        tvPrinterDown = view.findViewById(R.id.tvDialogPrinterDown);
        tvApplet = view.findViewById(R.id.tvDialogApplet);
        tvAppletUp = view.findViewById(R.id.tvDialogAppletUp);
        tvAppletDown = view.findViewById(R.id.tvDialogAppletDown);
        tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        ivPrinterUp = view.findViewById(R.id.ivDialogPrinterUp);
        ivPrinterDown = view.findViewById(R.id.ivDialogPrinterDown);
        ivAppletUp = view.findViewById(R.id.ivDialogAppletUp);
        ivAppletDown = view.findViewById(R.id.ivDialogAppletDown);
        ivPrinterUp.setOnClickListener(new MyClickListener());
        ivPrinterDown.setOnClickListener(new MyClickListener());
        ivAppletUp.setOnClickListener(new MyClickListener());
        ivAppletDown.setOnClickListener(new MyClickListener());
        tvTitle.setText(getLanguageValue("commodity") + getLanguageValue("operation"));
        tvPrinter.setText(getLanguageValue("cashRegister"));
        tvPrinterUp.setText(getLanguageValue("onTheShelf"));
        tvPrinterDown.setText(getLanguageValue("offTheShelf"));
        tvApplet.setText(getLanguageValue("miniProgram"));
        tvAppletUp.setText(getLanguageValue("onTheShelf"));
        tvAppletDown.setText(getLanguageValue("offTheShelf"));
        tvConfirm.setText(getLanguageValue("confirm"));

        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
        switch (printerStatus) {
            case 1:
                ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }
        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
        switch (appletStatus) {
            case 1:
                ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                break;
            case 2:
                ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                break;
        }

        Window window = dialogPlatform.getWindow();
        window.setGravity(Gravity.BOTTOM);
        window.setWindowAnimations(R.style.dialog_anim);
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialogPlatform.show();

        view.findViewById(R.id.tvDialogConfirm).setOnClickListener(v -> {
            if (printerStatus == 0) {
                showMessage(getLanguageValue("cashRegisterStatus"));
                return;
            }
            if (appletStatus == 0) {
                showMessage(getLanguageValue("appletStatus"));
                return;
            }
            postGoods();
            dialogPlatform.dismiss();
        });
    }

    public class MyClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            Log.e(tag, "v = " + v.getId());
            switch (v.getId()) {
                //商品操作
                case R.id.linDialogCate:
                    //转移分类
                    startActivityForResult(new Intent(getActivity(), GoodsCateActivity.class)
                                    .putExtra("type", 1)
                            , Constants.CATE);
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogPrint:
                    //打印价签
                    printType = 0;
                    setPrint();
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogChu:
                    //商品出库
                    if (TextUtils.isEmpty(goodsBarcode)) {
                        showMessage(getLanguageValue("noBarcode"));
                        return;
                    }
                    startActivity(new Intent(getActivity(), ChuRuSelectActivity.class)
                            .putExtra("result", goodsBarcode)
                            .putExtra("isRu", 2)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogRu:
                    //商品入库
                    if (TextUtils.isEmpty(goodsBarcode)) {
                        showMessage(getLanguageValue("noBarcode"));
                        return;
                    }
                    startActivity(new Intent(getActivity(), ChuRuSelectActivity.class)
                            .putExtra("result", goodsBarcode)
                            .putExtra("isRu", 1)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogStatus:
                    //上下架
                    showDialogPlatform();
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogPan:
                    //盘库记录
                    startActivity(new Intent(getActivity(), PanGoodsRecordActivity.class)
                            .putExtra("goodsBarcode", goodsBarcode)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogChange:
                    //变更记录
                    startActivity(new Intent(getActivity(), GoodsChangeRecordActivity.class)
                            .putExtra("goodsBarcode", goodsBarcode)
                            .putExtra("goodsName", goodsName)
                            .putExtra("img", goodsImg)
                    );
                    if (dialogMore != null) {
                        dialogMore.dismiss();
                    }
                    break;
                case R.id.linDialogDel:
                    //删除商品
                    IAlertDialog.showDialog(getActivity(),
                            getLanguageValue("deleteProduct") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postGoodsDel();
                                if (dialogMore != null) {
                                    dialogMore.dismiss();
                                }
                            });
                    break;
                //商品批量操作
                case R.id.linDialogBatchCate:
                    //批量转移分类
                    startActivityForResult(new Intent(getActivity(), GoodsCateActivity.class)
                                    .putExtra("type", 1)
                            , Constants.CATE);
                    if (dialogBatch != null) {
                        dialogBatch.dismiss();
                    }
                    break;
                case R.id.linDialogBatchPrint:
                    //批量打印标签
                    printType = 1;
                    setPrintBatch();
                    if (dialogBatch != null) {
                        dialogBatch.dismiss();
                    }
                    break;
                case R.id.linDialogBatchDel:
                    //删除选中商品
                    IAlertDialog.showDialog(getActivity(),
                            getLanguageValue("deleteProduct") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postGoodsDel();
                                if (dialogBatch != null) {
                                    dialogBatch.dismiss();
                                }
                            });
                    break;
                case R.id.linDialogBatchStatus:
                    //批量上下架
                    showDialogPlatform();
                    if (dialogBatch != null) {
                        dialogBatch.dismiss();
                    }
                    break;
                //选择上下架平台
                case R.id.ivDialogPrinterUp:
                    //收银机上架
                    if (printerStatus == 1) {
                        printerStatus = 0;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        printerStatus = 1;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chosen001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    }
                    break;
                case R.id.ivDialogPrinterDown:
                    //收银机下架
                    if (printerStatus == 2) {
                        printerStatus = 0;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        printerStatus = 2;
                        ivPrinterUp.setImageResource(R.mipmap.ic_chose001);
                        ivPrinterDown.setImageResource(R.mipmap.ic_chosen001);
                    }
                    break;
                case R.id.ivDialogAppletUp:
                    //小程序上架
                    if (appletStatus == 1) {
                        appletStatus = 0;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        appletStatus = 1;
                        ivAppletUp.setImageResource(R.mipmap.ic_chosen001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    }
                    break;
                case R.id.ivDialogAppletDown:
                    //小程序下架
                    if (appletStatus == 2) {
                        appletStatus = 0;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chose001);
                    } else {
                        appletStatus = 2;
                        ivAppletUp.setImageResource(R.mipmap.ic_chose001);
                        ivAppletDown.setImageResource(R.mipmap.ic_chosen001);
                    }
                    break;
            }
        }
    }

    /***********************打印价签start**********************/
    private int printType,//0.单个打印 1.批量打印
            printerType,//打印机型号 0.佳博 GP-M322 1.精臣 NIIMBOT B3S
            pageCount,//总页数
            quantity, //页打印份数
            generatedPrintDataPageCount = 0;//全局变量，用于跟踪已生成的打印数据页数
    private boolean isError,//是否打印错误
            isCancel;//是否取消打印
    private ArrayList<String> jsonList = new ArrayList<>(),//图像数据
            infoList = new ArrayList<>();//图像处理书记
    private String printName, //打印商品名称
            printBarcode, //打印商品条码
            printPrice;//打印商品售价

    /**
     * 打印价签
     */
    private void setPrint() {
        if (TextUtils.isEmpty(goodsBarcode)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("barcode") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(goodsName)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("name") + getLanguageValue("notEmpty"));
            return;
        }
        if (TextUtils.isEmpty(goodsPrice)) {
            showMessage(getLanguageValue("commodity") + getLanguageValue("sellingPrice") + getLanguageValue("notEmpty"));
            return;
        }
        if (printerType == 0) {
            if (Printer.getPortManager() != null && Printer.getConnectState()) {
                ThreadPoolManager.getInstance().addTask(() -> {
                    try {
                        if (Printer.getPortManager() == null) {
                            getActivity().runOnUiThread(() -> {
                                showMessage(getLanguageValue("connectPrinter"));
                            });
                            return;
                        }
                        boolean result = Printer.getPortManager().writeDataImmediately(getLabel(goodsBarcode, goodsName, goodsPrice));
                        getActivity().runOnUiThread(() -> {
                            if (result) {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                            } else {
                                tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                            }
                        });
                        LogUtils.e("send result", result);
                    } catch (Exception e) {
                        getActivity().runOnUiThread(() -> {
                            tipsDialog(getLanguageValue("print") + getLanguageValue("failed") + e.getMessage());
                        });
                    } finally {
                        if (Printer.getPortManager() == null) {
                            Printer.close();
                        }
                    }
                });
            } else {
                startActivityForResult(new Intent(getActivity(), PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        } else {
            if (PrintUtil.isConnection() == 0) {
                printLabel(1, 1);
            } else {
                startActivityForResult(new Intent(getActivity(), PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        }
    }

    /**
     * 打印价签(批量)
     */
    private void setPrintBatch() {
        if (printerType == 0) {
            if (Printer.getPortManager() != null && Printer.getConnectState()) {
                ThreadPoolManager.getInstance().addTask(() -> {
                    try {
                        if (Printer.getPortManager() == null) {
                            getActivity().runOnUiThread(() -> {
                                showMessage(getLanguageValue("connectPrinter"));
                            });
                            return;
                        }
                        for (int i = 0; i < batchList.size(); i++) {
                            goodsBarcode = batchList.get(i).getGoodsBarcode();
                            goodsName = batchList.get(i).getGoodsName();
                            goodsPrice = String.valueOf(batchList.get(i).getGoodsSalePrice());
                            if (!TextUtils.isEmpty(goodsBarcode) && !TextUtils.isEmpty(goodsName) && !TextUtils.isEmpty(goodsPrice)) {
                                boolean result = Printer.getPortManager().writeDataImmediately(getLabel(goodsBarcode, goodsName, goodsPrice));
                                Log.e(tag, "send result = " + result);
                                if (i == (batchList.size() - 1)) {
                                    getActivity().runOnUiThread(() -> {
                                        if (result) {
                                            tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                                        } else {
                                            tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                                        }
                                    });
                                }
                            }
                        }
                    } catch (Exception e) {
                        getActivity().runOnUiThread(() -> {
                            tipsDialog(getLanguageValue("print") + getLanguageValue("failed") + e.getMessage());
                        });
                    } finally {
                        if (Printer.getPortManager() == null) {
                            Printer.close();
                        }
                    }
                });
            } else {
                startActivityForResult(new Intent(getActivity(), PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        } else {
            if (PrintUtil.isConnection() == 0) {
                printLabel(batchList.size(), 1);
            } else {
                startActivityForResult(new Intent(getActivity(), PrinterSettingActivity.class)
                        , Constants.BLUETOOTH);
            }
        }
    }

    /**
     * 标签打印测试页(佳博)
     *
     * @param goodsBarcode
     * @param name
     * @param price
     * @return
     */
    public Vector<Byte> getLabel(String goodsBarcode, String name, String price) {
        //标签模版 1.无店铺名称 2.有店铺名称 3.优乐购 4.小版
        int printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);
        String shopName = sp.getString("shopName", "");
        LabelCommand tsc = new LabelCommand();
        // 设置标签尺寸宽高，按照实际尺寸设置 单位mm
        tsc.addUserCommand("\r\n");
        if (printType == 4) {
            tsc.addSize(50, 30);
        } else {
            tsc.addSize(70, 38);
        }
        // 设置标签间隙，按照实际尺寸设置，如果为无间隙纸则设置为0 单位mm
        tsc.addGap(2);
        //设置纸张类型为黑标，发送BLINE 指令不能同时发送GAP指令
//        tsc.addBline(2);
        // 设置打印方向
//        tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
        // 设置原点坐标
        tsc.addReference(0, 0);
        //设置浓度
        tsc.addDensity(LabelCommand.DENSITY.DNESITY4);
        // 撕纸模式开启
        tsc.addTear(LabelCommand.RESPONSE_MODE.ON);
        // 清除打印缓冲区
        tsc.addCls();
        //标签方向
        if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
            //正向
            tsc.addDirection(LabelCommand.DIRECTION.FORWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            name);
                    tsc.addText(415, 194,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(115, 154,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            goodsBarcode);
                    break;
                case 2:
                    tsc.addText(230, 16,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(88, 76,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(400, 216,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(60, 185,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            goodsBarcode);
                    break;
                case 4:
                    tsc.addText(155, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(44, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(255, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(35, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            25,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        } else {
            //反向
            tsc.addDirection(LabelCommand.DIRECTION.BACKWARD, LabelCommand.MIRROR.NORMAL);
            //标签样式
            switch (printType) {
                case 1:
                    tsc.addText(124, 25,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            name);
                    tsc.addText(415, 194,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(115, 154,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            goodsBarcode);
                    break;
                case 2:
                    tsc.addText(270, 16,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(118, 73,
                            LabelCommand.FONTTYPE.SIMPLIFIED__32_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(420, 216,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(100, 185,
                            LabelCommand.BARCODETYPE.CODE128,
                            40,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            goodsBarcode);
                    break;
                case 4:
                    tsc.addText(195, 15,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            shopName);
                    tsc.addText(84, 62,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_1,
                            LabelCommand.FONTMUL.MUL_1,
                            name);
                    tsc.addText(295, 185,
                            LabelCommand.FONTTYPE.SIMPLIFIED_24_CHINESE,
                            LabelCommand.ROTATION.ROTATION_0,
                            LabelCommand.FONTMUL.MUL_2,
                            LabelCommand.FONTMUL.MUL_2,
                            price);
                    tsc.add1DBarcode(75, 152,
                            LabelCommand.BARCODETYPE.CODE128,
                            25,
                            LabelCommand.READABEL.EANBEL,
                            LabelCommand.ROTATION.ROTATION_0,
                            1,
                            2,
                            goodsBarcode);
                    break;
            }
        }
        // 打印标签
        tsc.addPrint(1, 1);
        // 打印标签后 蜂鸣器响
        tsc.addSound(2, 100);
        //开启钱箱
//        tsc.addCashdrwer(LabelCommand.FOOT.F5, 255, 255);
        Vector<Byte> datas = tsc.getCommand();
        // 发送数据
        return datas;
    }

    /**
     * 打印标签（精臣）
     *
     * @param pages  页数（页表示数据不一样）
     * @param copies 份数
     */
    private void printLabel(int pages, int copies) {
        // 检查是否连接了打印机
        if (PrintUtil.isConnection() != 0) {
            showMessage(getLanguageValue("printerNotConnect"));
            return;
        }
        isError = false;
        isCancel = false;
        jsonList.clear();
        infoList.clear();
        // 在每次打印任务前初始化生成的打印数据页数
        generatedPrintDataPageCount = 0;
        // 设置打印的总页数和份数
        pageCount = pages;
        quantity = copies;
        int totalQuantity = pageCount * quantity;
        PrintUtil.getInstance().setTotalPrintQuantity(totalQuantity);
        /*
         * 参数1：打印浓度 ，参数2:纸张类型 参数3:打印模式
         * 打印浓度 B50/B50W/T6/T7/T8 建议设置6或8，Z401/B32建议设置8，B3S/B21/B203/B1建议设置3
         */
        PrintUtil.getInstance().startPrintJob(4, 1, 1, new PrintCallback() {
            @Override
            public void onProgress(int pageIndex, int quantityIndex, HashMap<String, Object> hashMap) {
                // 更新打印进度
                String progressMessage = "打印进度:已打印到第" + pageIndex + "页,第" + quantityIndex + "份";
                Log.d(tag, "测试:" + progressMessage);
                // 处理打印完成情况
                if (pageIndex == pageCount && quantityIndex == quantity) {
                    Log.d(tag, "测试:onProgress: 结束打印");
                    //endJob，使用方法含义更明确的endPrintJob
                    requireActivity().runOnUiThread(() -> {
                        if (PrintUtil.getInstance().endPrintJob()) {
                            Log.d(tag, "结束打印成功");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("succeed"));
                        } else {
                            Log.d(tag, "结束打印失败");
                            tipsDialog(getLanguageValue("send") + getLanguageValue("failed"));
                        }
                    });

                }
            }


            @Override
            public void onError(int i) {

            }


            @Override
            public void onError(int errorCode, int printState) {
                Log.d(tag, "测试：报错");
                isError = true;
                requireActivity().runOnUiThread(() -> {
                    showMessage(getLanguageValue("printError") + " errorCode = " + errorCode + " printState = " + printState);
                });
            }

            @Override
            public void onCancelJob(boolean isSuccess) {
                Log.d(tag, "onCancelJob: " + isSuccess);
                isCancel = true;
                requireActivity().runOnUiThread(() -> {
                    showMessage(getLanguageValue("cancelPrint"));
                });
            }

            /**
             * SDK缓存空闲回调，可以在此处传入打印数据
             *
             * @param pageIndex 当前回调函数处理下一页的打印索引
             * @param bufferSize 缓存空间的大小
             */
            @Override
            public void onBufferFree(int pageIndex, int bufferSize) {
                // 如果出现错误、已取消打印，或 pageIndex 超过总页数，则返回
                if (isError || isCancel || pageIndex > pageCount) {
                    return;
                }
                if (generatedPrintDataPageCount < pageCount) {
                    // 计算本次要生成的数据长度，以免超过总页数
                    int commitDataLength = Math.min((pageCount - generatedPrintDataPageCount), bufferSize);
                    // 生成数据
                    setPrinterData(generatedPrintDataPageCount, generatedPrintDataPageCount + commitDataLength);
                    // 提交打印数据
                    PrintUtil.getInstance().commitData(jsonList.subList(generatedPrintDataPageCount, generatedPrintDataPageCount + commitDataLength),
                            infoList.subList(generatedPrintDataPageCount, generatedPrintDataPageCount + commitDataLength));
                    // 更新已生成的打印数据页数
                    generatedPrintDataPageCount += commitDataLength;
                }
            }
        });
    }

    /**
     * 生成打印数据(精臣)
     *
     * @param index      起始索引，生成数据的起始页。
     * @param cycleIndex 结束索引，生成数据的结束页。
     */
    private void setPrinterData(int index, int cycleIndex) {
        while (index < cycleIndex) {
            Log.e(tag, "index = " + index + " cycleIndex = " + cycleIndex + " printType = " + printType + " size = " + batchList.size());
            if (printType == 0) {
                printName = goodsName;
                printBarcode = goodsBarcode;
                if (TextUtils.isEmpty(goodsUnit)) {
                    printPrice = goodsPrice;
                } else {
                    printPrice = goodsPrice + "/" + goodsUnit;
                }
            } else {
                if (batchList.size() > index) {
                    printName = batchList.get(index).getGoodsName();
                    printBarcode = batchList.get(index).getGoodsBarcode();
                    if (TextUtils.isEmpty(batchList.get(index).getGoodsUnit())) {
                        printPrice = DFUtils.getNum2(batchList.get(index).getGoodsSalePrice());
                    } else {
                        printPrice = DFUtils.getNum2(batchList.get(index).getGoodsSalePrice()) + "/" + batchList.get(index).getGoodsUnit();
                    }
                } else {
                    printName = "";
                    printBarcode = "";
                    printPrice = "";
                }
            }
            if (TextUtils.isEmpty(printName)) {
                showMessage(getLanguageValue("commodity") + getLanguageValue("barcode") + getLanguageValue("notEmpty"));
                return;
            }
            if (TextUtils.isEmpty(printBarcode)) {
                showMessage(getLanguageValue("commodity") + getLanguageValue("name") + getLanguageValue("notEmpty"));
                return;
            }
            if (TextUtils.isEmpty(printPrice)) {
                showMessage(getLanguageValue("commodity") + getLanguageValue("sellingPrice") + getLanguageValue("notEmpty"));
                return;
            }
            float width = 70, height = 38;
            /**
             * 设置画布⼤⼩
             *
             * @param width 画布宽度(mm)
             * @param height 画布⾼度(mm)
             * @param orientation 画布旋转⻆度 注：不管用
             * @param fontDir 字体路径暂不可⽤，默认""即可
             */
            PrintUtil.getInstance().drawEmptyLabel(width, height, 0, "");

            //标签方向：0.正向 1.反向
            if (sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION) == 0) {
                //标签样式 1. 2. 3.
                if (sharedPreferencesUtil.getInt(2, Constants.PRINT_TYPE) == 3) {
                    /**
                     * 绘制⽂本
                     * @param x 位置x
                     * @param y 位置y
                     * @param width 宽
                     * @param height ⾼
                     * @param value 内容
                     * @param fontFamily 字体名称,未传输字体为空字符串时使⽤默认字体,暂时⽤默认字体
                     * @param fontSize 字体⼤⼩
                     * @param rotate 旋转
                     * @param textAlignHorizontal ⽔平对⻬⽅式：0:左对⻬ 1:居中对⻬ 2:右对⻬
                     * @param textAlignVertical 垂直 对⻬⽅式：0:顶对⻬ 1:垂直居中 2:底对⻬
                     * @param lineModel 1:宽⾼固定，内容⼤⼩⾃适应（字号/字符间距/⾏间距 按⽐例缩放）
                    2:宽度固定，⾼度⾃适应
                    3:宽⾼固定，超出内容后⾯加...
                    4:宽⾼固定,超出内容直裁切
                    6:宽⾼固定，内容超过预设宽⾼时⾃动缩⼩（字号/字符间距/⾏间距 按⽐例缩放）
                     * @param letterSpace 字⺟之间的标准间隔，单位mm
                     * @param lineSpace ⾏间距（倍距），单位mm
                     * @param mFontStyles 字体样式[加粗，斜体，下划线，删除下划线（预留）]
                     */
                    PrintUtil.getInstance().drawLabelText(40f, 13f, 30f, 5f, printName, "", 6f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                    /**
                     * 绘制⼀维码
                     *
                     * @param x ⽔平坐标
                     * @param y 垂直坐标
                     * @param width 宽度,单位mm
                     * @param height ⾼度,单位mm
                     * @param codeType ⼀维码类型20:CODE128 ,21:UPC-A,22:UPC-E,23:EAN8,24:EAN13,
                     * 25:CODE93,26:CODE39,27:CODEBAR, 28:ITF25
                     * @param value ⽂本内容
                     * @param fontSize ⽂本字号
                     * @param rotate 旋转⻆度，仅⽀持0,90,180,270
                     * @param textHeight ⽂本⾼度
                     * @param textPosition ⽂本位置，int,⼀维码⽂字识别码显示位置,0:下⽅显示,1:上⽅显
                    示,2:不显示
                     */
                    PrintUtil.getInstance().drawLabelBarCode(25f, 22f, 40f, 7f, 20, printBarcode, 3f, 0, 3f, 0);
                    PrintUtil.getInstance().drawLabelText(40f, 27f, 30f, 7f, "RM" + printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                } else {
                    PrintUtil.getInstance().drawLabelText(31f, 0f, 39f, 5f, getShop_name(), "", 6f,
                            0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                    PrintUtil.getInstance().drawLabelText(12f, 7f, 55f, 5f, printName, "", 6f,
                            0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                    PrintUtil.getInstance().drawLabelBarCode(10f, 23f, 28f, 8f, 20, printBarcode, 3f, 0, 3f, 0);
                    PrintUtil.getInstance().drawLabelText(48f, 25f, 23f, 7f, printPrice, "", 7f, 0, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
                }
            } else {
                PrintUtil.getInstance().drawLabelText(0f, 30f, 40f, 5f, getShop_name(), "", 6f,
                        180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                PrintUtil.getInstance().drawLabelText(0f, 22f, 60f, 5f, printName, "", 6f,
                        180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});

                PrintUtil.getInstance().drawLabelBarCode(34f, 5f, 28f, 8f, 20, printBarcode, 3f,
                        180, 3f, 0);
                PrintUtil.getInstance().drawLabelText(0f, 3f, 23f, 7f, printPrice, "", 7f,
                        180, 0, 0, 6, 0, 1, new boolean[]{true, false, false, false});
            }

            //生成打印数据
            byte[] jsonByte = PrintUtil.getInstance().generateLabelJson();
            jsonList.add(new String(jsonByte));
            //除B32/Z401/T8的printMultiple为11.81，其他的为8
            String jsonInfo = "{  " + "\"printerImageProcessingInfo\": " + "{    " + "\"orientation\":" + 0 + "," + "   \"margin\": [      0,      0,      0,      0    ], " + "   \"printQuantity\": " + 1 + ",  " + "  \"horizontalOffset\": 0,  " + "  \"verticalOffset\": 0,  " + "  \"width\":" + width + "," + "   \"height\":" + height + "," + "\"printMultiple\":" + 8f + "," + "  \"epc\": \"\"  }}";
            infoList.add(jsonInfo);
            index++;
        }
    }

    /**
     * 提示弹框
     *
     * @param message
     */
    private void tipsDialog(String message) {
        AlertDialog alertDialog = new AlertDialog.Builder(getActivity())
                .setTitle(getLanguageValue("hint"))
                .setMessage(message)
                .setIcon(R.mipmap.mylogo)
                .setPositiveButton(getLanguageValue("confirm"), (dialogInterface, i) -> {
                    //添加"Yes"按钮
                }).create();
        alertDialog.show();
    }


    /***********************打印价签end**********************/

    /**
     * 清除条件筛选
     */
    private void clearOrder() {
        tvSort.setTextColor(getResources().getColor(R.color.color_666));
        ivSort.setImageResource(R.mipmap.ic_arrow008);
        tvKucun.setTextColor(getResources().getColor(R.color.color_666));
        ivKucun.setImageResource(R.mipmap.ic_arrow008);
        tvPrice.setTextColor(getResources().getColor(R.color.color_666));
        ivPrice.setImageResource(R.mipmap.ic_arrow008);
        tvSale.setTextColor(getResources().getColor(R.color.color_666));
        ivPrice.setImageResource(R.mipmap.ic_arrow008);
    }

    /**
     * 清除状态刷选
     */
    private void clearType() {
        tvType0.setTextColor(getResources().getColor(R.color.black));
        vType0.setVisibility(View.INVISIBLE);
        tvType1.setTextColor(getResources().getColor(R.color.black));
        vType1.setVisibility(View.INVISIBLE);
        tvType2.setTextColor(getResources().getColor(R.color.black));
        vType2.setVisibility(View.INVISIBLE);
    }

    /**
     * 获取分类
     */
    private void getCate() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getdapaixu(),
                params,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        cateList.clear();
                        cateList.addAll(list);
                        cateSortList.clear();
                        for (int i = 0; i < cateList.size(); i++) {
                            if (cateList.get(i).getSort() > -1) {
                                cateSortList.add(cateList.get(i));
                            }
                        }
//                        if (cateList.size() > 0) {
//                            cateList.get(0).setCheck(true);
//                            groupUnique = cateList.get(0).getGroupUnique();
//                            if (cateList.get(0).getKindDetail().size() > 0) {
//                                cateList.get(0).getKindDetail().get(0).setCheck(true);
//                                kindUnique = cateList.get(0).getKindDetail().get(0).getKindUnique();
//                            }
//                        }
                        cateAdapter.setDataList(cateList);
                        page = 1;
                        getGoods();
                        getCateMore();
                    }
                });
    }

    /**
     * 获取分类（更多分类）
     */
    private void getCateMore() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getmoreclass(),
                params,
                CateMoreData.class,
                new RequestListListener<CateMoreData>() {
                    @Override
                    public void onResult(List<CateMoreData> list) {
                        if (list == null) {
                            return;
                        }
                        cateSortList.add(new CateData("更多分类", "", -2));
                        for (int i = 0; i < list.size(); i++) {
                            cateSortList.add(new CateData(list.get(i).getGoods_kind_name(), list.get(i).getGoods_kind_unique(), -1));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        Log.e(tag, "更多分类 error = " + msg);
                    }
                });
    }

    /**
     * 商品列表
     */
    private void getGoods() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("groupUnique", groupUnique);
        params.put("kindUnique", kindUnique);
        if (!TextUtils.isEmpty(kindUnique2)) {
            params.put("threeUnique", kindUnique2);
        }
        params.put("pageIndex", page);
        params.put("pageSize", 20);
        if (order != 0) {
            params.put("order", order);
        }
        if (orderType != 0) {
            params.put("orderType", orderType);
        }
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getSelectGoods(),
                params,
                GoodsData.class,
                new RequestListListener<GoodsData>() {
                    @Override
                    public void onResult(List<GoodsData> list) {
                        hideDialog();
                        if (page == 1) {
                            dataList1.clear();
                            smartRefreshLayout.finishRefresh();
                        } else {
                            smartRefreshLayout.finishLoadMore();
                        }
                        dataList1.addAll(list);
                        dataList2.clear();
                        dataList3.clear();
                        for (int i = 0; i < dataList1.size(); i++) {
                            if (dataList1.get(i).getShelfState() == 1) {
                                dataList2.add(dataList1.get(i));
                            } else if (dataList1.get(i).getShelfState() == 2) {
                                dataList3.add(dataList1.get(i));
                            }
                        }
                        switch (status) {
                            case 0:
                                dataList = dataList1;
                                mAdapter.setDataList(dataList);
                                setEmpty(dataList);
                                break;
                            case 1:
                                dataList = dataList2;
                                mAdapter.setDataList(dataList);
                                setEmpty(dataList);
                                break;
                            case 2:
                                dataList = dataList3;
                                mAdapter.setDataList(dataList);
                                setEmpty(dataList);
                                break;
                        }
                        getBatchList();
                    }

                    @Override
                    public void onError(String msg) {
                        Log.e(tag, "商品列表 error = " + msg);
                        hideDialog();
                        smartRefreshLayout.finishRefresh();
                        smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            dataList1.clear();
                            dataList2.clear();
                            dataList3.clear();
                            mAdapter.clear();
                            linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 商品上架/下架
     */
    private void postGoods() {
        if (!NetworkUtils.isConnectInternet(getActivity())) {
            showMessage(getLanguageValue("networkError"));
            return;
        }
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        params.put("pcShelfState", printerStatus);
        params.put("shelfState", appletStatus);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getGoodsShelfState(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showMessage(s);
                        page = 1;
                        getGoods();
                    }

                    @Override
                    public void onError(String msg) {
                        showMessage(msg);
                        hideDialog();
                    }
                });
    }

    /**
     * 设置列表空白页
     *
     * @param list
     */
    private void setEmpty(List<GoodsData> list) {
        if (dataList == null) {
            linEmpty.setVisibility(View.VISIBLE);
            return;
        }
        if (dataList.size() <= 0) {
            linEmpty.setVisibility(View.VISIBLE);
            return;
        }
        linEmpty.setVisibility(View.GONE);
    }

    /**
     * 保存分类排序
     *
     * @param cates
     */
    public void postCateSort(String cates) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShop_id());
        params.put("goods_kind_list", cates);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getbaocun(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        getCate();
                    }
                });
    }

    /**
     * 商品转移分类
     *
     * @param kindUnique
     */
    private void postGoodsKingTransfer(String kindUnique) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShop_id());
        map.put("goodsBarcode", goodsBarcode);
        map.put("kindUnique", kindUnique);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getGoodsKindTransfer(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        getCate();
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShop_id());
        params.put("goodsBarcode", goodsBarcode);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.deletegoods(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showMessage(s);
                        page = 1;
                        getGoods();
                    }
                });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            switch (requestCode) {
                case Constants.CATE:
                    //转移分类
                    IAlertDialog.showDialog(getActivity(),
                            getLanguageValue("confirmTransferClass") + "?",
                            getLanguageValue("confirm"),
                            (dialog, which) -> {
                                postGoodsKingTransfer(data.getStringExtra("unique"));
                            });
                    break;
                case Constants.BLUETOOTH:
                    //蓝牙返回mac地址
                    printerType = sharedPreferencesUtil.getInt(0, Constants.PRINTER_TYPE);
                    if (printType == 1) {
                        setPrintBatch();
                    } else {
                        setPrint();
                    }
                    break;
                case Constants.KU_INOUT:
                    //速录、扫码、添加商品
                    //出入库
                    page = 1;
                    getGoods();
                    break;
            }
        }
    }
}
