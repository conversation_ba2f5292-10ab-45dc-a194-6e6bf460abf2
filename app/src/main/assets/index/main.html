<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>HelloWord</title>
    <script type="text/javascript">
		
		if(window.plus){
			plusReady();
		}else{
			document.addEventListener('plusready', plusReady,false);
		}
		
		//扩展API准备完成后要执行的操作
		function plusReady(){
			
			var ws = plus.webview.currentWebview();
			
		}
    	//打开新webview窗口
		function openNewWebview(){

			alert("你点击了我");
			
		}
   		
   		
    </script>
</head>
<body>
	   Hello word <br/>
	   <button onclick="openNewWebview()">这是新页面</button>
	
	
</body>
</html>