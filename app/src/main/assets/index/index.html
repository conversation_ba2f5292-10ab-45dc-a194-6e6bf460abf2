<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>HelloWord</title>
    <script type="text/javascript">
		
		if(window.plus){
			plusReady();
		}else{
			document.addEventListener('plusready', plusReady,false);
		}
		
		//扩展API准备完成后要执行的操作
		function plusReady(){
			
			var ws = plus.webview.currentWebview();
			
		}
    	//打开新webview窗口
		function openNewWebview(){
			
			<!--var url = "file:///android_asset/index/main.html";-->
			<!--plus.webview.create(url).show();-->
			<!--document.write("You pressed OK!")-->
			<!--alert("你点击了我");-->
			<!--window.location.href = "main.html";-->
			contact.clickAndroid();
			
		}

   		
   		
    </script>
</head>
<body>
	   Hello word <br/>
	   <button onclick="openNewWebview()">打开新页面</button>
	
	
</body>
</html>