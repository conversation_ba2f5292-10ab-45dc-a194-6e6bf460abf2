<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="@dimen/dp2"
                android:left="@dimen/dp2"
                android:right="@dimen/dp2"
                android:top="@dimen/dp2" />
            <solid android:color="#25d1d1d1" />
            <corners android:radius="@dimen/dp8" />
        </shape>
    </item>

    <!-- 中心背景 -->
    <item>
        <shape
            android:shape="rectangle"
            android:useLevel="false">
            <solid android:color="@color/white" />
            <corners android:radius="@dimen/dp8" />
            <padding
                android:bottom="@dimen/dp8"
                android:left="@dimen/dp8"
                android:right="@dimen/dp8"
                android:top="@dimen/dp8" />
        </shape>
    </item>
</layer-list>